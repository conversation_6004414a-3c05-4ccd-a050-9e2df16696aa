{"components.StatusBadge.status": "{status}", "components.AppDataWarning.dockerVolumeMissingDescription": "Montowanie woluminu <code>{appDataPath}</code> nie zostało poprawnie skonfigurowane. Wszystkie dane zostaną wyczyszczone po zatrzymaniu lub ponownym uruchomieniu kontenera.", "components.Discover.DiscoverMovieGenre.genreMovies": "Filmy {genre}", "components.Discover.popularmovies": "Popularne filmy", "components.LanguageSelector.languageServerDefault": "Domyślny ({language})", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> we<PERSON><PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> wersja stabilna", "components.Login.email": "Adres e-mail", "components.Login.forgotpassword": "Zapomniane hasło?", "components.Login.password": "<PERSON><PERSON><PERSON>", "components.Login.loginerror": "Coś poszło nie tak przy próbie logowania.", "components.Login.signin": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.signingin": "Logowanie…", "components.Login.signinheader": "Zaloguj się aby kontynuować", "components.Login.signinwithoverseerr": "Użyj swojego konta {applicationTitle}", "components.IssueDetails.IssueComment.areyousuredelete": "<PERSON>zy na pewno chcesz usunąć ten komentarz?", "components.IssueDetails.IssueComment.delete": "Us<PERSON>ń komentarz", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.validationComment": "<PERSON><PERSON><PERSON> w<PERSON> wia<PERSON>", "components.IssueDetails.openedby": "#{issueId} ot<PERSON><PERSON> przez {username} {relativeTime}", "components.IssueModal.CreateIssueModal.submitissue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problem", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Raport o problemie dla <strong>{title}</strong> przesłany pomyślnie!", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Coś poszło nie tak podczas zgłaszania problemu.", "components.IssueDetails.IssueDescription.deleteissue": "Usuń problem", "components.IssueDetails.IssueDescription.edit": "<PERSON><PERSON><PERSON>j opis", "components.IssueDetails.allepisodes": "Wszystkie odcinki", "components.IssueDetails.allseasons": "Wszystkie sezony", "components.IssueDetails.closeissue": "<PERSON>am<PERSON><PERSON><PERSON> problem", "components.IssueDetails.closeissueandcomment": "Zamknij z komentarzem", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.episode": "<PERSON><PERSON><PERSON><PERSON> {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problem", "components.IssueDetails.lastupdated": "Ostatnio <PERSON>zowan<PERSON>", "components.IssueDetails.leavecomment": "Komentarz", "components.IssueDetails.openinarr": "Otw<PERSON>rz w {arr}", "components.IssueDetails.toasteditdescriptionfailed": "Coś poszło nie tak podczas edytowania opisu problemu.", "components.IssueDetails.toastissuedeletefailed": "Podczas usuwania problemu coś poszło nie tak.", "components.IssueDetails.toaststatusupdatefailed": "Coś poszło nie tak podczas aktualizowania stanu problemu.", "components.IssueDetails.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.openarr": "Otw<PERSON>rz w {arr}", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemepisode": "Dotknięty odcinek", "components.IssueModal.CreateIssueModal.problemseason": "Dotk<PERSON><PERSON><PERSON>", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problem", "components.IssueModal.issueSubtitles": "Na<PERSON><PERSON>", "components.IssueModal.issueVideo": "Wideo", "components.ManageSlideOver.manageModalIssues": "<PERSON><PERSON><PERSON><PERSON> problemy", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediarequested": "Prośba o oczekuje na zatwierdzenie", "components.PermissionEdit.admin": "Admin", "components.PermissionEdit.autoapprove4kMovies": "Automatyczne zatwierdzanie filmów 4K", "components.IssueDetails.openin4karr": "Otwórz w 4K {arr}", "components.IssueDetails.issuetype": "<PERSON><PERSON>", "components.IssueDetails.nocomments": "Brak komentarzy.", "components.IssueDetails.problemepisode": "<PERSON><PERSON><PERSON><PERSON>, którego dotyczy problem", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON>", "components.IssueList.IssueItem.opened": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.openeduserdate": "{date} przez {user}", "components.IssueList.IssueItem.problemepisode": "<PERSON><PERSON><PERSON><PERSON>, którego dotyczy problem", "components.IssueList.IssueItem.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "<PERSON><PERSON><PERSON><PERSON> {episodeNumber}", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON><PERSON> problem", "components.IssueList.issues": "<PERSON><PERSON>", "components.IssueList.showallissues": "Pokaż wszystkie problemy", "components.IssueList.sortModified": "Ostatnio zmodyfikowane", "components.IssueModal.CreateIssueModal.allepisodes": "Wszystkie odcinki", "components.IssueModal.CreateIssueModal.allseasons": "Wszystkie sezony", "components.Login.signinwithplex": "Użyj swojego konta Plex", "components.Login.validationemailrequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy adres e-mail", "components.Login.validationpasswordrequired": "<PERSON><PERSON><PERSON> hasło", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON>", "components.LanguageSelector.originalLanguageDefault": "Wszystkie języki", "components.Layout.LanguagePicker.displaylanguage": "Język wyświetlania", "components.Layout.SearchInput.searchPlaceholder": "Szukaj filmów i seriali", "components.Layout.Sidebar.dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.issues": "<PERSON><PERSON>", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Ustawienia", "components.Layout.Sidebar.users": "Użytkownicy", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.UserDropdown.settings": "Ustawienia", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commity}} za", "components.Layout.VersionStatus.outofdate": "Nieaktualny", "components.ManageSlideOver.manageModalClearMedia": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane", "components.ManageSlideOver.manageModalNoRequests": "Brak próśb.", "components.NotificationTypeSelector.issuecomment": "Komentarz do problemu", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy problemy otrzymają nowe komentarze.", "components.NotificationTypeSelector.issuecreated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problem", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>dom<PERSON>, gdy zostaną zgłoszone problemy.", "components.NotificationTypeSelector.issueresolved": "Problem rozwiązany", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy problemy zostaną rozwiązane.", "components.NotificationTypeSelector.mediaAutoApproved": "Prośba o automatyczne zatwierdzenie", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> pow<PERSON>dom<PERSON>ia, gdy użytkownicy składają nowe prośby o media, które są automatycznie zatwierdzane.", "components.NotificationTypeSelector.mediaapprovedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy prośby o multimedia zostaną ręcznie zatwierdzone.", "components.NotificationTypeSelector.mediaavailableDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy proś<PERSON> o multimedia staną się dostępne.", "components.NotificationTypeSelector.mediadeclinedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy <PERSON>ś<PERSON> o multimedia zostaną odrzucone.", "components.NotificationTypeSelector.mediafailed": "Przetwarzanie żądania nie powiodło się", "components.PermissionEdit.autoapproveSeriesDescription": "Przyznaj automatyczne zatwierdzanie próśb o filmy inne niż 4K.", "components.PermissionEdit.createissues": "Zgłoś problemy", "components.PermissionEdit.manageissues": "Zarządzaj <PERSON>ami", "components.PermissionEdit.manageissuesDescription": "Udziel uprawnień do zarządzania problemami z multimediami.", "components.ManageSlideOver.manageModalClearMediaWarning": "* Spowoduje to nieodwracalne usunięcie wszystkich danych dla {mediaType}, w tym wszelkie prośby. Jeśli ten element istnieje w Twojej bibliotece {mediaServerName}, informacje o multimediach zostaną odtworzone podczas następnego skanowania.", "components.IssueModal.CreateIssueModal.providedetail": "Podaj szczegółowe wyjaśnienie napotkanego problemu.", "components.IssueModal.CreateIssueModal.whatswrong": "Co jest nie tak?", "components.Discover.MovieGenreList.moviegenres": "Gatunki filmowe", "components.Discover.TvGenreList.seriesgenres": "Gatunki seriali", "components.Discover.recentrequests": "Ostatnie <PERSON>", "components.Discover.upcomingmovies": "Nadchodzące filmy", "components.IssueModal.CreateIssueModal.reportissue": "<PERSON><PERSON>ł<PERSON>ś problem", "components.IssueModal.CreateIssueModal.season": "Sezon {seasonNumber}", "components.IssueModal.CreateIssueModal.validationMessageRequired": "<PERSON><PERSON><PERSON> opis", "components.IssueModal.issueAudio": "Audio", "components.IssueDetails.reopenissueandcomment": "Otwórz ponownie z komentarzem", "components.IssueModal.issueOther": "Inny", "components.CollectionDetails.numberofmovies": "{count} <PERSON>y", "components.Discover.MovieGenreSlider.moviegenres": "Gatunki filmowe", "components.Discover.upcoming": "Nadchodzące filmy", "components.IssueDetails.problemseason": "<PERSON><PERSON>, którego dotyczy problem", "components.IssueDetails.season": "Sezon {seasonNumber}", "components.IssueDetails.toastissuedeleted": "Pomyślnie usunięto problem!", "components.CollectionDetails.overview": "Podsumowanie", "components.Discover.StudioSlider.studios": "St<PERSON><PERSON>", "components.Discover.discover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.TvGenreSlider.tvgenres": "Gatunki seriali", "components.Discover.recentlyAdded": "Niedawno dodane", "components.IssueDetails.toasteditdescriptionsuccess": "Pomyślnie edytowano opis problemu!", "components.IssueDetails.IssueDescription.description": "Opis", "components.IssueDetails.toaststatusupdated": "Pomyślnie zaktualizowano status problemu!", "components.IssueDetails.deleteissueconfirm": "<PERSON>zy na pewno chcesz usunąć ten problem?", "components.NotificationTypeSelector.userissuecommentDescription": "Otrzy<PERSON><PERSON>dom<PERSON>, gdy problemy zgłoszone przez ciebie otrzymają nowe komentarze.", "components.MovieDetails.overview": "Przegląd", "components.NotificationTypeSelector.usermediaavailableDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy <PERSON>je prośby o multimedia staną się dostępne.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Przyznaj automatyczne zatwierdzanie próśb o seriale 4K.", "components.IssueDetails.deleteissue": "Usuń problem", "components.MovieDetails.budget": "Budżet", "components.MovieDetails.mark4kavailable": "Oznacz jako dostępne w 4K", "components.IssueDetails.play4konplex": "Odtwarzanie w 4K na platformie {mediaServerName}", "components.ManageSlideOver.movie": "film", "components.IssueDetails.reopenissue": "Otwórz ponownie problem", "components.MovieDetails.recommendations": "Rekomendacje", "components.PermissionEdit.autoapprove4kDescription": "Przyznaj automatyczne zatwierdzanie wszystkich próśb o media 4K.", "components.MovieDetails.watchtrailer": "Obejrzyj zwiastun", "components.NotificationTypeSelector.adminissuecommentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy inni użytkownicy skomentują problem.", "components.MovieDetails.showless": "Pokaż mniej", "components.MovieDetails.similar": "Podobne tytuły", "components.ManageSlideOver.markavailable": "Oznacz jako <PERSON>", "components.MediaSlider.ShowMoreCard.seemore": "Zobacz więcej", "components.PermissionEdit.adminDescription": "Pełny dostęp administratora. Omija wszystkie inne kontrole uprawnień.", "components.ManageSlideOver.manageModalTitle": "Zarządzaj {mediaType}", "components.ManageSlideOver.mark4kavailable": "Oznacz jako dostępne w 4K", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy inni użytkownicy zgłaszają problemy.", "components.NotificationTypeSelector.userissueresolvedDescription": "<PERSON>tr<PERSON><PERSON><PERSON>, gdy zgłoszone przez ciebie problemy zostaną rozwiązane.", "components.NotificationTypeSelector.usermediadeclinedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy Twoje prośby o multimedia zostaną odrzucone.", "components.PermissionEdit.autoapprove4k": "Automatycznie zatwierdzaj 4K", "components.ManageSlideOver.openarr4k": "Otwórz w 4K {arr}", "components.ManageSlideOver.tvshow": "seria", "components.MovieDetails.MovieCast.fullcast": "Pełna obsada", "components.MovieDetails.cast": "Obsada", "components.MovieDetails.markavailable": "Oznacz jako <PERSON>", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.originallanguage": "<PERSON><PERSON><PERSON><PERSON>ygin<PERSON>ł<PERSON>", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "Przegląd niedostępny.", "components.MovieDetails.showmore": "Pokaż więcej", "components.NotificationTypeSelector.notificationTypes": "Typy powiadomień", "components.NotificationTypeSelector.usermediaapprovedDescription": "Otr<PERSON><PERSON><PERSON>, gdy Twoje prośby o multimedia zostaną zatwierdzone.", "components.NotificationTypeSelector.usermediarequestedDescription": "Otr<PERSON><PERSON><PERSON>, gdy inni użytkownicy prześlą nowe prośby o multimedia, które wymagają zatwierdzenia.", "components.PermissionEdit.autoapprove": "Automatycznie zatwierdzaj", "components.PermissionEdit.autoapproveMovies": "Automatyczne zatwierdzanie filmów", "components.PermissionEdit.autoapproveMoviesDescription": "Przyznaj automatyczne zatwierdzanie próśb o filmy inne niż 4K.", "components.PermissionEdit.requestTvDescription": "Udziel pozwolenia na przesyłanie próśb o seriale inne niż 4K.", "components.PersonDetails.appearsin": "Wystąpienia", "components.PersonDetails.alsoknownas": "Znany również jako: {names}", "components.PermissionEdit.viewissuesDescription": "Przyznaj uprawnienia do przeglądania problemów z multimediami zgłoszonych przez innych użytkowników.", "components.QuotaSelector.unlimited": "Bez ograniczeń", "components.RegionSelector.regionDefault": "Wszystkie regiony", "components.RegionSelector.regionServerDefault": "Domyślny ({region})", "components.RequestBlock.profilechanged": "<PERSON><PERSON>", "components.RequestBlock.server": "<PERSON><PERSON> do<PERSON>owy", "components.RequestBlock.requestoverrides": "Zastąpienia żądań", "components.RequestBlock.rootfolder": "Folder główny", "components.RequestButton.approverequest": "Zatwierdź prośbę", "components.PersonDetails.lifespan": "{birthdate} - {deathdate}", "components.RequestButton.requestmore": "Poproś o więcej", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON> prośbę", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON><PERSON> prośbę", "components.RequestButton.viewrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.requestmore4k": "Poproś o więcej w 4K", "components.RequestModal.AdvancedRequester.notagoptions": "Brak tagów.", "components.RequestList.sortModified": "Ostatnio zmodyfikowany", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON><PERSON>", "components.PermissionEdit.managerequestsDescription": "Przyznaj uprawnienia do zarządzania prośbami o multimedia. Wszystkie prośby złożone przez użytkownika z tym uprawnieniem zostaną automatycznie zatwierdzone.", "components.PermissionEdit.request4k": "Poproś o 4K", "components.PermissionEdit.request4kDescription": "Udziel zgody na przesyłanie próśb o multimedia 4K.", "components.PermissionEdit.request4kMovies": "Poproś o filmy 4K", "components.PermissionEdit.request4kMoviesDescription": "Udziel zgody na przesyłanie próśb o filmy 4K.", "components.PermissionEdit.request4kTvDescription": "Udziel zgody na przesyłanie próśb o seriale 4K.", "components.PermissionEdit.requestMovies": "Poproś o filmy", "components.PermissionEdit.requestMoviesDescription": "Udziel pozwolenia na przesyłanie próśb o filmy inne niż 4K.", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON> prośbę", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modified": "Zmodyfikowan<PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} przez {user}", "components.RequestList.RequestItem.requested": "Prośba zgłoszona", "components.RequestList.RequestItem.requesteddate": "Prośba zgłoszona", "components.RequestList.showallrequests": "Pokaż wszystkie prośby", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON> do<PERSON>owy", "components.RequestModal.AdvancedRequester.qualityprofile": "<PERSON><PERSON>", "components.RequestModal.AdvancedRequester.rootfolder": "Folder główny", "components.RequestModal.AdvancedRequester.selecttags": "<PERSON><PERSON><PERSON><PERSON> tagi", "components.RequestModal.AdvancedRequester.tags": "Tagi", "components.RequestModal.QuotaDisplay.movie": "film", "components.PermissionEdit.viewrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.users": "Zarządzanie użytkownikami", "components.PermissionEdit.viewissues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> problemy", "components.PersonDetails.birthdate": "Urodzony {birthdate}", "components.PermissionEdit.usersDescription": "Udziel uprawnień do zarządzania użytkownikami. Użytkownicy z tym uprawnieniem nie mogą modyfikować użytkowników z uprawnieniami administratora ani ich udzielać.", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filmy po {language}", "components.Discover.DiscoverNetwork.networkSeries": "Seriale z {network}", "components.Discover.DiscoverStudio.studioMovies": "Filmy studia {studio}", "components.Discover.NetworkSlider.networks": "Platformy", "components.Discover.populartv": "Popularne seriale", "components.Discover.trending": "<PERSON><PERSON>", "components.Discover.upcomingtv": "Nadchodzące seriale", "components.DownloadBlock.estimatedtime": "Szacowany czas {time}", "components.RequestModal.seasonnumber": "Sezon {number}", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload zresetowany pomyślnie!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Template Variable Help", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Wysłano powiadomienie testowe webhook!", "components.Settings.Notifications.encryptionOpportunisticTls": "Zawsze używaj STARTTLS", "components.Settings.Notifications.pgpPrivateKey": "Klucz prywatny PGP", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON><PERSON><PERSON> po cichu", "components.Settings.Notifications.sendSilentlyTip": "Wysyłaj powiadomienia bez dźwięku", "components.Settings.Notifications.senderName": "Nazwa nadawcy", "components.Settings.Notifications.smtpHost": "Host SMTP", "components.Settings.Notifications.smtpPort": "Port SMTP", "components.Settings.Notifications.telegramsettingsfailed": "Nie udało się zapisać ustawień powiadomień Telegram.", "components.Settings.Notifications.telegramsettingssaved": "Ustawienia powiadomień Telegram zostały zapisane pomyślnie!", "components.Settings.Notifications.toastDiscordTestFailed": "<PERSON><PERSON> udało się wysłać powiadomienia testowego Discord.", "components.Settings.Notifications.toastDiscordTestSending": "Wysłanie powiadomienia testowego Discord…", "components.Settings.Notifications.toastDiscordTestSuccess": "Powiadomienie testowe Discord wysłane!", "components.Settings.Notifications.toastEmailTestFailed": "<PERSON>e udało się wysłać testowego powiadomienia e-mail.", "components.Settings.Notifications.toastEmailTestSending": "Wysyłanie powiadomienia testowego e-mail…", "components.Settings.Notifications.toastEmailTestSuccess": "Wysłano powiadomienie testowe e-mail!", "components.Settings.Notifications.toastTelegramTestFailed": "<PERSON>e udało się wysłać powiadomienia testowego Telegram.", "components.Settings.Notifications.toastTelegramTestSuccess": "Wysłano powiadomienie testowe Telegram!", "components.Settings.RadarrModal.hostname": "Nazwa hosta lub adres IP", "components.Settings.RadarrModal.servername": "<PERSON><PERSON><PERSON> ser<PERSON>a", "components.Settings.RadarrModal.ssl": "Użyj SSL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Mu<PERSON>z dostar<PERSON>ć poprawny payload JSON", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON> serwer", "components.Settings.RadarrModal.notagoptions": "Brak tagów.", "components.Settings.RadarrModal.validationApplicationUrl": "Musisz podać poprawny adres URL", "components.Settings.RadarrModal.loadingTags": "Ładowanie tagów…", "components.PersonDetails.crewmember": "Ekipa", "components.RequestButton.approverequest4k": "Zatwierdź prośbę 4K", "components.RequestButton.declinerequest4k": "Odrzuć prośbę o 4K", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.requestseasons": "<PERSON><PERSON><PERSON> o {seasonCount} {seasonCount, plural, one {sezon} other {sezony}}", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON>(y)", "components.ResetPassword.gobacklogin": "Wróć do strony logowania", "components.ResetPassword.requestresetlinksuccessmessage": "Link do resetowania hasła zostanie wysłany na podany adres e-mail, je<PERSON><PERSON> jest on powiązany z użytkownikiem.", "components.ResetPassword.resetpassword": "Zresetuj swoje hasło", "components.ResetPassword.validationemailrequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy adres e-mail", "components.ResetPassword.validationpasswordmatch": "Hasła muszą być zgodne", "components.ResetPassword.validationpasswordminchars": "Hasło jest zbyt krótkie; powinno mieć co najmniej 8 znaków", "components.ResetPassword.validationpasswordrequired": "<PERSON><PERSON><PERSON> hasło", "components.Search.search": "Szukaj", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> w<PERSON>, gdy nie używasz profilu <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "<PERSON>e udało się zapisać ustawień powiadomień LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Ustawienia powiadomień LunaSea zostały pomyślnie zapisane!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "<PERSON><PERSON> udało się wysłać powiadomienia testowego LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Wysyłanie powiadomienia testowego LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Wysłano powiadomienie testowe LunaSea!", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Musisz podać poprawny adres URL", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL Webhook", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Token dostępu (Access Token)", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink> Zarejestruj aplikację</ApplicationRegistrationLink> do użytku z Jellyseerr", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Pushover.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Ustawienia powiadomień Pushover zapisane pomyślnie!", "components.Settings.Notifications.NotificationsPushover.userToken": "Klucz użytkownika lub grupy", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "<PERSON>e udało się wysłać powiadomienia testowego Slack.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Wysyłanie powiadomienia testowego Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Wysłano powiadomienie testowe Slack!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Mu<PERSON>z podać prawidłowy adres URL", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL Webhook", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Utwórz integrację <WebhookLink>Incoming Webhook</WebhookLink>", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> powiadomienia web push, <PERSON><PERSON><PERSON><PERSON> musi by<PERSON> u<PERSON> za pośrednictwem HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "<PERSON><PERSON> udało się wysłać powiadomienia testowego Web push.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Wysyłanie powiadomia testowego web push…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Powiadomienie testowe web push wysłane!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Web Push.", "components.Settings.Notifications.NotificationsWebhook.authheader": "Nagłówek autoryzacji", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Musisz podać poprawny adres URL", "components.Settings.Notifications.authUser": "Nazwa użytkownika SMTP", "components.Settings.Notifications.botAPI": "Token autoryzacji bota", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Stw<PERSON>rz bota</CreateBotLink> do użycia z Jellyseerr", "components.Settings.Notifications.botAvatarUrl": "Adres URL awatara bota", "components.Settings.Notifications.botUsername": "Nazwa użytkownika bota", "components.Settings.Notifications.validationPgpPassword": "<PERSON><PERSON><PERSON> pod<PERSON>ć hasło PGP", "components.Settings.Notifications.validationPgpPrivateKey": "Należy podać prawidłowy klucz prywatny PGP", "components.Settings.Notifications.validationSmtpHostRequired": "<PERSON><PERSON>z podać prawidłową nazwę hosta lub adres IP", "components.Settings.Notifications.webhookUrl": "URL Webhook", "components.Settings.Notifications.webhookUrlTip": "Utwórz <DiscordWebhookLink>integracj<PERSON> webhook</DiscordWebhookLink> na swoim serwerze", "components.Settings.Notifications.NotificationsWebhook.customJson": "Payload JSON", "components.Settings.RadarrModal.baseUrl": "Baza URL", "components.Settings.RadarrModal.selectMinimumAvailability": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON><PERSON> profil j<PERSON>", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "<PERSON><PERSON><PERSON> w<PERSON>brać <PERSON>", "components.Settings.RadarrModal.validationNameRequired": "Musisz podać nazwę serwera", "components.Settings.RadarrModal.validationPortRequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy numer portu", "components.Settings.RadarrModal.validationRootFolderRequired": "Musisz wybrać folder główny", "components.Settings.SettingsAbout.Releases.currentversion": "Aktualny", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Dane o wydaniu są obecnie niedostępne.", "components.Settings.SettingsAbout.Releases.releases": "Wydania", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} <PERSON><PERSON> z<PERSON>", "components.RequestModal.QuotaDisplay.season": "sezon", "components.RequestModal.requestcancelled": "<PERSON>ś<PERSON> o <strong>{title}</strong> została anulowana.", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.RequestModal.requestSuccess": "<PERSON>ś<PERSON> o <strong>{title}</strong> wysłana p<PERSON>!", "components.RequestModal.season": "sezon", "components.ResetPassword.emailresetlink": "Link do odzyskiwania przez adres e-mail", "components.RequestModal.requestadmin": "Ta prośba zostanie zatwierdzona automatycznie.", "components.ResetPassword.passwordreset": "<PERSON><PERSON><PERSON><PERSON> hasła", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Powiadomienie testowe Pushbullet wysłane!", "components.ResetPassword.confirmpassword": "Potwierd<PERSON> hasło", "components.ResetPassword.email": "Adres e-mail", "components.ResetPassword.password": "<PERSON><PERSON><PERSON>", "components.ResetPassword.resetpasswordsuccessmessage": "Hasło zostało zresetowane pomyślnie!", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nazwa profilu", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "<PERSON><PERSON><PERSON> dostę<PERSON>", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<LunaSeaLink>Tw<PERSON>j adres URL obiektu webhook powiadomienia</LunaSeaLink> oparty na użytkowniku lub urząd<PERSON>iu", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Ustawienia powiadomień pushbullet zostały pomyślnie zapisane!", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Powiadomienie testowe Pushover wysłane!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Slack.", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Ustawienia powiadomień web push zostały pomyślnie zapisane!", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Serial", "components.MovieDetails.runtime": "{minutes} minuty", "components.IssueDetails.IssueComment.postedby": "Opublikowane przez {username} {relativeTime}", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "<PERSON><PERSON> udało się wysłać powiadomienia testowego Pushover.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "<PERSON><PERSON><PERSON> pod<PERSON> prawidłowy token aplikacji", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Należy podać prawidłowy klucz użytkownika lub grupy", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Wysyłanie powiadomienia testowego Pushover…", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Twój 30-znakowy <UsersGroupsLink>identyfikator użytkownika lub grupy</UsersGroupsLink>", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Ustawienia powiadomień Slack zapisane pomyślnie!", "components.Discover.DiscoverTvGenre.genreSeries": "Seriale {genre}", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Wysyłanie powiadomienia testowego webhook…", "components.Settings.Notifications.botUsernameTip": "Pozwól użytkownikom również rozpocząć czat z botem i skonfigurować własne powiadomienia", "components.Settings.Notifications.pgpPrivateKeyTip": "Podpisuj zaszyfrowane wiadomości e-mail za pomocą <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ustawienia domyślne", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "<PERSON>e udało się wysłać powiadomienia testowego Webhook.", "components.Settings.Notifications.encryptionTip": "W większości przypadków niejawny TLS używa portu 465, a STARTTLS używa portu 587", "components.Settings.Notifications.pgpPassword": "Hasło PGP", "components.Settings.Notifications.pgpPasswordTip": "Podpisuj zaszyfrowane wiadomości e-mail za pomocą <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.IssueDetails.IssueComment.postedbyedited": "Opublikowane przez {username} {relativeTime} (edytowano)", "components.MovieDetails.MovieCrew.fullcrew": "Pełna obsada", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studia}}", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy inni użytkownicy składają nowe prośby o multimedia, które są automatycznie zatwierdzane.", "components.Settings.RadarrModal.edit4kradarr": "Edyt<PERSON>j serwer 4K Radarr", "components.Settings.RadarrModal.loadingprofiles": "Ładowanie profili j<PERSON>…", "components.CollectionDetails.requestcollection4k": "Poproś o kolekcję w 4K", "components.MovieDetails.streamingproviders": "Obecnie dostępne na", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data wydania} other {Daty wydania}}", "components.MovieDetails.viewfullcrew": "Zobacz pełną obsadę", "components.PermissionEdit.autoapproveDescription": "Przyznaj automatyczne zatwierdzanie wszystkich próśb o multimedia inne niż 4K.", "components.RequestButton.approve4krequests": "<PERSON><PERSON><PERSON><PERSON><PERSON> {requestCount, plural, one {<PERSON>ś<PERSON>} other {{requestCount} Prośby}} 4K", "components.Settings.RadarrModal.minimumAvailability": "<PERSON><PERSON><PERSON>", "components.RequestButton.decline4krequests": "<PERSON><PERSON><PERSON><PERSON> {requestCount, plural, one {prośby 4K} other {{requestCount} próśb 4K}}", "components.NotificationTypeSelector.usermediafailedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy pro<PERSON> o <PERSON> nie zostaną dodane do Radarr lub Sonarr.", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.advancedrequestDescription": "Przyznaj uprawnienia do modyfikowania zaawansowanych opcji próśb multimedia.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Przyznaj automatyczne zatwierdzanie próśb o filmy 4K.", "components.PermissionEdit.autoapprove4kSeries": "Automatyczne zatwierdzanie seriali 4K", "components.PermissionEdit.autoapproveSeries": "Automatyczne zatwierdzanie seriali", "components.QuotaSelector.seasons": "{count, plural, one {sezon} other {sezony}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} na{quotaDays} {days}</quotaUnits>", "components.RequestBlock.seasons": "{seasonCount, plural, one {sezon} other {sezony}}", "components.RequestButton.viewrequest4k": "Wyświetl prośbę 4K", "components.RequestCard.failedretry": "Coś poszło nie tak podczas ponawiania prośby.", "components.RequestButton.approverequests": "Zat<PERSON><PERSON><PERSON> {requestCount, plural, one {prośba} other {{requestCount} próśb}}", "components.NotificationTypeSelector.mediarequestedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy użytkownicy składają nowe prośby dotyczące multimediów, które wymagają zatwierdzenia.", "components.PermissionEdit.requestDescription": "Udzielenie zgody na składanie próśb na multimedia inne niż 4K.", "components.PermissionEdit.viewrequestsDescription": "Przyznaj uprawnienia do przeglądania próśb o multimedia przesłanych przez innych użytkowników.", "components.PersonDetails.ascharacter": "jako {character}", "components.QuotaSelector.days": "{count, plural, one {d<PERSON><PERSON>} other {dni}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} na {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {film} other {filmy}}", "components.RequestButton.declinerequests": "<PERSON><PERSON><PERSON><PERSON> {requestCount, plural, one {prośbę} other {{requestCount} prośby}}", "components.RequestCard.mediaerror": "Tytuł skojarzony z tą prośbą nie jest już dos<PERSON>ę<PERSON>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Możesz wyświetlić podsumowanie limitów próśb tego użytkownika na jego <ProfileLink>stronie profilu</ProfileLink>.", "components.Settings.Notifications.validationSmtpPortRequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy numer portu", "components.Settings.RadarrModal.create4kradarr": "Dodaj nowy serwer 4K Radarr", "components.Settings.RadarrModal.createradarr": "<PERSON><PERSON><PERSON> nowy serwer <PERSON>", "components.RequestModal.QuotaDisplay.quotaLink": "Możesz wyświetlić podsumowanie limitów próśb na swojej <ProfileLink>stronie profilu</ProfileLink>.", "components.Settings.Notifications.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.validationUrl": "Mu<PERSON>z podać prawidłowy adres URL", "components.Settings.RadarrModal.default4kserver": "Domyślny serwer 4K", "components.Settings.RadarrModal.loadingrootfolders": "Ładowanie folderów głównych…", "components.Settings.RadarrModal.defaultserver": "Domyślny serwer", "components.Settings.RadarrModal.validationProfileRequired": "<PERSON><PERSON><PERSON> w<PERSON> profil j<PERSON>", "components.IssueDetails.playonplex": "Odtwórz na {mediaServerName}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {sezon} other {sezony}}", "components.NotificationTypeSelector.mediaapproved": "Prośba zatwierdzona", "components.NotificationTypeSelector.mediaavailable": "Dostępne", "components.NotificationTypeSelector.mediadeclined": "Prośba odrzucona", "components.NotificationTypeSelector.mediafailedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy pro<PERSON> o multimedia nie zostaną dodane do Radarr lub Sonarr.", "components.PermissionEdit.createissuesDescription": "Udzielanie zgody na zgłaszanie problemów z multimediami.", "components.PermissionEdit.managerequests": "Zarządzaj <PERSON>mi", "components.PermissionEdit.request": "Prośba", "components.PermissionEdit.request4kTv": "Poproś o serial w 4K", "components.PermissionEdit.requestTv": "Poproś o serial", "components.RequestCard.seasons": "{seasonCount, plural, one {Sezon} other {Sezony}}", "components.RequestList.RequestItem.failedretry": "Coś poszło nie tak podczas ponawiania prośby.", "components.RequestList.RequestItem.mediaerror": "Tytuł powiązany z tą prośbą nie jest już dos<PERSON>ę<PERSON>.", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {sezon} other {sezony}}", "components.RequestList.requests": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.animenote": "* Ta seria to anime.", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON> jako", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON>} other {<strong>#</strong>}} {type} {remaining, plural, one {żądanie} other {żądań}} pozostało", "components.RequestModal.QuotaDisplay.requiredquota": "<PERSON><PERSON> prz<PERSON><PERSON>ć prośbę o ten serial, musisz mieć co najmniej <strong>{seasons}</strong> {seasons, plural, one {prośbę} other {prośby}}.", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON><PERSON><PERSON><PERSON><strong>{limit}</strong> próśb {type} co <strong>{days}</strong> dni.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Ten użytkownik może p<PERSON> <strong>{limit}</strong> próśb {type} co <strong>{days}</strong> dni.", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {filmy}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Brak wystarczającej liczby próśb o sezon", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Aby przesłać prośbę o ten serial, ten użytkownik musi mieć co najmniej <strong>{seasons}</strong> {seasons, plural, one {prośbę} other {prośby}}.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezon} other {sezony}}", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Nie mogliśmy automatycznie spełnić Twojej <PERSON>. Wybierz odpowiednie dopasowanie z poniższej listy.", "components.RequestModal.alreadyrequested": "<PERSON><PERSON>", "components.RequestModal.autoapproval": "Automatyczne zatwierdzenie", "components.RequestModal.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.errorediting": "Coś poszło nie tak podczas edytowania prośby.", "components.RequestModal.numberofepisodes": "Liczba odcinków", "components.RequestModal.pending4krequest": "Oczekująca prośba o 4K", "components.RequestModal.pendingapproval": "Twoja prośba oczekuje na zatwierdzenie.", "components.RequestModal.pendingrequest": "Oczekująca pro<PERSON>", "components.RequestModal.requestCancel": "<PERSON>ś<PERSON> o <strong>{title}</strong> została anulowana.", "components.RequestModal.requestedited": "Proś<PERSON> o <strong>{title}</strong> została pomyślnie edytowana!", "components.RequestModal.requesterror": "Coś poszło nie tak podczas przesyłania prośby.", "components.RequestModal.requestfrom": "Prośba użytkownika {username} oczekuje na zatwierdzenie.", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON>", "components.Search.searchresults": "Wyniki wyszukiwania", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Utwórz token z poziomu <PushbulletSettingsLink>Ustawień konta</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "<PERSON>e udało się wysłać powiadomienia testowego Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Wysyłanie powiadomienia testowego Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Włącz agenta", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "<PERSON>e udało się zapisać ustawień powiadomień Pushbullet.", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token API aplikacji", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL Webhook", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień webhook.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Ustawienia powiadomień webhook zostały pomyślnie zapisane!", "components.Settings.Notifications.agentenabled": "Włącz agenta", "components.Settings.Notifications.allowselfsigned": "Zezwalaj na certyfikaty z podpisem własnym", "components.Settings.Notifications.authPass": "Hasło SMTP", "components.Settings.Notifications.encryptionNone": "Brak", "components.Settings.Notifications.toastTelegramTestSending": "Wysyłanie powiadomia testowego Telegram…", "components.Settings.Notifications.validationBotAPIRequired": "<PERSON><PERSON><PERSON> token autoryzacji bota", "components.Settings.Notifications.validationChatIdRequired": "Mu<PERSON>z podać poprawny identyfikator c<PERSON>", "components.Settings.Notifications.validationEmail": "<PERSON><PERSON>z podać poprawny adres e-mail", "components.Settings.Notifications.chatId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Chat ID)", "components.Settings.Notifications.chatIdTip": "Rozpocznij czat ze swoim botem, dodaj <GetIdBotLink>@get_id_bot</GetIdBotLink> i wydaj polecenie <code>/my_id</code>", "components.Settings.Notifications.discordsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Discord.", "components.Settings.Notifications.discordsettingssaved": "Ustawienia powiadomień Discorda zapisane pomyślnie!", "components.Settings.Notifications.emailsender": "<PERSON><PERSON>", "components.Settings.Notifications.emailsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień e-mail.", "components.Settings.Notifications.emailsettingssaved": "Ustawienia powiadomień e-mail zostały zapisane pomyślnie!", "components.Settings.Notifications.encryption": "<PERSON><PERSON> s<PERSON>", "components.Settings.Notifications.encryptionDefault": "<PERSON><PERSON><PERSON>j STARTTLS, j<PERSON><PERSON><PERSON> jest dos<PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Używanie niejawnego protokołu TLS", "components.Settings.RadarrModal.apiKey": "Klucz API", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.RadarrModal.rootfolder": "Folder główny", "components.Settings.RadarrModal.selectRootFolder": "Wybierz folder główny", "components.Settings.RadarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON> tagi", "components.Settings.RadarrModal.server4k": "Serwer 4K", "components.Settings.RadarrModal.testFirstQualityProfiles": "Połączenie testowe w celu załadowania profili jak<PERSON>ci", "components.Settings.RadarrModal.testFirstRootFolders": "Połączenie testowe w celu załadowania folderów głównych", "components.Settings.RadarrModal.testFirstTags": "Połączenie testowe, aby wczytać tagi", "components.Settings.RadarrModal.toastRadarrTestFailure": "Nie udało się połączyć z Radarr.", "components.Settings.RadarrModal.editradarr": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "components.Settings.RadarrModal.enableSearch": "Włącz automatyczne wyszukiwanie", "components.Settings.RadarrModal.externalUrl": "Zewnętrzny adres URL", "components.Settings.RadarrModal.syncEnabled": "Włącz skanowanie", "components.Settings.RadarrModal.tags": "Tagi", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Połączenie z Radarr nawiązane pomyślnie!", "components.Settings.RadarrModal.validationApiKeyRequired": "<PERSON><PERSON>z pod<PERSON>ć klucz <PERSON>", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Adres URL nie może kończyć się ukośnikiem", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Bazowy adres URL musi zaczynać się ukośnikiem", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Bazowy URL nie może być zakończony ukośnikiem", "components.Settings.RadarrModal.validationHostnameRequired": "<PERSON><PERSON>z podać prawidłową nazwę hosta lub adres IP", "components.Settings.SettingsJobsCache.editJobSchedule": "Zmodyfikuj <PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Co{jobScheduleHours, plural, one {godzinę} other {{jobScheduleHours} godzin}}", "components.Settings.SettingsJobsCache.flushcache": "Opróżnij pamięć podręczną", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Co {jobScheduleMinutes, plural, one {minutę} other {{jobScheduleMinutes} minut}}", "components.Settings.SettingsJobsCache.jobs": "Zadania", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} roz<PERSON>częte.", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Skanowanie ostatnio dodanych na Plex", "components.Settings.SettingsJobsCache.process": "Proces", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "Skan<PERSON><PERSON>r", "components.Settings.SettingsJobsCache.runnow": "Uruchom teraz", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.copiedLogMessage": "Skopiowano komunikat dziennika do schowka.", "components.Settings.SettingsLogs.extraData": "Dodatko<PERSON> dane", "components.Settings.SettingsLogs.filterDebug": "Debug", "components.Settings.SettingsLogs.filterInfo": "Informacje", "components.Settings.SettingsLogs.filterWarn": "Ostrzeżenie", "components.Settings.SettingsLogs.level": "Powaga", "components.Settings.SettingsLogs.logDetails": "Szczegóły dziennika", "components.Settings.SettingsLogs.label": "Etykieta", "components.Settings.SettingsLogs.logs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.resumeLogs": "Wznów", "components.Settings.SettingsLogs.showall": "Pokaż wszystkie logi", "components.Settings.SettingsLogs.time": "Sygnatura c<PERSON>", "components.Settings.SettingsUsers.defaultPermissions": "Domyślne uprawnienia", "components.Settings.SettingsUsers.localLogin": "Włącz lokalne logowanie", "components.Settings.SettingsUsers.localLoginTip": "Zezwalaj użytkownikom na logowanie się przy użyciu adresu e-mail i hasła, a nie Plex OAuth", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globalny limit próśb o filmy", "components.Settings.SettingsAbout.totalrequests": "Łączna liczba próśb", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON><PERSON> podręczna", "components.Settings.SettingsAbout.Releases.viewchangelog": "Zobacz dziennik zmian", "components.Settings.SettingsAbout.Releases.viewongithub": "Zobacz na GitHub", "components.Settings.SettingsAbout.about": "O", "components.Settings.SettingsAbout.betawarning": "To jest oprogramowanie BETA. Funkcje mogą być uszkodzone i/lub niestabilne. Zgłaszaj wszelkie problemy na GitHub!", "components.Settings.SettingsAbout.documentation": "Dokumentacja", "components.Settings.SettingsAbout.gettingsupport": "Uzyskiwanie pomocy technicznej", "components.Settings.SettingsAbout.githubdiscussions": "Dyskusje na GitHubie", "components.Settings.SettingsAbout.helppaycoffee": "Po<PERSON><PERSON><PERSON> z<PERSON> za kawę", "components.Settings.SettingsAbout.outofdate": "Nieaktualne", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON> <PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Preferowane", "components.Settings.SettingsAbout.runningDevelop": "Korzystasz z gałęzi <code>deweloperskiej</code> <PERSON><PERSON><PERSON><PERSON>, kt<PERSON>ra jest zalecana tylko dla osób przyczyniających się do rozwoju lub pomagających w testach.", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.totalmedia": "Multimedia ogółem", "components.Settings.SettingsAbout.uptodate": "Aktualne", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr buforuje żądania do zewnętrznych punktów końcowych API, aby zoptymalizowa<PERSON> wydajnoś<PERSON> i uniknąć wykonywania niepotrzebnych wywołań API.", "components.Settings.SettingsJobsCache.cacheflushed": "<PERSON><PERSON><PERSON><PERSON> podręczna {cachename} została opróżniona.", "components.Settings.SettingsJobsCache.cachehits": "Trafienia", "components.Settings.SettingsJobsCache.cachekeys": "Klucze ogółem", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.command": "Polecenie", "components.Settings.SettingsJobsCache.download-sync": "Synchronizuj pobierania", "components.Settings.SettingsJobsCache.download-sync-reset": "Zresetuj synchronizację pobierania", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Coś poszło nie tak podczas zapisywania zadania.", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr wykonuje pewne zadania konserwacyjne jako cyklicznie zaplanowane zadania, ale mogą być one również ręcznie uruchamiane poniżej. Ręczne uruchomienie zadania nie spowoduje zmiany jego harmonogramu.", "components.Settings.SettingsJobsCache.nextexecution": "Następne wykonanie", "components.Settings.SettingsLogs.logsDescription": "Możesz również zobaczyć te logi bezpośrednio przez <code>stdout</code>, lub w <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsJobsCache.jobname": "Nazwa zadania", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Zadanie edytowane pomyślnie!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} an<PERSON>wan<PERSON>.", "components.Settings.SettingsJobsCache.jobsandcache": "Zadania i pamięć podręczna", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON>", "components.Settings.SettingsLogs.copyToClipboard": "Skopiuj do schowka", "components.Settings.SettingsUsers.newPlexLogin": "Wł<PERSON>cz funkcję New Plex Sign-In", "components.Settings.SettingsUsers.toastSettingsSuccess": "Ustawienia użytkownika zostały zapisane pomyślnie!", "components.Settings.SettingsJobsCache.cacheksize": "Rozmiar klucza", "components.Settings.SettingsJobsCache.cachemisses": "Chybienia", "components.Settings.SettingsJobsCache.cachename": "Nazwa pamięci podręcznej", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-full-scan": "Pełne skanowanie biblioteki Plex", "components.Settings.SettingsLogs.filterError": "Błąd", "components.Settings.SettingsLogs.message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.defaultPermissionsTip": "Początkowe uprawnienia nadawane nowym użytkownikom", "components.Settings.SettingsUsers.newPlexLoginTip": "Zezwalaj użytkownikom Plex na logowanie się bez wcześniejszego importowania", "components.Settings.SettingsUsers.toastSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień.", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globalny limit żądań serii", "components.Settings.SettingsUsers.userSettings": "Ustawienia użytkownika", "components.Settings.SettingsUsers.users": "Użytkownicy", "components.Settings.SonarrModal.animeTags": "Tagi anime", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON> anime", "components.Settings.SonarrModal.animequalityprofile": "<PERSON>il j<PERSON> anime", "components.Settings.SonarrModal.baseUrl": "Baza URL", "components.Settings.SonarrModal.create4ksonarr": "Dodaj nowy serwer 4K Sonarr", "components.Settings.SonarrModal.createsonarr": "<PERSON><PERSON><PERSON> nowy serwer <PERSON>", "components.Settings.SonarrModal.edit4ksonarr": "Edyt<PERSON>j serwer 4K Sonarr", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON><PERSON><PERSON> ser<PERSON>", "components.Settings.SonarrModal.loadingprofiles": "Ładowanie profili j<PERSON>…", "components.Settings.SonarrModal.notagoptions": "Brak tagów.", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.seasonfolders": "Foldery sezonów", "components.Settings.SonarrModal.testFirstQualityProfiles": "Przetest<PERSON>j <PERSON>, aby załado<PERSON> jako<PERSON>", "components.Settings.SonarrModal.testFirstRootFolders": "Połączenie testowe w celu załadowania folderów głównych", "components.Settings.SonarrModal.tags": "Tagi", "components.Settings.SonarrModal.testFirstTags": "Przetestuj <PERSON>ł<PERSON>, aby załadowa<PERSON> tagi", "components.Settings.SonarrModal.toastSonarrTestFailure": "Nie udało się połączyć z Sonarrem.", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Przetest<PERSON>j <PERSON>, aby załadować profile językowe", "components.Settings.SonarrModal.validationApplicationUrl": "Musisz podać poprawny adres URL", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Podstawowy adres URL musi mieć wiodący ukośnik", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON><PERSON><PERSON> wybrać profil j<PERSON>wy", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Bazowy adres URL nie może być zakończony ukośnikiem", "components.Settings.SonarrModal.validationHostnameRequired": "<PERSON><PERSON>z podać prawidłową nazwę hosta lub adres IP", "components.Settings.SonarrModal.validationPortRequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy numer portu", "components.Settings.SonarrModal.validationProfileRequired": "<PERSON><PERSON><PERSON> w<PERSON> profil j<PERSON>", "components.Settings.SonarrModal.validationNameRequired": "Musisz podać nazwę serwera", "components.Settings.SonarrModal.validationRootFolderRequired": "Musisz wybrać folder główny", "components.Settings.activeProfile": "Aktywny profil", "components.Settings.copied": "Skopiowano klucz API do schowka.", "components.Settings.default": "Domyślny", "components.Settings.default4k": "Domyślne 4K", "components.Settings.deleteserverconfirm": "<PERSON>zy na pewno chcesz usunąć ten serwer?", "components.Settings.email": "E-mail", "components.Settings.enablessl": "Użyj SSL", "components.Settings.librariesRemaining": "Pozostałe biblioteki: {count}", "components.Settings.hostname": "Nazwa hosta lub adres IP", "components.Settings.is4k": "4K", "components.Settings.manualscan": "Ręczne skanowanie biblioteki", "components.Settings.manualscanDescription": "<PERSON><PERSON><PERSON> będzie to uruchamiane tylko raz na 24 godziny. <PERSON><PERSON><PERSON><PERSON> sprawdzi ostatnio dodane serwery Plex bardziej agresywnie. <PERSON><PERSON><PERSON> konfigurujesz Plex po raz pier<PERSON>, zalecane jest jednorazowe pełne ręczne skanowanie biblioteki!", "components.Settings.mediaTypeMovie": "film", "components.Settings.mediaTypeSeries": "serial", "components.Settings.menuAbout": "O", "components.Settings.menuGeneralSettings": "Ogólne", "components.Settings.menuJobs": "Zadania i pamięć podręczna", "components.Settings.noDefault4kServer": "Serwer 4K {serverType} musi być oznaczony jako <PERSON>, aby um<PERSON><PERSON><PERSON><PERSON>ć użytkownikom składanie żądań 4K {mediaType}.", "components.Settings.noDefaultNon4kServer": "<PERSON><PERSON><PERSON> masz tylko jeden serwer {serverType} dla zawartości nie 4K i 4K (lub jeśli tylko pobierasz zawartość 4K), <PERSON><PERSON><PERSON><PERSON> serwer {serverType} pow<PERSON><PERSON> być <strong>NIE</strong> oznaczony jako serwer 4K.", "components.Settings.noDefaultServer": "Co najmniej jeden serwer {serverType} musi być oz<PERSON>y jako <PERSON>, aby żądania {mediaType} były przetwarzane.", "components.Settings.notificationAgentSettingsDescription": "Skonfiguruj i włącz agentów powiadomień.", "components.Settings.notifications": "Powiadomienia", "components.Settings.notificationsettings": "Ustawienia powiadomień", "components.Settings.notrunning": "<PERSON><PERSON>", "components.Settings.plexlibraries": "Biblioteki Plex", "components.Settings.plexlibrariesDescription": "Biblioteki, kt<PERSON><PERSON> Jellyseerr skanuje w poszukiwaniu tytułów. Skonfiguruj i zapisz ustawienia połączenia Plex, a następnie kliknij przycisk poniżej, jeśli na liście nie ma żadnych bibliotek.", "components.Settings.plexsettings": "Ustawienia Plex", "components.Settings.radarrsettings": "Ustawienia Radarr", "components.Settings.scanning": "Synchronizacja…", "components.Settings.serverLocal": "lokalny", "components.Settings.serverRemote": "<PERSON><PERSON><PERSON>", "components.Settings.serverSecure": "bezpieczne", "components.Settings.serverpreset": "<PERSON><PERSON>", "components.Settings.serverpresetLoad": "Naciś<PERSON><PERSON>, aby za<PERSON><PERSON><PERSON><PERSON> dostępne serwery", "components.Settings.serverpresetManualMessage": "Ręczna konfiguracja", "components.Settings.serverpresetRefreshing": "Pobieranie serwerów…", "components.Settings.services": "Usługi", "components.Settings.settingUpPlexDescription": "<PERSON>by skon<PERSON><PERSON><PERSON><PERSON><PERSON> aplik<PERSON><PERSON><PERSON>, m<PERSON><PERSON><PERSON><PERSON> wprow<PERSON><PERSON>ć szczegóły ręcznie lub wybrać serwer pobrany z witryny <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Naciśnij przycisk znajdujący się po prawej stronie listy rozwijanej, aby wyświetlić listę dostępnych serwerów.", "components.Settings.toastPlexRefresh": "Pobieranie listy serwerów z Plex…", "components.Settings.validationHostnameRequired": "<PERSON><PERSON>z podać prawidłową nazwę hosta lub adres IP", "components.Settings.webAppUrl": "<WebAppLink> Adres URL aplikacji internetowej</WebAppLink>", "components.Setup.finishing": "Kończenie…", "components.Setup.signinMessage": "Zacznij logując się na swoje konto Plex", "components.Setup.welcome": "<PERSON><PERSON><PERSON> w <PERSON><PERSON><PERSON><PERSON>", "components.StatusBadge.status4k": "4K {status}", "components.TvDetails.anime": "Anime", "components.TvDetails.nextAirDate": "Następna data emisji", "components.UserList.bulkedit": "<PERSON><PERSON><PERSON>", "components.UserList.create": "Utwórz", "components.UserList.created": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.edituser": "Edytuj uprawnienia użytkownika", "components.UserList.email": "Adres e-mail", "components.UserList.usercreatedsuccess": "Pomyślnie utworzono użytkownika!", "components.UserList.userdeleted": "Użytkownik został pomyślnie usunięty!", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "<PERSON><PERSON>ś<PERSON><PERSON><PERSON> na<PERSON>wa", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Zastąp globalny limit", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Użytkownik lokalny", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Domyślny ({language})", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Ustawienia powiadomień Discord zostały pomyślnie zapisane!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Wysyłaj powiadomienia bez dźwięku", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Potwierd<PERSON> hasło", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Ustawienia powiadomień web push zostały pomyślnie zapisane!", "i18n.noresults": "Brak wyników.", "i18n.notrequested": "Brak próśb", "i18n.partiallyavailable": "Częściowo dostępne", "i18n.retry": "Ponów próbę", "i18n.view": "<PERSON><PERSON><PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "Wewnętrzny błąd serwera", "pages.oops": "Ups", "pages.pagenotfound": "Nie znaleziono strony", "components.CollectionDetails.requestcollection": "Poproś o kolekcję", "components.Setup.setup": "Konfiguracja", "i18n.close": "Zamknij", "components.Settings.webpush": "Web Push", "components.UserList.accounttype": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtruj zawartość według języka oryginału", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Rozpocznij czat</TelegramBotLink> ze swoim botem, dodaj <GetIdBotLink>@get_id_bot</GetIdBotLink> i wydaj polecenie <code>/my_id</code>", "i18n.usersettings": "Ustawienia użytkownika", "pages.returnHome": "Powrót do domu", "pages.serviceunavailable": "Usługa niedostępna", "components.TvDetails.overview": "Podsumowanie", "components.UserList.admin": "Administrator", "components.UserList.userdeleteerror": "Coś poszło nie tak podczas usuwania użytkownika.", "components.UserList.userfail": "Coś poszło nie tak podczas zapisywania uprawnień użytkownika.", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON><PERSON><PERSON> profil", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limit próśb o serial", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Ustawienia zostały zapisane pomyślnie!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Powiadomienia", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Ustawienia powiadomień Telegram zostały zapisane pomyślnie!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Mu<PERSON>z podać prawidłowy identyfikator użytkownika", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Mu<PERSON>z podać prawidłowy klucz publiczny PGP", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Uprawnienia zostały pomyślnie zapisane!", "components.UserProfile.UserSettings.menuChangePass": "<PERSON><PERSON><PERSON>", "components.UserProfile.movierequests": "Prośby o filmy", "i18n.canceling": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.resolved": "Rozwiązane", "i18n.resultsperpage": "Wyświetlaj {pageSize} wyników na stronę", "i18n.retrying": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.status": "<PERSON>", "components.Settings.SonarrModal.hostname": "Nazwa hosta lub adres IP", "components.Settings.SonarrModal.languageprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.loadingTags": "Ładowanie tagów…", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "components.Settings.plexsettingsDescription": "Skonfiguruj ustawienia serwera Plex. Je<PERSON>seerr skanuje biblioteki Plex, a<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> tre<PERSON>.", "components.Settings.port": "Port", "components.Settings.scan": "Synchronizuj biblioteki", "components.Settings.toastPlexConnecting": "Próba połączenia z Plex…", "components.Settings.toastPlexConnectingFailure": "Nie udało się połączyć z Plex.", "components.Settings.validationPortRequired": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy numer portu", "components.Settings.webhook": "Webhooki", "components.Setup.configureservices": "Skonfiguruj <PERSON>ługi", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.TvCast.fullseriescast": "Pełna obsada serialu", "components.TvDetails.TvCrew.fullseriescrew": "Pełna ekipa serialu", "components.TvDetails.episodeRuntime": "Czas trwania odcinka", "components.TvDetails.originallanguage": "Język oryginalny", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "Podsumowanie niedostępne.", "components.TvDetails.recommendations": "Rekomendacje", "components.TvDetails.seasons": "{seasonCount, plural, one {# Sezon} other {# Sezony}}", "components.TvDetails.showtype": "Typ serialu", "components.TvDetails.similar": "Podobne seriale", "components.TvDetails.streamingproviders": "Obecnie dostępne na", "components.UserList.autogeneratepassword": "Automatycznie generuj hasło", "components.UserList.autogeneratepasswordTip": "Wyślij do użytkownika wiadomość e-mail z hasłem wygenerowanym przez serwer", "components.UserList.createlocaluser": "Utwórz użytkownika lokalnego", "components.UserList.creating": "<PERSON>rz<PERSON>e…", "components.UserList.deleteuser": "Usuń użytkownika", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {użytkownik Plex został zaimportowany} other {użytkowników Plex zostało zaimportowanych}} pomyślnie!", "components.UserList.localLoginDisabled": "Ustawienie <strong>Włącz Lokalne Logowanie</strong> jest obecnie wyłączone.", "components.UserList.localuser": "Użytkownik lokalny", "components.UserList.nouserstoimport": "Brak nowych użytkowników Plex do zaimportowania.", "components.UserList.owner": "Właściciel", "components.UserList.password": "<PERSON><PERSON><PERSON>", "components.UserList.passwordinfodescription": "Skonfiguruj adres URL aplikacji i włącz powiadomienia e-mail, aby umożliwić automatyczne generowanie hasła.", "components.UserList.plexuser": "Użytkownik Plex", "components.UserList.role": "Rola", "components.UserList.sortRequests": "Liczba próśb", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "<PERSON><PERSON><PERSON> dostę<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "<PERSON><PERSON><PERSON> pod<PERSON> prawidłowy token aplikacji", "components.UserProfile.recentrequests": "Ostatnie <PERSON>", "components.Settings.SonarrModal.enableSearch": "Włącz automatyczne wyszukiwanie", "components.Settings.menuLogs": "<PERSON><PERSON>", "components.Settings.SonarrModal.externalUrl": "Zewnętrzny adres URL", "components.Settings.menuNotifications": "Powiadomienia", "components.Settings.plex": "Plex", "components.UserList.users": "Użytkownicy", "components.UserList.user": "Użytkownik", "components.UserList.usercreatedfailed": "Coś poszło nie tak podczas tworzenia użytkownika.", "components.UserList.usercreatedfailedexisting": "Podany adres e-mail jest już używany przez innego użytkownika.", "components.UserList.userlist": "Lista użytkowników", "components.UserList.userssaved": "Uprawnienia użytkownika zostały pomyślnie zapisane!", "components.UserList.validationEmail": "<PERSON>res e-mail jest wymagany", "components.UserList.validationpasswordminchars": "Hasło jest zbyt krótkie; powinno mieć co najmniej 8 znaków", "components.UserProfile.ProfileHeader.joindate": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł {joindate}", "components.UserProfile.ProfileHeader.userid": "ID użytkownika: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limit próśb o filmy", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Właściciel", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Użytkownik Plex", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Odkryj region", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "<PERSON>dk<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtruj zawartość według dostępności regionalnej", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rola", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Discord.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień e-mail.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Ustawienia powiadomień e-mail zostały zapisane pomyślnie!", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Ustawienia powiadomień", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Klucz publiczny PGP", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Szyfruj wiadomości e-mail za pomocą <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Utwórz token z poziomu <PushbulletSettingsLink>ustawień konta</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Ustawienia powiadomień pushbullet zostały pomyślnie zapisane!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Token interfejsu API aplikacji", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Zarejestruj aplikację</ApplicationRegistrationLink> do użycia z {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Klucz użytkownika lub grupy", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Twój 30-znakowy <UsersGroupsLink>identyfikator użytkownika lub grupy</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Ustawienia powiadomień pushover zostały pomyślnie zapisane!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Mu<PERSON>z podać prawidłowy klucz użytkownika lub grupy", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "<PERSON><PERSON><PERSON> pod<PERSON>ć prawidłowy identyfikator c<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Web Push.", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nowe hasło", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Hasło zostało zapisane pomyślnie!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON><PERSON><PERSON> nowe hasło", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Hasła muszą być zgodne", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "<PERSON><PERSON>z podać swoje aktualne hasło", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "<PERSON><PERSON><PERSON> nowe hasło", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Hasło jest zbyt krótkie; powinno mieć co najmniej 8 znaków", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "<PERSON>e mo<PERSON><PERSON><PERSON> modyfikować własnych uprawnień.", "components.UserProfile.UserSettings.menuGeneralSettings": "Ogólne", "components.UserProfile.UserSettings.menuNotifications": "Powiadomienia", "components.UserProfile.UserSettings.menuPermissions": "Uprawnienia", "components.UserProfile.UserSettings.unauthorizedDescription": "Nie masz uprawnień do modyfikowania ustawień tego użytkownika.", "components.UserProfile.limit": "{remaining} z {limit}", "components.UserProfile.pastdays": "{type} (ostatnie {days} dni)", "components.UserProfile.requestsperdays": "Pozostało {limit}", "components.UserProfile.seriesrequest": "Prośby o seriale", "components.UserProfile.unlimited": "Bez ograniczeń", "i18n.advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.all": "Wszystkie", "i18n.approve": "Zatwierdź", "i18n.areyousure": "<PERSON>zy na pewno?", "i18n.back": "Powró<PERSON>", "i18n.decline": "<PERSON><PERSON><PERSON><PERSON>", "i18n.declined": "Odrzucony", "i18n.delete": "Usuń", "i18n.deleting": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "<PERSON><PERSON><PERSON><PERSON>", "i18n.experimental": "Eksperymentalne", "i18n.failed": "<PERSON><PERSON><PERSON><PERSON>", "i18n.loading": "Ładowanie…", "i18n.movie": "Film", "i18n.movies": "Filmy", "i18n.next": "Następny", "i18n.open": "<PERSON><PERSON><PERSON><PERSON>", "i18n.pending": "Ocz<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.previous": "Poprzedni", "i18n.processing": "Przetwarzanie", "i18n.request": "<PERSON><PERSON><PERSON>", "i18n.request4k": "Poproś o 4K", "i18n.requested": "Prośba zgłoszona", "i18n.requesting": "Prośba…", "i18n.save": "Zapisz zmiany", "i18n.saving": "Zapisywan<PERSON>…", "i18n.settings": "Ustawienia", "i18n.showingresults": "Wyświetlanie wyników od<strong>{from}</strong> do <strong>{to}</strong> z <strong>{total}</strong>", "i18n.test": "Test", "i18n.testing": "<PERSON><PERSON><PERSON>…", "i18n.tvshow": "Serial", "i18n.tvshows": "Seriale", "i18n.unavailable": "Niedostępny", "pages.somethingwentwrong": "Coś poszło nie tak", "components.Settings.SonarrModal.defaultserver": "Domyślny serwer", "components.Settings.SonarrModal.selectRootFolder": "Wybierz folder główny", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON> serwer", "components.Settings.SonarrModal.loadingrootfolders": "Ładowanie folderów głównych…", "components.Settings.SonarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.rootfolder": "Folder główny", "components.Settings.address": "<PERSON><PERSON>", "components.Settings.SonarrModal.ssl": "Użyj SSL", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Adres URL nie może kończyć się ukośnikiem", "components.Settings.SettingsUsers.userSettingsDescription": "Konfiguruje globalne i domyślne ustawienia użytkowników.", "components.Settings.SonarrModal.animerootfolder": "Folder główny anime", "components.Settings.SonarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON><PERSON> profil j<PERSON>", "components.Settings.SonarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON> tagi", "components.Settings.toastPlexRefreshFailure": "Nie udało się pobrać listy serwerów Plex.", "components.Settings.SonarrModal.apiKey": "Klucz API", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.default4kserver": "Domyślny serwer 4K", "components.Settings.SonarrModal.loadinglanguageprofiles": "Ładowanie profili <PERSON>…", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON><PERSON><PERSON> profil <PERSON>wy", "components.Settings.SonarrModal.server4k": "Serwer 4K", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Połączenie z Sonarr nawiązane pomyślnie!", "components.Settings.SonarrModal.validationApiKeyRequired": "<PERSON><PERSON>z pod<PERSON>ć klucz <PERSON>", "components.Settings.addradarr": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.servername": "<PERSON><PERSON><PERSON> ser<PERSON>a", "components.Settings.SonarrModal.syncEnabled": "Włącz skanowanie", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuUsers": "Użytkownicy", "components.Settings.currentlibrary": "Bieżąca biblioteka: {name}", "components.Settings.menuServices": "Usługi", "components.Settings.sonarrsettings": "Ustawienia Sonarr", "components.Settings.ssl": "Protokół SSL", "components.Settings.toastPlexConnectingSuccess": "Połączenie Plex nawiązane pomyślnie!", "components.Settings.serviceSettingsDescription": "Skonfiguruj poniżej swój serwer(y) {serverType}. <PERSON><PERSON><PERSON><PERSON>ł<PERSON>czyć wiele serwerów {serverType}, ale tylko dwa z nich mogą być oznaczone jako do<PERSON>e (jeden nie-4K i jeden 4K). Administratorzy mogą zmienić serwer używany do przetwarzania nowych żądań przed zatwierdzeniem.", "components.Settings.toastPlexRefreshSuccess": "Lista serwerów Plex została pobrana pomyślnie!", "components.Settings.startscan": "Rozpocznij skanowanie", "components.Setup.finish": "Zakończ konfigurację", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Użytkownik", "components.Settings.webAppUrlTip": "Opcjonalnie kieruj użytkowników do aplikacji internetowej na Twoim serwerze zamiast do \"hostowanej\" aplikacji internetowej", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minut", "components.TvDetails.viewfullcrew": "Zobacz pełną ekipę", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Ogólne", "components.TvDetails.firstAirDate": "Pierwsza data emisji", "components.UserList.deleteconfirm": "Czy na pewno chcesz usunąć tego użytkownika? Wszystkie jego dane zostaną trwale usunięte.", "components.TvDetails.cast": "Obsada", "components.TvDetails.watchtrailer": "Obejrzyj zwiastun", "components.UserList.sortCreated": "Data dołączenia", "components.UserList.importfromplexerror": "Coś poszło nie tak podczas importowania użytkowników Plex.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień.", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Identyfikator użytkownika", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON><PERSON><PERSON> po cichu", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>Wielocyfrowy numer ID</FindDiscordIdLink> powiązany z Twoim kontem użytkownika", "components.UserList.importfromplex": "Importuj użytkowników Plex", "i18n.available": "Dostępny", "components.UserList.sortDisplayName": "<PERSON><PERSON>ś<PERSON><PERSON><PERSON> na<PERSON>wa", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Język wyświetlania", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Ustawienia ogólne", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "<PERSON><PERSON> konta", "components.UserProfile.UserSettings.UserPermissions.permissions": "Uprawnienia", "components.UserProfile.totalrequests": "Łączna liczba próśb", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.approved": "Zatwierdzone", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "To konto użytkownika nie ma obecnie ustawionego hasła. Skonfiguruj hasło poni<PERSON>, aby umoż<PERSON>wić temu kontu logowanie się jako \"użytkownik lokalny\"", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Twoje konto nie ma obecnie ustawionego hasła. Skonfiguruj hasło p<PERSON>, aby umo<PERSON><PERSON><PERSON>ć zalogowanie się jako \"użytkownik lokalny\" przy użyciu Twojego adresu e-mail.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Nie masz uprawnień do zmiany hasła tego użytkownika.", "components.UserProfile.UserSettings.UserPasswordChange.password": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Coś poszło nie tak podczas zapisywania hasła.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Coś poszło nie tak podczas zapisywania hasła. <PERSON><PERSON> aktualne hasło zostało wpisane poprawnie?", "components.NotificationTypeSelector.adminissueresolvedDescription": "Otrzy<PERSON><PERSON>dom<PERSON>, gdy problemy zostaną rozwiązane przez innych użytkowników.", "components.NotificationTypeSelector.issuereopened": "Problem ponownie otwarty", "components.NotificationTypeSelector.userissuereopenedDescription": "Otrzymuj powiadomienia o ponownym otwarciu zgłoszonych problemów.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Otr<PERSON><PERSON><PERSON>, gdy problemy zostaną ponownie otwarte przez innych użytkowników.", "components.NotificationTypeSelector.issuereopenedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, gdy problemy zostaną ponownie otwarte.", "components.RequestModal.requestmovies4k": "<PERSON><PERSON><PERSON> o {count} {count, plural, one {film} other {filmów}} w 4k", "components.RequestModal.selectmovies": "Wybierz film(y)", "components.RequestModal.requestmovies": "<PERSON><PERSON><PERSON> o {count} {count, plural, one {film} other {filmów}}", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON><PERSON>…", "components.MovieDetails.productioncountries": "Produk<PERSON>ja {countryCount, plural, one {kraj} other {kraje}}", "components.RequestModal.requestseasons4k": "Prośba o {seasonCount} {seasonCount, plural, one {sezon} other {sezony}} w 4K", "components.TvDetails.productioncountries": "Produk<PERSON>ja {countryCount, plural, one {kraj} other {kraje}}", "components.RequestModal.approve": "Zatwierdź prośbę", "components.RequestModal.requestApproved": "<PERSON>ś<PERSON> o <strong>{title}</strong> zatwierdzona!", "components.Settings.RadarrModal.announced": "Zapowiedziany", "components.Settings.RadarrModal.inCinemas": "<PERSON> kinach", "components.Settings.RadarrModal.released": "Wyd<PERSON>", "components.Settings.Notifications.enableMentions": "Włącz wzmianki", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "<PERSON>e udało się zapisać ustawień powiadomień Gotify.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Ustawienia powiadomień Gotify zostały pomyślnie zapisane!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Wysyłanie powiadomienia o testowego Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Wysłano powiadomienie testowe Gotify!", "components.Settings.Notifications.NotificationsGotify.url": "Adres URL serwera", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Musisz wybrać co najmniej jeden typ powiadomienia", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Mu<PERSON>z podać prawidłowy adres URL", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "Adres URL nie może kończyć się ukośnikiem", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Włącz agenta", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "<PERSON>e udało się wysłać powiadomienia testowego Gotify.", "components.Settings.Notifications.NotificationsGotify.token": "<PERSON>ken a<PERSON>", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "<PERSON><PERSON><PERSON> aplikacji", "components.Settings.toastTautulliSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień Tautulli.", "components.ManageSlideOver.pastdays": "Ostatnie {days, number} dni", "components.ManageSlideOver.markallseasons4kavailable": "Oznacz wszystkie sezony jako Dostępne w 4K", "components.Settings.validationUrlTrailingSlash": "Adres URL nie może kończyć się ukośnikiem", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {odtworzenie} other {odtworzenia}}", "components.UserList.newplexsigninenabled": "Ustawienie <strong><PERSON><PERSON><PERSON><PERSON> logowanie Plex</strong> jest obecnie włączone. Użytkownicy Plex, którzy mają dostęp do biblioteki nie muszą być importowani by <PERSON><PERSON>.", "components.ManageSlideOver.alltime": "Cały czas", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia": "Multimedia", "components.ManageSlideOver.manageModalMedia4k": "Multimedia w 4K", "components.ManageSlideOver.markallseasonsavailable": "Oznacz wszystkie sezony jako Do<PERSON>ę<PERSON>ne", "components.ManageSlideOver.opentautulli": "Otwórz w Tautulli", "components.ManageSlideOver.playedby": "Odgry<PERSON><PERSON> p<PERSON>ez", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Tag kanału", "components.Settings.externalUrl": "Zewnętrzny adres URL", "components.Settings.tautulliApiKey": "Klucz API", "components.Settings.tautulliSettings": "Ustawi<PERSON>", "components.Settings.tautulliSettingsDescription": "Opcjonalnie skonfiguruj ustawienia serwera <PERSON>. Je<PERSON>seerr pobiera dane historii oglądania dla multimediów Plex z Tautulli.", "components.Settings.toastTautulliSettingsSuccess": "Ustawienia Tautulli zostały pomyślnie zapisane!", "components.Settings.urlBase": "Baza URL", "components.Settings.validationApiKey": "<PERSON><PERSON>z pod<PERSON>ć klucz <PERSON>", "components.Settings.validationUrl": "Mu<PERSON>z podać prawidłowy adres URL", "components.Settings.validationUrlBaseLeadingSlash": "Baza adresu URL musi mieć ukośnik", "components.Settings.validationUrlBaseTrailingSlash": "Baza adresu URL nie może kończyć się ukośnikiem", "components.UserProfile.recentlywatched": "Ostatnio <PERSON>", "i18n.import": "Import<PERSON>j", "i18n.importing": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID użytkownika Discorda", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>Wielocyfrowy numer ID</FindDiscordIdLink> powiązany z Twoim kontem użytkownika Discord", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Musisz podać poprawne ID użytkownika Discord", "components.Settings.SettingsAbout.appDataPath": "Katalog danych", "components.StatusChecker.restartRequiredDescription": "Uruchom ponownie serwer, aby z<PERSON><PERSON><PERSON> zaktualizowane ustawienia.", "components.StatusChecker.appUpdated": "Zaktualizowano {applicationTitle}", "components.Settings.deleteServer": "<PERSON><PERSON><PERSON> serwer {serverType}", "components.StatusChecker.appUpdatedDescription": "Kliknij przycisk poniżej, aby ponownie załadować aplikację.", "components.StatusChecker.reloadApp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {applicationTitle}", "i18n.restartRequired": "<PERSON><PERSON><PERSON><PERSON> jest ponowne urucho<PERSON>nie", "components.RequestBlock.languageprofile": "<PERSON><PERSON>", "components.StatusChecker.restartRequired": "<PERSON><PERSON><PERSON>e ponowne uruchomienie serwera", "components.MovieDetails.digitalrelease": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>e", "components.MovieDetails.physicalrelease": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.theatricalrelease": "<PERSON><PERSON><PERSON><PERSON> kinowe", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Twoja lista obserwowanych", "components.Discover.plexwatchlist": "Twoja lista obserwowanych", "components.PermissionEdit.autorequest": "Automatyczna prośba", "components.PermissionEdit.autorequestDescription": "Udziel zgody na automatyczne przesyłanie próśb dotyczących multimediów innych niż 4K za pośrednictwem listy obserwowanych Plex.", "components.NotificationTypeSelector.mediaautorequested": "Prośba przesłana automatycznie", "components.NotificationTypeSelector.mediaautorequestedDescription": "Otrzymuj powiadomienia o automatycznym składaniu nowych próśb o multimedia dla pozycji znajdujących się na liście obserwowanych.", "components.PermissionEdit.autorequestMovies": "Filmy z próśb automatycznych", "components.Discover.DiscoverWatchlist.watchlist": "Lista obserwowanych", "components.MovieDetails.managemovie": "Zarządzaj filmem", "components.MovieDetails.reportissue": "<PERSON><PERSON>ł<PERSON>ś problem", "components.PermissionEdit.autorequestMoviesDescription": "Udziel zgody na automatyczne przesyłanie próśb dotyczących multimediów innych niż 4K za pośrednictwem listy obserwowanych Plex.", "components.PermissionEdit.autorequestSeries": "Automatyczna prośba o serial", "components.AirDateBadge.airedrelative": "Wyemitowany {relativeTime}", "components.AirDateBadge.airsrelative": "Data emisji {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Prośby o filmy", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Prośby o seriale", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON>", "components.MovieDetails.rtaudiencescore": "Ocena publiczności Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatometer Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Ocena użytkowników TMDB", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "Utwórz niestandardowy suwak", "components.Discover.CreateSlider.addfail": "Nie udało się utworzyć nowego suwaka.", "components.Discover.CreateSlider.addsuccess": "Stworzony nowy suwak i zapisano dostosowywania odkrywania.", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.editfail": "<PERSON><PERSON> udało się edytować suwaka.", "components.Discover.CreateSlider.needresults": "Musisz mieć przynajmniej 1 wynik.", "components.Discover.CreateSlider.nooptions": "Brak wyników.", "components.Discover.CreateSlider.providetmdbgenreid": "Podaj ID gatunku TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Podaj ID stacji TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Podaj zapytanie wyszukiwania", "components.Discover.CreateSlider.providetmdbstudio": "Podaj ID studia TMDB", "components.Discover.CreateSlider.searchGenres": "Szukaj gatunków…", "components.Discover.CreateSlider.searchStudios": "Wyszukaj studia…", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.starttyping": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> aby w<PERSON>.", "components.Discover.CreateSlider.validationDatarequired": "<PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON> danych.", "components.Discover.CreateSlider.validationTitlerequired": "Na<PERSON>ży podać tytuł.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Filmy: {keywordTitle}", "components.Discover.DiscoverMovies.discovermovies": "Filmy", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularność rosnąco", "components.Discover.DiscoverMovies.sortPopularityDesc": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data wydania malejąco", "components.Discover.DiscoverMovies.sortTitleAsc": "Tytuł (A-Z) rosnąco", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) malejąco", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Ocena TMDB rosnąco", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Ocena TMDB malejąco", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON>e udało się usunąć suwaka.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Pomyślnie usunięto suwak.", "components.Discover.DiscoverSliderEdit.enable": "Przełącz widoczność", "components.Discover.DiscoverSliderEdit.remove": "Usuń", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Data premiery rosnąco", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Data premiery malejąco", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularność rosnąco", "components.Discover.DiscoverTv.sortPopularityDesc": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverTv.sortTitleAsc": "Tytuł (A-Z) rosnąco", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) malejąco", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Ocena TMDB malejąco", "components.Discover.FilterSlideover.clearfilters": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć aktywne filtry", "components.Discover.FilterSlideover.filters": "Filtry", "components.Discover.FilterSlideover.firstAirDate": "Data pierwszego wyemitowania", "components.Discover.FilterSlideover.from": "Od", "components.Discover.FilterSlideover.genres": "Gatunki", "components.Discover.FilterSlideover.keywords": "Słowa kluczowe", "components.Discover.FilterSlideover.ratingText": "<PERSON><PERSON><PERSON> {minValue} a {maxValue}", "components.Discover.CreateSlider.providetmdbkeywordid": "Podaj ID słowa kluczowego TMDB", "components.Discover.CreateSlider.searchKeywords": "Wyszukaj słowa kluczowe…", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data wydania rosnąco", "components.Discover.DiscoverTv.discovertv": "Seriale", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Ocena TMDB rosnąco", "components.Discover.DiscoverTvKeyword.keywordSeries": "Seriale: {keywordTitle}", "components.Discover.FilterSlideover.originalLanguage": "Oryginalny język", "components.Discover.FilterSlideover.releaseDate": "Data wydania", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minut trwania", "components.Discover.FilterSlideover.streamingservices": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuservotecount": "Liczba głosów użytkowników TMDB", "components.Discover.FilterSlideover.to": "Do", "components.Discover.FilterSlideover.voteCount": "Liczba głosów między {minValue} a {maxValue}", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Twoja lista obserwowanych", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Niedawno dodane", "components.Discover.createnewslider": "<PERSON><PERSON><PERSON> suwak", "components.Discover.customizediscover": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Media dodane do twojej <PlexWatchlistSupportLink>listy obserwowanych Plex</PlexWatchlistSupportLink> zostaną wyświetlone tutaj.", "components.Discover.emptywatchlist": "Media dodane do twojej <PlexWatchlistSupportLink>listy obserwowanych Plex</PlexWatchlistSupportLink> zostaną wyświetlone tutaj.", "components.Discover.tmdbstudio": "Studio TMDB", "components.Discover.tmdbtvkeyword": "Słowo kluczowe serialu TMDB", "components.Discover.tmdbtvgenre": "Gatunek serialu TMDB", "components.Discover.tmdbsearch": "Wyszukiwanie TMDB", "components.Discover.FilterSlideover.tmdbuserscore": "Ocena użytkowników TMDB", "components.Discover.moviegenres": "Gatunki filmu", "components.Discover.networks": "Kanały", "components.Discover.CreateSlider.editsuccess": "Edytowano suwak i zapisano ustawienia personalizacji.", "components.Discover.stopediting": "Zakończ edytowanie", "components.Discover.studios": "St<PERSON><PERSON>", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON> {seasonNumber} Odcinek {episodeNumber}", "components.Discover.tvgenres": "Gatunki seriali", "components.Discover.updatefailed": "Coś poszło nie tak podczas aktualizacji ustawień personalizacji wykrywania.", "components.Discover.updatesuccess": "Zaktualizowano ustawienia personalizacji wykrywania.", "components.Discover.resettodefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.resetfailed": "Coś poszło nie tak podczas resetowania ustawień personalizacji wykrywania.", "components.Discover.resetsuccess": "Pomyślnie zresetowano ustawienia wykrywania.", "components.Discover.tmdbmoviegenre": "Gatunek filmu TMDB", "components.Layout.Sidebar.browsetv": "Seriale", "components.Layout.UserWarnings.emailInvalid": "Adres e-mail jest ni<PERSON><PERSON>idłowy.", "components.Layout.UserWarnings.passwordRequired": "<PERSON><PERSON><PERSON><PERSON> jest podanie hasła.", "components.Login.credentialerror": "Nazwa użytkownika lub hasło są nieprawidłowe.", "components.Login.emailtooltip": "Adres nie musi być powiązany z instancją {mediaServerName}.", "components.Login.initialsignin": "Połącz", "components.Login.initialsigningin": "Łączenie…", "components.Login.save": "<PERSON><PERSON><PERSON>", "components.Login.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.Login.signinwithjellyfin": "Użyj swojego konta {mediaServerName}", "components.Login.title": "Dodaj e-mail", "components.Login.username": "Nazwa użytkownika", "components.Login.validationEmailRequired": "<PERSON><PERSON><PERSON> pod<PERSON> adres e-mail", "components.Login.validationemailformat": "Wymagany poprawny adres e-mail", "components.Login.validationhostformat": "Wymagany poprawny adres URL", "components.Login.validationhostrequired": "{mediaServerName} URL wymagany", "components.Login.validationusernamerequired": "Wymagana nazwa użytkownika", "components.ManageSlideOver.removearr4k": "Usuń z 4k {arr}", "components.MovieDetails.downloadstatus": "Status pobierania", "components.MovieDetails.openradarr4k": "Otwórz film 4k w Radarr", "components.MovieDetails.play": "Odtwórz na {mediaServerName}", "components.MovieDetails.play4k": "Odtwórz w 4K na {mediaServerName}", "components.PermissionEdit.viewrecent": "Wyświetl ostatnio dodane", "components.PermissionEdit.viewrecentDescription": "Zezwolenie na wyświetlanie listy ostatnio dodanych multimediów.", "components.RequestBlock.approve": "Zatwierdź żądanie", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON>ć żądan<PERSON>", "components.RequestBlock.delete": "Usuń żądanie", "components.RequestBlock.lastmodifiedby": "Ostatnio modyfikowany przez", "components.RequestBlock.requestdate": "Data żądania", "components.RequestBlock.requestedby": "Żądany przez", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON><PERSON> prośbę", "components.RequestCard.editrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.unknowntitle": "<PERSON>ez<PERSON><PERSON> tytuł", "components.RequestModal.requestcollectiontitle": "Poproś o kolekcję", "components.RequestModal.requestmovie4ktitle": "Poproś o film w 4K", "components.RequestModal.requestmovietitle": "Poproś o film", "components.RequestModal.requestseries4ktitle": "Poproś o serial w 4K", "components.RequestModal.requestseriestitle": "Poproś o serial", "components.Selector.searchGenres": "Wybierz gatunki…", "components.Selector.searchKeywords": "Wyszukaj słowa kluczowe…", "components.Selector.searchStudios": "Szukaj studiów…", "components.Selector.showless": "Pokaż mniej", "components.Selector.starttyping": "Roz<PERSON>cz<PERSON>j wpisywanie, aby w<PERSON><PERSON>.", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Ostatnio dodane skanowanie Jellyfin", "components.Settings.SettingsMain.apikey": "Klucz API", "components.Settings.SettingsMain.applicationTitle": "Nazwa aplikacji", "components.Settings.SettingsMain.applicationurl": "URL aplikacji", "components.Settings.SettingsMain.general": "Ogólne", "components.Settings.SettingsMain.generalsettings": "Ustawienia ogólne", "components.Settings.SettingsMain.generalsettingsDescription": "Konfiguracja globalnych i domyślnych ustawień Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "Ukryj dostępne media", "components.Settings.SettingsMain.locale": "Wyświetlany język", "components.Settings.SettingsMain.partialRequestsEnabled": "Zezwalaj na częściowe żądania seriali", "components.Settings.SettingsMain.toastSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień.", "components.Settings.jellyfinSettings": "{mediaServerName} Ustawienia", "components.Settings.jellyfinSettingsFailure": "Coś poszło nie tak podczas zapisywania ustawień {mediaServerName}.", "components.Settings.jellyfinSettingsSuccess": "Ustawienia {mediaServerName} zostały pomyślnie zapisane!", "components.Settings.jellyfinlibrariesDescription": "Biblioteki {mediaServerName} skanują w poszukiwaniu tytułów. Kliknij poniższy przycisk, jeśli na liście nie ma żadnych bibliotek.", "components.Settings.jellyfinsettings": "{mediaServerName} ustawienia", "components.Settings.jellyfinsettingsDescription": "Konfiguracja ustawień serwera {mediaServerName}. {mediaServerName} skanuje biblioteki {mediaServerName}, aby <PERSON>, jaka <PERSON> jest dostę<PERSON>na.", "components.Settings.manualscanJellyfin": "Ręczne skanowanie biblioteki", "components.Settings.saving": "Zapisywan<PERSON>…", "components.Settings.syncing": "Synchronizowanie", "components.Setup.signinWithPlex": "Wprowadź swoje dane Plex", "components.StatusBadge.openinarr": "Otw<PERSON>rz w {arr}", "components.StatusBadge.playonplex": "Odtwórz na {mediaServerName}", "components.TitleCard.watchlistError": "Coś poszło nie tak, spróbuj ponownie.", "components.TvDetails.Season.somethingwentwrong": "Coś poszło nie tak podczas pobierania danych sezonu.", "components.TvDetails.seasonnumber": "Sezon {seasonNumber}", "components.TvDetails.seasonstitle": "Sezony", "components.TvDetails.status4k": "4K {status}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Automatyczne żądania filmów", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Zapisz zmiany", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Zapisywan<PERSON>…", "components.Layout.Sidebar.browsemovies": "Filmy", "components.Login.validationEmailFormat": "Nieprawidłowy adres e-mail", "components.RequestModal.SearchByNameModal.nomatches": "Nie udało nam się znaleźć odpowiednika dla tego serialu.", "components.Selector.showmore": "Pokaż więcej", "components.Settings.SettingsMain.cacheImagesTip": "Pam<PERSON><PERSON>ć podręczna obrazów pochodzących z zewnątrz (wymaga znacznej ilości miejsca na dysku)", "components.Settings.SettingsMain.originallanguageTip": "Filtrowanie zawartości według oryginalnego języka", "components.Settings.SettingsMain.toastSettingsSuccess": "Ustawienia zapisane pomyślnie!", "components.Settings.experimentalTooltip": "Włączenie tego ustawienia może spowodować nieoczekiwane zachowanie aplikacji", "components.UserList.mediaServerUser": "Użytkownik {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.email": "Email", "components.Discover.resetwarning": "Przywróć domyślne ustawienia wszystkich suwaków. Spowoduje to również usunięcie wszystkich niestandardowych suwaków!", "components.Layout.UserWarnings.emailRequired": "W<PERSON><PERSON>y jest adres e-mail.", "components.MovieDetails.openradarr": "Otwórz film w Radarr", "components.RequestModal.requestcollection4ktitle": "Poproś o kolekcję w 4K", "components.Setup.signinWithJellyfin": "Wprowadź swoje dane <PERSON>", "components.Login.description": "Ponieważ logujesz się do {applicationName} po raz pier<PERSON>, musisz dodać prawidłowy adres e-mail.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Spowoduje to nieodwracalne usunięcie tego {mediaType} z {arr}, w tym wszystkich plików.", "components.ManageSlideOver.removearr": "<PERSON><PERSON><PERSON> z {arr}", "components.PermissionEdit.autorequestSeriesDescription": "Zezwolenie na automatyczne przesyłanie żądań dotyczących seriali innych niż 4K za pośrednictwem listy obserwowanych Plex.", "components.PermissionEdit.viewwatchlists": "Wyświetl {mediaServerName} watchlistę", "components.RequestCard.approverequest": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.tagRequestsInfo": "Automatycznie dodawaj dodatkowy znacznik z identyfikatorem użytkownika i wyświetlaną nazwą użytkownika", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Pełne skanowanie bibliotek Jellyfin", "components.Settings.SettingsLogs.viewdetails": "Zobacz szczegóły", "components.Settings.jellyfinSettingsDescription": "Opcjonalnie skonfiguruj wewnętrzne i zewnętrzne punkty końcowe dla serwera {mediaServerName}. W większości przypadków zewnętrzny adres URL różni się od wewnętrznego adresu URL. Niestandardowy adres URL resetowania hasła można również ustawić dla logowania {mediaServerName}, na wypadek gdybyś chciał przekierować na inną stronę resetowania hasła. Możesz również zmienić klucz API Jellyfin, który został uprzednio wygenerowany automatycznie.", "components.Settings.save": "Zapisz zmiany", "components.Settings.syncJellyfin": "Synchronizuj biblioteki", "components.TvDetails.Season.noepisodes": "Lista odcinków jest niedostępna.", "components.Settings.Notifications.NotificationsPushover.sound": "Dźwięk powiadomień", "components.Settings.RadarrModal.tagRequests": "Żądania tagów", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktualna <PERSON>", "components.Settings.SettingsMain.validationApplicationTitle": "<PERSON><PERSON><PERSON> podać tytuł aplikacji", "components.Settings.SonarrModal.seriesType": "Typ seriali", "components.Settings.advancedTooltip": "Nieprawidłowe skonfigurowanie tego ustawienia może spowodować nieprawidłowe działanie", "components.TvDetails.reportissue": "<PERSON><PERSON>ł<PERSON>ś problem", "components.RequestBlock.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.unknowntitle": "<PERSON>ez<PERSON><PERSON> tytuł", "components.Selector.nooptions": "Brak wyników.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Domyślne urządzenie", "components.Settings.Notifications.userEmailRequired": "Wymagaj adresu e-mail użytkownika", "components.Settings.manualscanDescriptionJellyfin": "<PERSON><PERSON><PERSON> będzie to uruchamiane tylko raz na 24 godziny. <PERSON><PERSON><PERSON><PERSON> będzie bardziej agresywnie sprawdzać ostatnio dodane biblioteki serwera {mediaServerName}. <PERSON><PERSON><PERSON> po raz pierwszy konfi<PERSON><PERSON><PERSON><PERSON>, zale<PERSON> jest jednorazowe pełne ręczne skanowanie biblioteki!", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Aktywny filtr} other {# Aktywne filtry}}", "components.Login.back": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.validationHostnameRequired": "<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> prawidłowy adres IP lub nazwę hosta", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> został z powodzeniem usunięty z listy Do obejrzenia!", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> został z powodzeniem dodany do listy Do obejrzenia!", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Co {jobScheduleSeconds, plural, one {sekundę} other {{jobScheduleSeconds} sekundy}}", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Czyszczenie pamięci podręcznej obrazów", "components.Settings.SettingsJobsCache.imagecache": "Pam<PERSON><PERSON>ć podręczna obrazów", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Aktywny filtr} other {# Aktywne filtry}}", "components.Login.enablessl": "Użyj SSL", "components.Login.invalidurlerror": "Niepowodzenie połączenia z serwerem {mediaServerName}.", "components.Login.port": "Port", "components.Login.servertype": "<PERSON><PERSON> ser<PERSON>a", "components.Login.urlBase": "Bazowy URL", "components.Login.validationPortRequired": "<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> prawidłowy numer portu", "components.Login.validationUrlBaseLeadingSlash": "Bazowy URL musi rozpoczynać się ukośnikiem", "components.Login.validationUrlBaseTrailingSlash": "Bazowy URL nie może kończyć się ukośnikiem", "components.Login.validationUrlTrailingSlash": "URL nie może kończyć się ukośnikiem", "components.Login.validationservertyperequired": "Prosz<PERSON> wybrać typ serwera", "components.MovieDetails.addtowatchlist": "Dodaj do listy Do obejrzenia", "components.MovieDetails.removefromwatchlist": "Usuń z listy Do obejrzenia", "components.MovieDetails.watchlistError": "Coś poszło nie tak, spróbuj ponownie.", "components.Discover.FilterSlideover.status": "Status", "components.Discover.tmdbmoviekeyword": "Słowo kluczowe TMDB", "components.Settings.SonarrModal.animeSeriesType": "Rodzaj serialu Anime", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.TitleCard.watchlistDeleted": "Z powodzeniem usunięto <strong>{title}</strong> z listy Do obejrzenia!", "components.PermissionEdit.viewwatchlistsDescription": "Udziel zgody na wyświetlanie list Do obejrzenia {mediaServerName} innych użytkowników.", "components.Selector.canceled": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.returningSeries": "Powracający serial", "components.Settings.SettingsJobsCache.imagecacheDescription": "Po włączeniu w us<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> bę<PERSON><PERSON> buforował obrazy z skonfigurowanych źródeł zewnętrznych i pośredniczył w ich wyświetlaniu. Buforowane obrazy są zapisywane w folderze danych aplikacji. Pliki można znaleźć w <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Synchronizacja listy Do obejrzenia Plex", "components.Settings.SonarrModal.tagRequestsInfo": "Automatycznie dodawaj dodatkowy znacznik z identyfikatorem użytkownika i wyświetlaną nazwą użytkownika", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.TvDetails.addtowatchlist": "Dodaj do listy Do obejrzenia", "components.UserList.noJellyfinuserstoimport": "Brak użytkowników {mediaServerName} do zaimportowania.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatycznie składaj prośby o filmy z twojej listy <PlexWatchlistSupportLink>Do obejrzenia Plex</PlexWatchlistSupportLink>", "components.TitleCard.watchlistSuccess": "Z powodzeniem dodano <strong>{title}</strong> do listy Do obejrzenia!", "components.TvDetails.play4k": "Odtwórz w 4K na {mediaServerName}", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> został z powodzeniem dodany do listy Do obejrzenia!", "components.UserList.importedfromJellyfin": "Z powodzeniem z<PERSON>mpo<PERSON> <strong>{userCount}</strong> {userCount, plural, one {użytkownika} other {użytkowników}} {mediaServerName}!", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Dźwięk powiadomień", "components.UserList.newJellyfinsigninenabled": "Ustawienie <strong><PERSON><PERSON><PERSON><PERSON> nowe logowanie {mediaServerName}</strong> jest aktualnie aktywne. Użytkownicy {mediaServerName} z dostępem do biblioteki nie muszą być zaimportowani aby się zalogować.", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Wymagany poprawny adres e-mail", "components.UserProfile.emptywatchlist": "Media dodane do twojej listy <PlexWatchlistSupportLink>Do obejrzenia Plex</PlexWatchlistSupportLink> zostaną wyświetlone tutaj.", "components.Login.adminerror": "Do zalogowania musisz użyć konta administratora.", "components.Settings.SettingsJobsCache.availability-sync": "Synchronizacja dostępności mediów", "components.Settings.SettingsJobsCache.imagecachecount": "Zbuforowane obrazy", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Niestandardowa autentykacja nie jest obsługiwana w połączeniu z Automatycznym grupowaniem bibliotek", "components.Settings.jellyfinSyncFailedGenericError": "Coś poszło nie tak w trakcie synchronizacji bibliotek", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Nie znaleziono żadnych bibliotek", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Aktywny filtr} other {# Aktywne filtry}}", "components.Login.hostname": "{mediaServerName} URL", "components.MovieDetails.imdbuserscore": "Ocena użytkowników IMDB", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.profileName": "Profil", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.Selector.ended": "Zakończone", "components.Selector.inProduction": "W produkcji", "components.Selector.pilot": "Pilot", "components.Selector.planned": "Planowany", "components.Selector.searchStatus": "Wybierz status...", "components.Settings.SettingsJobsCache.imagecachesize": "Całkowity rozmiar pamięci podręcznej", "components.Settings.SettingsMain.cacheImages": "Włącz Buforowanie obrazów", "components.Settings.SettingsMain.originallanguage": "Język funkcji Odkryj", "components.Settings.SettingsMain.toastApiKeyFailure": "Coś poszło nie tak w trakcie generowania nowego klucza API.", "components.Settings.SettingsMain.toastApiKeySuccess": "Nowy klucz API został pomyślnie wygenerowany!", "components.Settings.SettingsMain.validationApplicationUrl": "Musisz podać poprawny adres URL", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "Adres URL nie może kończyć się ukośnikiem", "components.Settings.SonarrModal.tagRequests": "Żądania tagów", "components.Settings.invalidurlerror": "Niepowodzenie połączenia z serwerem {mediaServerName}.", "components.Settings.jellyfinForgotPasswordUrl": "Adres URL odzyskiwania hasła", "components.Settings.jellyfinlibraries": "Biblioteki {mediaServerName}", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON> z<PERSON>a tego ustawienia została zastosowana, na<PERSON>ży ponownie <PERSON><PERSON><PERSON><PERSON>", "components.Settings.timeout": "<PERSON><PERSON>", "components.Setup.back": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configemby": "Skonfiguruj <PERSON>", "components.Setup.configjellyfin": "Skonfi<PERSON><PERSON><PERSON>", "components.Setup.configplex": "Skonfiguruj Plex", "components.Setup.configuremediaserver": "Skonfiguruj serwer multimediów", "components.Setup.servertype": "<PERSON><PERSON><PERSON><PERSON> ser<PERSON>", "components.Setup.signin": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.signinWithEmby": "Wprowadź swoje dane <PERSON>", "components.Setup.subtitle": "Zacznij od wybrania serwera multimediów", "components.StatusBadge.managemedia": "Zarządzaj {mediaType}", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.TitleCard.addToWatchList": "Dodaj do listy Do obejrzenia", "components.TitleCard.cleardata": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dane", "components.TitleCard.mediaerror": "{mediaType} nie odnaleziony", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TitleCard.watchlistCancel": "lista obserwowanych dla <strong>{title}</strong> została anulowana.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# odcinek} other {# odcinki}}", "components.TvDetails.manageseries": "Zarządzaj serialem", "components.TvDetails.play": "Odtwórz na {mediaServerName}", "components.TvDetails.removefromwatchlist": "Usuń z listy Do obejrzenia", "components.TvDetails.rtaudiencescore": "Ocena publiczności Rotten Tomatoes", "components.TvDetails.rtcriticsscore": "Tomatometer Rotten Tomatoes", "components.TvDetails.tmdbuserscore": "Ocena użytkowników TMDB", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> został z powodzeniem usunięty z listy Do obejrzenia!", "components.TvDetails.watchlistError": "Coś poszło nie tak, spróbuj ponownie.", "components.UserList.importfromJellyfin": "Importuj użytkowników {mediaServerName}", "components.UserList.importfrommediaserver": "Importuj użytkowników {mediaServerName}", "components.UserList.importfromJellyfinerror": "Coś poszło nie tak w trakcie importowania użytkowników {mediaServerName}.", "components.UserList.username": "Nazwa użytkownika", "components.UserList.validationUsername": "Musisz podać nazwę użytkownika", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "Użytkownik {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Automatyczna prośba o serial", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatycznie składaj prośby o seriale z twojej listy <PlexWatchlistSupportLink>Do obejrzenia Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "<PERSON>res e-mail jest wymagany", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Domyślne urządzenie", "components.UserProfile.localWatchlist": "<PERSON> obejrzenia {username}", "components.UserProfile.plexwatchlist": "Do obejrzenia Plex", "i18n.collection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviestreamingservices": "Usługi strumieniowe TMDB", "components.Discover.tmdbnetwork": "Sieć TMDB", "components.Discover.tmdbtvstreamingservices": "Telewizyjne usługi strumieniowe TMDB", "component.BlacklistBlock.blacklistdate": "Data umieszczenia na czarnej liście", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> nie znajduje się na czarnej liście.", "components.Settings.Notifications.webhookRoleIdTip": "Identyfikator roli do wskazania w wiadomości webhook. Pozostaw puste, aby wył<PERSON><PERSON><PERSON><PERSON> wzmianki", "components.Settings.SettingsJobsCache.usersavatars": "Awatary użytkowników", "components.Settings.SettingsMain.discoverRegionTip": "Filtrowanie zawartości według dostępności regionalnej", "components.PermissionEdit.viewblacklistedItems": "Wyświetl media znajdujące się na czarnej liście", "components.Settings.SettingsMain.discoverRegion": "Odkryj region", "components.PermissionEdit.blacklistedItemsDescription": "Zezwól na umieszczenie mediów na czarnej liście.", "components.PermissionEdit.manageblacklistDescription": "Udziel uprawnień do zarządzania multimediami znajdującymi się na czarnej liście.", "components.Settings.Notifications.validationWebhookRoleId": "<PERSON><PERSON><PERSON> pod<PERSON> prawidłowy Discord Role ID", "component.BlacklistBlock.blacklistedby": "Wpisany na czarną listę przezClick to apply", "component.BlacklistModal.blacklisting": "Czarna lista", "components.Blacklist.blacklistSettingsDescription": "Zarządzanie mediami znajdującymi się na czarnej liście.", "components.Blacklist.blacklistdate": "data", "components.Blacklist.blacklistedby": "{date} przez {user}", "components.Blacklist.blacklistsettings": "Ustawienia czarnej listy", "components.Blacklist.mediaName": "Nazwa", "components.Blacklist.mediaType": "<PERSON><PERSON>", "components.Layout.Sidebar.blacklist": "Czarna lista", "components.PermissionEdit.manageblacklist": "Zarządzanie czarną listą", "components.PermissionEdit.blacklistedItems": "Dodaj multimedia do czarnej listy.", "components.PermissionEdit.viewblacklistedItemsDescription": "Zezwól na wyświetlanie mediów z czarnej listy.", "components.RequestList.RequestItem.removearr": "<PERSON><PERSON><PERSON> z {arr}", "components.RequestList.sortDirection": "Zmień kierunek sortowania", "components.Settings.Notifications.webhookRoleId": "Powiadomienie Role ID"}