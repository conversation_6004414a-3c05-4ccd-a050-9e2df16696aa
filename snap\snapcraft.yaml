name: jellyseerr
adopt-info: jellyseerr
license: MIT
summary: Request management and media discovery tool for media servers
description: >
  Jellyseerr is a free and open source software application for managing requests for your media library.
  It is a a fork of Overseerr built to bring support for & focusing mainly on Jellyfin & Emby media servers!
  It integrates with your existing services such as Sonarr, Radarr, and Jellyfin/Emby/Plex.
base: core20
confinement: strict

architectures:
  - build-on: amd64
  - build-on: arm64
  # - build-on: armhf

parts:
  jellyseerr:
    plugin: nil
    build-packages:
      - git
      - ca-certificates
      - curl
      - gnupg
      - on arm64:
          - build-essential
          - automake
          - python-gi
          - python-gi-dev
      # - on armhf:
      #     - libatomic1
      #     - build-essential
      #     - automake
      #     - python-gi
      #     - python-gi-dev
    source: .
    override-pull: |
      snapcraftctl pull
      # Get information to determine snap grade and version
      git config --global --add safe.directory /data/parts/jellyseerr/src
      #setup yarn.rc
      echo "--install.frozen-lockfile\n--install.network-timeout 1000000" > .yarnrc
      BRANCH=$(git rev-parse --abbrev-ref HEAD)
      COMMIT=$(git rev-parse HEAD)
      COMMIT_SHORT=$(git rev-parse --short HEAD)
      VERSION='v'$(cat package.json | grep 'version' | head -1 | sed 's/.*"\(.*\)"\,/\1/')
      if [ "$VERSION" = "v0.1.0" ]; then
        SNAP_VERSION=$COMMIT_SHORT
        GRADE=stable
      else
        SNAP_VERSION=$VERSION
        GRADE=stable
      fi
      # Write COMMIT_TAG as it is needed durring the build process
      echo $COMMIT > commit.txt
      # Print debug info for build version
      echo "{\"commitShort\": \"$COMMIT_SHORT\", \
      \"version\": \"$VERSION\", \
      \"snapVersion\": \"$SNAP_VERSION\", \
      \"snapGrade\": \"$GRADE\", \
      \"branch\": \"$BRANCH\", \
      \"commit\": \"$COMMIT\"}"
      echo "{\"commitTag\": \"$COMMIT\"}" > committag.json
      # Set snap version and grade
      snapcraftctl set-version "$SNAP_VERSION"
      snapcraftctl set-grade "$GRADE"
    build-environment:
      - PATH: '$SNAPCRAFT_PART_BUILD/node_modules/.bin:$PATH'
      - CYPRESS_INSTALL_BINARY: '0'
    override-build: |
      set -e
      # Install necessary packages
      mkdir -p /etc/apt/keyrings
      # Add Node.js repository key
      curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg

      # Set Node.js version
      NODE_MAJOR=20
      # Add Node.js repository to sources list
      echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list

      # Update package sources and install Node.js
      apt-get update
      apt-get install nodejs -y

      # Install Yarn
      npm install -g yarn
      # Set COMMIT_TAG before the build begins
      export COMMIT_TAG=$(cat $SNAPCRAFT_PART_BUILD/commit.txt)
      snapcraftctl build
      yarn install --frozen-lockfile --network-timeout 1000000
      yarn build
      # Copy files needed for staging
      cp $SNAPCRAFT_PART_BUILD/committag.json $SNAPCRAFT_PART_INSTALL/
      cp -R $SNAPCRAFT_PART_BUILD/.next $SNAPCRAFT_PART_INSTALL/
      cp -R $SNAPCRAFT_PART_BUILD/dist $SNAPCRAFT_PART_INSTALL/
      cp -R $SNAPCRAFT_PART_BUILD/node_modules $SNAPCRAFT_PART_INSTALL/
      # Remove .github and gitbook as it will fail snap lint
      rm -rf $SNAPCRAFT_PART_INSTALL/.github
    # stage-packages:
    #   - on armhf:
    #       - libatomic1
    stage: [.next, ./*]
    prime: [.next, ./*]

apps:
  daemon:
    command: /bin/sh -c "cd $SNAP && node dist/index.js"
    daemon: simple
    restart-condition: on-failure
    restart-delay: 5s
    plugs:
      - home
      - network
      - network-bind
    environment:
      PATH: '$SNAP/usr/sbin:$SNAP/usr/bin:$SNAP/sbin:$SNAP/bin:$PATH'
      OVERSEERR_SNAP: 'True'
      CONFIG_DIRECTORY: $SNAP_USER_COMMON
      LOG_LEVEL: 'debug'
      NODE_ENV: 'production'
