{"components.RequestCard.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.PersonDetails.ascharacter": "como {character}", "components.PersonDetails.appearsin": "Aparece em", "components.MovieDetails.studio": "{studioCount, plural, one {Estúdio} other {Estúdios}}", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.runtime": "{minutes} minutos", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Lança<PERSON>} other {Lançamentos}}", "components.MovieDetails.recommendations": "Recomendações", "components.MovieDetails.overviewunavailable": "Sinopse indisponível.", "components.MovieDetails.overview": "Sinopse", "components.MovieDetails.originallanguage": "Língua Original", "components.MovieDetails.cast": "Elenco", "components.MovieDetails.budget": "Orçamento", "components.MovieDetails.MovieCast.fullcast": "Elenco Completo", "components.Layout.UserDropdown.signout": "<PERSON><PERSON>", "components.Layout.Sidebar.users": "Usuários", "components.Layout.Sidebar.settings": "Configurações", "components.Layout.Sidebar.requests": "Solicitações", "components.Layout.Sidebar.dashboard": "Explorar", "components.Layout.SearchInput.searchPlaceholder": "Pesquisar Filmes & Séries", "components.Discover.upcomingmovies": "Filmes em Breve", "components.Discover.upcoming": "Filmes em Breve", "components.Discover.trending": "Em Alta", "components.Discover.recentrequests": "Solicitações Recentes", "components.Discover.recentlyAdded": "Adicionado <PERSON>", "components.Discover.populartv": "Séries Populares", "components.Discover.popularmovies": "Filmes Populares", "components.Settings.plexlibraries": "Bibliotecas do Plex", "components.Settings.notrunning": "Parado", "components.RequestModal.season": "Temporada", "components.Settings.notificationsettings": "Configurações de Notificação", "components.Settings.menuServices": "Serviços", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notificações", "components.Settings.menuLogs": "Logs", "components.Settings.menuJobs": "Tarefas & Cache", "components.Settings.menuGeneralSettings": "G<PERSON>", "components.Settings.menuAbout": "Sobre", "components.Settings.manualscanDescription": "Normalmente, isso só será executado uma vez a cada 24 horas. Je<PERSON><PERSON>rr irá checar em detalhes items recentemente adicionados ao seu servidor Plex. Se essa é a primeira vez que você configura um servidor Plex, é recomendado a varredura completa de sua biblioteca!", "components.Settings.manualscan": "Varredura Manual da Biblioteca", "components.Settings.librariesRemaining": "Bibliotecas Restantes: {count}", "components.Settings.hostname": "Nome ou IP do Servidor", "components.Settings.deleteserverconfirm": "Tem certeza que deseja apagar esse servidor?", "i18n.deleting": "Apagando…", "i18n.delete": "<PERSON><PERSON><PERSON>", "components.Settings.default4k": "Padrão 4K", "components.Settings.default": "Padrão", "components.Settings.currentlibrary": "Biblioteca Atual: {name}", "components.Settings.copied": "Chave de API copiada.", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.address": "Endereço", "components.Settings.addradarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.activeProfile": "Perfil Ativo", "components.Settings.SonarrModal.validationRootFolderRequired": "Você deve selecionar uma pasta raíz", "components.Settings.SonarrModal.validationProfileRequired": "Você deve selecionar um perfil de qualidade", "components.Settings.SonarrModal.validationPortRequired": "Você deve prover uma porta válida", "components.Settings.SonarrModal.validationNameRequired": "Você deve prover o nome do servidor", "components.Settings.SonarrModal.validationHostnameRequired": "Você deve prover o nome ou IP do servidor", "components.Settings.SonarrModal.validationApiKeyRequired": "Você deve prover uma chave de API", "components.Settings.SonarrModal.testFirstRootFolders": "Teste conexão para carregar as pastas raízes", "components.Settings.SonarrModal.testFirstQualityProfiles": "Teste conexão para carregar perfis de qualidade", "components.Settings.SonarrModal.ssl": "Usar SSL", "components.Settings.SonarrModal.servername": "Nome do Servidor", "components.Settings.SonarrModal.server4k": "Servidor 4K", "components.Settings.SonarrModal.selectRootFolder": "Selecione a pasta raíz", "components.Settings.SonarrModal.selectQualityProfile": "Selecione o perfil de qualidade", "components.Settings.SonarrModal.seasonfolders": "Temporadas Em Pastas", "components.Settings.SonarrModal.rootfolder": "Pasta Raíz", "components.Settings.SonarrModal.qualityprofile": "Perfil de Qualidade", "components.Settings.SonarrModal.port": "Porta", "components.Settings.SonarrModal.loadingrootfolders": "Carregando pastas raízes…", "components.Settings.SonarrModal.loadingprofiles": "Carregando Perfis de Qualidade…", "components.Settings.SonarrModal.hostname": "Nome ou IP do Servidor", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON>", "components.Settings.SonarrModal.defaultserver": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.createsonarr": "Adicionar Um Novo Servidor <PERSON>arr", "components.Settings.SonarrModal.baseUrl": "URL Base", "components.Settings.SonarrModal.apiKey": "Chave de <PERSON>", "components.Settings.SonarrModal.animerootfolder": "Pasta Raíz de Animes", "components.Settings.SonarrModal.animequalityprofile": "Perfil de Qualidade Para Animes", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "Vers<PERSON>", "components.Settings.SettingsAbout.totalrequests": "Total de Solicitações", "components.Settings.SettingsAbout.totalmedia": "Total de Títulos", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "Discussões no GitHub", "components.Settings.SettingsAbout.gettingsupport": "Obtenha Suporte", "components.Settings.RadarrModal.validationRootFolderRequired": "Você deve selecionar uma pasta raíz", "components.Settings.RadarrModal.validationProfileRequired": "Você deve selecionar um perfil de qualidade", "components.Settings.RadarrModal.validationPortRequired": "Você deve prover uma porta válida", "components.Settings.RadarrModal.validationNameRequired": "Você deve prover o nome do servidor", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Você deve selecionar a disponibilidade mínima", "components.Settings.RadarrModal.validationHostnameRequired": "Você deve prover o nome ou IP do servidor", "components.Settings.RadarrModal.validationApiKeyRequired": "Você deve prover uma chave de API", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Conexão com Radarr estabelecida com sucesso!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Falha ao conectar-se ao Radarr.", "components.Settings.RadarrModal.testFirstRootFolders": "Teste conexão para carregar as pastas raízes", "components.Settings.RadarrModal.testFirstQualityProfiles": "Teste conexão para carregar perfis de qualidade", "components.Settings.RadarrModal.ssl": "Usar SSL", "components.Settings.RadarrModal.servername": "Nome do Servidor", "components.Settings.RadarrModal.server4k": "Servidor 4K", "components.Settings.RadarrModal.selectRootFolder": "Selecione a pasta raíz", "components.Settings.RadarrModal.selectQualityProfile": "Selecione o perfil de qualidade", "components.Settings.RadarrModal.selectMinimumAvailability": "Selecione disponibilidade mínima", "components.Settings.RadarrModal.rootfolder": "Pasta Raíz", "components.Settings.RadarrModal.qualityprofile": "Perfil de Qualidade", "components.Settings.RadarrModal.port": "Porta", "components.Settings.RadarrModal.minimumAvailability": "Disponibilidade Mínima", "components.Settings.RadarrModal.loadingrootfolders": "Carregando Pastas Raízes…", "components.Settings.RadarrModal.loadingprofiles": "Carregando Perfis de Qualidade…", "components.Settings.RadarrModal.hostname": "Nome ou IP do Servidor", "components.Settings.RadarrModal.editradarr": "<PERSON><PERSON>", "components.Settings.RadarrModal.defaultserver": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.createradarr": "Adicionar Novo Servidor Radarr", "components.Settings.RadarrModal.baseUrl": "URL Base", "components.Settings.RadarrModal.apiKey": "Chave de <PERSON>", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.webhookUrl": "URL de Webhook", "components.Settings.Notifications.validationSmtpPortRequired": "Você deve prover uma porta válida", "components.Settings.Notifications.validationSmtpHostRequired": "Você deve prover um nome válido ou IP de servidor", "components.Settings.Notifications.smtpPort": "Porta SMTP", "components.Settings.Notifications.smtpHost": "Servidor SMTP", "components.Settings.Notifications.emailsettingssaved": "Configurações de notificação via e-mail salvas com sucesso!", "components.Settings.Notifications.emailsettingsfailed": "Falha ao salvar configurações de notificação via e-mail.", "components.Settings.Notifications.emailsender": "Email do Remetente", "components.Settings.Notifications.discordsettingssaved": "Configurações de notificação via Discord salvas com sucesso!", "components.Settings.Notifications.discordsettingsfailed": "Falha ao salvar configurações de notificação via Discord.", "components.Settings.Notifications.authUser": "Usuário SMTP", "components.Settings.Notifications.authPass": "Senha SMTP", "components.Settings.Notifications.agentenabled": "Habilitar Agente", "components.Search.searchresults": "Resultados da Pesquisa", "components.RequestModal.selectseason": "Selecione Temporada(s)", "components.RequestModal.seasonnumber": "Temporada {number}", "components.RequestModal.requestfrom": "Existe uma solicitação pendente de {username}.", "components.RequestModal.requestadmin": "Essa solicitação será aprovada automaticamente.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> solicitado com sucesso!", "components.RequestModal.requestCancel": "Solicitação para <strong>{title}</strong> cancelada.", "components.RequestModal.pendingrequest": "Solicitação Pendente", "components.RequestModal.numberofepisodes": "# de Episódeos", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON>", "components.RequestList.requests": "Solicitações", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configureservices": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.validationPortRequired": "Você deve prover uma porta válida", "components.Settings.validationHostnameRequired": "Você deve prover o Nome ou IP do Servidor", "components.Settings.startscan": "<PERSON><PERSON>ar <PERSON>", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "Configurações do Sonarr", "components.Settings.radarrsettings": "Configurações do Radarr", "components.Settings.port": "Porta", "components.Settings.plexsettingsDescription": "Configure os dados de conexão com servidor Plex. Jellyseerr escanea suas bibliotecas do Plex em busca de novo conteúdo disponível.", "components.Settings.plexsettings": "Configurações do Plex", "components.Settings.plexlibrariesDescription": "Bibliotecas que Jellyseerr irá buscar por títulos. Configure e salve as informações de conexão com Plex e clique no botão abaixo se nenhuma biblioteca for listada.", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Ajude a Pagar o Café", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON> o <PERSON>", "components.Settings.SettingsAbout.Releases.viewongithub": "Ver no GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "<PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.versionChangelog": "Mudanças em {version}", "components.Settings.SettingsAbout.Releases.releases": "Versõ<PERSON>", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Informações de versão indisponíveis.", "components.Settings.SettingsAbout.Releases.latestversion": "Última Versão", "components.Settings.SettingsAbout.Releases.currentversion": "Atual", "components.TvDetails.recommendations": "Recomendações", "components.TvDetails.overviewunavailable": "Sinopse indisponível.", "components.TvDetails.overview": "Sinopse", "components.TvDetails.originallanguage": "Língua Original", "components.TvDetails.network": "{networkCount, plural, one {Emissora} other {Emissoras}}", "components.TvDetails.cast": "Elenco", "components.TvDetails.anime": "Animes", "components.TvDetails.TvCast.fullseriescast": "Elenco Completo da Série", "components.Setup.welcome": "Bem-<PERSON><PERSON> ao <PERSON>", "components.Setup.signinMessage": "Comece entrando com sua conta Plex", "components.Setup.finishing": "Finalizando…", "components.Setup.finish": "Finalizar Configurações", "pages.returnHome": "Voltar Para Página Inicial", "pages.oops": "Opa", "i18n.unavailable": "Indisponível", "i18n.tvshows": "Séries", "i18n.processing": "Processando", "i18n.pending": "Pendente", "i18n.partiallyavailable": "Parcialmente Disponível", "i18n.movies": "Filmes", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.decline": "<PERSON><PERSON><PERSON><PERSON>", "i18n.close": "<PERSON><PERSON><PERSON>", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.available": "Disponível", "i18n.approved": "Aprovada", "i18n.approve": "<PERSON><PERSON><PERSON>", "components.UserList.userlist": "Lista de Usuários", "components.UserList.userdeleteerror": "Algo deu errado ao remover usu<PERSON>rio.", "components.UserList.userdeleted": "Usuário removido com sucesso!", "components.UserList.user": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.totalrequests": "Solicitações", "components.UserList.role": "Privilégio", "components.UserList.plexuser": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.deleteuser": "<PERSON>eta<PERSON>", "components.UserList.deleteconfirm": "Tem certeza que deseja apagar esse usuário? Todas informações de solicitações feitas por esse usuário serão permanentemente removidas.", "components.UserList.created": "<PERSON><PERSON><PERSON>", "components.UserList.admin": "Administrador", "components.TvDetails.similar": "Séries Semelhantes", "components.TvDetails.showtype": "Tipo de Série", "components.RequestModal.requestseasons": "Solicitar {seasonCount} {seasonCount, plural, one {Temporada} other {Temporadas}}", "components.TvDetails.viewfullcrew": "Ver Toda Equipe Técnica", "components.TvDetails.TvCrew.fullseriescrew": "Equipe Técnica Completa da Série", "components.PersonDetails.crewmember": "Me<PERSON><PERSON>", "components.MovieDetails.viewfullcrew": "Ver Equipe Técnica Completa", "components.MovieDetails.MovieCrew.fullcrew": "Equipe Técnica Completa", "components.UserList.importfromplexerror": "Algo deu errado ao importar usuários do Plex.", "components.UserList.importfromplex": "Importar Usuários do Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {usuário Plex importado} other {usuários Plex importados}} com sucesso!", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Habilitar Agente", "components.RequestList.RequestItem.failedretry": "Algo deu errado ao retentar fazer a solicitação.", "components.MovieDetails.watchtrailer": "<PERSON><PERSON><PERSON>", "components.CollectionDetails.requestcollection": "Solicitar Coleção", "components.CollectionDetails.overview": "Sinopse", "components.CollectionDetails.numberofmovies": "{count} Filmes", "i18n.requested": "Solicitado", "i18n.retry": "Tentar Novamente", "i18n.failed": "Fal<PERSON>", "components.TvDetails.watchtrailer": "<PERSON><PERSON><PERSON><PERSON>er", "components.TvDetails.firstAirDate": "Primeira Exibição", "components.Settings.Notifications.validationChatIdRequired": "Você deve prover um ID de Chat válido", "components.Settings.Notifications.validationBotAPIRequired": "Você deve prover uma chave de autorização do Bot", "components.Settings.Notifications.senderName": "Nome do Remetente", "components.Settings.Notifications.telegramsettingssaved": "Configurações de notificação via Telegram salvas com sucesso!", "components.Settings.Notifications.telegramsettingsfailed": "Falha ao salvar configurações de notificação via Telegram.", "components.Settings.Notifications.chatId": "ID de Chat", "components.Settings.Notifications.botAPI": "Token de Autorização do Bot", "components.Settings.Notifications.allowselfsigned": "Permitir certificados auto-assinados", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Configurações de notificação via Slack salvas com sucesso!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Falha ao salvar configurações de notificação via Slack.", "components.Settings.SettingsAbout.documentation": "Documentação", "components.NotificationTypeSelector.mediarequestedDescription": "Envia notificações quando outros usuários solicitarem novas mídias que requerem aprovação.", "components.NotificationTypeSelector.mediaavailable": "Solicitação Disponível", "components.NotificationTypeSelector.mediaapproved": "Solicitação Aprovada", "components.NotificationTypeSelector.mediarequested": "Solicitação com Aprovação Pendente", "components.NotificationTypeSelector.mediafailedDescription": "Envia notificaões quando solicitações de mídia falharem ao serem adicionadas ao Radarr ou Sonarr.", "components.NotificationTypeSelector.mediafailed": "Falha ao Processar Solicitação", "components.NotificationTypeSelector.mediaavailableDescription": "Envia notificações quando mídias solicitadas estiverem disponíveis.", "components.NotificationTypeSelector.mediaapprovedDescription": "Enviar notificações quando solicitações de mídia são aprovadas manualmente.", "i18n.request": "Solicitar", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Você deve prover uma chave válida de usúario ou grupo", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Você deve prover uma chave válida de acesso", "components.Settings.Notifications.NotificationsPushover.userToken": "Chave do Usuário ou Grupo", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Configurações de notificação via Pushover salvas com sucesso!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Falha ao salvar configurações de notificação via Pushover.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsPushover.accessToken": "<PERSON><PERSON>", "components.RequestList.sortModified": "Última <PERSON>", "components.RequestList.sortAdded": "<PERSON><PERSON>", "components.RequestList.showallrequests": "<PERSON><PERSON><PERSON>", "components.StatusBadge.status4k": "4K {status}", "components.RequestModal.pending4krequest": "Solicitação em 4K Pendente", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Configurações de notificação via Webhook salvas com sucesso!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Falha ao salvar configurações de notificação via Webhook.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Você deve prover um conteúdo JSON válido", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Ajuda Com Modelos de Variáveis", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON restaurado para conteúdo padrão!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.customJson": "<PERSON><PERSON><PERSON>do <PERSON>", "components.Settings.Notifications.NotificationsWebhook.authheader": "Cabeçalho de Autorização", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Habilitar Agente", "components.RequestButton.viewrequest4k": "Ver Solicitação 4K", "components.RequestButton.viewrequest": "Ver Solicitação", "components.RequestButton.requestmore4k": "Solicitar Mais em 4K", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON>", "components.RequestButton.declinerequests": "Rejeitar {requestCount, plural, one {Solicitação} other {{requestCount} Solicitações}}", "components.RequestButton.declinerequest4k": "Rejeitar Solicitação 4K", "components.RequestButton.declinerequest": "Re<PERSON><PERSON>r <PERSON>", "components.RequestButton.decline4krequests": "Rejeitar {requestCount, plural, one {Solicitação} other {{requestCount} Solicitações}} em 4K", "components.RequestButton.approverequests": "Aprovar {requestCount, plural, one {Solicitação} other {{requestCount} Solicitações}}", "components.RequestButton.approverequest4k": "Aprovar <PERSON> 4K", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.approve4krequests": "Aprovar {requestCount, plural, one {Solicitação 4K} other {{requestCount} Solicitações 4K}}", "components.UserList.validationpasswordminchars": "Senha muito curta; necessário ter no mínimo 8 caracteres", "components.UserList.usercreatedsuccess": "Usuário criado com sucesso!", "components.UserList.usercreatedfailed": "Algo deu errado ao criar usuário.", "components.UserList.passwordinfodescription": "Configure a URL da aplicação e habilite notificações via e-mail para permitir a geração automática de senha.", "components.UserList.password": "<PERSON><PERSON>", "components.UserList.localuser": "Usuário Local", "components.UserList.email": "Endereço de E-mail", "components.UserList.creating": "<PERSON><PERSON><PERSON>", "components.UserList.createlocaluser": "Criar Usuário Local", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "<PERSON><PERSON><PERSON>", "components.Login.validationpasswordrequired": "Você deve prover uma senha", "components.Login.validationemailrequired": "Você deve prover um e-mail válido", "components.Login.signinwithoverseerr": "Entrar com sua conta {applicationTitle}", "components.Login.password": "<PERSON><PERSON>", "components.Login.loginerror": "Algo deu errado ao tentar se autenticar.", "components.Login.email": "Endereço de E-mail", "components.NotificationTypeSelector.mediadeclinedDescription": "Envia notificações quando solicitações de mídia são recusadas.", "components.NotificationTypeSelector.mediadeclined": "Solicitação Recusada", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON>", "components.RequestModal.requestedited": "Solicitação de <strong>{title}</strong> alterada com sucesso!", "components.RequestModal.requestcancelled": "Solicitação de <strong>{title}</strong> foi cancelada.", "components.RequestModal.errorediting": "Algo deu errado ao modificar a solicitação.", "components.RequestModal.autoapproval": "Aprovação Automática", "components.RequestModal.AdvancedRequester.rootfolder": "Pasta Raiz", "components.RequestModal.AdvancedRequester.qualityprofile": "Perfil de Qualidade", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (Padrão)", "components.RequestModal.AdvancedRequester.animenote": "* Esta série é um anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "Avançado", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestBlock.rootfolder": "Pasta Raiz", "components.RequestBlock.requestoverrides": "Mudanças na solicitação", "components.RequestBlock.profilechanged": "Perfil de Qualidade", "i18n.edit": "<PERSON><PERSON>", "i18n.experimental": "Experimental", "components.RequestModal.requesterror": "Algo deu errado ao solicitar mídia.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Não conseguimos correlacionar sua solicitação automaticamente. Por favor selecione a correspondência correta na lista abaixo.", "components.Login.signin": "Entrar", "components.UserList.userssaved": "Permissões de usuário salvas com sucesso!", "components.UserList.bulkedit": "Edição Em Massa", "components.Settings.toastPlexRefreshSuccess": "Lista de servidores do Plex obtida com sucesso!", "components.Settings.toastPlexRefreshFailure": "Falha ao obter a lista de servidores do Plex.", "components.Settings.toastPlexRefresh": "Obtendo lista de servidores do Plex…", "components.Settings.toastPlexConnectingSuccess": "Conexão com Plex estabelecida com sucesso!", "components.Settings.toastPlexConnectingFailure": "Falha ao se conectar ao Plex.", "components.Settings.toastPlexConnecting": "Tentando se conectar ao Plex…", "components.Settings.settingUpPlexDescription": "Para configurar o Plex, você pode entrar com as configurações manualmente ou escolher um dos servidores disponívies obtivos de <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Clique no botão próximo à lista para obter os servidores disponíveis.", "components.Settings.serverpresetRefreshing": "Obtendo servidores…", "components.Settings.serverpresetManualMessage": "Configurar manualmente", "components.Settings.serverpresetLoad": "Clique para carregar servidores disponíveis", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverRemote": "remoto", "components.Settings.serverLocal": "local", "components.Settings.notificationAgentSettingsDescription": "Configure e habilite agentes de notificação.", "components.Login.signingin": "Autenticando…", "components.PermissionEdit.usersDescription": "Concede permissão para gerenciar usuários. Usuários com essa permissão não podem modificar usuários com acesso Administrativo, ou condecer tal permissão.", "components.PermissionEdit.users": "Gerenciar Usuários", "components.PermissionEdit.requestDescription": "Concede permissão para solicitar mídia não 4K.", "components.PermissionEdit.request4kTvDescription": "Concede permissão para solicitar séries em 4K.", "components.PermissionEdit.request4kTv": "Solicitar Séries em 4K", "components.PermissionEdit.request4kMoviesDescription": "Concede permissão para solicitar filmes em 4K.", "components.PermissionEdit.request4kMovies": "Solicitar Filmes em 4K", "components.PermissionEdit.request4kDescription": "Concede permissão para solicitar mídia em 4K.", "components.PermissionEdit.request4k": "Solicitar 4K", "components.PermissionEdit.request": "Solicitar", "components.PermissionEdit.managerequestsDescription": "Concede permissão para gerenciar solicitações de mídia. Todas solicitações feitas por um usuário com esse perfil serão automaticamente aprovadas.", "components.PermissionEdit.managerequests": "Gerenciar Solicitações", "components.PermissionEdit.autoapproveSeriesDescription": "Concede aprovação automática para solicitações de séries não 4K.", "components.PermissionEdit.autoapproveSeries": "Aprovar Séries Automaticamente", "components.PermissionEdit.autoapproveMoviesDescription": "Concede aprovação automática para solicitações de filmes não 4K.", "components.PermissionEdit.autoapproveMovies": "Aprovar Filmes Automaticamente", "components.PermissionEdit.autoapproveDescription": "Concede aprovação automática para todas solicitações de mídia não 4K.", "components.PermissionEdit.autoapprove": "<PERSON><PERSON>r <PERSON>e", "components.PermissionEdit.advancedrequestDescription": "Concede permissão para alterar solicitações avançadas de mídia.", "components.PermissionEdit.advancedrequest": "Solicitações Avançadas", "components.PermissionEdit.adminDescription": "Acesso administrativo completo. Ignora todas outras checagens de privilégios.", "components.PermissionEdit.admin": "Administrador", "components.Login.signinwithplex": "Entrar com sua conta Plex", "components.Login.signinheader": "Autentique para continuar", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Conexão com Sonarr estabelecida com sucesso!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Falha ao conectar-se ao Sonarr.", "components.Settings.SonarrModal.syncEnabled": "Habilitar Escaneamento", "components.Settings.SonarrModal.externalUrl": "URL Externa", "components.Settings.RadarrModal.syncEnabled": "Habilitar Escaneamento", "components.Settings.RadarrModal.externalUrl": "URL Externa", "components.MovieDetails.markavailable": "Marcar como Disponível", "components.MovieDetails.mark4kavailable": "Marcar como Disponível em 4K", "components.Settings.SettingsJobsCache.cacheflushed": "Cache {cachename} limpo.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Tamanho do Valor", "components.Settings.SettingsJobsCache.cachemisses": "Não Encontrado", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Total de Chaves", "components.Settings.SettingsJobsCache.cachehits": "Encontrado", "components.Settings.SettingsJobsCache.cachename": "Nome do Cache", "i18n.advanced": "Avançado", "components.Settings.SettingsJobsCache.runnow": "Executar Agora", "components.Settings.SettingsJobsCache.process": "Processo", "components.Settings.SettingsJobsCache.nextexecution": "Próxima Execução", "components.Settings.SettingsJobsCache.jobtype": "Tipo", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} iniciado(a).", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr executa tarefas regularares de manutenção de forma automática, mas elas podem também serem iniciadas manualmente abaixo. Tarefas executadas manualmente não irão afetar o agendamento da próxima execução.", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobname": "Nome da Tarefa", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} cancelado(a).", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.command": "Comand<PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr armazena temporariamente as solicitações à APIs externas para otimizar performance e evitar novas chamadas desnecessárias.", "components.Settings.SettingsAbout.preferredmethod": "Preferido", "components.UserList.users": "Usuários", "components.Setup.setup": "Configurar", "components.Search.search": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.requestas": "Solicitar Como", "components.Discover.discover": "Explorar", "components.AppDataWarning.dockerVolumeMissingDescription": "O ponto de montagem<code>{appDataPath}</code> não foi corretamente configurado. Todos dados serão perdidos quando o container parar ou reiniciar.", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "A URL não pode terminar com uma barra", "components.Settings.SonarrModal.validationApplicationUrl": "Você deve prover uma URL válida", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "A URL não pode terminar com uma barra", "components.Settings.RadarrModal.validationApplicationUrl": "Você deve prover uma URL válida", "components.PermissionEdit.viewrequestsDescription": "Concede permissão para visualizar solicitações de mídia feita por outros usuários.", "components.PermissionEdit.viewrequests": "Visualizar Solicita<PERSON>es", "components.UserList.validationEmail": "Você deve prover um e-mail válido", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "URL Base não deve terminar com uma barra", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "URL Base deve ter iniciar com uma barra", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL Base não deve terminar com uma barra", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL Base deve iniciar com uma barra", "components.Settings.Notifications.validationEmail": "Você deve prover um e-mail válido", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Você deve prover uma URL válida", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Você deve prover uma URL válida", "components.TvDetails.nextAirDate": "Próxima Transmissão", "components.ResetPassword.email": "Endereço de E-mail", "components.ResetPassword.resetpassword": "Alterar sua <PERSON>", "components.ResetPassword.validationpasswordrequired": "Você deve prover uma senha", "components.ResetPassword.validationpasswordminchars": "Senha muito curta. Ela deve ter minimo 8 caracteres", "components.ResetPassword.validationpasswordmatch": "As senhas devem coincidir", "components.ResetPassword.validationemailrequired": "Você deve prover um e-mail válido", "components.ResetPassword.requestresetlinksuccessmessage": "Um link para alteração de senha será enviado ao endereço de e-mail informado caso o mesmo esteja associado a um usuário válido.", "components.ResetPassword.resetpasswordsuccessmessage": "Senha redefinida com sucesso!", "components.ResetPassword.password": "<PERSON><PERSON>", "components.ResetPassword.gobacklogin": "Voltar para Página de Autenticação", "components.ResetPassword.emailresetlink": "Envie um Link de Recuperação", "components.ResetPassword.confirmpassword": "Confirm<PERSON> a <PERSON>", "components.Login.forgotpassword": "Esqueceu a Senha?", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Você deve selecionar um perfil de idioma", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Teste conexão para carregar perfis de idioma", "components.Settings.SonarrModal.selectLanguageProfile": "Selecione um perfil de Idioma", "components.Settings.SonarrModal.loadinglanguageprofiles": "Carregando perfis de Idioma…", "components.Settings.SonarrModal.languageprofile": "Perfil de Idioma", "components.Settings.SonarrModal.animelanguageprofile": "Perfil de Idioma de Animes", "components.RequestModal.AdvancedRequester.languageprofile": "Perfil de Idioma", "components.Settings.Notifications.sendSilentlyTip": "Envia notificações sem som", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON>", "components.UserList.sortRequests": "Número de Solicitações", "components.UserList.sortDisplayName": "Nome de Exibição", "components.UserList.sortCreated": "Data de Criação", "components.PermissionEdit.autoapprove4k": "Aprovar 4K Automaticamente", "components.PermissionEdit.autoapprove4kSeriesDescription": "Concede aprovação automática para solicitações de séries em 4K.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Concede aprovação automática para solicitações de filmes em 4K.", "components.PermissionEdit.autoapprove4kSeries": "Aprovar Automaticamente Séries em 4K", "components.PermissionEdit.autoapprove4kMovies": "Aprovar Automaticamente Filmes em 4K", "components.PermissionEdit.autoapprove4kDescription": "Concede aprovação automática para todas solicitações de mídia em 4K.", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Você deve prover um token de acesso", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Configurações de notificação via Pushbullet salvas com sucesso!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Falha ao salvar configurações de notificação via Pushover.", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Você deve prover uma nova senha", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Você deve prover sua senha atual", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON><PERSON><PERSON> deve confirmar a nova senha", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Usuário Local", "components.UserProfile.recentrequests": "Solicitações Recentes", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Senha salva com sucesso!", "components.UserProfile.UserSettings.menuChangePass": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.password": "<PERSON><PERSON>", "components.UserProfile.UserSettings.menuPermissions": "Permissões", "components.UserProfile.UserSettings.UserPermissions.permissions": "Permissões", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nova Senha", "components.UserProfile.UserSettings.menuNotifications": "Notificações", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Nome de Exibição", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID do Usuário", "components.UserProfile.ProfileHeader.settings": "Editar <PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Confirm<PERSON> a <PERSON>", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Permissões salvas com sucesso!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Configurações salvas com sucesso!", "components.UserProfile.UserSettings.menuGeneralSettings": "G<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Configurações Gerais", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Configurações de Notificação", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "As senhas devem coincidir", "components.UserList.userfail": "Algo deu errado ao salvar permissões de usuário.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Algo deu errado ao salvar configurações.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Algo deu errado ao salvar configurações.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "<PERSON>go deu errado ao salvar senha.", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "A senha é muito curta; Ela deve ter no mínimo 8 caractéres", "components.UserList.edituser": "Editar Permissões de Usuário", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON>", "components.Layout.UserDropdown.settings": "Configurações", "components.Layout.UserDropdown.myprofile": "Perfil", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Você deve prover um ID válido de usuário", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "O <FindDiscordIdLink>número de identificação</FindDiscordIdLink> correspondente ao seu usuário", "components.CollectionDetails.requestcollection4k": "Solicitar Coleção em 4K", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Região de Exploração", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Idioma de Exploração", "components.Settings.webhook": "Webhook", "components.Settings.email": "E-mail", "components.RegionSelector.regionDefault": "Todas Regiões", "components.Discover.upcomingtv": "Séries em Breve", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtra conteúdo por disponibilidade na região", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtra conteúdo pela língua original", "components.RegionSelector.regionServerDefault": "Padrão ({region})", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Você não tem permissão para modificar a senha desse usuário.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Privilégio", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Don<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrador", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Tipo de Conta", "components.UserList.owner": "Don<PERSON>", "components.UserList.accounttype": "Tipo", "components.Settings.SettingsJobsCache.download-sync": "Sincronizar Downloads", "components.Settings.SettingsJobsCache.download-sync-reset": "Limpar Sincronização de Download", "i18n.loading": "Carregando…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Você deve prover um ID válido de chat", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Inicie uma conversa</TelegramBotLink>, adicione <GetIdBotLink>@get_id_bot</GetIdBotLink>, e envie o comando <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID de Chat", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Envia notificações sem som", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON>", "components.TvDetails.seasons": "{seasonCount, plural, one {# Temporada} other {# Temporadas}}", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.botUsername": "Usuário do Bot", "components.Discover.DiscoverTvGenre.genreSeries": "Séries de {genre}", "components.Discover.DiscoverNetwork.networkSeries": "Séries por {network}", "components.Discover.DiscoverStudio.studioMovies": "Filmes por {studio}", "components.Discover.DiscoverMovieGenre.genreMovies": "Filmes de {genre}", "components.Settings.Notifications.validationUrl": "Você deve prover uma URL válida", "components.Settings.Notifications.botAvatarUrl": "URL de Avatar do Bot", "components.RequestList.RequestItem.requested": "Solicitado", "components.RequestList.RequestItem.modifieduserdate": "{date} por {user}", "components.RequestList.RequestItem.modified": "Modificada", "components.Discover.StudioSlider.studios": "Estúdios", "components.Discover.NetworkSlider.networks": "Emissora", "components.Discover.DiscoverTvLanguage.languageSeries": "Séries em {language}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filmes em {language}", "components.Settings.scanning": "Sincronizando…", "components.Settings.scan": "Sincronizar Bibliotecas", "components.Settings.SettingsJobsCache.sonarr-scan": "Escaneamento do Sonarr", "components.Settings.SettingsJobsCache.radarr-scan": "Escanemento do Radarr", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Recentemente Adicionado ao Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Escaneamento de Todas Bibliotecas do Plex", "components.UserProfile.UserSettings.unauthorizedDescription": "Você não tem permissão para modificar as configurações desse usuários.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Você não pode modificar suas próprias permissões.", "components.UserProfile.ProfileHeader.userid": "ID de Usuário: {userid}", "components.UserProfile.ProfileHeader.joindate": "<PERSON><PERSON><PERSON> em {joindate}", "components.Settings.menuUsers": "Usuários", "components.Settings.SettingsUsers.userSettingsDescription": "Define configurações globais e padrões de usuário.", "components.Settings.SettingsUsers.userSettings": "Configurações de Usuário", "components.Settings.SettingsUsers.toastSettingsSuccess": "Configurações do usuário salvas com sucesso!", "components.Settings.SettingsUsers.toastSettingsFailure": "Algo deu errado ao salvar configurações.", "components.Settings.SettingsUsers.localLogin": "Habilitar Autenticação Local", "components.Settings.SettingsUsers.defaultPermissions": "Permissões <PERSON>", "components.Settings.Notifications.pgpPrivateKeyTip": "<PERSON><PERSON><PERSON> mensagens encriptadas de e-mail usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Chave PGP privada", "components.Settings.Notifications.pgpPasswordTip": "<PERSON><PERSON><PERSON> mensagens encriptadas de e-mail usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Senha PGP", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Envia notificações quando usuários solicitarem mídias que são aprovadas automaticamente.", "components.NotificationTypeSelector.mediaAutoApproved": "Solicitação Aprovada Automaticamente", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutos", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.TvDetails.episodeRuntime": "Duração do Episódio", "components.Discover.TvGenreSlider.tvgenres": "Gêneros de Séries", "components.Discover.TvGenreList.seriesgenres": "Gêneros de Séries", "components.Discover.MovieGenreSlider.moviegenres": "Gêneros de Filmes", "components.Discover.MovieGenreList.moviegenres": "Gêneros de Filmes", "components.RequestModal.alreadyrequested": "<PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "components.ResetPassword.passwordreset": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.logDetails": "Detalhes do Log", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Algo deu errado ao salvar sua senha. Sua senha foi digitada corretamente?", "pages.somethingwentwrong": "<PERSON>go deu errado", "pages.serviceunavailable": "Serviço Indisponível", "pages.pagenotfound": "Página Não Encontrada", "pages.internalservererror": "Erro Interno no Servidor", "i18n.usersettings": "Configurações de Usuário", "i18n.settings": "Configurações", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notificações", "components.UserProfile.UserSettings.UserGeneralSettings.general": "G<PERSON>", "components.Settings.services": "Serviços", "components.Settings.plex": "Plex", "components.Settings.notifications": "Notificações", "components.Settings.enablessl": "Usar SSL", "components.Settings.SettingsUsers.users": "Usuários", "components.Settings.SettingsLogs.time": "Data e Hora", "components.Settings.SettingsLogs.showall": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.resumeLogs": "Resumir", "components.Settings.SettingsLogs.pauseLogs": "Pausar", "components.Settings.SettingsLogs.message": "Mensagem", "components.Settings.SettingsLogs.logsDescription": "Você pode ver esses logs diretamente via <code>stdout</code>, ou em <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Logs", "components.Settings.SettingsLogs.level": "Severidade", "components.Settings.SettingsLogs.label": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Informacional", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterDebug": "Debug", "components.Settings.SettingsLogs.extraData": "Informações Adicionais", "components.Settings.SettingsLogs.copyToClipboard": "Copiar", "components.Settings.SettingsLogs.copiedLogMessage": "Mensagem copiada para área de transferência.", "components.Settings.SettingsJobsCache.jobsandcache": "Tarefas & Cache", "components.Settings.SettingsAbout.about": "Sobre", "components.UserList.nouserstoimport": "Nenhum novo usuário Plex para importar.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.alsoknownas": "Tamb<PERSON><PERSON>(a) Como: {names}", "i18n.delimitedlist": "{a}, {b}", "i18n.view": "<PERSON><PERSON><PERSON>", "i18n.tvshow": "Série", "i18n.testing": "<PERSON><PERSON><PERSON>…", "i18n.test": "<PERSON>ar", "i18n.status": "Estado", "i18n.showingresults": "Exibindo de <strong>{from}</strong> até <strong>{to}</strong> de <strong>{total}</strong> resultado(s)", "i18n.saving": "<PERSON><PERSON><PERSON>", "i18n.save": "<PERSON><PERSON>", "i18n.resultsperpage": "Exibir {pageSize} resultados por página", "i18n.requesting": "Solicitando…", "i18n.request4k": "Solicitar em 4K", "i18n.previous": "Anterior", "i18n.notrequested": "Não <PERSON>(a)", "i18n.noresults": "Sem resultados.", "i18n.next": "Próxima", "i18n.movie": "Filme", "i18n.canceling": "Cancelando…", "i18n.back": "Voltar", "i18n.areyousure": "Você tem certeza?", "i18n.all": "<PERSON><PERSON>", "components.UserProfile.requestsperdays": "{limit} restante(s)", "components.UserProfile.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.totalrequests": "Total de Solicitações", "components.UserProfile.seriesrequest": "Solicitações de Séries", "components.UserProfile.pastdays": "{type} (últimos {days} dias)", "components.UserProfile.movierequests": "Solicitações de Filmes", "components.UserProfile.limit": "{remaining} de {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limite de Solicitações de Séries", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limite de Solicitações de Filmes", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Sobrepor Limite Global", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Limite Global de Solicitações de Séries", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Limite Global de Solicitações de Filmes", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {temporada} other {temporadas}}", "components.RequestModal.QuotaDisplay.season": "temporada", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Esse usuário precisa ter ao menos <strong>{seasons}</strong> {seasons, plural, one {solicitação restante de série} other {solicitações restantes de séries}} para poder solicitar essa série.", "components.RequestModal.QuotaDisplay.requiredquota": "Você precisa ter ao menos <strong>{seasons}</strong> {seasons, plural, one {solicitação restante de série} other {solicitações restantes de séries}} para poder solicitar essa série.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} other {<strong>#</strong>}} {remaining, plural, one {solicitação de {type} restante} other {solicitações de {type}s restantes}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Você pode ver um resumo dos limites de solicitação desse usuário em seu <ProfileLink>perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Você pode ver um resumo dos seus limites de solicitação em seu <ProfileLink>perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Não há solicitações de temporada suficientes restantes", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {filme} other {filmes}}", "components.RequestModal.QuotaDisplay.movie": "filme", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON>sse usuário pode solicitar <strong>{limit}</strong> {type} a cada <strong>{days}</strong> dias.", "components.RequestModal.QuotaDisplay.allowedRequests": "Você pode solicitar <strong>{limit}</strong> {type} a cada <strong>{days}</strong> dias.", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON>(as)", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.LanguageSelector.originalLanguageDefault": "Todos Idiomas", "components.LanguageSelector.languageServerDefault": "Padrão ({language})", "components.RequestModal.AdvancedRequester.notagoptions": "Nenhuma Tag.", "components.Settings.SonarrModal.testFirstTags": "Teste a conexão para carregar as tags", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.SonarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON> as tags", "components.Settings.SonarrModal.notagoptions": "<PERSON>enhuma tag.", "components.Settings.SonarrModal.loadingTags": "Carregando tags…", "components.Settings.SonarrModal.edit4ksonarr": "Editar Servidor <PERSON>arr 4K", "components.Settings.SonarrModal.default4kserver": "Servidor 4K Padrão", "components.Settings.SonarrModal.create4ksonarr": "Adicionar Novo Servidor Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Tags Para Animes", "components.Settings.RadarrModal.testFirstTags": "Teste a conexão para carregar as tags", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.RadarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON> as tags", "components.Settings.RadarrModal.notagoptions": "<PERSON>enhuma tag.", "components.Settings.RadarrModal.loadingTags": "Carregando tags…", "components.Settings.RadarrModal.edit4kradarr": "Editar Servidor Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Servidor 4K Padrão", "components.Settings.RadarrModal.create4kradarr": "Adicionar Novo Servidor Radarr 4K", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.AdvancedRequester.selecttags": "<PERSON><PERSON><PERSON><PERSON> as tags", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Esse usuário ainda não possui uma senha definida. Defina uma senha abaixo para habilitar autenticação local usando seu endereço de e-mail.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Esse usuário ainda não possui uma senha definida. Defina uma senha abaixo para habilitar autenticação como \"usuário local.\"", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Você deve prover uma chave pública PGP válida", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Criptografa mensagens de e-mail usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.serviceSettingsDescription": "Configure seu(s) servidor(es) {serverType} abaixo. Você pode se conectar à múltiplos servidores {serverType}, mas apenas dois podem ser marcados como padrão (um não 4K e outro 4K). Administradores podem sobrescrever o servidor usado antes de aprovar as novas solicitações.", "components.Settings.noDefaultServer": "Ao menos um servidor {serverType} deve ser marcado como padrão para que as solicitações de {mediaType} sejam processadas.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Configurações de notificação via Telegram salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Falha ao salvar configurações de notificação via Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Chave Pública PGP", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Configurações de notificação via e-mail salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Falha ao salvar configurações de notificação via e-mail.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Configurações de notificação via Discord salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Falha ao salvar configurações de notificação via Discord.", "components.Settings.noDefaultNon4kServer": "Se você tem apenas um servidor {serverType} para conteúdo 4K e não 4K (ou se você baixa apenas conteúdo 4K), seu servidor {serverType} <strong>NÃO</strong> deve ser designado como um servidor 4K.", "components.Settings.mediaTypeSeries": "série", "components.Settings.mediaTypeMovie": "filme", "components.Settings.SettingsAbout.uptodate": "Atualizado", "components.Settings.SettingsAbout.outofdate": "Desatualizado", "components.Settings.Notifications.validationPgpPrivateKey": "Você deve prover uma chave PGP privada válida", "components.Settings.Notifications.validationPgpPassword": "Você deve prover uma senha <PERSON>", "components.Settings.Notifications.botUsernameTip": "Permitir que usuários iniciem uma conversa com o seu bot e configure suas próprias notificações", "components.RequestModal.pendingapproval": "Sua solicitação está aguardando aprovação.", "components.RequestList.RequestItem.mediaerror": "{mediaType} Não Encontrado", "components.RequestList.RequestItem.deleterequest": "Apagar <PERSON>", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.mediaerror": "{mediaType} Não Encontrado", "components.RequestCard.deleterequest": "Apagar <PERSON>", "components.NotificationTypeSelector.notificationTypes": "Tipos de Notificação", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>.", "components.Layout.VersionStatus.outofdate": "Desatualizado", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {versão} other {versões}} atrasado(a)", "components.UserList.autogeneratepasswordTip": "Envia para o usuário uma senha gerada automaticamente", "i18n.retrying": "Tentando <PERSON>…", "components.UserList.usercreatedfailedexisting": "O e-mail informado já está em uso por outro usuário.", "components.Settings.serverSecure": "segura", "components.RequestModal.edit": "Editar <PERSON>", "components.RequestList.RequestItem.editrequest": "Editar <PERSON>", "components.Settings.SonarrModal.enableSearch": "Habilitar Busca Automática", "components.Settings.RadarrModal.enableSearch": "Habilitar Busca Automática", "components.PermissionEdit.requestMovies": "Solicitar Filmes", "components.DownloadBlock.estimatedtime": "Est<PERSON><PERSON>va {time}", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Configurações de notificação via web push salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Padrão ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Idioma da Interface", "components.Settings.webpush": "Web Push", "components.Settings.is4k": "4K", "components.Settings.SettingsUsers.newPlexLoginTip": "Permite que novos usuários do Plex entrem sem terem que ser importados", "components.Settings.SettingsUsers.newPlexLogin": "Habilitar Novo Método de Início de Sessão do Plex", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON>r um <DiscordWebhookLink>webhook de integração</DiscordWebhookLink> no seu servidor", "components.Settings.Notifications.encryptionTip": "Na maioria do casos TLS Implícito usa a porta 465 e STARTTLS usa a porta 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Sempre usar STARTTLS", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Usar TLS Implícito", "components.Settings.Notifications.encryptionDefault": "Usar STARTTLS se disponível", "components.Settings.Notifications.encryption": "Método de Encriptação", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Criar um bot</CreateBotLink> para usar com Je<PERSON>seerr", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Configurações de notificação via web push salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Falha ao salvar configurações de notificação via web push.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Falha ao salvar configurações de notificação via web push.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Para receber notificações via web push, o Jellyseerr deve ser acessível via HTTPS.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Criar integração para um <WebhookLink>webhook de entrada</WebhookLink>", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Seu <UsersGroupsLink>identificador de usuário ou grupo</UsersGroupsLink> contendo 30 caractéres", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registre uma aplicação</ApplicationRegistrationLink> para usar com Jellyseerr", "components.Settings.Notifications.toastTelegramTestSuccess": "Notificação de teste via Telegram enviada!", "components.Settings.Notifications.toastEmailTestSuccess": "Notificação de teste via e-mail enviada!", "components.Settings.Notifications.toastDiscordTestSuccess": "Notificação de teste via Discord enviada!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Notificação de teste via webhook enviada!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Notificação de teste via web push enviada!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Notificação de teste via Slack enviada!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Notificação de teste via Pushover enviada!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Notificação de teste via Pushbullet enviada!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Falha ao enviar notificação de teste via web push.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Falha ao enviar notificação de teste via webhook.", "components.Settings.Notifications.toastEmailTestFailed": "Falha ao enviar notificação de teste via e-mail.", "components.Settings.Notifications.toastTelegramTestSending": "Enviando notificação de teste via Telegram…", "components.Settings.Notifications.toastEmailTestSending": "Enviando notificação de teste via e-mail…", "components.Settings.Notifications.toastDiscordTestSending": "Enviando notificação de teste via Discord…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Enviando notificação de teste via webhook…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Enviando notificação de teste via web push…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Enviando notificação de teste via Slack…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Enviando notificação de teste via Pushover…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Enviando notificação de teste via Pushbullet…", "components.Settings.Notifications.toastTelegramTestFailed": "Falha ao enviar notificação de teste via Telegram.", "components.Settings.Notifications.toastDiscordTestFailed": "Falha ao enviar notificação de teste via Discord.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Falha ao enviar notificação de teste via Slack.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Falha ao enviar notificação de teste via Pushover.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Falha ao enviar notificação de teste via Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Criar um token à partir de sua <PushbulletSettingsLink>Configuração de Conta</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Sua <LunaSeaLink>URL de webhook</LunaSeaLink> para notificação baseada em usuário ou dispositivo", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Você deve prover uma URL válida", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Notificação de teste via LunaSea enviada!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Enviando notificação de teste via LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Falha ao enviar notificação de teste via LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Configurações de notificação via LunaSea salvas com sucesso!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Falha ao salvar configurações de notificação via LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Necessário apenas quando não estiver usando o perfil <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nome de Perfil", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Habilitar Agente", "components.PermissionEdit.requestTvDescription": "Concede permissão para solicitar séries em não 4K.", "components.PermissionEdit.requestTv": "Solicitar Séries", "components.PermissionEdit.requestMoviesDescription": "Concede permissão para solicitar filmes em não 4K.", "components.Settings.Notifications.chatIdTip": "Inicie um chat com seu bot, adicione <GetIdBotLink>@get_id_bot</GetIdBotLink> e execute o comando <code>/my_id</code>", "components.UserList.localLoginDisabled": "A opção <strong>Habilitar Autenticação Local</strong> está atualmente desabilitada.", "components.Settings.webAppUrlTip": "Você tem a opção de direcionar os usuários ao aplicativo web ao invés da versão \"hospedada\" em seu servidor", "components.Settings.webAppUrl": "URL do <WebAppLink>Aplicativo Web</WebAppLink>", "components.Settings.noDefault4kServer": "Um servidor {serverType} 4K deve ser marcado como padrão para que usuários possam solicitar {mediaType} em 4K.", "components.RequestList.RequestItem.requesteddate": "Solicitado", "components.RequestCard.failedretry": "Algo deu errado ao retentar fazer a solicitação.", "components.Settings.SettingsUsers.localLoginTip": "Permitir que usuários se autentiquem usando seus endereços e-mails e senhas ao invés de Plex OAuth", "components.Settings.SettingsUsers.defaultPermissionsTip": "Permissões iniciais atribuídas à novos usuários", "components.Settings.Notifications.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{temporadas} a cada {quotaDays} {dias}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {temporada} other {temporadas}}", "components.QuotaSelector.movies": "{count, plural, one {file} other {filmes}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{filmes} a cada {quotaDays} {dias}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {dia} other {dias}}", "components.NotificationTypeSelector.usermediarequestedDescription": "Seja notificado quando outros usuários solicitarem novas mídias que requerem aprovação.", "components.NotificationTypeSelector.usermediafailedDescription": "Seja notificado quando solicitações de mídia falharem ao serem adicionadas ao Radarr ou Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Seja notificado quando suas solicitações de mídia forem rejeitadas.", "components.NotificationTypeSelector.usermediaavailableDescription": "Seja notificado quando suas solicitações de mídia se tornarem disponíveis.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Seja notificado quando suas solicitações de mídia são aprovadas.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Seja notificado quando outros usuários solicitarem novas mídias que são automaticamente aprovadas.", "components.Settings.SettingsAbout.betawarning": "Essa é uma versão BETA. Algumas funcionalidades podem ser instáveis ou não funcionarem. Por favor reporte qualquer problema no GitHub!", "components.Layout.LanguagePicker.displaylanguage": "Idioma da Interface", "components.MovieDetails.showmore": "<PERSON><PERSON>", "components.MovieDetails.showless": "<PERSON><PERSON>", "components.TvDetails.streamingproviders": "Em Exibição na", "components.MovieDetails.streamingproviders": "Em Exibição na", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Tarefa modificada com sucesso!", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nova Frequência", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "A cada {jobScheduleHours, plural, one {hora} other {{jobScheduleHours} horas}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "A cada {jobScheduleMinutes, plural, one {minuto} other {{jobScheduleMinutes} minutos}}", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "Você tem certeza que deseja apagar este comentário?", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON>", "components.IssueDetails.IssueComment.postedby": "<PERSON><PERSON> {relativeTime} por {username}", "components.IssueDetails.IssueComment.postedbyedited": "<PERSON><PERSON> {relativeTime} por {username} (Edited)", "components.IssueDetails.IssueComment.validationComment": "Você deve escrever uma mensagem", "components.IssueDetails.closeissueandcomment": "<PERSON><PERSON><PERSON> com Comentário", "components.IssueDetails.comments": "Comentários", "components.IssueDetails.episode": "Episódio {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problema", "components.IssueDetails.issuetype": "Tipo", "components.IssueDetails.IssueDescription.deleteissue": "Apa<PERSON> Problema", "components.IssueDetails.IssueDescription.description": "Descrição", "components.IssueDetails.deleteissueconfirm": "Você tem certeza que deseja apagar este problema?", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON> come<PERSON>.", "components.IssueDetails.allepisodes": "Todos Episódios", "components.IssueDetails.IssueDescription.edit": "Alterar <PERSON>ri<PERSON>", "components.IssueDetails.allseasons": "<PERSON><PERSON>", "components.IssueDetails.leavecomment": "Comentar", "components.IssueDetails.closeissue": "Encerrar Problema", "components.IssueDetails.deleteissue": "Apa<PERSON> Problema", "components.IssueDetails.lastupdated": "Última Atualização", "components.IssueDetails.openedby": "#{issueId} aberto {relativeTime} por {username}", "components.IssueDetails.openinarr": "Abrir no {arr}", "components.IssueDetails.reopenissue": "Re-abrir <PERSON>a", "components.IssueDetails.problemepisode": "Episódio Afetado", "components.IssueDetails.problemseason": "Temporada Afetada", "components.IssueDetails.reopenissueandcomment": "Re-abrir com Comentário", "components.IssueDetails.toasteditdescriptionfailed": "Algo deu errado ao editar a descrição do problema.", "components.IssueDetails.toastissuedeleted": "Problema apagado com sucesso!", "components.IssueDetails.openin4karr": "Abrir em {arr} 4K", "components.IssueDetails.playonplex": "Assistir no Plex", "components.IssueDetails.season": "Temporada {seasonNumber}", "components.IssueDetails.toasteditdescriptionsuccess": "Descrição do problema alterada com sucesso!", "components.IssueDetails.play4konplex": "Assistir em 4K no Plex", "components.IssueDetails.toastissuedeletefailed": "Algo deu errado ao apagar problema.", "components.IssueDetails.toaststatusupdated": "Estado do problema atualizado com sucesso!", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Espisódio} other {Episódios}}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.providedetail": "Por favor, explique em detalhes o problema que você encontrou.", "components.IssueList.IssueItem.issuestatus": "Estado", "components.IssueList.IssueItem.issuetype": "Tipo", "components.IssueList.IssueItem.opened": "Abe<PERSON>o", "components.IssueList.IssueItem.openeduserdate": "{date} por {user}", "components.IssueList.IssueItem.unknownissuetype": "Desconhecido", "components.IssueDetails.toaststatusupdatefailed": "Algo deu errado ao atualizar o estado do problema.", "components.IssueDetails.unknownissuetype": "Desconhecido", "components.IssueList.issues": "Problemas", "components.IssueModal.CreateIssueModal.problemepisode": "Episódio Afetado", "components.IssueModal.CreateIssueModal.problemseason": "Temporada Afetada", "components.IssueList.IssueItem.problemepisode": "Episódio Afetado", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.IssueList.IssueItem.viewissue": "Ver Problema", "components.IssueList.showallissues": "<PERSON><PERSON><PERSON>", "components.IssueList.sortAdded": "<PERSON><PERSON>", "components.IssueList.sortModified": "Última Modificação", "components.IssueModal.CreateIssueModal.allepisodes": "Todos Episódios", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "Episódio {episodeNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Enviar Problema", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Relato de problema em <strong>{title}</strong> enviado com sucesso!", "components.IssueModal.issueVideo": "Vídeo", "components.Layout.Sidebar.issues": "Problemas", "components.ManageSlideOver.downloadstatus": "Downloads", "components.IssueModal.CreateIssueModal.season": "Temporada {seasonNumber}", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Algo deu errado ao enviar problema.", "components.IssueModal.CreateIssueModal.toastviewissue": "Ver Problema", "components.IssueModal.CreateIssueModal.reportissue": "Reportar um Problema", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Você deve prover uma descrição", "components.IssueModal.CreateIssueModal.whatswrong": "O quê há de errado?", "components.IssueModal.issueAudio": "<PERSON><PERSON><PERSON>", "components.IssueModal.issueOther": "Outros", "components.IssueModal.issueSubtitles": "<PERSON>a", "components.ManageSlideOver.manageModalClearMedia": "Lim<PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Isso irá remover em definitivo todos dados desse(a) {mediaType}, incluindo quaisquer solicitações para esse item. Se este item existir in sua biblioteca do {mediaServerName}, os dados de mídia serão recriados na próxima sincronia.", "components.ManageSlideOver.manageModalIssues": "Problemas <PERSON>", "components.ManageSlideOver.manageModalNoRequests": "Nenhuma solicitação.", "components.ManageSlideOver.manageModalRequests": "Solicitações", "components.ManageSlideOver.manageModalTitle": "Gerenciar {mediaType}", "components.ManageSlideOver.mark4kavailable": "Marcar como Disponível em 4K", "components.ManageSlideOver.markavailable": "Marcar como Disponível", "components.ManageSlideOver.movie": "filme", "components.ManageSlideOver.openarr4k": "Abrir no {arr} 4K", "components.ManageSlideOver.tvshow": "série", "components.PermissionEdit.createissuesDescription": "Concede permissão para reportar problemas com mídias.", "components.NotificationTypeSelector.issuecomment": "Comentário no Problema", "components.NotificationTypeSelector.issuecommentDescription": "Enviar notificações quando problemas receberem novos comentários.", "components.NotificationTypeSelector.issuereopened": "Problema Re-aberto", "components.NotificationTypeSelector.issuereopenedDescription": "Enviar notificações quando problemas são re-abertos.", "components.NotificationTypeSelector.issueresolvedDescription": "Enviar notificações quando problemas são resolvidos.", "components.NotificationTypeSelector.userissuereopenedDescription": "Receber notificação quando problemas que você reportou forem re-abertos.", "components.NotificationTypeSelector.userissueresolvedDescription": "Receber notificação quando problemas que você reportou forem resolvidos.", "components.NotificationTypeSelector.userissuecreatedDescription": "Receber notificação quando outros usuários reportarem problemas.", "components.PermissionEdit.manageissues": "Gerenciar Problemas", "components.PermissionEdit.viewissues": "Ver Problemas", "components.PermissionEdit.viewissuesDescription": "Concede permissão para ver problemas em mídias reportados por outros problemas.", "components.RequestModal.requestmovies4k": "Solicitar {count} {count, plural, one {<PERSON>e} other {Filmes}} em 4K", "components.RequestModal.selectmovies": "Selecionar Filme(s)", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registre uma aplicação</ApplicationRegistrationLink> para uso com {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Chave do Usuário ou Grupo", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Seu <UsersGroupsLink>identificador de usuário ou grupo</UsersGroupsLink> contendo 30 caractéres", "components.NotificationTypeSelector.issueresolved": "Problema Resolvido", "components.ManageSlideOver.openarr": "Abrir no {arr}", "components.NotificationTypeSelector.adminissuecommentDescription": "Receber notificação quando outros usuários comentarem nos problemas.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Receber notificação quando problemas são resolvidos por outros usuários.", "components.NotificationTypeSelector.issuecreatedDescription": "Enviar notificações quando problemas são reportados.", "components.NotificationTypeSelector.userissuecommentDescription": "Receber notificação quando problemas reportados por você receberem novos comentários.", "components.MovieDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {Países}} de Produção", "components.PermissionEdit.manageissuesDescription": "Concede permissão para gerenciar problemas com mídia.", "components.RequestModal.requestmovies": "Solicitar {count} {count, plural, one {<PERSON>e} other {Filmes}}", "components.RequestModal.requestseasons4k": "Solicitar {seasonCount} {seasonCount, plural, one {Temporada} other {Temporadas}} em 4K", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON>", "components.Settings.SettingsAbout.runningDevelop": "Você está usando a versão <code>develop</code> do Jellyseerr que é recomendada apenas para àqueles contribuindo com o desenvolvimento ou ajudando no teste de novas funcionalidades.", "components.TvDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {Países}} de Produção", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Falha ao salvar configurações de notificação via Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Criar um token à partir de sua <PushbulletSettingsLink>Configuração de Conta</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Configurações de notificação via Pushbullet salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Falha ao salvar configurações de notificação via Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Você deve prover um token de acesso", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Configurações de notificação via Pushover salvas com sucesso!", "components.NotificationTypeSelector.adminissuereopenedDescription": "Receber notificação quando problemas forem re-abertos por outros usuários.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Algo deu errado ao salvar tarefa.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Você deve prover uma chave válida de acesso", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Você deve prover uma chave válida de usúario ou grupo", "components.PermissionEdit.createissues": "Reportar Problemas", "components.NotificationTypeSelector.issuecreated": "Problema Reportado", "i18n.open": "Abe<PERSON>o", "i18n.resolved": "Resolvido", "components.IssueDetails.commentplaceholder": "Adicionar um comentário…", "components.RequestModal.requestApproved": "Solicitação de <strong>{title}</strong> aprovada!", "components.RequestModal.approve": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.inCinemas": "Nos Cinemas", "components.Settings.RadarrModal.released": "Lançado", "components.Settings.Notifications.enableMentions": "Habilitar Menções", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Falha ao salvar configurações de notificação via Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Falha ao enviar notificação de teste via Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Enviando notificação de teste via Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Notificação de teste via Gotify enviada!", "components.Settings.Notifications.NotificationsGotify.token": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.url": "URL do Servidor", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Você deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "A URL não deve terminar com uma barra", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Você deve prover uma URL válida", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Configurações de notificação via Gotify salvas com sucesso!", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Você deve prover um token de acesso", "components.Settings.externalUrl": "URL Externa", "components.ManageSlideOver.manageModalAdvanced": "Avançado", "components.ManageSlideOver.manageModalMedia4k": "Mídia 4K", "components.ManageSlideOver.manageModalMedia": "Mí<PERSON>", "components.ManageSlideOver.markallseasons4kavailable": "<PERSON><PERSON>das Temporadas como Disponíveis em 4K", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON> Temporadas como Disponíveis", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON> <PERSON>", "components.ManageSlideOver.pastdays": "Últimos {days, number} Dias", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {Reprodução} other {Reproduções}}", "components.Settings.tautulliApiKey": "Chave de <PERSON>", "components.Settings.tautulliSettings": "Configurações do Tautulli", "components.Settings.tautulliSettingsDescription": "Você tem a opção de configurar a integração com seu servidor <PERSON>. Overseer irá obter o histórico de reproduções de suas mídias no Plex através do Tautulli.", "components.Settings.toastTautulliSettingsFailure": "Algo deu errado ao salvar configurações do Tautulli.", "components.Settings.toastTautulliSettingsSuccess": "Configurações do Tautulli salvas com sucesso!", "components.Settings.urlBase": "URL Base", "components.Settings.validationApiKey": "Você deve prover uma chave de API válida", "components.Settings.validationUrl": "Você deve prover uma URL válida", "components.Settings.validationUrlBaseLeadingSlash": "URL Base deve iniciar com uma barra", "components.Settings.validationUrlBaseTrailingSlash": "A URL base não pode terminar com uma barra", "components.Settings.validationUrlTrailingSlash": "A URL não pode terminar com uma barra", "components.UserList.newplexsigninenabled": "A opção <strong>Habilitar Novo Método de Início de Sessão do Plex</strong> está habilitada. Usuários Plex com acesso à bibliotecas podem se autenticar sem que precisem serem importados.", "i18n.importing": "Importando…", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID do Usuário Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "O <FindDiscordIdLink>número de identificação</FindDiscordIdLink> associado à sua conta Discord", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Você deve prover um ID válido de usuário <PERSON>rd", "components.UserProfile.recentlywatched": "Assistidos Recentemente", "i18n.import": "Importar", "components.ManageSlideOver.alltime": "<PERSON><PERSON>", "components.ManageSlideOver.playedby": "Reproduzido por", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Tag do Canal", "components.Settings.SettingsAbout.appDataPath": "Configurações", "components.RequestBlock.languageprofile": "Perfil de Idioma", "components.StatusChecker.appUpdated": "{applicationTitle} Atualizado", "components.StatusChecker.restartRequiredDescription": "Por favor, reinicie o servidor para aplicar as novas configurações.", "i18n.restartRequired": "Reinicialização Necessária", "components.StatusChecker.reloadApp": "Recarregar {applicationTitle}", "components.StatusChecker.restartRequired": "Reinicialização do Servidor Necessária", "components.StatusChecker.appUpdatedDescription": "Por favor, clique no botão abaixo para recarregar a aplicação.", "components.Settings.deleteServer": "Remover Servidor {serverType}", "components.MovieDetails.digitalrelease": "Lançamento Digital", "components.MovieDetails.physicalrelease": "Lançamento em Disco", "components.MovieDetails.theatricalrelease": "Lançamento no Cinema", "components.PermissionEdit.viewrecent": "Ver Recentemente Adicionados", "components.PermissionEdit.viewrecentDescription": "Concede permissão para ver lista de mídias adicionadas recentemente.", "components.RequestCard.tmdbid": "ID do TMDB", "components.RequestCard.tvdbid": "ID do TheTVDB", "components.RequestList.RequestItem.tmdbid": "ID do TMDB", "components.RequestList.RequestItem.tvdbid": "ID do TheTVDB", "components.TitleCard.mediaerror": "{mediaType} Não Encontrado", "components.TitleCard.tmdbid": "ID do TMDB", "components.TitleCard.tvdbid": "ID do TheTVDB", "components.TitleCard.cleardata": "Lim<PERSON>", "components.PermissionEdit.autorequest": "Solicitar Automaticamente", "components.PermissionEdit.autorequestMovies": "Solicitar Filmes Automaticamente", "components.PermissionEdit.autorequestSeries": "Solicitar Séries Automaticamente", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Solicitar Filmes Automaticamente", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Solicitar Séries Automaticamente", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Sua Lista Para Assistir do Plex", "components.Discover.plexwatchlist": "Sua Lista Para Assistir do Plex", "components.Discover.DiscoverWatchlist.watchlist": "Lista Para Assistir do Plex", "components.AirDateBadge.airedrelative": "Exibido em {relativeTime}", "components.AirDateBadge.airsrelative": "Exibição {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Solicitações de Filmes", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Solicitações de Séries", "components.Layout.UserDropdown.requests": "Solicitações", "components.MovieDetails.managemovie": "Gerenciar Filme", "components.NotificationTypeSelector.mediaautorequestedDescription": "Ser notificado quando novas mídias de sua Lista Para Assistir do Plex forem automaticamente solicitadas.", "components.NotificationTypeSelector.mediaautorequested": "Solicitação Enviada Automaticamente", "components.PermissionEdit.autorequestDescription": "Concede permissão para enviar automaticamente solicitações de mídias não 4K via Lista Para Assistir do Plex.", "components.PermissionEdit.autorequestMoviesDescription": "Concede permissão para enviar automaticamente solicitações de filmes não 4K via Lista Para Assistir do Plex.", "components.PermissionEdit.autorequestSeriesDescription": "Concede permissão para enviar automaticamente solicitações de séries não 4K via Lista Para Assistir do Plex.", "components.PermissionEdit.viewwatchlists": "Ver Listas Para Assistir do Plex", "components.PermissionEdit.viewwatchlistsDescription": "Conceder per<PERSON><PERSON><PERSON> para ver a Lista Para Assistir do Plex de outros usuários.", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sincronizar Lista Para Assistir do Plex", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON>", "components.Settings.advancedTooltip": "Se configurada incorretamente essa funcionalidade pode parar de funcionar", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> deve ser reiniciado para as mudanças nessa configuração terem efeito", "components.Settings.experimentalTooltip": "Habilitar essa opção pode resultar em um comportamento inesperado da aplicação", "components.TvDetails.reportissue": "Reportar um Problema", "components.MovieDetails.reportissue": "Reportar um Problema", "components.StatusBadge.managemedia": "Gerenciar {mediaType}", "components.StatusBadge.openinarr": "Abrir em {arr}", "components.TvDetails.Season.somethingwentwrong": "Algo deu errado ao obter dados da temporada.", "components.TvDetails.manageseries": "Gerenciar Série", "components.TvDetails.rtcriticsscore": "Tomatômetro do <PERSON> Tomatoes", "components.TvDetails.seasonnumber": "Temporada {seasonNumber}", "components.TvDetails.seasonstitle": "Temporadas", "components.TvDetails.rtaudiencescore": "Avaliação da Audiência no Rotten Tomatoes", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.tmdbuserscore": "Avaliação de Usuários do TMDB", "components.MovieDetails.tmdbuserscore": "Avaliação de Usuários do TMDB", "components.MovieDetails.rtaudiencescore": "Avaliação da Audiência no Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatômetro do <PERSON> Tomatoes", "components.RequestBlock.edit": "Editar <PERSON>", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON>", "components.RequestBlock.requestdate": "Data de Solicitação", "components.RequestCard.declinerequest": "Re<PERSON><PERSON>r <PERSON>", "components.RequestCard.editrequest": "Editar <PERSON>", "components.StatusBadge.playonplex": "Assitir no {mediaServerName}", "components.RequestBlock.decline": "Re<PERSON><PERSON>r <PERSON>", "components.RequestBlock.lastmodifiedby": "Última Mudança Feita Por", "components.RequestBlock.delete": "Apagar <PERSON>", "components.RequestBlock.requestedby": "Solicitado Por", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Frequência Atual", "components.Discover.networks": "Emissora", "components.Discover.studios": "Estúdios", "components.Settings.SettingsMain.apikey": "Chave de <PERSON>", "components.Settings.SettingsMain.cacheImagesTip": "Armazenar em cache imagens de origem externa (requer uma quantidade significativa de espaço em disco)", "components.Settings.SettingsMain.general": "G<PERSON>", "components.Settings.SettingsMain.generalsettings": "Configurações Gerais", "components.Settings.SettingsMain.toastSettingsFailure": "Algo de errado ao salvar configurações.", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.searchStudios": "Pesquisar estúdios…", "components.Discover.CreateSlider.slidernameplaceholder": "Nome do Carrossel", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filmes", "components.Discover.DiscoverSliderEdit.deletesuccess": "Carrossel removido com sucesso.", "components.Discover.DiscoverSliderEdit.enable": "Alternar a Visibilidade", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Sua Lista Para Assistir do Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Adicionado <PERSON>", "components.Discover.createnewslider": "<PERSON><PERSON><PERSON> <PERSON>", "components.Discover.customizediscover": "Personalizar Exploração", "components.Discover.resettodefault": "<PERSON><PERSON><PERSON>", "components.Discover.tmdbsearch": "Pesquisa no TMDB", "components.Discover.tmdbstudio": "Estúdio no TMDB", "components.Discover.tmdbtvgenre": "Gênero de Série no TMDB", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularidade Ascendente", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data de Lançamento Descendente", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.DiscoverSliderEdit.deletefail": "Falha ao remover carrossel.", "components.Discover.DiscoverSliderEdit.remove": "Remover", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Classificação TMDB Ascendente", "components.Discover.tmdbtvkeyword": "Palavra-chave de Série no TMDB", "components.Layout.Sidebar.browsemovies": "Filmes", "components.Layout.Sidebar.browsetv": "Séries", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {seasonNumber} Episódio {episodeNumber}", "components.RequestModal.requestcollection4ktitle": "Solicitação Coleção em 4K", "components.RequestModal.requestmovietitle": "Solicitar Filme", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecachecount": "Imagens Armazenadas em Cache", "components.UserProfile.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.UserProfile.plexwatchlist": "Lista Para Assistir do Plex", "components.Discover.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.RequestCard.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecachesize": "Tamanho Total do Cache", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Solicitar automaticamente filmes em sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink>", "components.Discover.CreateSlider.addfail": "Falha ao criar novo carrossel.", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON>", "components.Discover.CreateSlider.editfail": "Falha ao editar carrossel.", "components.Discover.CreateSlider.needresults": "Você precisa ter ao menos 1 resultado.", "components.Discover.CreateSlider.providetmdbgenreid": "Forneça um ID de gênero do TMDB", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data de lançamento Ascendente", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Classificação TMDB Descendente", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularidade Descendente", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Classificação TMDB Descendente", "components.Discover.CreateSlider.nooptions": "Sem resultados.", "components.Selector.nooptions": "Sem resultados.", "components.Discover.CreateSlider.providetmdbkeywordid": "Forneça um ID de palavra-chave do TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Forneça um dado para pesquisa", "components.Discover.CreateSlider.providetmdbstudio": "Forneça o ID do estúdio no TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Forneça o ID de emissora do TMDB", "components.Discover.CreateSlider.searchGenres": "<PERSON><PERSON><PERSON><PERSON>…", "components.Discover.FilterSlideover.ratingText": "Avaliações entre {minValue} e {maxValue}", "components.Discover.CreateSlider.starttyping": "Comece a digitar para pesquisar.", "components.Selector.starttyping": "Comece a digitar para pesquisar.", "components.Discover.CreateSlider.validationTitlerequired": "Você deve prover um título.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularidade Descendente", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Classificação TMDB Ascendente", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularidade Ascendente", "components.Discover.DiscoverMovies.discovermovies": "Filmes", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Selector.searchStudios": "Pesquisar estúdios…", "components.TvDetails.Season.noepisodes": "Lista de episódios indisponível.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Solicitar automaticamente séries em sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink>", "components.Discover.DiscoverTv.discovertv": "Séries", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Primeira Data de Exibição Ascendente", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Primeira Data de Exibição Descendente", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Discover.FilterSlideover.from": "De", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "Palavras-chave", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.FilterSlideover.clearfilters": "Limpar Filtros Ativos", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.firstAirDate": "Primeira Exibição", "components.Discover.CreateSlider.searchKeywords": "Pesquisar palavras-chave…", "components.Discover.FilterSlideover.originalLanguage": "Língua Original", "components.Discover.FilterSlideover.releaseDate": "Data de Lançamento", "components.Discover.FilterSlideover.runtime": "Duração", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Séries", "components.Discover.FilterSlideover.runtimeText": "Duração de {minValue}-{maxValue} minutos", "components.Discover.FilterSlideover.streamingservices": "Serviços de Streaming", "components.Discover.FilterSlideover.studio": "Estúdio", "components.Discover.FilterSlideover.tmdbuserscore": "Avaliação de Usuários do TMDB", "components.Discover.FilterSlideover.to": "<PERSON><PERSON>", "components.Discover.tmdbmoviekeyword": "Palavra-chave de Filme no TMDB", "components.Discover.tmdbnetwork": "Emissora no TMDB", "components.Discover.tvgenres": "Gêneros de Séries", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.Discover.moviegenres": "Gêneros de Filmes", "components.Discover.stopediting": "Cancelar Edição", "components.Discover.tmdbmoviegenre": "Gênero de Filmes no TMDB", "components.RequestModal.requestseriestitle": "Solicitar Série", "components.RequestModal.requestcollectiontitle": "Solicitar Coleção", "components.RequestModal.requestmovie4ktitle": "Solicitar Filme em 4K", "components.RequestModal.requestseries4ktitle": "Solicitar Série em 4K", "components.Selector.searchGenres": "Selecione os gêneros…", "components.Selector.showless": "<PERSON><PERSON>", "components.Selector.showmore": "<PERSON><PERSON>", "components.Selector.searchKeywords": "Pesquisar palavras-chave…", "components.Settings.SettingsMain.generalsettingsDescription": "Defina configurações globais e padrões para o Jellyseerr.", "components.Settings.SettingsMain.toastApiKeySuccess": "Nova chave de API gerada com sucesso!", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Limpeza de Cache de Imagens", "components.Settings.SettingsMain.applicationTitle": "Título da Aplicação", "components.Settings.SettingsMain.applicationurl": "URL da Aplicação", "components.Settings.SettingsMain.cacheImages": "Habilitar <PERSON>", "components.Settings.SettingsMain.hideAvailable": "<PERSON><PERSON>lta<PERSON>", "components.Settings.SettingsMain.locale": "Idioma da Interface", "components.Settings.SettingsMain.originallanguage": "Idioma de Exploração", "components.Settings.SettingsMain.originallanguageTip": "Filtrar conteúdo pelo idioma original", "components.Settings.SettingsMain.partialRequestsEnabled": "Per<PERSON><PERSON>ões Parciais de Séries", "components.Settings.SettingsMain.toastApiKeyFailure": "Algo deu errado ao gerar a nova chave de API.", "components.Settings.SettingsMain.toastSettingsSuccess": "Configurações salvas com sucesso!", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "A URL não deve terminar com uma barra", "components.Settings.SettingsMain.validationApplicationTitle": "Você deve prover um título para a aplicação", "components.Settings.SettingsMain.validationApplicationUrl": "Você deve prover uma URL válida", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Espisódio} other {# Episódios}}", "components.Discover.CreateSlider.editsuccess": "Deslizador editado e configurações customizadas de procura salvas.", "components.Discover.resetwarning": "Todos os deslizadores resetados para os valores padrões. <PERSON><PERSON> também deletará todos os deslizadores personalizados!", "components.RequestModal.SearchByNameModal.nomatches": "Nós fomos incapazes de encontrar um série correspondente à serie solicitada.", "components.Discover.resetsuccess": "As configurações de descoberta personalizadas foram resetadas com sucesso.", "components.Discover.CreateSlider.addsuccess": "Novo deslizador criado e configurações de procura customizada salvas.", "components.Discover.CreateSlider.validationDatarequired": "Você deve informar um valor.", "components.Discover.resetfailed": "Algo deu errado com o reset das configurações personalizadas de descoberta.", "components.Discover.updatefailed": "Algo deu errado com a mudança das configurações de descoberta personalizadas.", "components.Discover.updatesuccess": "Configurações personalizadas de descoberta atualizadas.", "components.Settings.SettingsJobsCache.imagecacheDescription": "Quando ativado nas configurações, o Overseer irá obter e guardar imagens de fontes externas pré configuradas. As imagens guardadas são salvas na sua pasta de configuração. Você pode encontrar os arquivos em <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "A cada {jobScheduleSeconds, plural, one {segundo} other {{jobScheduleSeconds} segundos}}", "components.Settings.SettingsJobsCache.availability-sync": "Sincronização de Disponibilidade de Mídia", "components.Discover.tmdbtvstreamingservices": "Serviços de Streaming de Filmes do TMDB TV", "components.Discover.tmdbmoviestreamingservices": "Serviços de Streaming de Filmes do TMDB", "components.Discover.FilterSlideover.tmdbuservotecount": "Qtd de Votos de Usuários TMDB", "components.Discover.FilterSlideover.voteCount": "Qtd the votos entre {minValue} e {maxValue}", "components.Settings.RadarrModal.tagRequestsInfo": "Adicione automaticamente uma tag extra com o ID de usuário e o nome de exibição do solicitante", "components.Layout.UserWarnings.emailRequired": "Um endereço de e-mail é necessário.", "components.Login.credentialerror": "O nome de usuário ou senha está incorreto.", "components.Login.description": "<PERSON><PERSON> que é sua primeira vez entrando em {applicationName}, você precisa adicionar um e-mail válido.", "components.Login.initialsignin": "Conectar", "components.Login.initialsigningin": "<PERSON><PERSON><PERSON><PERSON>…", "components.Login.save": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.saving": "<PERSON><PERSON><PERSON><PERSON>…", "components.Login.signinwithjellyfin": "Use sua conta de {mediaServerName}", "components.Login.title": "Adicionar E-Mail", "components.Login.username": "Nome de usuário", "components.Login.validationEmailFormat": "E-mail inválido", "components.Login.validationEmailRequired": "Você precisa providenciar um e-mail", "components.Login.validationemailformat": "E-mail válido necess<PERSON>", "components.Login.validationhostformat": "URL válido necessário", "components.Login.validationusernamerequired": "Nome de usuário necessário", "components.ManageSlideOver.removearr": "Remover de {arr}", "components.ManageSlideOver.removearr4k": "Remover de {arr} 4K", "components.MovieDetails.downloadstatus": "Status de download", "components.MovieDetails.imdbuserscore": "Avaliação de usuário no IMDB", "components.MovieDetails.openradarr": "Abrir filme no Radarr", "components.Settings.Notifications.NotificationsPushover.sound": "Som de notificação", "components.Login.emailtooltip": "Endereço não precisa ser associado à sua instância de {mediaServerName}.", "components.Layout.UserWarnings.emailInvalid": "Endereço de e-mail inválido.", "components.Layout.UserWarnings.passwordRequired": "Uma senha é necessária.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Padrão do dispositivo", "components.Login.validationhostrequired": "URL de {mediaServerName} necessário", "components.MovieDetails.openradarr4k": "Abrir filme em Radarr 4K", "components.MovieDetails.play": "Reproduzir em {mediaServerName}", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Isto irá remover este {mediaType} de {arr}, incluindo todos os arquivos.", "components.MovieDetails.play4k": "Reproduzir em 4K em {mediaServerName}", "components.Login.validationUrlBaseLeadingSlash": "A URL base deve iniciar com uma barra", "components.Settings.Notifications.userEmailRequired": "Requer o email do usuário", "components.Login.invalidurlerror": "Não foi possível conectar-se ao servidor {mediaServerName}.", "components.MovieDetails.removefromwatchlist": "Remover da Lista para Assistir", "components.Settings.jellyfinlibrariesDescription": "As bibliotecas {mediaServerName} procuram por títulos. Clique no botão abaixo se nenhuma biblioteca estiver listada.", "components.UserList.importfromJellyfinerror": "Algo deu errado durante a importação de usuários {mediaServerName}.", "components.Login.back": "Voltar", "components.Selector.ended": "Finalizada", "components.Login.enablessl": "Usar SSL", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> Removido da lista para assistir com sucesso!", "components.Settings.jellyfinSettingsDescription": "Opcionalmente, configure os endpoints internos e externos do seu servidor {mediaServerName}. Na maioria dos casos, o URL externo é diferente do URL interno. Uma URL de redefinição de senha personalizado também pode ser definido para o login {mediaServerName}, caso você queira redirecionar para uma página de redefinição de senha diferente. Você também pode alterar a chave de API do Jellyfin, que foi gerada automaticamente anteriormente.", "components.UserList.newJellyfinsigninenabled": "A configuração <strong>Ativar novo login {mediaServerName}</strong> está ativada no momento. Os usuários do {mediaServerName} com acesso a biblioteca não precisam ser importados para fazer login.", "components.TitleCard.addToWatchList": "Adicionar a lista para assistir", "components.Login.port": "Porta", "components.Login.servertype": "<PERSON><PERSON><PERSON>", "components.Login.validationHostnameRequired": "Você deve fornecer um nome de host ou endereço IP válido", "components.Login.validationPortRequired": "Você deve fornecer um número de porta válido", "components.Login.validationUrlBaseTrailingSlash": "A URL base não deve terminar com uma barra", "components.Login.validationUrlTrailingSlash": "A URL não deve terminar com uma barra", "components.Login.validationservertyperequired": "Por favor, selecione um tipo de servidor", "components.MovieDetails.addtowatchlist": "Adicionar a Lista para Assistir", "components.MovieDetails.watchlistError": "<PERSON>go deu errado, tente novamente.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> adicionado a lista para assistir com sucesso!", "components.RequestList.RequestItem.profileName": "Perfil", "components.Selector.canceled": "Cancelado", "components.Selector.inProduction": "Em Produção", "components.Selector.pilot": "<PERSON><PERSON>", "components.Selector.planned": "Planejada", "components.Selector.returningSeries": "Séries Retornando", "components.Selector.searchStatus": "Selecione status...", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON> o <PERSON>", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Escaneamento completa da biblioteca Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Escanear adicionados recentemente ao Jellyfin", "components.Settings.SonarrModal.tagRequestsInfo": "Adiciona automaticamente uma tag extra com o ID de usuário e o nome de exibição do solicitante", "components.Settings.invalidurlerror": "Não foi possível conectar-se ao servidor {mediaServerName}.", "components.Settings.jellyfinForgotPasswordUrl": "URL do Esqueci minha senha", "components.Settings.jellyfinSettings": "Ajustes do {mediaServerName}", "components.Settings.jellyfinSettingsFailure": "<PERSON><PERSON> deu errado ao <PERSON>l<PERSON> as configurações de {mediaServerName}.", "components.Settings.jellyfinSettingsSuccess": "As configurações de {mediaServerName} foram salvas com sucesso!", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Não há suporte para autenticação personalizada com Agrupamento Automático de Bibliotecas", "components.Settings.jellyfinSyncFailedGenericError": "Algo deu errado durante a sincronização das bibliotecas", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Não foram encontradas bibliotecas", "components.Settings.jellyfinlibraries": "Bibliotecas {mediaServerName}", "components.Settings.jellyfinsettings": "Ajustes do {mediaServerName}", "components.Settings.jellyfinsettingsDescription": "<PERSON><PERSON>a as configuraç<PERSON><PERSON> do seu servidor {mediaServerName}. {mediaServerName} examina suas bibliotecas {mediaServerName} para ver qual conteúdo está disponível.", "components.Settings.saving": "<PERSON><PERSON><PERSON>", "components.Settings.syncJellyfin": "Sincronizar Bibliotecas", "components.Settings.syncing": "Sincronizando", "components.Setup.back": "Voltar", "components.Setup.servertype": "Selecione o tipo de servidor", "components.Setup.signin": "Entrar", "components.Setup.signinWithJellyfin": "<PERSON><PERSON>a seus dados <PERSON>", "components.Setup.signinWithEmby": "Insira seus dados <PERSON>", "components.Setup.signinWithPlex": "Insira seus dados Plex", "components.Setup.subtitle": "Comece escolhendo seu servidor de mídia", "components.TitleCard.watchlistCancel": "lista para assistir para <strong>{title}</strong> cancelada.", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> removido da lista para assistir com sucesso!", "components.TitleCard.watchlistError": "<PERSON>go deu errado, tente novamente.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> adicionado a lista para assistir com sucesso!", "components.TvDetails.addtowatchlist": "Adicionar a lista para assistir", "components.TvDetails.play": "Reproduzir em {mediaServerName}", "components.TvDetails.play4k": "Reproduzir 4K em {mediaServerName}", "components.TvDetails.removefromwatchlist": "Remover da lista para assistir", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> removido da lista para assistir com sucesso!", "components.TvDetails.watchlistError": "<PERSON>go deu errado, tente novamente.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> adicionado a lista para assistir com sucesso!", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {user} other {users}} importados com sucesso!", "components.UserList.importfromJellyfin": "Importar usuários {mediaServerName}", "components.UserList.importfrommediaserver": "Importar usuários {mediaServerName}", "components.UserList.mediaServerUser": "Usu<PERSON>rio {mediaServerName}", "components.UserList.noJellyfinuserstoimport": "Não há usuários {mediaServerName} para importar.", "components.UserList.username": "Nome de Usuário", "components.UserList.validationUsername": "Você precisa fornecer um nome de usuário", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "Usu<PERSON>rio {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.save": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Email válido necess<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Email necess<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Dispositivo Padrão", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Som de Notificação", "components.Settings.manualscanDescriptionJellyfin": "Normalmente, isso só é executado uma vez a cada 24 horas. O Jellyseerr verificará os recentemente adicionados do {mediaServerName} de forma mais agressiva. Se esta for sua primeira vez configurando o Je<PERSON>seerr, recomenda-se uma verificação manual completa da biblioteca uma única vez!", "components.Settings.manualscanJellyfin": "Escaneamento Manual da Biblioteca", "components.Settings.save": "<PERSON><PERSON>", "components.UserProfile.localWatchlist": "Lista para Assistir de {username}", "i18n.collection": "Coleção", "components.Login.adminerror": "Você deve usar uma conta de administrador para fazer login."}