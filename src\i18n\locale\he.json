{"components.ManageSlideOver.alltime": "כל הזמנים", "components.Login.validationemailrequired": "יש לספק מייל חוקי", "components.NotificationTypeSelector.userissuereopenedDescription": "קבל התראה כשבעיות שפתחת נפתחות מחדש.", "components.AppDataWarning.dockerVolumeMissingDescription": "ה <code>{appDataPath}</code> אחסון לא הוגדר כראוי. כל המידע יוסר כאשר הקונטיינר יעצור או יותחל מחדש.", "components.CollectionDetails.overview": "תצוגה כללית", "components.CollectionDetails.numberofmovies": "{כמות} סרטים", "components.CollectionDetails.requestcollection": "אוסף בקשות", "components.CollectionDetails.requestcollection4k": "אוסף בקשות ב4K", "components.Discover.DiscoverMovieGenre.genreMovies": "סרטי {genre}", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} סרטים", "components.Discover.DiscoverNetwork.networkSeries": "{network} סדרות", "components.Discover.DiscoverStudio.studioMovies": "{studio} סרטים", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} סדרות", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} סדרות", "components.IssueDetails.commentplaceholder": "הוסף תגובה …", "components.IssueDetails.comments": "תגובות", "components.IssueDetails.deleteissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.deleteissueconfirm": "האם אתה בטוח שאתה רוצה למחוק את המקרה?", "components.AirDateBadge.airsrelative": "ישודר בעוד {relativeTime}", "components.Discover.DiscoverWatchlist.discoverwatchlist": "רשימת הצפייה שלך", "components.Discover.MovieGenreList.moviegenres": "סוגי סרטים", "components.Discover.StudioSlider.studios": "אולפנים", "components.Discover.TvGenreList.seriesgenres": "סוגי סדרות", "components.Discover.TvGenreSlider.tvgenres": "סוגי סדרות", "components.Discover.recentlyAdded": "נוספו לאחרונה", "components.Discover.recentrequests": "בקשות אחרונות", "components.Discover.trending": "חמים", "components.Discover.upcoming": "סרטים שיצאו ב<PERSON><PERSON><PERSON>ב", "components.Discover.upcomingmovies": "סרטים שיצאו ב<PERSON><PERSON><PERSON>ב", "components.Discover.upcomingtv": "סדרות שיצאו ב<PERSON>ר<PERSON>ב", "components.DownloadBlock.estimatedtime": "{time} משוער", "components.IssueDetails.IssueComment.delete": "מחיק<PERSON> תגובה", "components.IssueDetails.IssueComment.areyousuredelete": "למחוק את התגובה?", "components.IssueDetails.IssueComment.edit": "עריכת תגובה", "components.IssueDetails.IssueDescription.edit": "ערוך תיאור", "components.IssueDetails.allepisodes": "כל הפרקים", "components.IssueDetails.allseasons": "כל העונות", "components.IssueDetails.closeissue": "סגור מקרה", "components.IssueDetails.closeissueandcomment": "סגור עם תגובה", "components.IssueDetails.episode": "פרק {episodeNumber}", "components.IssueDetails.issuepagetitle": "מקרה", "components.IssueDetails.playonplex": "הפעל ב-Plex", "components.IssueDetails.play4konplex": "הפעל 4K ב-Plex", "components.IssueDetails.problemepisode": "פרק מושפע", "components.IssueDetails.toastissuedeleted": "מקרה נמחק בהצלחה!", "components.IssueList.IssueItem.issuetype": "סוג", "components.IssueList.IssueItem.opened": "נפתח", "components.IssueList.IssueItem.openeduserdate": "{date} ע\"י {user}", "components.IssueModal.issueSubtitles": "כתוביות", "components.IssueModal.issueVideo": "וידאו", "components.Layout.Sidebar.dashboard": "גילוי חדשים", "components.Login.signingin": "מבצע לוגאין…", "components.Login.signinheader": "יש להתחבר בכדי להמשיך", "components.Login.signinwithoverseerr": "שימוש בחשבון {applicationTitle}", "components.Login.signinwithplex": "שימוש בחשבון Plex", "components.ManageSlideOver.downloadstatus": "הורדות", "components.Discover.DiscoverWatchlist.watchlist": "רשימת צפייה", "components.Discover.MovieGenreSlider.moviegenres": "סוגי סרטים", "components.Discover.populartv": "סדרות פופולריות", "components.IssueDetails.IssueComment.postedby": "פורסם לפני {relativeTime} ע\"י {username}", "components.IssueDetails.IssueComment.postedbyedited": "פורסם לפני {relativeTime} ע\"י {username} (נערך)", "components.IssueDetails.IssueDescription.description": "תיאור", "components.IssueDetails.openedby": "#{issueId} נפתח לפני {relativeTime} ע\"י {username}", "components.IssueDetails.openin4karr": "נפתח ב4K {arr}", "components.IssueDetails.openinarr": "פתח ב {arr}", "components.IssueList.IssueItem.problemepisode": "פרק מושפע", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON> ע<PERSON><PERSON>ני", "components.IssueList.sortModified": "עוד<PERSON><PERSON> לאחרונה", "components.IssueModal.CreateIssueModal.allepisodes": "כל הפרקים", "components.IssueModal.CreateIssueModal.providedetail": "יש לספק מידע מפורט אודות המקרה שחווית.", "components.IssueModal.CreateIssueModal.submitissue": "הג<PERSON> מקרה", "components.LanguageSelector.originalLanguageDefault": "כל השפות", "components.Layout.Sidebar.requests": "בקשות", "components.Layout.Sidebar.settings": "הגדרות", "components.Layout.Sidebar.users": "משתמשים", "components.Layout.UserDropdown.myprofile": "פרופיל", "components.Layout.UserDropdown.settings": "הגדרות", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> פיתוח", "components.AirDateBadge.airedrelative": "שודר ב-{relativeTime}", "components.Discover.NetworkSlider.networks": "רשתות שידור", "components.Discover.discover": "לגלות", "components.Discover.plexwatchlist": "רשימת הצפייה שלך", "components.Discover.popularmovies": "סרטים פופולרים", "components.IssueDetails.IssueComment.validationComment": "אנא הכנס הודעה", "components.IssueDetails.IssueDescription.deleteissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "סוג", "components.IssueDetails.lastupdated": "עוד<PERSON><PERSON> לאחרונה", "components.IssueDetails.leavecomment": "תגובה", "components.IssueDetails.nocomments": "אין תגובות.", "components.IssueDetails.problemseason": "עונה מושפעת", "components.IssueDetails.reopenissue": "פתח בעיה מחדש", "components.IssueDetails.reopenissueandcomment": "פתח מחדש עם תגובה", "components.IssueDetails.season": "עונה {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "משהו השת<PERSON>ש בזמן עריכת תיאור המקרה.", "components.IssueDetails.toasteditdescriptionsuccess": "תיאור המקרה נערך בהצלחה!", "components.IssueDetails.toastissuedeletefailed": "משהו השת<PERSON>ש בזמן מחיקת המקרה.", "components.IssueDetails.toaststatusupdated": "סטאטוס המקרה עודכן בהצלחה!", "components.IssueDetails.toaststatusupdatefailed": "משהו השת<PERSON>ש בזמן עדכון סטאטוס המקרה.", "components.IssueDetails.unknownissuetype": "לא ידוע", "components.IssueList.IssueItem.issuestatus": "סטאטוס", "components.IssueList.IssueItem.unknownissuetype": "לא ידוע", "components.IssueList.IssueItem.viewissue": "צ<PERSON>ה במקרה", "components.IssueList.issues": "מקרים", "components.IssueList.showallissues": "הצג את כל המקרים", "components.IssueModal.CreateIssueModal.allseasons": "כל העונות", "components.IssueModal.CreateIssueModal.episode": "פרק {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "תוספות", "components.IssueModal.CreateIssueModal.problemepisode": "פרק מושפע", "components.IssueModal.CreateIssueModal.season": "עונה {seasonNumber}", "components.IssueModal.CreateIssueModal.problemseason": "עונה מושפעת", "components.IssueModal.CreateIssueModal.toastFailedCreate": "משהו השתבש בזמן הגשת מקרה.", "components.IssueModal.CreateIssueModal.reportissue": "דוו<PERSON> על מקרה", "components.IssueModal.CreateIssueModal.whatswrong": "מה השתבש?", "components.IssueModal.issueAudio": "אודיו", "components.IssueModal.issueOther": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.toastviewissue": "צ<PERSON>ה במקרה", "components.IssueModal.CreateIssueModal.validationMessageRequired": "אנא כתוב תיאור", "components.Layout.LanguagePicker.displaylanguage": "שפת תצוגה", "components.Layout.SearchInput.searchPlaceholder": "חיפוש סרטים וסדרות", "components.LanguageSelector.languageServerDefault": "ברירת מחדל({language})", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "בקשות סרטים", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "בקשות סדרות", "components.Login.forgotpassword": "שכחת סיסמה?", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> יציבה", "components.Login.email": "כתובת מייל", "components.ManageSlideOver.manageModalAdvanced": "מתקדם", "components.ManageSlideOver.manageModalClearMedia": "ניקוי מידע", "components.Discover.emptywatchlist": "מדיה נוספה לתוך <PlexWatchlistSupportLink>רשימת צפייה</PlexWatchlistSupportLink> תוצג פה.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "דווח מקרה של <strong>{title}</strong> הוגש בהצלחה!", "components.Layout.Sidebar.issues": "מקרים", "components.Layout.UserDropdown.requests": "בקשות", "components.Layout.UserDropdown.signout": "התנתק", "components.Layout.VersionStatus.outofdate": "לא מעודכן", "components.Login.loginerror": "משהו השת<PERSON><PERSON> בלוגאין.", "components.Login.password": "סיסמה", "components.Login.signin": "התחברות", "components.Login.validationpasswordrequired": "יש לספק סיסמה", "components.Discover.CreateSlider.editSlider": "ערוך סליידר", "components.Discover.CreateSlider.editfail": "נכשלה עריכת סליידר.", "components.Discover.CreateSlider.needresults": "אתה צריך לפחות תוצאה אחת.", "components.Discover.CreateSlider.nooptions": "אין תוצאות.", "components.Discover.CreateSlider.providetmdbgenreid": "ספק מזהה ז'אנר TMDB", "components.Discover.CreateSlider.providetmdbkeywordid": "ספק מזהה מילת מפתח TMDB", "components.Discover.CreateSlider.providetmdbsearch": "ספ<PERSON> שאילתת חיפוש", "components.Discover.CreateSlider.providetmdbstudio": "ספק מזהה סטודיו TMDB", "components.Discover.CreateSlider.searchGenres": "חפ<PERSON> ז'אנרים…", "components.Discover.CreateSlider.searchKeywords": "חפש מילות מפתח…", "components.Discover.CreateSlider.searchStudios": "ח<PERSON><PERSON> אולפנים…", "components.Discover.CreateSlider.slidernameplaceholder": "שם הסליידר", "components.Discover.CreateSlider.validationDatarequired": "עליך לספק ערך.", "components.Discover.CreateSlider.validationTitlerequired": "עליך לספק כותר.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "סרטים {keywordTitle}", "components.Discover.DiscoverMovies.discovermovies": "סרטים", "components.Discover.DiscoverMovies.sortPopularityAsc": "פופולריות בסדר עולה", "components.Discover.DiscoverMovies.sortPopularityDesc": "פופולריות בסדר יורד", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "תאריך שחרור בסדר עולה", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "תאריך שחרור בסדר יורד", "components.Discover.DiscoverMovies.sortTitleAsc": "כותר (א-ת, A-Z) עולה", "components.Discover.DiscoverMovies.sortTitleDesc": "כותר (ת-א, A-Z) יורד", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "דירוג TMDB בסדר עולה", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "דירוג TMDB בסדר יורד", "components.Discover.DiscoverSliderEdit.deletesuccess": "סליידר נמחק בהצלחה.", "components.Discover.DiscoverSliderEdit.enable": "שנה נראות", "components.Discover.DiscoverSliderEdit.remove": "הסר", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {פילטר אחד פעיל} other {# פילטרים פעילים}}", "components.Discover.DiscoverTv.discovertv": "סדרה", "components.Discover.CreateSlider.addfail": "נכשלה יצירת סליידר חדש.", "components.Discover.DiscoverTv.sortPopularityAsc": "פופולריות בסדר עולה", "components.Discover.DiscoverTv.sortPopularityDesc": "פופולריות בסדר יורד", "components.Discover.DiscoverTv.sortTitleAsc": "כותר (א-ת, A-Z) עולה", "components.Discover.DiscoverTv.sortTitleDesc": "כותר (א-ת, A-Z) יורד", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "דירוג TMDB בסדר עולה", "components.Discover.DiscoverTvKeyword.keywordSeries": "סדרה {keywordTitle}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {פילטר אחד פעיל} other {# פילטרים פעילים}}", "components.Discover.FilterSlideover.clearfilters": "נקה פילטרים פעילים", "components.Discover.FilterSlideover.filters": "פילטרים", "components.Discover.FilterSlideover.firstAirDate": "תאריך שידור ראשון", "components.Discover.FilterSlideover.from": "מאת", "components.Discover.FilterSlideover.keywords": "מילות מפתח", "components.Discover.FilterSlideover.originalLanguage": "שפה מקורית", "components.Discover.FilterSlideover.ratingText": "דירוג בין {minValue} ל {maxValue}", "components.Discover.FilterSlideover.releaseDate": "תאריך שחרור", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON> ריצה", "components.Discover.FilterSlideover.streamingservices": "שירותי הזרמה", "components.Discover.FilterSlideover.studio": "אול<PERSON>ן", "components.Discover.FilterSlideover.tmdbuserscore": "דירוג משתמש TMDB", "components.Discover.CreateSlider.addSlider": "הוסף סליידר", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON><PERSON> סליידר מותאם", "components.Discover.CreateSlider.addsuccess": "סליידר חדש נוצר בהצלחה ונשמרו הגדרות התאמה.", "components.Discover.CreateSlider.providetmdbnetwork": "ספק מזהה רשת TMDB", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "תאריך שידור ראשון בסדר יורד", "components.Discover.CreateSlider.starttyping": "התחל להזין כדי לחפש.", "components.Discover.FilterSlideover.runtimeText": "<PERSON><PERSON><PERSON> ריצה בין {minValue} ל {maxValue}", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {פילטר פעיל} other {# פילטרים פעילים}}", "components.Discover.DiscoverSliderEdit.deletefail": "כשל במ<PERSON><PERSON><PERSON><PERSON> סליידר.", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "תאריך שידור ראשון בסדר עולה", "components.Discover.CreateSlider.editsuccess": "סליידר נערך ונשמר בהצלחה.", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "דירוג TMDB בסדר יורד", "components.Discover.FilterSlideover.genres": "ז'אנרים", "components.Discover.FilterSlideover.tmdbuservotecount": "מספר הצבעות משתמשים בTMDB", "components.Discover.FilterSlideover.to": "בשביל", "components.Discover.FilterSlideover.voteCount": "מספר הצבעות בין {minValue} ל-{maxValue}", "components.Discover.PlexWatchlistSlider.emptywatchlist": "מדיה נוספה ל<PlexWatchlistSupportLink>רשימת צפייה ב-Plex</PlexWatchlistSupportLink>ותופיע שם.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "רשימת הצפייה שלך", "components.Discover.RecentlyAddedSlider.recentlyAdded": "נוספו לאחרונה", "components.Discover.createnewslider": "יצירת מחוון חדש", "components.Discover.customizediscover": "התאם אישית את הגילוי", "components.Discover.networks": "רשתות", "components.Discover.resetsuccess": "הגדרות הגילוי אופסו בהצלחה.", "components.Discover.resettodefault": "אופס לברירת מחדל", "components.Discover.stopediting": "סיים עריכה", "components.Discover.studios": "אולפנים", "components.Discover.tmdbmoviegenre": "ז'אנר סרט בTMDB", "components.Discover.tmdbmoviekeyword": "מילת מפתח של סרט בTMDB", "components.Discover.tmdbmoviestreamingservices": "רשת צפייה של סרט בTMDB", "components.Discover.tmdbnetwork": "רשת בTMDB", "components.Discover.tmdbsearch": "חיפוש בTMDB", "components.Discover.tmdbstudio": "אולפן בTMDB", "components.Discover.tmdbtvgenre": "ז'אנר סדרה בTMDB", "components.Discover.tmdbtvkeyword": "מילת מפתח של סדרה בTMDB", "components.Discover.tvgenres": "ז'אנרים של סדרות", "components.Discover.updatefailed": "משהו השתבש במהלך עדכון הגדרות של גילוי.", "components.Discover.updatesuccess": "הגדרות הגילוי התעדכנו.", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {עונה} other {עונות}}", "components.Layout.Sidebar.browsetv": "סדרות", "components.Layout.UserWarnings.emailRequired": "דרו<PERSON>ה כתובת מייל.", "components.Layout.UserWarnings.passwordRequired": "דרושה סיסמה.", "components.Login.credentialerror": "שם המשתמש או הסיסמה שגויים.", "components.Login.description": "בהתחברות ראשונית ל-{applicationName}, יש להוסיף כתובת מייל.", "components.Login.initialsignin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.initialsigningin": "מתחבר…", "components.Login.save": "הוספה", "components.Login.saving": "מוסיף…", "components.Login.title": "הוספת מייל", "components.Login.username": "שם משתמש", "components.Login.validationEmailFormat": "כתובת מייל שגוייה", "components.Login.validationEmailRequired": "יש להוסיף מייל", "components.Login.validationemailformat": "נדרש מייל תקין", "components.Login.validationhostrequired": "נדרש קישור של {mediaServerName}", "components.Login.validationusernamerequired": "נדרש שם משתמש", "components.ManageSlideOver.manageModalIssues": "תקלות פתוחות", "components.ManageSlideOver.manageModalMedia": "מדיה", "components.ManageSlideOver.manageModalMedia4k": "מדיה ב-4K", "components.ManageSlideOver.manageModalNoRequests": "אין בקשות.", "components.ManageSlideOver.manageModalRequests": "בקשות", "components.ManageSlideOver.manageModalTitle": "נהל {mediaType}", "components.ManageSlideOver.mark4kavailable": "סמן כזמין באיכות 4K", "components.ManageSlideOver.markallseasonsavailable": "סמן את כל העונות כזמינות", "components.ManageSlideOver.markavailable": "<PERSON><PERSON><PERSON> כזמין", "components.ManageSlideOver.movie": "סרט", "components.ManageSlideOver.openarr": "הפעל ב-{arr}", "components.ManageSlideOver.openarr4k": "הפעל גרסת 4K ב-{arr}", "components.ManageSlideOver.opentautulli": "הפעל ב-<PERSON><PERSON><PERSON>", "components.ManageSlideOver.pastdays": "{days, number} ימים עברו", "components.ManageSlideOver.playedby": "משוח<PERSON> על ידי", "components.ManageSlideOver.removearr": "הסר מ-{arr}", "components.ManageSlideOver.removearr4k": "הסר את איכות 4K מ-{arr}", "components.ManageSlideOver.tvshow": "סדרה", "components.MediaSlider.ShowMoreCard.seemore": "ראה עוד", "components.MovieDetails.MovieCast.fullcast": "כל השחקנים", "components.MovieDetails.MovieCrew.fullcrew": "כל הצוות", "components.MovieDetails.budget": "תק<PERSON><PERSON><PERSON>", "components.MovieDetails.cast": "ליה<PERSON><PERSON>", "components.MovieDetails.digitalrelease": "מהדורה דיגיטלית", "components.MovieDetails.downloadstatus": "מצב הורדה", "components.MovieDetails.imdbuserscore": "דירוג משתמש ב-IMDB", "components.MovieDetails.mark4kavailable": "סמן כזמין באיכות 4K", "components.MovieDetails.markavailable": "<PERSON><PERSON><PERSON> כזמין", "components.MovieDetails.openradarr": "הפעל סרט ב-Radarr", "components.MovieDetails.openradarr4k": "הפעל סרט באיכות 4K ב-Radarr", "components.MovieDetails.originallanguage": "שפת מקור", "components.MovieDetails.originaltitle": "השם במקור", "components.MovieDetails.overview": "סקירה", "components.MovieDetails.physicalrelease": "מהדורה פיזית", "components.MovieDetails.play": "הפעל ב-{mediaServerName}", "components.MovieDetails.play4k": "הפעל באיכות 4K ב-{mediaServerName}", "components.MovieDetails.recommendations": "המלצות", "components.MovieDetails.revenue": "רווח", "components.MovieDetails.rtaudiencescore": "דירוג צופים ב-Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "דירוג ב-Rotten Tomatoes", "components.MovieDetails.runtime": "{minutes} דק<PERSON>ת", "components.MovieDetails.showless": "הראה פחות", "components.MovieDetails.showmore": "הראה יותר", "components.MovieDetails.streamingproviders": "ז<PERSON><PERSON>ן כעת ב", "components.MovieDetails.studio": "{studioCount, plural, one {אולפן} other {אולפנים}}", "components.Discover.moviegenres": "ז'אנרים של סרטים", "components.Discover.resetfailed": "משהו השתבש, מא<PERSON><PERSON> את ההתאמה האישית בגילוי.", "components.Discover.resetwarning": "מאפס את כל המחוונים, זה ימחק כל מחוון מותאם אישית!", "components.Discover.tmdbtvstreamingservices": "רשתות צפייה של סדרות בTMDB", "components.DownloadBlock.formattedTitle": "{title}: עונ<PERSON> {seasonNumber} פרק {episodeNumber}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {פרק} other {פרקים}}", "components.Layout.Sidebar.browsemovies": "סרטים", "components.Layout.UserWarnings.emailInvalid": "כתובת מייל אינה תקינה.", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} מאחור", "components.Login.signinwithjellyfin": "שימוש בחשבון {mediaServerName}", "components.Login.validationhostformat": "נדרש קישור תקין", "components.ManageSlideOver.manageModalClearMediaWarning": "* כל הנתונים שלך ל-{mediaType}, כולל בקשות, ימחק<PERSON>. אם זה קיים בספרייה של {mediaServerName}, זה יצור אותה מחדש בסריקה הבאה.", "components.ManageSlideOver.markallseasons4kavailable": "סמן את כל העונות כזמינות ב-4K", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {הפעל} other {הפעלות}}", "components.MovieDetails.managemovie": "נהל סרט", "components.MovieDetails.overviewunavailable": "סקירה לא זמינה.", "components.MovieDetails.productioncountries": "נוצר ב-{countryCount, plural, one {Country} other {Countries}}", "components.MovieDetails.releasedate": "{releaseCount, plural, one {תאריך השקה} other {תאריכי השקה}}", "components.MovieDetails.reportissue": "דווח על תקלה", "components.MovieDetails.similar": "כותרים דומים", "components.Login.emailtooltip": "הכתובת אינה צריכה להיות משוייכת ל-{mediaServerName}.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* הפעולה תסיר את {mediaType} מ-{arr} כולל כל הקבצים.", "components.MovieDetails.theatricalrelease": "שי<PERSON><PERSON><PERSON><PERSON> מוגבל", "components.MovieDetails.tmdbuserscore": "די<PERSON><PERSON><PERSON> TMDB", "components.MovieDetails.viewfullcrew": "צ<PERSON>י<PERSON>ה בכל הצוות", "components.MovieDetails.watchtrailer": "<PERSON><PERSON><PERSON> טריילר", "components.NotificationTypeSelector.adminissuecommentDescription": "קבלת התראות כאשר משתמשים אחרים מגיבים למקרה.", "components.NotificationTypeSelector.adminissuereopenedDescription": "קבלת התראות כאשר משתמשים אחרים פותחים את המקרה מחדש.", "components.NotificationTypeSelector.adminissueresolvedDescription": "קבלת התראות כאשר המקרה נפתר ע״י משתמשים אחרים.", "components.NotificationTypeSelector.issuecomment": "תגובה למקרה", "components.NotificationTypeSelector.issuecommentDescription": "קבלת התראות כאשר מתקבלות תגובות חדשות למקרים.", "components.NotificationTypeSelector.issuecreated": "מקרה חדש נפתח", "components.NotificationTypeSelector.issuecreatedDescription": "קבלת התראות כאשר מקרה חדש נפתח.", "components.NotificationTypeSelector.issuereopened": "מקרה נפתח מחדש", "components.NotificationTypeSelector.issuereopenedDescription": "קבלת התראות כאשר מקרים נפתחים מחדש.", "components.NotificationTypeSelector.issueresolved": "פנייה נפתרה", "components.NotificationTypeSelector.issueresolvedDescription": "קבלת התראות כאשר פניות נפתרות.", "components.NotificationTypeSelector.mediaAutoApproved": "בקשה אושרה אוטומטית", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "קבלת התראות כאשר משתמשים פותחים בקשה חדשה שמאושרת אוטומטית.", "components.NotificationTypeSelector.mediaapproved": "בק<PERSON>ה אושרה", "components.NotificationTypeSelector.mediaapprovedDescription": "שליחת התראות כאשר בקשות מדיה מאושרות אוטומטית.", "components.NotificationTypeSelector.mediaautorequested": "בק<PERSON>ה נפתחה אוטומטית", "components.NotificationTypeSelector.mediaautorequestedDescription": "שליחת התראות כאשר הבקשה נוצרת אוטומטית עבור מדיה ברשימת הצפייה.", "components.NotificationTypeSelector.mediaavailable": "ב<PERSON><PERSON><PERSON> זמינה", "components.NotificationTypeSelector.mediaavailableDescription": "שליחת התראות כאשר ניתן ליצור בקשות מדיה.", "components.NotificationTypeSelector.mediadeclined": "בק<PERSON><PERSON> נדחתה", "components.NotificationTypeSelector.mediadeclinedDescription": "קבלת התראות כאשר בקשות מדיה נדחות.", "components.NotificationTypeSelector.mediafailed": "עיבוד הבק<PERSON>ה נכשל", "components.NotificationTypeSelector.mediarequested": "בק<PERSON>ה ממתינה לאישור", "components.NotificationTypeSelector.mediarequestedDescription": "קבלת התראות כאשר משתמשים פותחים בקשות מדיה שדורשות אישור.", "components.NotificationTypeSelector.userissuecreatedDescription": "קבלת התראות כאשר משתמשים אחרים מדווחים על תקלות.", "components.PermissionEdit.admin": "מנהל", "components.PermissionEdit.adminDescription": "גישת מנהל מלאה. עוקף את כל ההרשאות שסומנו.", "components.PermissionEdit.advancedrequest": "בקשות מתקדמות", "components.PermissionEdit.advancedrequestDescription": "הרשאות לשינוי אפשרויות בקשות מדיה.", "components.PermissionEdit.autoapprove": "איש<PERSON>ר אוטומטי", "components.PermissionEdit.autoapprove4k": "אישור אוטומטי של 4K", "components.PermissionEdit.autoapprove4kMovies": "אישור אוטומטי סרטי 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "אישור אוטומטי של בקשות לסרטים 4K.", "components.PermissionEdit.autoapprove4kSeries": "אישור אוטומטי של סדרות 4K", "components.PermissionEdit.autoapproveDescription": "אישור אוטומטי של בקשות לסדרות ברזולוציית נמוכה מ-4K.", "components.PermissionEdit.autoapproveMovies": "אישור סרטים אוטומטי", "components.PermissionEdit.autoapproveMoviesDescription": "אישור בקשות סרטים אוטומטי (ללא 4K).", "components.PermissionEdit.autoapproveSeries": "אישור סדרות אוטומטי", "components.PermissionEdit.autorequest": "ב<PERSON><PERSON><PERSON> אוטומטית", "components.NotificationTypeSelector.mediafailedDescription": "קבלת התראות כאשר יש כשל בהוספת בקשות מדיה אל Radarr או Sonarr", "components.NotificationTypeSelector.notificationTypes": "סוגי התראות", "components.NotificationTypeSelector.userissuecommentDescription": "קבלת התראות כאשר תקלות שדיווחת מקבלות תגובות חדשות.", "components.PermissionEdit.autoapprove4kDescription": "אישור אוטומטי של בקשות מדיה 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "אישור אוטומטי של בקשות לסדרות ברזולוציית 4K.", "components.PermissionEdit.autoapproveSeriesDescription": "אישור בק<PERSON>ות סדרות אוטומטי (ללא 4K).", "component.BlacklistBlock.blacklistdate": "תאריך חסימה", "components.Login.hostname": "{mediaServerName} כתובת URL", "components.Login.validationHostnameRequired": "עליך להזין שם מארח או כתובת IP תקינה", "components.Login.validationUrlBaseTrailingSlash": "בסיס ה-<PERSON><PERSON> לא יכול להסתיים בסלאש", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> נוסף לרשימת הצפייה בהצלחה!", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "קבלת התראות כאשר משתמשים אחרים מגישים בקשות מדיה חדשות אשר מאושרות אוטומטית.", "components.PermissionEdit.createissuesDescription": "הענקת גישה לדווח על בעיות מדיה.", "components.PermissionEdit.usersDescription": "הענקת גישה לניהול משתמשים. משתמשים עם גישה זאת לא יכולים לשנות משתמשים עם הרשאות מנהל או להעניק הרשאות מנהל.", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} לכל {quotaDays} {days}</quotaUnits>", "components.RequestBlock.languageprofile": "פרופיל שפה", "components.RequestList.RequestItem.failedretry": "משהו השתבש בעת ניסוי שליחת הבקשה מחדש.", "components.RequestModal.AdvancedRequester.animenote": "* סדרה זו היא אנימה.", "components.RequestModal.QuotaDisplay.quotaLink": "ניתן לראות סיכום של מגבלת הבקשות שלך ב<ProfileLink>דף הפרופיל</ProfileLink> שלך.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "ניתן לראות סיכום של מגבלת הבקשות של משתמש זה ב<ProfileLink>דף הפרופיל</ProfileLink> שלו.", "components.RequestModal.requestmovies4k": "בקשת {count} {count, plural, one {סרט} other {סרטים}} ב-4K", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>רשימת יישום</ApplicationRegistrationLink> בשביל שימוש עם Je<PERSON>seerr", "component.BlacklistBlock.blacklistedby": "נחסם על ידי", "components.NotificationTypeSelector.usermediarequestedDescription": "קבלת התראות כאשר משתמשים אחרים מגישים בקשות מדיה חדשות שדורשות אישור.", "components.PermissionEdit.autorequestMovies": "בקשת סרטים אוטומטית", "components.PermissionEdit.autorequestMoviesDescription": "הענקת גישה לשליחה אוטומטית של סרטים שאינם 4K בעזרת רשימת הצפייה של Plex.", "components.PermissionEdit.managerequestsDescription": "הענקת גישה לניהול בקשות מדיה. כל הבקשות שנעשות על ידי משתמש עם גישה זאת יאושרו אוטומטית.", "components.PermissionEdit.requestMoviesDescription": "הענקת גישה לשליחת בקשות לסרטים שאינם 4K.", "components.PermissionEdit.viewrequestsDescription": "הענקת גישה לצפות בבקשות מדיה שנשלחו על ידי משתמשים אחרים.", "components.RequestBlock.lastmodifiedby": "שונה לאחרונה על ידי", "components.RequestButton.approverequests": "אישור {requestCount, plural, one {בקשה} other {{requestCount} בקשות}}", "components.RequestCard.failedretry": "משהו השתבש בעת ניסוי שליחת הבקשה מחדש.", "components.RequestModal.requestCancel": "בק<PERSON><PERSON> ל-<strong>{title}</strong> בוטלה.", "components.RequestModal.selectseason": "בחירת עונה/ות", "components.ResetPassword.requestresetlinksuccessmessage": "קישור לאיפוס סיסמה יישלח לכתובת המייל שסופקה אם היא קשורה למשתמש תקין.", "components.Selector.canceled": "בוטל", "components.Selector.ended": "נ<PERSON><PERSON><PERSON>", "components.Selector.searchStudios": "חיפוש אולפנים…", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "עליך לספק אסימון יישום", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "הגדרות ההתראות של Slack נכשלו להישמר.", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "עליך לספק כתובת URL תקינה", "components.Layout.Sidebar.blacklist": "רשימת חסימות", "components.Login.back": "חזרה", "components.PermissionEdit.viewissuesDescription": "הענקת גישה לראות בעיות מדיה שדווחו על ידי משתמשים אחרים.", "components.RequestButton.viewrequest4k": "צפייה בבקשת 4K", "components.RequestModal.QuotaDisplay.allowedRequests": "מותר לך לבקש <strong>{limit}</strong> {type} כל <strong>{days}</strong> ימים.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "על משתמש זה להיות עם לפחות <strong>{seasons}</strong> {seasons, plural, one {בקשת עונה} other {בקשות עונה}} שנותרו כדי לשלוח בקשה עבור סדרה זו.", "components.RequestModal.pending4krequest": "בקשת 4K ממתינה", "components.RequestModal.requestedited": "בק<PERSON><PERSON> עבור <strong>{title}</strong> נערכה בהצלחה!", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "הגדרות ההתראות של Gotify נכשלו להישמר.", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "עליך לספק כתובת URL תקינה", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "שולח התראת בדיקה של Pushbullet…", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "<UsersGroupsLink>מזהה המשתמש או הקבוצה</UsersGroupsLink> בעל ה-30 תווים שלך", "components.Login.adminerror": "עליך להשתמש בחשבון מנהל בשביל להתחבר.", "components.NotificationTypeSelector.userissueresolvedDescription": "קבלת התראות כאשר הבעיות שדיווחת נפתרות.", "components.Discover.FilterSlideover.status": "סטטוס", "components.PermissionEdit.blacklistedItemsDescription": "הענקת גישה לחסימת מדיה.", "components.RequestBlock.seasons": "{seasonCount, plural, one {עונה} other {עונות}}", "components.RequestModal.errorediting": "משהו השתבש בזמן עריכת הבקשה.", "components.ResetPassword.validationpasswordrequired": "יש לספק סיסמה", "components.Selector.inProduction": "בה<PERSON><PERSON>ה", "components.Selector.pilot": "פרק דוגמה", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "הגדרות ההתראות של LunaSea נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "יצירת אסימון מתוך <PushbulletSettingsLink>הגדרות המשתמש</PushbulletSettingsLink> שלך", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "עליך לספק אסימון גישה", "components.PermissionEdit.manageblacklistDescription": "הענקת גישה לניהול מדיה חסומה.", "components.PersonDetails.appearsin": "מופעים", "components.RequestCard.tmdbid": "מזהה TMDB", "components.DiscoverTvUpcoming.upcomingtv": "סדרות שיצאו ב<PERSON>ר<PERSON>ב", "components.Login.enablessl": "השתמש ב-SSL", "components.Login.invalidurlerror": "לא ניתן להתחבר לשרת {mediaServerName}.", "components.Login.loginwithapp": "התחברות עם {appName}", "components.Login.noadminerror": "לא נמצא חשבון מנהל בשרת.", "components.Login.orsigninwith": "או התחברות עם", "components.Login.port": "פורט", "components.Login.servertype": "סוג שרת", "components.Login.urlBase": "בסיס URL", "components.Login.validationPortRequired": "עליך להזין מספר פורט תקין", "components.Login.validationUrlBaseLeadingSlash": "בסיס ה-<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> להתחיל בסלאש", "components.Login.validationUrlTrailingSlash": "ה-U<PERSON> לא יכול להסתיים בסלאש", "components.Login.validationservertyperequired": "אנא בחר סוג שרת", "components.MovieDetails.addtowatchlist": "הוספה לרשימת הצפייה", "components.MovieDetails.removefromwatchlist": "הסרה מרשימת הצפייה", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> נמחק מרשימת הצפייה בהצלחה!", "components.MovieDetails.watchlistError": "משהו השתבש. אנא נסה שוב.", "components.NotificationTypeSelector.usermediaapprovedDescription": "קבלת התראות שבקשות המדיה שלך מאושרות.", "components.NotificationTypeSelector.usermediaavailableDescription": "קבלת התראות כאשר בקשות המדיה שלך הופכות לזמינות.", "components.NotificationTypeSelector.usermediadeclinedDescription": "קבלת התראות כאשר בקשות המדיה שלך נדחות.", "components.NotificationTypeSelector.usermediafailedDescription": "קבלת התראות כאשר בקשות מדיה נכשלות להתווסף ל-Radarr או Sonarr.", "components.PermissionEdit.autorequestDescription": "הענקת גישה לשליחה אוטומטית של בקשות מדיה שאינה 4K בעזרת רשימת הצפייה של Plex.", "components.PermissionEdit.autorequestSeries": "בקשת סדרות אוטומטית", "components.PermissionEdit.autorequestSeriesDescription": "הענקת גישה לשליחה אוטומטית של סדרות שאינן 4K בעזרת רשימת הצפייה של Plex.", "components.PermissionEdit.createissues": "דיווח על בעיות", "components.PermissionEdit.manageblacklist": "ניהול רשימת חסימה", "components.PermissionEdit.manageissues": "ניהול בעיות", "components.PermissionEdit.manageissuesDescription": "הענקת גישה לניהול בעיות מדיה.", "components.PermissionEdit.managerequests": "ניהול בקשות", "components.PermissionEdit.request": "בק<PERSON>ה", "components.PermissionEdit.request4k": "בקשת 4K", "components.PermissionEdit.request4kDescription": "הענקת גישה לשליחת בקשות למדיה 4K.", "components.PermissionEdit.request4kMovies": "בקשת סרטי 4K", "components.PermissionEdit.request4kMoviesDescription": "הענקת גישה לשליחת בקשות לסרטי 4K.", "components.PermissionEdit.request4kTvDescription": "הענקת גישה לשליחת בקשות לסדרות 4K.", "components.PermissionEdit.request4kTv": "בקשת סדרות 4K", "components.PermissionEdit.requestDescription": "הענקת גישה לשליחת בקשות למדיה שאינה 4K.", "components.PermissionEdit.requestMovies": "בקשת סרטים", "components.PermissionEdit.requestTv": "בק<PERSON><PERSON> סדרה", "components.PermissionEdit.requestTvDescription": "הענקת גישה לשליחת בקשות לסדרות שאינן 4K.", "components.PermissionEdit.users": "ניהול משתמשים", "components.PermissionEdit.viewblacklistedItems": "צפייה במדיה חסומה.", "components.PermissionEdit.viewblacklistedItemsDescription": "הענקת גישה לצפייה במדיה חסומה.", "components.PermissionEdit.viewissues": "צפייה בבעיות", "components.PermissionEdit.viewrecent": "צפייה בנוסף לאחרונה", "components.PermissionEdit.viewrecentDescription": "הענקת גישה לצפייה ברשימת המדיה שנוספה לאחרונה.", "components.PermissionEdit.viewrequests": "צפייה בבקשות", "components.PermissionEdit.viewwatchlists": "צפייה ברשימות הצפייה של {mediaServerName}", "components.PermissionEdit.viewwatchlistsDescription": "הענקת גישה לצפות ברשימות הצפייה של משתמשים אחרים ב-{mediaServerName}.", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} לכל {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {עונה} other {עונות}}", "components.QuotaSelector.unlimited": "אין-סו<PERSON>י", "components.RegionSelector.regionDefault": "כל האזורים", "components.RegionSelector.regionServerDefault": "ברירת מחדל ({region})", "components.RequestBlock.approve": "אישו<PERSON> ב<PERSON><PERSON>ה", "components.RequestBlock.decline": "ד<PERSON><PERSON><PERSON><PERSON> בקשה", "components.RequestBlock.delete": "מחיקת בקשה", "components.RequestBlock.edit": "עריכת בקשה", "components.RequestBlock.profilechanged": "פרופיל איכות", "components.RequestBlock.requestdate": "תאריך בקשה", "components.RequestBlock.requestedby": "התב<PERSON><PERSON> על ידי", "components.RequestBlock.requestoverrides": "עקיפות בקשה", "components.RequestBlock.rootfolder": "תיקיית שורש", "components.RequestBlock.server": "שרת יעד", "components.RequestButton.approve4krequests": "אישור {requestCount, plural, one {בקשת 4K} other {{requestCount} בקשות 4K}}", "components.RequestButton.approverequest": "אישו<PERSON> ב<PERSON><PERSON>ה", "components.RequestButton.approverequest4k": "אישור בקשת 4K", "components.RequestButton.decline4krequests": "דחיית {requestCount, plural, one {בקשת 4K} other {{requestCount} בקשות 4K}}", "components.RequestButton.declinerequest": "ד<PERSON><PERSON><PERSON><PERSON> בקשה", "components.RequestButton.declinerequest4k": "דחיית בקשת 4K", "components.RequestButton.declinerequests": "דחיית {requestCount, plural, one {בקשה} other {{requestCount} בקשות}}", "components.RequestButton.requestmore": "בקש/י עוד", "components.RequestButton.requestmore4k": "בקש/י עוד ב-4K", "components.RequestButton.viewrequest": "צפייה בבקשה", "components.RequestCard.approverequest": "אישו<PERSON> ב<PERSON><PERSON>ה", "components.RequestCard.cancelrequest": "ביטול בקשה", "components.RequestCard.declinerequest": "ד<PERSON><PERSON><PERSON><PERSON> בקשה", "components.RequestCard.deleterequest": "מחיקת בקשה", "components.RequestCard.editrequest": "עריכת בקשה", "components.RequestCard.mediaerror": "{mediaType} לא נמצא", "components.RequestCard.seasons": "{seasonCount, plural, one {עונה} other {עונות}}", "components.RequestCard.tvdbid": "מזהה TheTVDB", "components.RequestCard.unknowntitle": "כותרת לא ידועה", "components.RequestList.RequestItem.cancelRequest": "ביטול בקשה", "components.RequestList.RequestItem.deleterequest": "מחיקת בקשה", "components.RequestList.RequestItem.editrequest": "עריכת בקשה", "components.RequestList.RequestItem.mediaerror": "{mediaType} לא נמצא", "components.RequestList.RequestItem.modified": "שונה", "components.RequestList.RequestItem.modifieduserdate": "{date} על ידי {user}", "components.RequestList.RequestItem.profileName": "פרופיל", "components.RequestList.RequestItem.removearr": "הסרה מ-{arr}", "components.RequestList.RequestItem.requested": "התבקש", "components.RequestList.RequestItem.requesteddate": "התבקש", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {עונה} other {עונות}}", "components.RequestList.RequestItem.tmdbid": "מזהה TMDB", "components.RequestList.RequestItem.tvdbid": "מזהה TheTVDB", "components.RequestList.RequestItem.unknowntitle": "כותרת לא ידועה", "components.RequestList.requests": "בקשות", "components.RequestList.showallrequests": "הצגת כל הבקשות", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON> ע<PERSON><PERSON>ני", "components.RequestList.sortDirection": "החלפת כיוון מיון", "components.RequestModal.AdvancedRequester.advancedoptions": "מתקדם", "components.RequestModal.AdvancedRequester.default": "{name} (בריר<PERSON> מחדל)", "components.RequestModal.AdvancedRequester.destinationserver": "שרת יעד", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "פרופיל שפה", "components.RequestModal.AdvancedRequester.notagoptions": "אין תגים.", "components.RequestModal.AdvancedRequester.qualityprofile": "פרופיל איכות", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON> בתור", "components.RequestModal.AdvancedRequester.rootfolder": "תיקיית שורש", "components.RequestModal.AdvancedRequester.selecttags": "בחירת תגים", "components.RequestModal.AdvancedRequester.tags": "תגים", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "מותר למשתמש זה לבקש <strong>{limit}</strong> {type} כל <strong>{days}</strong> ימים.", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {סרט} other {סרטים}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "אין מס<PERSON><PERSON>ק בקשות עונות שנותרו", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {בקשה} other {בקשות}} נותרו", "components.RequestModal.QuotaDisplay.requiredquota": "עליך להיות עם לפחות <strong>{seasons}</strong> {seasons, plural, one {בקשת עונה} other {בקשות עונה}} שנותרו כדי לשלוח בקשה עבור סדרה זו.", "components.RequestModal.QuotaDisplay.season": "עונה", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {עונה} other {עונות}}", "components.RequestModal.SearchByNameModal.nomatches": "לא הצלחנו למצוא התאמה עבור סדרה זו.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "לא הצלחנו להתאים אוטומטית את הסדרה הזו. אנא בחר/י את ההתאמה הנכונה למטה.", "components.RequestModal.alreadyrequested": "הת<PERSON><PERSON><PERSON> כבר", "components.RequestModal.approve": "אישו<PERSON> ב<PERSON><PERSON>ה", "components.RequestModal.autoapproval": "איש<PERSON>ר אוטומטי", "components.RequestModal.cancel": "ביטול בקשה", "components.RequestModal.edit": "עריכת בקשה", "components.RequestList.sortModified": "עוד<PERSON><PERSON> לאחרונה", "components.RequestModal.numberofepisodes": "# של פרקים", "components.RequestModal.pendingapproval": "בקשתך מחכה לאישור.", "components.RequestModal.pendingrequest": "בק<PERSON>ה ממתינה", "components.RequestModal.requestApproved": "בק<PERSON>ה ל-<strong>{title}</strong> אושרה!", "components.RequestModal.requestSuccess": "<strong>{title}</strong> התבקשה בהצלחה!", "components.RequestModal.requestadmin": "בק<PERSON><PERSON> זו תאושר אוטומטית.", "components.RequestModal.requestcancelled": "ב<PERSON><PERSON><PERSON> עבור <strong>{title}</strong> בוטלה.", "components.RequestModal.requestcollection4ktitle": "אוסף בקשות ב-4K", "components.RequestModal.requestcollectiontitle": "אוסף בקשות", "components.RequestModal.requesterror": "משהו השת<PERSON>ש בזמן שליחת הבקשה.", "components.RequestModal.requestfrom": "ה<PERSON><PERSON><PERSON><PERSON> של {username} <PERSON><PERSON><PERSON><PERSON> לאישור.", "components.RequestModal.requestmovie4ktitle": "בק<PERSON>ת סרט ב-4K", "components.RequestModal.requestmovies": "בקשת {count} {count, plural, one {סרט} other {סרטים}}", "components.RequestModal.requestmovietitle": "בק<PERSON>ת סרט", "components.RequestModal.requestseasons": "בקשת {seasonCount} {seasonCount, plural, one {עונה} other {עונות}}", "components.RequestModal.requestseasons4k": "בקשת {seasonCount} {seasonCount, plural, one {עונה} other {עונות}} ב-4K", "components.RequestModal.requestseries4ktitle": "בק<PERSON><PERSON> סדרה ב-4K", "components.RequestModal.requestseriestitle": "בק<PERSON><PERSON> סדרה", "components.RequestModal.season": "עונה", "components.RequestModal.seasonnumber": "עונה {number}", "components.RequestModal.selectmovies": "בחירת סרט/ים", "components.ResetPassword.confirmpassword": "אישור סיסמה", "components.ResetPassword.email": "כתובת מייל", "components.ResetPassword.emailresetlink": "קישור לשחזור מייל", "components.ResetPassword.gobacklogin": "חזרה לדף ההתחברות", "components.ResetPassword.password": "סיסמה", "components.ResetPassword.passwordreset": "איפוס סיסמה", "components.ResetPassword.resetpassword": "איפוס סיסמתך", "components.ResetPassword.resetpasswordsuccessmessage": "סיסמה אופסה בהצלחה!", "components.ResetPassword.validationemailrequired": "יש לספק כתובת מייל חוקית", "components.ResetPassword.validationpasswordmatch": "הסיסמאות חייבות להתאים", "components.ResetPassword.validationpasswordminchars": "הסיסמה קצרה מדי; צריך להיות מינימום של 8 תווים", "components.Search.search": "חיפוש", "components.Search.searchresults": "תוצאות חיפוש", "components.Selector.nooptions": "אין תוצאות.", "components.Selector.planned": "מתוכ<PERSON>ן", "components.Selector.returningSeries": "סדרה חוזרת", "components.Selector.searchGenres": "בחירת ז'נרים…", "components.Selector.searchKeywords": "חיפוש מילות מפתח…", "components.Selector.searchStatus": "בחירת סטטוס...", "components.Selector.searchUsers": "בחירת משתמשים…", "components.Selector.showless": "הראה פחות", "components.Selector.showmore": "הראה יותר", "components.Selector.starttyping": "התחל להזין כדי לחפש.", "components.Settings.Notifications.NotificationsGotify.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "הגדרות ההתראות של Gotify נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "התראת הבדיקה של Gotify נכשלה להשלח.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "שולח התראת בדיקה של Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "התראת הבדיקה של Gotify נשלחה!", "components.Settings.Notifications.NotificationsLunaSea.profileName": "שם פרופיל", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "רק דרוש אם הפרופיל <code>default</code> לא בשימוש", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "הגדרות ההתראות של LunaSea נכשלו להישמר.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "התראת הבדיקה של LunaSea נכשלה להשלח.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "שולח התראת בדיקה של LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "התראת הבדיקה של LunaSea נשלחה!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "עליך לבחור לפחות סוג התראות אחד", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL של ה-Webhook", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "אסימון גישה", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "תג ערוץ", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "הגדרות ההתראות של Pushbullet נכשלו להישמר.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "הגדרות ההתראות של Pushbullet נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "התראת הבדיקה של Pushbullet נכשלה להשלח.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "התראת הבדיקה של Pushbullet נשלחה!", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "עליך לבחור לפחות סוג התראה אחד", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "התראת הבדיקה של Pushover נכשלה להשלח.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "שולח התראת בדיקה של Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "התראת הבדיקה של Pushover נשלחה!", "components.Settings.Notifications.NotificationsPushover.userToken": "מפתח משתמש או קבוצה", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "עליך לספק אסימון יישום תקין", "components.Settings.Notifications.NotificationsPushover.validationTypes": "עליך לבחור לפחות סוג התראה אחד", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "עליך לספק משתמש או מפתח קבוצה תקין", "components.Settings.Notifications.NotificationsSlack.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "הגדרות ההתראות של Slack נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "התראת הבדיקה של Slack נכשלה להשלח.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "שולח התראת בדיקה של <PERSON>lack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "התראת הבדיקה של Slack נשלחה!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "עליך לבחור לפחות סוג התראה אחד", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL של ה-Webhook", "component.BlacklistModal.blacklisting": "חסימה", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> לא חסום.", "components.Blacklist.blacklistSettingsDescription": "ניהול מדיה חסומה.", "components.Blacklist.blacklistdate": "תאריך", "components.Blacklist.blacklistedby": "{date} על ידי {user}", "components.Blacklist.blacklistsettings": "הגדרות חסימה", "components.Blacklist.mediaName": "שם", "components.Blacklist.mediaTmdbId": "מזהה tmdb", "components.Blacklist.mediaType": "סוג", "components.PermissionEdit.blacklistedItems": "חסימת מדיה.", "components.PersonDetails.alsoknownas": "גם ידוע בתור: {names}", "components.PersonDetails.ascharacter": "בתור {character}", "components.PersonDetails.birthdate": "נולד/ה ב-{birthdate}", "components.PersonDetails.crewmember": "צוות", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {יום} other {ימים}}", "components.QuotaSelector.movies": "{count, plural, one {סרט} other {סרטים}}", "components.RequestModal.QuotaDisplay.movie": "סרט", "components.Settings.Notifications.NotificationsGotify.token": "אסימון יישום", "components.Settings.Notifications.NotificationsGotify.url": "כתובת URL של שרת", "components.Settings.Notifications.NotificationsGotify.validationTypes": "עליך לבחור לפחות סוג התראות אחד", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "עליך לספק כתובת URL תקינה", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "על כתובת ה-U<PERSON> לא להסתיים בסלאש", "components.Settings.Notifications.NotificationsPushover.accessToken": "אסימון API של היישום", "components.Settings.Notifications.NotificationsPushover.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "ברירת המחדל של המכשיר", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "הגדרות ההתראות של Pushover נכשלו להישמר.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "הגדרות ההתראות של Pushover נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsPushover.sound": "צליל התראה", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "איפוס לברירת מחדל", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<LunaSeaLink>קישו<PERSON> Webhook להתראות</LunaSeaLink> מבוסס מכשיר או משתמש", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsWebhook.authheader": "כותרת עליונה של הרשאות", "components.Settings.Notifications.NotificationsWebhook.customJson": "מטען JSON", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "מטען JSON התאפס בהצלחה!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "התראת הבדיקה של ה-Webhook נשלחה!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "עליך לספק מטען JSON תקין", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "עליך לבחור לפחות סוג התראה אחד", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "עליך לספק כתובת URL תקינה", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL של ה-Webhook", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "הגדרות ההתראות של ה-Webhook נכשלו להשמר.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "הגדרות ההתראות של ה-Webhook נשמרו בהצלחה!", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "הפעל סוכן", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "התראת הבדיקה של ה-Webhook נכשלה להשלח.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "שולח התראת בדיקה של ה-Webhook…"}