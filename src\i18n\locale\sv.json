{"components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Setup.finish": "Slutför konfiguration", "components.Setup.continue": "Fortsätt", "components.Setup.configureservices": "Konfigurera tjä<PERSON>er", "components.Settings.validationPortRequired": "Du måste ange ett giltigt portnummer", "components.Settings.validationHostnameRequired": "Du måste ange ett giltigt värdnamn eller en IP-adress", "components.Settings.startscan": "<PERSON><PERSON> skanning", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "Sonarr-inställningar", "components.Settings.radarrsettings": "Radarr-inställningar", "components.Settings.port": "Port", "components.Settings.plexsettingsDescription": "Konfigurera inställningarna för din Plex-server. <PERSON><PERSON><PERSON><PERSON> skannar din Plex-server för att avgöra tillgängligt innehåll.", "components.Settings.plexsettings": "Plex-inställningar", "components.Settings.plexlibrariesDescription": "De bibliotek som Jellyseerr skannar efter titlar. <PERSON><PERSON><PERSON> in och spara dina Plex-anslutningsinställningar, och klicka sedan på knappen nedan om inga bibliotek visas.", "components.Settings.plexlibraries": "Plex-bibliotek", "components.Settings.notrunning": "K<PERSON>rs inte", "components.Settings.notificationsettings": "Meddelandeinställningar", "components.Settings.menuServices": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Meddelanden", "components.Settings.menuLogs": "Loggar", "components.Settings.menuJobs": "Jobb och cache", "components.Settings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuAbout": "Om", "components.Settings.manualscanDescription": "Normalt kommer detta endast att köras en gång var 24:e timme. <PERSON><PERSON><PERSON>rr kommer att kontrollera nyligen tillagda filmer på din Plex-server mer intensivt. Om det är första gången du konfigurerar Plex rekommenderas en engång<PERSON>, fullständig manuell biblioteksskanning!", "components.Settings.manualscan": "Manuell biblioteksskanning", "components.Settings.librariesRemaining": "Återstående bibliotek: {count}", "components.Settings.hostname": "Värdnamn eller IP-adress", "components.Settings.deleteserverconfirm": "Är du säker på att du vill radera denna instans?", "components.Settings.default4k": "Standard 4K", "components.Settings.default": "Standard", "components.Settings.currentlibrary": "Nuvarande bibliotek: {name}", "components.Settings.copied": "API-nyckel kopierad till urklipp.", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON><PERSON><PERSON> skanning", "components.Settings.addsonarr": "Lägg till Sonarr-instans", "components.Settings.address": "<PERSON>ress", "components.Settings.addradarr": "Lägg till Radarr-instans", "components.Settings.activeProfile": "Aktiv profil", "components.Settings.SonarrModal.validationRootFolderRequired": "Du måste ange en rotmapp", "components.Settings.SonarrModal.validationProfileRequired": "Du måste ange en kvalitetsprofil", "components.Settings.SonarrModal.validationPortRequired": "Du måste ange ett giltigt portnummer", "components.Settings.SonarrModal.validationNameRequired": "Du måste ange ett instansnamn", "components.Settings.SonarrModal.validationHostnameRequired": "Du måste ange ett giltigt värdnamn eller en IP-adress", "components.Settings.SonarrModal.validationApiKeyRequired": "Du måste ange en API-nyckel", "components.Settings.SonarrModal.testFirstRootFolders": "Testa anslutningen för att ladda in rotmappar", "components.Settings.SonarrModal.testFirstQualityProfiles": "Testa anslutningen för att ladda in kvalitetsprofiler", "components.Settings.SonarrModal.ssl": "Använd SSL", "components.Settings.SonarrModal.servername": "Instansnamn", "components.Settings.SonarrModal.server4k": "4K-instans", "components.Settings.SonarrModal.selectRootFolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectQualityProfile": "Välj kvalitetsprofil", "components.Settings.SonarrModal.seasonfolders": "Säsongsmappar", "components.Settings.SonarrModal.rootfolder": "Rotmapp", "components.Settings.SonarrModal.qualityprofile": "Kvalitetsprofiler", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON> rot<PERSON>…", "components.Settings.SonarrModal.loadingprofiles": "Laddar kvalitetsprofiler…", "components.Settings.SonarrModal.hostname": "Värdnamn eller IP-adress", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON>-instans", "components.Settings.SonarrModal.defaultserver": "Standard-instans", "components.Settings.SonarrModal.createsonarr": "Lägg till ny Sonarr-instans", "components.Settings.SonarrModal.baseUrl": "URL-bas", "components.Settings.SonarrModal.apiKey": "API-nyckel", "components.Settings.SonarrModal.animerootfolder": "Anime-rot<PERSON><PERSON>", "components.Settings.SonarrModal.animequalityprofile": "Anime-kvalitetsprofil", "components.Settings.SonarrModal.add": "Lägg till instans", "components.Settings.SettingsAbout.version": "Version", "components.Settings.SettingsAbout.totalrequests": "Totalt antal förfrågningar", "components.Settings.SettingsAbout.totalmedia": "Totalt antal mediaobjekt", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub-diskussioner", "components.Settings.SettingsAbout.gettingsupport": "Få support", "components.Settings.RadarrModal.validationRootFolderRequired": "Du måste ange en rotmapp", "components.Settings.RadarrModal.validationProfileRequired": "Du måste välja en kvalitetsprofil", "components.Settings.RadarrModal.validationPortRequired": "Du måste ange ett giltigt portnummer", "components.Settings.RadarrModal.validationNameRequired": "Du måste ange ett instansnamn", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Du måste välja lägsta tillgänglighet", "components.Settings.RadarrModal.validationHostnameRequired": "Du måste ange ett giltigt värdnamn eller en IP-adress", "components.Settings.RadarrModal.validationApiKeyRequired": "Du måste ange en API-nyckel", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Anslutningen till Radarr lyckades!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Kunde inte ansluta till Radarr.", "components.Settings.RadarrModal.testFirstRootFolders": "Testa anslutningen för att ladda in rotmappar", "components.Settings.RadarrModal.testFirstQualityProfiles": "Testa anslutningen för att ladda in kvalitetsprofiler", "components.Settings.RadarrModal.ssl": "Använd SSL", "components.Settings.RadarrModal.servername": "Instansnamn", "components.Settings.RadarrModal.server4k": "4K-instans", "components.Settings.RadarrModal.selectRootFolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectQualityProfile": "Välj kvalitetsprofil", "components.Settings.RadarrModal.selectMinimumAvailability": "Ange lägsta tillgänglighet", "components.Settings.RadarrModal.rootfolder": "Rotmapp", "components.Settings.RadarrModal.qualityprofile": "Kvalitetsprofil", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.minimumAvailability": "Lägsta tillgänglighet", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON><PERSON><PERSON> rot<PERSON>…", "components.Settings.RadarrModal.loadingprofiles": "Laddar kvalitetsprofiler…", "components.Settings.RadarrModal.hostname": "Värdnamn eller IP-adress", "components.Settings.RadarrModal.editradarr": "<PERSON><PERSON>-instans", "components.Settings.RadarrModal.defaultserver": "Standard-instans", "components.Settings.RadarrModal.createradarr": "Lägg till ny Radarr-instans", "components.Settings.RadarrModal.baseUrl": "Bas-URL", "components.Settings.RadarrModal.apiKey": "API-nyckel", "components.Settings.RadarrModal.add": "Lägg till instans", "components.Settings.Notifications.webhookUrl": "Webhook-URL", "components.Settings.Notifications.validationSmtpPortRequired": "Du måste ange ett giltigt portnummer", "components.Settings.Notifications.validationSmtpHostRequired": "Du måste ange ett giltigt värdnamn eller en IP-adress", "components.Settings.Notifications.smtpPort": "SMTP-port", "components.Settings.Notifications.smtpHost": "SMTP-värd", "components.Settings.Notifications.emailsettingssaved": "E-postmeddelandeinställningar har sparats!", "components.Settings.Notifications.discordsettingsfailed": "Discords meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.emailsettingsfailed": "E-postmeddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.emailsender": "Avsändaradress", "components.Settings.Notifications.discordsettingssaved": "Discords meddelandeinställningar har sparats!", "components.Settings.Notifications.authUser": "SMTP-användarnamn", "components.Settings.Notifications.authPass": "SMTP-lösenord", "components.Settings.Notifications.agentenabled": "Aktivera agent", "components.Search.searchresults": "Sökresultat", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON>(er)", "components.RequestModal.seasonnumber": "Säsong {number}", "components.RequestModal.season": "Säsong", "components.RequestModal.requestfrom": "F<PERSON>rfrågan från {username} väntar på godkännande.", "components.RequestModal.requestadmin": "Denna förfrågan kommer att godkännas automatiskt.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> har begärts!", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> för <strong>{title}</strong> av<PERSON><PERSON><PERSON>.", "components.RequestModal.pendingrequest": "Väntande förfrågan", "components.RequestModal.numberofepisodes": "<PERSON><PERSON>", "components.RequestModal.cancel": "Avb<PERSON>t f<PERSON>", "components.RequestList.requests": "Förfrågningar", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {säsong} other {säsonger}}", "components.RequestCard.seasons": "{seasonCount, plural, one {säsong} other {säsonger}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {säsong} other {säsonger}}", "components.PersonDetails.ascharacter": "som {character}", "components.PersonDetails.appearsin": "Kan ses i", "components.MovieDetails.studio": "{studioCount, plural, one {studio} other {studior}}", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON> titlar", "components.MovieDetails.runtime": "{minutes} minuter", "components.MovieDetails.revenue": "Inkomster", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Utgivningsdatum} other {Utgivningsdatum}}", "components.MovieDetails.recommendations": "Rekommendationer", "components.MovieDetails.overview": "Beskrivning", "components.MovieDetails.overviewunavailable": "Ingen översikt tillgänglig.", "components.MovieDetails.originallanguage": "Originalspråk", "components.MovieDetails.cast": "Roller", "components.MovieDetails.budget": "Budget", "components.MovieDetails.MovieCast.fullcast": "Rollista", "components.Layout.UserDropdown.signout": "Logga ut", "components.Layout.Sidebar.users": "Användare", "components.Layout.Sidebar.settings": "Inställningar", "components.Layout.Sidebar.requests": "Förfrågningar", "components.Layout.Sidebar.dashboard": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "<PERSON><PERSON>k filmer och serier", "components.Discover.upcomingmovies": "Kommande filmer", "components.Discover.upcoming": "Kommande filmer", "components.Discover.trending": "Trendande", "components.Discover.recentrequests": "Senaste förfrågningar", "components.Discover.recentlyAdded": "<PERSON>yligen <PERSON>", "components.Discover.populartv": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "components.Discover.popularmovies": "Populära filmer", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCast.fullseriescast": "Rollista", "components.Setup.welcome": "Välkommen till Jellyseerr", "components.Setup.signinMessage": "Kom igång genom att logga in", "components.RequestModal.requestseasons": "Begär {seasonCount} {seasonCount, plural, one {säsong} other {säsonger}}", "pages.returnHome": "Gå tillbaka till startsidan", "pages.oops": "<PERSON><PERSON><PERSON>", "i18n.unavailable": "Otillgänglig", "i18n.tvshows": "Serier", "i18n.processing": "Behandlar", "i18n.pending": "Väntande", "i18n.partiallyavailable": "<PERSON><PERSON>", "i18n.movies": "Filmer", "i18n.deleting": "Tar bort…", "i18n.delete": "<PERSON> bort", "i18n.declined": "<PERSON><PERSON><PERSON>", "i18n.decline": "Neka", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.available": "Till<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.approved": "Godkänd", "i18n.approve": "Godkä<PERSON>", "components.UserList.userlist": "Användarlista", "components.UserList.userdeleteerror": "<PERSON><PERSON><PERSON> gick fel när användaren skulle tas bort.", "components.UserList.userdeleted": "Anvä<PERSON>re borttagen!", "components.UserList.user": "Användare", "components.UserList.totalrequests": "Förfrågningar", "components.UserList.role": "Roll", "components.UserList.plexuser": "Plex-användare", "components.UserList.deleteuser": "Ta bort användare", "components.UserList.deleteconfirm": "Är du säker på att du vill ta bort denna användare? All deras förfrågningsdata kommer att tas bort permanent.", "components.UserList.created": "<PERSON>ick med", "components.UserList.admin": "Administratör", "components.TvDetails.similar": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.showtype": "Serietyp", "components.TvDetails.recommendations": "Rekommendationer", "components.TvDetails.overviewunavailable": "Beskrivning saknas.", "components.TvDetails.overview": "Beskrivning", "components.TvDetails.originallanguage": "Originalspråk", "components.TvDetails.network": "{networkCount, plural, one {nätverk} other {nätverk}}", "components.TvDetails.cast": "Roller", "i18n.close": "Stäng", "components.Settings.SettingsAbout.timezone": "T<PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Stötta med en kopp kaffe", "components.Settings.SettingsAbout.Releases.viewongithub": "Visa på GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Visa ändringslogg", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version}-ändringslogg", "components.Settings.SettingsAbout.Releases.releases": "Versioner", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Versionsdata är för nä<PERSON>rande inte tillgänglig.", "components.Settings.SettingsAbout.Releases.latestversion": "Senaste versionen", "components.Settings.SettingsAbout.Releases.currentversion": "Aktuell", "components.UserList.importfrommediaserver": "Importera {mediaServerName}-användare", "components.UserList.importfromplex": "Importera Plex-användare", "components.UserList.importfromplexerror": "<PERSON><PERSON><PERSON> gick fel när Plex-användare skulle importeras.", "components.TvDetails.watchtrailer": "Visa trailer", "components.Settings.Notifications.allowselfsigned": "<PERSON><PERSON>t självsignerade certifikat", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Meddelandeinställningarna för Slack sparades!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Meddelandeinställningarna för S<PERSON>ck kunde inte sparas.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Aktivera <PERSON>", "components.MovieDetails.watchtrailer": "Visa trailer", "components.CollectionDetails.requestcollection": "<PERSON><PERSON><PERSON><PERSON>", "components.CollectionDetails.overview": "Översikt", "components.CollectionDetails.numberofmovies": "{count} filmer", "i18n.retry": "Försök igen", "i18n.requested": "<PERSON><PERSON><PERSON><PERSON>", "i18n.request": "<PERSON><PERSON><PERSON><PERSON>", "i18n.failed": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {användare} other {användare}} importerades!", "components.TvDetails.viewfullcrew": "Visa hela rollistan", "components.TvDetails.firstAirDate": "Första sändningsdatum", "components.TvDetails.TvCrew.fullseriescrew": "<PERSON><PERSON> roll<PERSON>", "components.StatusBadge.status4k": "4K {status}", "components.Settings.SettingsAbout.documentation": "Dokumentation", "components.Settings.Notifications.validationChatIdRequired": "Du måste ange ett giltigt chatt-ID", "components.Settings.Notifications.validationBotAPIRequired": "Du måste ange en bot-auktoriseringstoken", "components.Settings.Notifications.telegramsettingssaved": "Telegrams meddelandeinställningar har sparats!", "components.Settings.Notifications.telegramsettingsfailed": "Telegrams meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.senderName": "Avsändarens namn", "components.Settings.Notifications.chatId": "Chatt-<PERSON>", "components.Settings.Notifications.botAPI": "Botens auktoriseringstoken", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook-meddelandeinställningar har sparats!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook-meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Du måste ange en giltig JSON-nyttolast", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "<PERSON><PERSON><PERSON> för <PERSON>", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON-nyttolast återställd!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Återställ till standard", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON-nyttolast", "components.Settings.Notifications.NotificationsWebhook.authheader": "<PERSON><PERSON><PERSON>eringru<PERSON>k (header)", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aktivera Webhook", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Du måste ange en giltig användar- eller grupp<PERSON>l", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Du måste ange en giltig applikationstoken", "components.Settings.Notifications.NotificationsPushover.userToken": "Användar- <PERSON><PERSON> g<PERSON>", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Inställningarna för <PERSON>-meddelanden sparades!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Meddelandeinställningarna för <PERSON>over kunde inte sparas.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Aktivera <PERSON>", "components.Settings.Notifications.NotificationsPushover.accessToken": "Applikation-API-token", "components.RequestModal.pending4krequest": "Väntande 4K-förfrågan", "components.RequestList.sortModified": "<PERSON><PERSON>", "components.RequestList.sortAdded": "Senaste", "components.RequestList.showallrequests": "Visa alla förfrågningar", "components.RequestList.RequestItem.failedretry": "Något gick fel vid återförsök av förfrågan.", "components.RequestButton.viewrequest4k": "Visa 4K-förfrågan", "components.RequestButton.viewrequest": "Visa förfrågan", "components.RequestButton.requestmore4k": "Begär fler i 4K", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON><PERSON> fler", "components.RequestButton.declinerequests": "Neka {requestCount, plural, one {fö<PERSON><PERSON><PERSON><PERSON>} other {{requestCount} förfrågningar}}", "components.RequestButton.declinerequest4k": "Neka 4K-förfrågan", "components.RequestButton.declinerequest": "Neka förfrågan", "components.RequestButton.decline4krequests": "Neka {requestCount, plural, one {4K-förfr<PERSON><PERSON>} other {{requestCount} 4K-förfrågningar}}", "components.RequestButton.approverequests": "Godkänn {requestCount, plural, one {förf<PERSON><PERSON><PERSON>} other {{requestCount} förfrågningar}}", "components.RequestButton.approverequest4k": "Godkänn 4K-förfrågan", "components.RequestButton.approverequest": "Godkänn förfrågan", "components.RequestButton.approve4krequests": "Godkänn {requestCount, plural, one {4K-förfr<PERSON><PERSON>} other {{requestCount} 4K-förfrågningar}}", "components.NotificationTypeSelector.mediarequestedDescription": "Skicka meddelanden när användare skickar nya medieförfrågningar som kräver godkännande.", "components.NotificationTypeSelector.mediarequested": "Förfrågan väntar på godkännande", "components.NotificationTypeSelector.mediafailedDescription": "Skicka meddelanden när medieförfrågningar misslyckas att läggas till i Radarr eller Sonarr.", "components.NotificationTypeSelector.mediafailed": "Bearbetning av förfrågan misslyckades", "components.NotificationTypeSelector.mediaavailableDescription": "Skicka meddelanden när medieförfrågningar blir tillgängliga.", "components.NotificationTypeSelector.mediaavailable": "Förfrågan tillgänglig", "components.NotificationTypeSelector.mediaapprovedDescription": "<PERSON><PERSON>a meddelanden när medieförfrågningar godkänns manuellt.", "components.NotificationTypeSelector.mediaapproved": "Förfrågan godkänd", "components.MovieDetails.viewfullcrew": "Visa fullt filmteam", "components.MovieDetails.MovieCrew.fullcrew": "Filmteam", "components.UserList.userssaved": "Användarbehörigheter sparade!", "components.UserList.bulkedit": "Mass-redigering", "components.PermissionEdit.usersDescription": "Ge behörighet att hantera användare. Användare med denna behörighet kan inte ändra eller lägga till användare med adminrättigheter.", "components.PermissionEdit.users": "Hantera användare", "components.PermissionEdit.requestDescription": "Ge behörighet att skicka in förfrågningar för media som inte är 4K.", "components.PermissionEdit.request4kTvDescription": "Ge behörighet att skicka in förfrågningar för 4K-serier.", "components.PermissionEdit.request4kTv": "Göra 4K-serieförfrågningar", "components.PermissionEdit.request4kMoviesDescription": "Ge behörighet att skicka in förfrågningar för 4K-filmer.", "components.PermissionEdit.request4kMovies": "Göra 4K-filmförfrågningar", "components.PermissionEdit.request4kDescription": "Ge behörighet att skicka in förfrågningar för 4K-media.", "components.PermissionEdit.request4k": "Göra 4K-förfrågningar", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON> f<PERSON>ngar", "components.PermissionEdit.managerequestsDescription": "Ge behörighet att hantera medieförfrågningar. Alla förfrågningar gjorda av en användare med denna behörighet kommer automatiskt att godkännas.", "components.PermissionEdit.managerequests": "Hantera förfrågningar", "components.PermissionEdit.adminDescription": "Fullständig administratörsbehörighet. Överskrider alla andra beh<PERSON>righetskontroller.", "components.RequestModal.requesterror": "<PERSON><PERSON><PERSON> gick fel när förfrågan skickades.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Vi kunde inte automatiskt matcha denna serie. Välj rätt matchning nedan.", "i18n.experimental": "Experimentell", "components.RequestModal.autoapproval": "Automatiskt godkännande", "i18n.edit": "Rediger<PERSON>", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> för <strong>{title}</strong> har redigerats!", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> för <strong>{title}</strong> av<PERSON><PERSON><PERSON>.", "components.RequestModal.errorediting": "<PERSON>ågot gick fel vid redigering av förfrågan.", "components.RequestModal.AdvancedRequester.rootfolder": "Rotmapp", "components.RequestModal.AdvancedRequester.qualityprofile": "Kvalitetsprofil", "components.RequestModal.AdvancedRequester.destinationserver": "Destinationsserver", "components.RequestModal.AdvancedRequester.default": "{name} (standard)", "components.RequestModal.AdvancedRequester.animenote": "* Denna serie är en anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON>", "components.RequestBlock.server": "Destinationsserver", "components.RequestBlock.rootfolder": "Rotmapp", "components.RequestBlock.profilechanged": "Kvalitetsprofil", "components.MediaSlider.ShowMoreCard.seemore": "Visa fler", "components.UserList.validationpasswordminchars": "Lösenordet är för kort; det måste innehålla minst 8 tecken", "components.UserList.usercreatedsuccess": "Användare skapad!", "components.UserList.usercreatedfailed": "Någonting gick fel vid skapandet av användaren.", "components.UserList.passwordinfodescription": "Konfigurera en applikations-URL och aktivera e-postmeddelanden för att tillåta automatisk lösenordsgenerering.", "components.UserList.password": "L<PERSON>senord", "components.UserList.localuser": "Lokal användare", "components.UserList.email": "E-postadress", "components.UserList.creating": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.createlocaluser": "Skapa lokal användare", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "Generera lösenord automatiskt", "components.PersonDetails.crewmember": "Crew", "components.Login.validationemailrequired": "Du måste ange en giltig e-postadress", "components.Login.email": "E-postadress", "components.PermissionEdit.autoapproveSeriesDescription": "Ge automatiskt godkännande för serieförfrågningar som inte är 4K.", "components.PermissionEdit.autoapproveSeries": "Godkänn serier automatiskt", "components.PermissionEdit.autoapproveMoviesDescription": "Ge automatiskt godkännande för filmförfrågningar som inte är 4K.", "components.PermissionEdit.autoapproveMovies": "Godkänn filmer automatiskt", "components.PermissionEdit.autoapproveDescription": "Ge automatiskt godkännande för alla medieförfrågningar som inte är 4K.", "components.PermissionEdit.autoapprove": "Godkänn automatiskt", "components.PermissionEdit.advancedrequestDescription": "Ge behörighet att ändra avancerade alternativ för medieförfrågningar.", "components.PermissionEdit.advancedrequest": "Avancerade förfr<PERSON>gningar", "components.PermissionEdit.admin": "Admin", "components.NotificationTypeSelector.mediadeclinedDescription": "<PERSON><PERSON><PERSON> meddelanden när medieförfrågningar nekas.", "components.NotificationTypeSelector.mediadeclined": "Förfrågan nekad", "components.MovieDetails.markavailable": "Markera som till<PERSON>ä<PERSON>g", "components.MovieDetails.mark4kavailable": "Markera som tillgänglig i 4K", "components.Login.validationpasswordrequired": "Du måste ange ett lösenord", "components.Login.signinwithplex": "<PERSON><PERSON><PERSON><PERSON> ditt Plex-konto", "components.Login.signinwithoverseerr": "<PERSON><PERSON><PERSON><PERSON> ditt {applicationTitle}-konto", "components.Login.signinheader": "Logga in för att fortsätta", "components.Login.signingin": "Loggar in…", "components.Login.signin": "Logga in", "components.Login.password": "L<PERSON>senord", "components.Login.loginerror": "<PERSON><PERSON><PERSON> gick fel vid inloggningen.", "components.Login.forgotpassword": "Glömt lösenordet?", "components.Discover.discover": "<PERSON><PERSON><PERSON><PERSON>", "components.AppDataWarning.dockerVolumeMissingDescription": "Volymmonteringen för <code>{appDataPath}</code> är inte korrekt konfigurerad. All data kommer att rensas när containern stoppas eller startas om.", "components.ResetPassword.validationpasswordrequired": "Du måste ange ett lösenord", "components.ResetPassword.validationpasswordminchars": "Lösenordet är för kort; det måste innehålla minst 8 tecken", "components.ResetPassword.validationpasswordmatch": "Lösenorden måste vara samma", "components.ResetPassword.validationemailrequired": "Du måste ange en giltig e-postadress", "components.ResetPassword.resetpasswordsuccessmessage": "Lösenordet har återställts!", "components.ResetPassword.resetpassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> ditt l<PERSON>", "components.ResetPassword.requestresetlinksuccessmessage": "En länk för återställning av lösenord kommer att skickas till den angivna e-postadressen om den är kopplad till en giltig användare.", "components.ResetPassword.password": "L<PERSON>senord", "components.ResetPassword.gobacklogin": "Gå tillbaka till sidan för inloggning", "components.ResetPassword.emailresetlink": "<PERSON><PERSON><PERSON>tällningslänk", "components.ResetPassword.email": "E-postadress", "components.ResetPassword.confirmpassword": "Bekräfta lösenord", "components.TvDetails.nextAirDate": "Nästa sändningsdatum", "components.UserList.validationEmail": "E-postadress krävs", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Bas-URL får inte sluta med ett avslutande snedstreck", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Bas-URL måste börja med ett ledande snedstreck", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL-basen får inte sluta med ett snedstreck", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL-basen måste börja med ett inledande snedstreck", "components.Settings.Notifications.validationEmail": "Du måste ange en giltig e-postadress", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Du måste ange en giltig URL", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Du måste ange en giltig URL", "components.Settings.RadarrModal.validationApplicationUrl": "Du måste ange en giltig URL", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL får inte sluta med ett snedstreck", "components.Settings.SonarrModal.validationApplicationUrl": "Du måste ange en giltig URL", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL får inte sluta med ett snedstreck", "components.PermissionEdit.viewrequestsDescription": "Ge behörighet att visa medieförfrågningar inskickade av andra användare.", "components.PermissionEdit.viewrequests": "Visa förfrågningar", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON> som", "components.Setup.setup": "Installationsguide", "components.UserList.users": "Användare", "components.Search.search": "<PERSON>ö<PERSON>", "components.Settings.SettingsJobsCache.process": "Process", "components.Settings.SettingsJobsCache.command": "Kommando", "i18n.advanced": "Avancerad", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON> nu", "components.Settings.SettingsJobsCache.nextexecution": "<PERSON><PERSON><PERSON> k<PERSON>", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} startat.", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON>rr utför vissa underhållsuppgifter som schemalagda jobb automatiskt, men de kan också köras manuellt nedan. Att köra ett jobb manuellt ändrar inte schemat.", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} avbrutet.", "components.Settings.SettingsJobsCache.flushcache": "Rensa cache", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobb", "components.Settings.SettingsJobsCache.cachevsize": "Storleksvärde", "components.Settings.SettingsJobsCache.cachename": "Cache-namn", "components.Settings.SettingsJobsCache.cachemisses": "Missar", "components.Settings.SettingsJobsCache.cacheksize": "Nyckelstorlek", "components.Settings.SettingsJobsCache.cachekeys": "Totalt antal nycklar", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename}-cache rensad.", "components.Settings.SettingsJobsCache.cacheDescription": "Je<PERSON>seerr cachar förfrågningar till externa API:er för att optimera prestanda och undvika onödiga API-anrop.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SonarrModal.syncEnabled": "Aktivera skanning", "components.Settings.SonarrModal.externalUrl": "Extern URL", "components.Settings.RadarrModal.syncEnabled": "Aktivera skanning", "components.Settings.RadarrModal.externalUrl": "Extern URL", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Anslutningen till Sonarr lyckades!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Kunde inte ansluta till Sonarr.", "components.Settings.toastPlexRefreshSuccess": "Hämtning av serverlistan från Plex lyckades!", "components.Settings.toastPlexRefreshFailure": "Misslyckades med att hämta serverlistan från Plex.", "components.Settings.toastPlexRefresh": "Hämtar serverlistan från <PERSON>lex…", "components.Settings.toastPlexConnectingSuccess": "Anslutningen till Plex lyckades!", "components.Settings.toastPlexConnectingFailure": "<PERSON><PERSON><PERSON>ades med att ansluta till Plex.", "components.Settings.toastPlexConnecting": "<PERSON><PERSON>rs<PERSON><PERSON> ansluta till Plex…", "components.Settings.settingUpPlexDescription": "<PERSON><PERSON><PERSON> att konfigurera Plex kan du antingen ställa in inställningarna manuellt eller välja en server som hämtats via <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Tryck på knappen till höger om rullgardinsmenyn för att hämta listan över tillgängliga servrar.", "components.Settings.serverpresetRefreshing": "<PERSON><PERSON><PERSON><PERSON> servrar…", "components.Settings.serverpresetManualMessage": "Manuell konfiguration", "components.Settings.serverpresetLoad": "Klicka på knappen för att ladda in tillgängliga servrar", "components.Settings.serverpreset": "Server", "components.Settings.serverRemote": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.serverLocal": "lokal", "i18n.loading": "<PERSON><PERSON><PERSON>…", "components.UserProfile.recentrequests": "Senaste förfrågningar", "components.UserProfile.UserSettings.menuPermissions": "Behörigheter", "components.UserProfile.UserSettings.menuNotifications": "Meddelanden", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuChangePass": "L<PERSON>senord", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Behörighe<PERSON>a har sparats!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när inställningarna skulle sparas.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Behörigheter", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Lösenordet är för kort; det måste innehålla minst 8 tecken", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Du måste ange ett nytt lösenord", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Du måste ange ditt nuvarande l<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Lösenorden måste vara samma", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Du måste bekräfta det nya lösenordet", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Lösenordet har ändra<PERSON>!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när lösenordet skulle sparas.", "components.UserProfile.UserSettings.UserPasswordChange.password": "L<PERSON>senord", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Du har inte behörighet att ändra användarens lösenord.", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nytt lösenord", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Bekräfta lösenord", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Du måste ange ett giltigt chatt-ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Du måste ange ett giltigt användar-ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Starta en chatt</TelegramBotLink>, lägg till <GetIdBotLink>@get_id_bot</GetIdBotLink>, och kör kommandot <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chatt-<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "<PERSON><PERSON><PERSON> meddela<PERSON> utan ljud", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON> tyst", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Meddelandeinställningar", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Det <FindDiscordIdLink>flersiffriga ID-numret</FindDiscordIdLink> som är kopplat till ditt användarkonto", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Användar-ID", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Användare", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Inställningarna har sparats!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när inställningarna sparades.", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Roll", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrera innehåll efter regional tillgänglighet", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Region för Upptäck", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex-användare", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Ägare", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrera innehåll efter originalspråk", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Språk fö<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Lokal användare", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Allmänna inställningar", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Visningsnamn", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administratör", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Kontotyp", "components.UserProfile.ProfileHeader.userid": "Användar-ID: {userid}", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON> inställningar", "components.UserProfile.ProfileHeader.profile": "Visa profil", "components.UserProfile.ProfileHeader.joindate": "Medlem sedan {joindate}", "components.UserList.userfail": "<PERSON><PERSON><PERSON> gick fel när användarbehörigheter skulle sparas.", "components.UserList.sortRequests": "<PERSON><PERSON>", "components.UserList.sortDisplayName": "Visningsnamn", "components.UserList.sortCreated": "Gick med datum", "components.UserList.owner": "Ägare", "components.UserList.edituser": "Redigera an<PERSON>rbehörigheter", "components.UserList.accounttype": "<PERSON><PERSON>", "components.TvDetails.seasons": "{seasonCount, plural, one {# säsong} other {# säsonger}}", "components.Settings.webhook": "Webhook", "components.Settings.scanning": "Synkroniserar…", "components.Settings.scan": "Skanna bibliotek", "components.Settings.notificationAgentSettingsDescription": "Konfigurera och aktivera meddelandetjänster.", "components.Settings.menuUsers": "Användare", "components.Settings.email": "E-post", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Du måste välja en språkprofil", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Testa anslutningen för att ladda in språkprofiler", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>kprofil", "components.Settings.SonarrModal.loadinglanguageprofiles": "Laddar språkprofiler…", "components.Settings.SonarrModal.languageprofile": "Språkprofil", "components.Settings.SonarrModal.animelanguageprofile": "Anime-språkprofil", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigurera globala och standardinställningar för användare.", "components.Settings.SettingsUsers.userSettings": "Användarinställningar", "components.Settings.SettingsUsers.toastSettingsSuccess": "Användarinställningarna sparades!", "components.Settings.SettingsUsers.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när inställningarna skulle sparas.", "components.Settings.SettingsUsers.localLogin": "Aktivera lokal inloggning", "components.Settings.SettingsUsers.defaultPermissions": "Standardbehörigheter", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON>b", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr-skanning", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>-skanning", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Skanning av senast tillagda i Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Full biblioteksskanning för Plex", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Full Jellyfin-biblioteksskanning", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Skanning av nyligen tillagda objekt i Jellyfin", "components.Settings.SettingsJobsCache.download-sync-reset": "Återställ nedladdningssynkronisering", "components.Settings.SettingsJobsCache.download-sync": "Nedladdningssynkronisering", "components.Settings.SettingsAbout.preferredmethod": "F<PERSON><PERSON>raget", "components.Settings.Notifications.validationUrl": "Du måste ange en giltig URL", "components.Settings.Notifications.sendSilentlyTip": "<PERSON><PERSON><PERSON> meddela<PERSON> utan ljud", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON> tyst", "components.Settings.Notifications.botUsername": "Botens användarnamn", "components.Settings.Notifications.botAvatarUrl": "<PERSON><PERSON> avatar-URL", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Du måste ange en åtkomsttoken", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet-meddelandeinställningar har sparats!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet-meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aktivera Pushbullet", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Åtkomsttoken", "components.RequestModal.AdvancedRequester.languageprofile": "Språkprofil", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} av {user}", "components.RequestList.RequestItem.modified": "<PERSON><PERSON><PERSON>", "components.RequestBlock.requestoverrides": "Åsidosättningar för förf<PERSON>", "components.RegionSelector.regionServerDefault": "Standard ({region})", "components.RegionSelector.regionDefault": "Alla regioner", "components.PermissionEdit.autoapprove4kSeriesDescription": "Ge automatiskt godkännande för 4K-serieförfrågningar.", "components.PermissionEdit.autoapprove4kSeries": "Godkänn serier i 4K automatiskt", "components.PermissionEdit.autoapprove4kMoviesDescription": "Ge automatiskt godkännande för 4K-filmförfrågningar.", "components.PermissionEdit.autoapprove4kMovies": "Godkänn filmer i 4K automatiskt", "components.PermissionEdit.autoapprove4kDescription": "Ge automatiskt godkännande för alla 4K-medieförfrågningar.", "components.PermissionEdit.autoapprove4k": "Godkänn automatiskt 4K", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "<PERSON><PERSON>a meddelanden när användare skickar in nya medieförfrågningar som godkänns automatiskt.", "components.NotificationTypeSelector.mediaAutoApproved": "Förfrågan automatiskt godkänd", "components.Layout.UserDropdown.settings": "Inställningar", "components.Layout.UserDropdown.myprofile": "Profil", "components.Discover.upcomingtv": "Kommande serier", "components.Discover.StudioSlider.studios": "<PERSON>r", "components.Discover.NetworkSlider.networks": "Nätverk", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} serier", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}-serier", "components.Discover.DiscoverStudio.studioMovies": "{studio}-filmer", "components.Discover.DiscoverNetwork.networkSeries": "{network}-serier", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} filmer", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre}-filmer", "components.CollectionDetails.requestcollection4k": "Begär samling i 4K", "components.UserProfile.UserSettings.unauthorizedDescription": "Du har inte behörighet att ändra den här användarens inställningar.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Du kan inte ändra dina egna beh<PERSON>rig<PERSON>.", "components.Discover.MovieGenreList.moviegenres": "Filmgenrer", "components.Discover.MovieGenreSlider.moviegenres": "Filmgenrer", "components.Discover.TvGenreList.seriesgenres": "Seriegenrer", "components.Discover.TvGenreSlider.tvgenres": "Seriegenrer", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuter", "components.TvDetails.episodeRuntime": "Avsnittets speltid", "components.Settings.Notifications.pgpPrivateKeyTip": "Signera krypterade e-postmeddelanden med <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Privat PGP-nyckel", "components.Settings.Notifications.pgpPasswordTip": "Signera krypterade e-postmeddelanden med <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "PGP-lösenord", "components.RequestModal.alreadyrequested": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON><PERSON><PERSON>", "pages.somethingwentwrong": "<PERSON><PERSON><PERSON> gick fel", "pages.serviceunavailable": "Tjänsten är inte tillgänglig", "pages.pagenotfound": "<PERSON><PERSON> hittas inte", "pages.internalservererror": "Internt serverfel", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.usersettings": "Användarinställningar", "i18n.settings": "Inställningar", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "<PERSON><PERSON><PERSON> gick fel när lösenordet sparades. Har ditt nuvarande lösenord skrivits in korrekt?", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Meddelanden", "components.Settings.services": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.plex": "Plex", "components.Settings.notifications": "Meddelanden", "components.Settings.SettingsUsers.users": "Användare", "components.Settings.SettingsLogs.time": "Tidsstämpel", "components.Settings.SettingsLogs.showall": "Visa alla loggar", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.message": "Meddelande", "components.Settings.SettingsLogs.logsDescription": "Du kan också visa dessa loggar direkt via <code>stdout</code>, eller i <code>{appDataPath}/logs/jellyseerr.log</code>.", "components.Settings.SettingsLogs.label": "Etikett", "components.Settings.SettingsLogs.logs": "Loggar", "components.Settings.SettingsLogs.level": "Allvarlighetsgrad", "components.Settings.SettingsLogs.filterWarn": "Varning", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterDebug": "Felsök", "components.Settings.SettingsJobsCache.jobsandcache": "Jobb & Cache", "components.Settings.SettingsAbout.about": "Om", "components.ResetPassword.passwordreset": "Återställning av lösenord", "components.Settings.SettingsLogs.logDetails": "Logginformation", "components.Settings.SettingsLogs.extraData": "Ytterligare data", "components.Settings.SettingsLogs.copyToClipboard": "Kopiera till urklipp", "components.Settings.SettingsLogs.copiedLogMessage": "Ko<PERSON>rat loggmeddelande till urklipp.", "components.Settings.enablessl": "Använd SSL", "components.UserList.nouserstoimport": "Det finns inga Plex-användare att importera.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.alsoknownas": "<PERSON><PERSON> känd som: {names}", "i18n.delimitedlist": "{a}, {b}", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {Inga} other {<strong>#</strong>}} {type}{remaining, plural, one {förfrågan} other {förfrågningar}} kvar", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Du kan se en sammanfattning av denna användares förfrågningsgränser på deras <ProfileLink>profilsida</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Du kan se en sammanfattning av dina förfrågningsgränser på din <ProfileLink>profilsida</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Inte tillräckligt med säsongsförfrågningar kvar", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {filmer}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON> an<PERSON> får beg<PERSON> <strong>{limit}</strong> {type} var <strong>{days}</strong> dag(ar).", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON> får begära <strong>{limit}</strong> {type} var <strong>{days}</strong> dag(ar).", "components.QuotaSelector.unlimited": "Obegränsat", "i18n.view": "Visa", "i18n.tvshow": "Serie", "i18n.testing": "Testar…", "i18n.test": "Test", "i18n.status": "Status", "i18n.showingresults": "Visar <strong>{from}</strong> till <strong>{to}</strong> av <strong>{total}</strong> resultat", "i18n.saving": "<PERSON><PERSON>…", "i18n.save": "<PERSON><PERSON>", "i18n.resultsperpage": "Visa {pageSize} resultat per sida", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.request4k": "Begär i 4K", "i18n.previous": "Föregående", "i18n.notrequested": "<PERSON><PERSON> begärd", "i18n.noresults": "Inga resultat.", "i18n.next": "<PERSON><PERSON><PERSON>", "i18n.movie": "Film", "i18n.canceling": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.back": "Bakåt", "i18n.areyousure": "<PERSON>r du säker?", "i18n.all": "<PERSON>a", "components.UserProfile.unlimited": "Obegränsat", "components.UserProfile.totalrequests": "Totalt antal förfrågningar", "components.UserProfile.seriesrequest": "Serieförfrågningar", "components.UserProfile.requestsperdays": "{limit} kvar", "components.UserProfile.pastdays": "{type} (senaste {days} dagar)", "components.UserProfile.movierequests": "Filmförfrågningar", "components.UserProfile.limit": "{remaining} av {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "<PERSON><PERSON><PERSON><PERSON> för <PERSON>rågningar", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "<PERSON><PERSON><PERSON><PERSON> för filmförfrågningar", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Åsidosätt den globala gränsen", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Global gräns för serieförfrågningar", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Global gräns för filmförfrågningar", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Denna användare behöver ha minst <strong>{seasons}</strong> {seasons, plural, one {säsongsförfrågan} other {säsongsförfrågningar}} kvar för att kunna skicka in en förfrågan för denna serie.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {säsong} other {säsonger}}", "components.RequestModal.QuotaDisplay.season": "säsong", "components.RequestModal.QuotaDisplay.requiredquota": "<PERSON> behöver ha minst <strong>{seasons}</strong> {seasons, plural, one {säsongsförfrågan} other {säsongsförfrågningar}} kvar för att kunna skicka in en förfrågan för denna serie.", "components.TvDetails.originaltitle": "Originaltitel", "components.MovieDetails.originaltitle": "Originaltitel", "components.LanguageSelector.originalLanguageDefault": "<PERSON><PERSON>", "components.LanguageSelector.languageServerDefault": "Standard ({language})", "components.Settings.SonarrModal.testFirstTags": "Testa anslutningen för att ladda in taggar", "components.Settings.SonarrModal.tags": "Taggar", "components.Settings.SonarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.notagoptions": "<PERSON>ga taggar.", "components.Settings.SonarrModal.loadingTags": "<PERSON><PERSON><PERSON> taggar…", "components.Settings.SonarrModal.edit4ksonarr": "Redigera 4K Sonarr-instans", "components.Settings.SonarrModal.default4kserver": "Standard 4K-instans", "components.Settings.SonarrModal.create4ksonarr": "Lägg till ny 4K Sonarr-instans", "components.Settings.SonarrModal.animeTags": "Anime-taggar", "components.Settings.RadarrModal.testFirstTags": "Testa anslutningen för att ladda in taggar", "components.Settings.RadarrModal.tags": "Taggar", "components.Settings.RadarrModal.selecttags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.create4kradarr": "Lägg till ny 4K Radarr-instans", "components.Settings.RadarrModal.notagoptions": "<PERSON>ga taggar.", "components.Settings.RadarrModal.edit4kradarr": "Redigera 4K Radarr-instans", "components.Settings.RadarrModal.default4kserver": "Standard 4K-instans", "components.RequestModal.AdvancedRequester.tags": "Taggar", "components.RequestModal.AdvancedRequester.selecttags": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON>ga taggar.", "components.Settings.RadarrModal.loadingTags": "<PERSON><PERSON><PERSON> taggar…", "components.RequestList.RequestItem.mediaerror": "{mediaType} hittades inte", "components.RequestList.RequestItem.deleterequest": "Ta bort förf<PERSON>å<PERSON>", "components.RequestCard.mediaerror": "{mediaType} hittades inte", "components.RequestCard.deleterequest": "Ta bort förf<PERSON>å<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discords meddelandeinställningar kunde inte sparas.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Du måste ange en giltig publik PGP-nyckel", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegrams meddelandeinställningar har sparats!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegrams meddelandeinställningar kunde inte sparas.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Kryptera e-postmeddelanden med <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Publik PGP-nyckel", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "E-postmeddelandeinställningar har sparats!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "E-postmeddelandeinställningar kunde inte sparas.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-post", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discords meddelandeinställningar har sparats!", "components.Settings.Notifications.validationPgpPrivateKey": "Du måste ange en giltig privat PGP-nyckel", "components.Settings.Notifications.validationPgpPassword": "Du måste ange ett PGP-lösenord", "components.Settings.Notifications.botUsernameTip": "<PERSON><PERSON>t användare att även starta en chatt med din bot och konfigurera sina egna meddelanden", "components.RequestModal.pendingapproval": "<PERSON> förfrågan väntar på godkännande.", "components.RequestList.RequestItem.cancelRequest": "Avb<PERSON>t f<PERSON>", "components.NotificationTypeSelector.notificationTypes": "Meddelandetyper", "components.Layout.VersionStatus.streamstable": "Jellyseerr stable", "components.Layout.VersionStatus.streamdevelop": "Jellyseerr develop", "components.Layout.VersionStatus.outofdate": "F<PERSON>råldrad", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {ändring} other {ändringar}} efter", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "<PERSON>tt konto har för nä<PERSON>rande inget lösenord. Konfigurera ett lösenord nedan för att aktivera inloggning som en \"lokal användare\" med din e-postadress.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Det här användarkontot har för närvarande inget lösenord. Konfigurera ett lösenord nedan så att det här kontot kan logga in som en \"lokal användare.\"", "components.Settings.serviceSettingsDescription": "Kon<PERSON>gurera dina {serverType}-instanser nedan. Du kan ansluta flera {serverType}-instanser, men endast två av dem kan markeras som standard (en icke-4K och en 4K). Administratörer kan åsidosätta instansen som används för att bearbeta nya förfrågningar före godkännande.", "components.Settings.noDefaultServer": "Minst en {serverType}-instans måste markeras som standard för att {mediaType}-förfrågningar ska kunna bearbetas.", "components.Settings.noDefaultNon4kServer": "Om du bara har en enda {serverType}-instans för både icke-4K- och 4K-innehåll (eller om du bara har 4K-innehåll) bör din {serverType}-instans<strong> INTE </strong> betecknas som en 4K-instans.", "components.Settings.mediaTypeSeries": "serie", "components.Settings.mediaTypeMovie": "film", "components.Settings.SettingsAbout.uptodate": "Aktuell", "components.Settings.SettingsAbout.outofdate": "F<PERSON>råldrad", "components.UserList.autogeneratepasswordTip": "Skicka ett servergenererat lösenord via e-post till användaren", "i18n.retrying": "<PERSON><PERSON><PERSON><PERSON><PERSON> igen…", "components.Settings.serverSecure": "<PERSON><PERSON><PERSON>", "components.UserList.usercreatedfailedexisting": "Den angivna e-postadressen används redan.", "components.RequestModal.edit": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.enableSearch": "Aktivera automatisk sökning", "components.Settings.RadarrModal.enableSearch": "Aktivera automatisk sökning", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Webbpush", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Visningsspråk", "components.Settings.webpush": "Webbpush", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Webbpush-meddelandeinställningar har sparats!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Webbpush-meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aktivera <PERSON>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Du måste ange en giltig URL", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea-meddelandeinställningar har sparats!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea-meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Endast nödvändigt om du inte använder <code>standard</code>-profilen", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profilnamn", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aktivera LunaSea", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "<PERSON><PERSON><PERSON>-test<PERSON>…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet-testmeddelande kunde inte skickas.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea-testmeddelande skickat!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "<PERSON><PERSON><PERSON>-test<PERSON><PERSON>…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea-testmeddelande kunde inte skickas.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Webbpush-meddelandeinställningar har sparats!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Webbpush-meddelandeinställningar kunde inte sparas.", "components.Settings.noDefault4kServer": "En 4K {serverType}-instans måste markeras som standard för att användare ska kunna skicka in 4K {mediaType}-förfrågningar.", "components.Settings.is4k": "4K", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram-testmeddelande skickat!", "components.Settings.Notifications.toastTelegramTestSending": "<PERSON><PERSON><PERSON>-testmeddela<PERSON>…", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram-testmeddelande kunde inte skickas.", "components.Settings.Notifications.toastEmailTestSuccess": "E-posttestmeddelande skickat!", "components.Settings.Notifications.toastEmailTestSending": "kickar e-posttestmeddelande…", "components.Settings.Notifications.toastEmailTestFailed": "E-posttestmeddelande kunde inte skickas.", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord-testmeddelande skickat!", "components.Settings.Notifications.toastDiscordTestSending": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>…", "components.Settings.Notifications.toastDiscordTestFailed": "Discord-testmeddelande kunde inte skickas.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook-testmeddelande skickat!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "<PERSON><PERSON><PERSON>-test<PERSON>…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook-testmeddelande kunde inte skickas.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Webbpush-testmeddelande skickat!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Webbpush-testmeddelande kunde inte skickas.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack-testmeddelande skickat!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "<PERSON><PERSON><PERSON> test<PERSON><PERSON> via Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack-testmeddelandet kunde inte skickas.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover-test<PERSON><PERSON>nde skickat!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "<PERSON><PERSON><PERSON> test<PERSON><PERSON> via Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover-testmeddelandet kunde inte skickas.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet-testmeddelande skickat!", "components.Settings.SettingsUsers.newPlexLoginTip": "Tillåt {mediaServerName}-användare att logga in utan att först importeras", "components.Settings.SettingsUsers.newPlexLogin": "Aktivera ny {mediaServerName}-inloggning", "components.PermissionEdit.requestTvDescription": "Ge behörighet att skicka in förfrågningar för serier som inte är 4K.", "components.PermissionEdit.requestTv": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.requestMoviesDescription": "Ge behörighet att skicka in förfrågningar för filmer som inte är 4K.", "components.PermissionEdit.requestMovies": "Göra filmförfrågningar", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Standard ({language})", "components.Settings.Notifications.webhookUrlTip": "Skapa en <DiscordWebhookLink>webhook-integration</DiscordWebhookLink> på din server", "components.Settings.Notifications.encryptionTip": "I de flesta fall använder implicit TLS port 465 och STARTTLS använder port 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Använd alltid STARTTLS", "components.Settings.Notifications.encryptionNone": "Ingen", "components.Settings.Notifications.encryptionImplicitTls": "<PERSON>v<PERSON>nd implicit TLS", "components.Settings.Notifications.encryptionDefault": "Använd STARTTLS om tillgängligt", "components.Settings.Notifications.encryption": "Krypteringsmetod", "components.Settings.Notifications.chatIdTip": "Starta en chatt med din bot, l<PERSON>gg till <GetIdBotLink>@get_id_bot</GetIdBotLink>, och kör kommandot <code>/my_id</code>", "components.Settings.Notifications.botApiTip": "<CreateBotLink>S<PERSON><PERSON> en bot</CreateBotLink> för användning med Jellyseerr", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Skapa en <WebhookLink>Inkommande Webhook</WebhookLink>-integration", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Din <UsersGroupsLink>användare eller gruppidentifierare</UsersGroupsLink> (30 tecken)", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registrera en applikation</ApplicationRegistrationLink> för användning med Jellyseerr", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "<PERSON><PERSON><PERSON> en token fr<PERSON>n dina <PushbulletSettingsLink>kontoinställningar</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON>- <PERSON><PERSON> enhetsbaserade <LunaSeaLink>webhook-URL för medd<PERSON></LunaSeaLink>", "components.DownloadBlock.estimatedtime": "Beräknad {time}", "components.Settings.webAppUrlTip": "Alternativt skicka användare till den lokala webbappen istället för den \"hostade\" webbappen", "components.Settings.webAppUrl": "<WebAppLink>Webbapp</WebAppLink>-URL", "components.UserList.localLoginDisabled": "Inställningen <strong>Aktivera lokal inloggning</strong> är för närvarande inaktiverad.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "<PERSON><PERSON><PERSON> att kunna ta emot webbpushmeddelanden måste Jellyseerr servas över HTTPS.", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.failedretry": "Något gick fel vid återförsök av förfrågan.", "components.Settings.SettingsUsers.localLoginTip": "Till<PERSON>t användare att logga in med sin e-postadress och lösenord", "components.Settings.SettingsUsers.defaultPermissionsTip": "Inledande behörigheter som tilldelas nya användare", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {säsong} other {säsonger}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {filmer}}", "components.QuotaSelector.days": "{count, plural, one {dag} other {dagar}}", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Du måste välja minst en meddelandetyp", "components.NotificationTypeSelector.usermediarequestedDescription": "Få meddelanden när andra användare skickar nya medieförfrågningar som kräver godkännande.", "components.NotificationTypeSelector.usermediafailedDescription": "Få meddelanden när medieförfrågningar misslyckas att läggas till i Radarr eller Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Få meddelanden när dina medieförfrågningar nekas.", "components.NotificationTypeSelector.usermediaavailableDescription": "Få meddelanden när dina medieförfrågningar blir tillgängliga.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Få meddelanden när dina medieförfrågningar godkänn<PERSON>.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Få meddelanden när andra användare skickar nya medieförfrågningar som automatiskt godkänns.", "components.Settings.SettingsAbout.betawarning": "Detta är BETA-mjukvara. Funktioner kan vara trasiga och/eller instabila. Vänligen rapportera eventuella problem på GitHub!", "components.Layout.LanguagePicker.displaylanguage": "Visningsspråk", "components.MovieDetails.showmore": "Visa mer", "components.MovieDetails.showless": "Visa mindre", "components.TvDetails.streamingproviders": "Streamas för n<PERSON>rande på", "components.MovieDetails.streamingproviders": "Streamas nu på", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON> f<PERSON><PERSON>s", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Varje {jobScheduleHours, plural, one {timme} other {{jobScheduleHours} timmar}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Varje {jobScheduleMinutes, plural, one {minut} other {{jobScheduleMinutes} minuter}}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "<PERSON><PERSON><PERSON> gick fel när jobbet skulle sparas.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Jobbet har redigerats!", "components.Settings.SettingsAbout.runningDevelop": "<PERSON> k<PERSON> <code>develop</code>-g<PERSON><PERSON> av <PERSON><PERSON><PERSON>, vilket endast rekommenderas för de som bidrar till utveckling eller hjälper till med testning av den senaste versionen.", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "<PERSON>r du säker på att du vill ta bort den här kommentaren?", "components.IssueDetails.IssueComment.delete": "Ta bort kommentar", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON> kommentar", "components.IssueDetails.IssueComment.postedby": "Publicerad {relativeTime} av {username}", "components.IssueDetails.IssueComment.postedbyedited": "Publicerad {relativeTime} av {username} (redigerad)", "components.IssueDetails.IssueComment.validationComment": "Du måste skriva ett meddelande", "components.IssueDetails.IssueDescription.deleteissue": "Ta bort ärende", "components.IssueDetails.allepisodes": "Alla avsnitt", "components.IssueDetails.closeissue": "Stäng ärende", "components.IssueDetails.closeissueandcomment": "Stäng med kommentar", "components.IssueDetails.IssueDescription.description": "Beskrivning", "components.IssueDetails.IssueDescription.edit": "<PERSON><PERSON><PERSON> be<PERSON>", "components.IssueDetails.allseasons": "<PERSON><PERSON>", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.episode": "Avsnitt {episodeNumber}", "components.IssueDetails.issuepagetitle": "Ärende", "components.IssueDetails.issuetype": "<PERSON><PERSON>", "components.IssueDetails.lastupdated": "Senast uppdaterad", "components.IssueDetails.openinarr": "Öppna i {arr}", "components.IssueDetails.problemepisode": "Ber<PERSON>rt avsnitt", "components.IssueDetails.toasteditdescriptionfailed": "Något gick fel vid redigering av ärendebeskrivningen.", "components.IssueDetails.toastissuedeletefailed": "<PERSON><PERSON>got gick fel vid borttagning av ärendet.", "components.IssueDetails.toaststatusupdatefailed": "Något gick fel vid uppdatering av ärendestatus.", "components.IssueDetails.unknownissuetype": "Okä<PERSON>", "components.IssueDetails.openedby": "#{issueId} öppnades {relativeTime} av {username}", "components.IssueDetails.openin4karr": "Öppna i 4K {arr}", "components.IssueDetails.play4konplex": "Spela i 4K på {mediaServerName}", "components.IssueDetails.playonplex": "<PERSON><PERSON><PERSON> på {mediaServerName}", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.reopenissue": "Återöppna ärende", "components.IssueDetails.reopenissueandcomment": "Återöppna med kommentar", "components.IssueDetails.season": "Säsong {seasonNumber}", "components.IssueDetails.toasteditdescriptionsuccess": "Ärendebeskrivning redigerad!", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {säsong} other {säsonger}}", "components.IssueList.IssueItem.unknownissuetype": "Okä<PERSON>", "components.IssueList.IssueItem.openeduserdate": "{date} av {user}", "components.IssueList.IssueItem.viewissue": "Visa ärende", "components.IssueList.showallissues": "Visa alla ärenden", "components.IssueDetails.deleteissue": "Ta bort ärende", "components.IssueDetails.deleteissueconfirm": "Är du säker på att du vill ta bort det här ärendet?", "components.IssueList.IssueItem.opened": "Öppnat", "components.IssueDetails.leavecomment": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.issues": "Ärenden", "components.IssueDetails.nocomments": "<PERSON>ga kommentarer.", "components.IssueDetails.toaststatusupdated": "Ärendestatus uppdaterad!", "components.IssueDetails.toastissuedeleted": "Ärende borttaget!", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {avsnitt} other {avsnitt}}", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON>", "components.IssueList.IssueItem.problemepisode": "Ber<PERSON>rt avsnitt", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Ärenderapport för <strong>{title}</strong> inskickad!", "components.ManageSlideOver.tvshow": "serie", "components.ManageSlideOver.openarr4k": "Öppna i 4K {arr}", "components.IssueModal.CreateIssueModal.reportissue": "Rapportera ett problem", "components.IssueModal.CreateIssueModal.season": "Säsong {seasonNumber}", "components.IssueModal.issueOther": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.manageissuesDescription": "Ge behörighet att hantera medieärenden.", "components.IssueModal.CreateIssueModal.toastFailedCreate": "<PERSON><PERSON><PERSON> gick fel när ärendet skulle skickas.", "components.IssueModal.CreateIssueModal.submitissue": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.movie": "film", "components.IssueModal.CreateIssueModal.whatswrong": "Vad är fel?", "components.IssueModal.issueAudio": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.issues": "Ärenden", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Du måste ange en beskrivning", "components.IssueModal.issueSubtitles": "Undertext", "components.IssueModal.issueVideo": "Video", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Användar- <PERSON><PERSON> g<PERSON>", "components.ManageSlideOver.openarr": "Öppna i {arr}", "components.ManageSlideOver.manageModalTitle": "Hantera {mediaType}", "components.ManageSlideOver.mark4kavailable": "Markera som tillgänglig i 4K", "components.NotificationTypeSelector.adminissuecommentDescription": "Få meddelanden när andra användare kommenterar ärenden.", "components.ManageSlideOver.manageModalRequests": "Förfrågningar", "components.PermissionEdit.manageissues": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover-meddelandeinställningar kunde inte sparas.", "components.ManageSlideOver.markavailable": "Markera som till<PERSON>ä<PERSON>g", "components.NotificationTypeSelector.issuecomment": "Ärendekommentar", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON><PERSON> meddelanden när ärenden får nya kommentarer.", "components.PermissionEdit.createissuesDescription": "Ge behörighet att skapa medieärenden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Din 30 tecken långa <UsersGroupsLink>användar- eller gruppidentifierare</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registrera en applikation</ApplicationRegistrationLink> för användning med {applicationTitle}", "components.PermissionEdit.viewissuesDescription": "Ge behörighet att visa medieärenden skapade av andra användare.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Applikationens API-token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover-meddelandeinställningar har sparats!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Du måste ange en giltig applikationstoken", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Du måste ange en giltig användar- eller grupp<PERSON>l", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.toastviewissue": "Visa ärende", "components.NotificationTypeSelector.issuecreated": "Ärende skapat", "components.PermissionEdit.createissues": "Skapa ärenden", "components.PermissionEdit.viewissues": "Visa ärenden", "components.ManageSlideOver.manageModalClearMediaWarning": "* Detta kommer att ta bort all data för denna {mediaType} permanent, inklusive eventuella förfrågningar. Om detta objekt finns i ditt {mediaServerName}-bibliotek kommer mediainformationen att återskapas under nästa genomsökning.", "components.ManageSlideOver.manageModalNoRequests": "Inga förfrågningar.", "components.NotificationTypeSelector.userissueresolvedDescription": "Få meddelande när dina skapade ärenden har blivit lö<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Åtkomsttoken", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Du måste ange en åtkomsttoken", "components.IssueList.sortAdded": "Senaste", "components.IssueList.sortModified": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.allepisodes": "Alla avsnitt", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "Avsnitt {episodeNumber}", "components.IssueModal.CreateIssueModal.problemepisode": "Ber<PERSON>rt avsnitt", "components.IssueModal.CreateIssueModal.providedetail": "Beskriv problemet i detalj.", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Rensa data", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON>a meddelanden när ärenden skapas.", "components.NotificationTypeSelector.issueresolved": "Ä<PERSON><PERSON> lö<PERSON>", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON><PERSON> meddelanden när ärenden blir lö<PERSON>.", "components.NotificationTypeSelector.userissuecommentDescription": "Få meddelanden när ärenden du har skapat får nya kommentarer.", "components.NotificationTypeSelector.userissuecreatedDescription": "Få meddelanden när andra användare skapar ärenden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "<PERSON><PERSON><PERSON> en token fr<PERSON>n dina <PushbulletSettingsLink>kontoinställningar</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet-meddelandeinställningar kunde inte sparas.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet-meddelandeinställningar har sparats!", "i18n.open": "Öppna", "i18n.resolved": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalIssues": "Öppna ärenden", "components.IssueModal.CreateIssueModal.extras": "Extramaterial", "components.NotificationTypeSelector.adminissuereopenedDescription": "Få meddelanden när ärenden återöppnas av andra användare.", "components.NotificationTypeSelector.issuereopened": "Ärende återöppnat", "components.NotificationTypeSelector.issuereopenedDescription": "<PERSON><PERSON>a meddelanden när ärenden återöppnas.", "components.NotificationTypeSelector.userissuereopenedDescription": "Få meddelanden när ärenden du har skapat återöppnas.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Få meddelanden när ärenden blir lösta av andra anvä<PERSON>re.", "components.MovieDetails.productioncountries": "Produktions{countryCount, plural, one {land} other {länder}}", "components.RequestModal.selectmovies": "Välj film(er)", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON><PERSON> till en kommentar…", "components.RequestModal.approve": "Godkänn förfrågan", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> för <strong>{title}</strong> godkänd!", "components.RequestModal.requestmovies": "Begär {count} {count, plural, one {film} other {filmer}}", "components.RequestModal.requestmovies4k": "Begär {count} {count, plural, one {film} other {filmer}} i 4K", "components.RequestModal.requestseasons4k": "Begär {seasonCount} {seasonCount, plural, one {säsong} other {säsonger}} i 4K", "components.Settings.RadarrModal.inCinemas": "På bio", "components.Settings.RadarrModal.released": "Släppt", "components.Settings.RadarrModal.announced": "Tillkännagiven", "components.TvDetails.productioncountries": "Produktions{countryCount, plural, one {land} other {länder}}", "components.Settings.Notifications.enableMentions": "Aktivera omnämnanden", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aktivera Gotify", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify-meddelandeinställningar kunde inte sparas.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify-meddelandeinställningar har sparats!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify-testmeddelande kunde inte skickas.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "<PERSON><PERSON><PERSON> test<PERSON><PERSON> via Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify-testmeddelande skickat!", "components.Settings.Notifications.NotificationsGotify.token": "Applikationstoken", "components.Settings.Notifications.NotificationsGotify.url": "Server-URL", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Du måste ange en applikationstoken", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Du måste välja minst en meddelandetyp", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Du måste ange en giltig URL", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL:en får inte sluta med ett snedstreck", "components.UserList.newplexsigninenabled": "Inställningen <strong>Aktivera ny Plex-inloggning</strong> är för närvarande aktiverad. Plex-användare med biblioteksåtkomst behöver inte importeras för att kunna logga in.", "i18n.import": "Importera", "i18n.importing": "Importerar…", "components.ManageSlideOver.alltime": "<PERSON>a tider", "components.ManageSlideOver.manageModalAdvanced": "Avancerad", "components.ManageSlideOver.playedby": "Spelad av", "components.Settings.toastTautulliSettingsFailure": "<PERSON><PERSON><PERSON> gick fel nä<PERSON>-inställningarna skulle sparas.", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Kanaltagg", "components.Settings.urlBase": "URL-bas", "components.Settings.validationApiKey": "Du måste ange en API-nyckel", "components.ManageSlideOver.manageModalMedia4k": "4K-media", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.markallseasons4kavailable": "Markera alla säsonger som tillgängliga i 4K", "components.ManageSlideOver.markallseasonsavailable": "Markera alla säsonger som tillgängliga", "components.ManageSlideOver.opentautulli": "Öppna i Tautulli", "components.ManageSlideOver.pastdays": "Senaste {days, number} dagarna", "components.Settings.tautulliSettings": "Tautulli-inställningar", "components.Settings.toastTautulliSettingsSuccess": "Tautulli-inställningarna har sparats!", "components.Settings.validationUrlBaseLeadingSlash": "URL-basen måste ha ett inledande snedstreck", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {uppspelning} other {uppspelningar}}", "components.Settings.validationUrl": "Du måste ange en giltig URL", "components.Settings.externalUrl": "Extern URL", "components.Settings.tautulliApiKey": "API-nyckel", "components.Settings.tautulliSettingsDescription": "Valfritt: Konfigurera inställningarna för <PERSON>-server. Jellyseerr hämtar visningshistorikdata för din Plex-media fr<PERSON><PERSON>.", "components.UserProfile.recentlywatched": "<PERSON><PERSON><PERSON><PERSON> sett", "components.Settings.validationUrlBaseTrailingSlash": "URL-basen får inte sluta med ett snedstreck", "components.Settings.validationUrlTrailingSlash": "URL får inte sluta med ett snedstreck", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Du måste ange ett giltigt Discord användar-ID", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord användar-ID", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Det <FindDiscordIdLink>flersiffriga ID-numret</FindDiscordIdLink> som är kopplat till ditt Discord-användarkonto", "components.Settings.SettingsAbout.appDataPath": "Datakatalog", "components.RequestBlock.languageprofile": "Språkprofil", "components.StatusChecker.reloadApp": "Ladda om {applicationTitle}", "i18n.restartRequired": "Omstart krävs", "components.Settings.deleteServer": "<PERSON><PERSON><PERSON> {serverType}-instans", "components.StatusChecker.appUpdated": "{applicationTitle} uppdaterad", "components.StatusChecker.appUpdatedDescription": "<PERSON><PERSON><PERSON> på knappen nedan för att ladda om applikationen.", "components.StatusChecker.restartRequired": "<PERSON>n behöver startas om", "components.StatusChecker.restartRequiredDescription": "Starta om servern för att verkställa uppdaterade inställningar.", "components.MovieDetails.digitalrelease": "Digital utgåva", "components.MovieDetails.physicalrelease": "Fysisk utgåva", "components.PermissionEdit.viewrecent": "Se nyligen tillagda", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON>", "components.RequestCard.tmdbid": "TMDB-ID", "components.Discover.plexwatchlist": "<PERSON>", "components.Discover.DiscoverWatchlist.watchlist": "Plex bevakningslista", "components.MovieDetails.reportissue": "Rapportera ett problem", "components.AirDateBadge.airedrelative": "<PERSON><PERSON><PERSON> {relativeTime}", "components.AirDateBadge.airsrelative": "Sänds {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Serieförfrågningar", "components.RequestCard.approverequest": "Godkänn förfrågan", "components.RequestCard.tvdbid": "TheTVDB-ID", "components.RequestBlock.approve": "Godkänn förfrågan", "components.RequestBlock.decline": "Neka förfrågan", "components.RequestBlock.delete": "Ta bort förf<PERSON>å<PERSON>", "components.RequestBlock.edit": "<PERSON><PERSON><PERSON>", "components.RequestBlock.lastmodifiedby": "Senast ändrad av", "components.RequestCard.declinerequest": "Neka förfrågan", "components.RequestCard.cancelrequest": "Avb<PERSON>t f<PERSON>", "components.MovieDetails.theatricalrelease": "Biopremiär", "components.PermissionEdit.autorequest": "Automatisk förfrågan", "components.MovieDetails.managemovie": "Hantera film", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Filmförfrågningar", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes publiksiffror", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.tmdbuserscore": "TMDB-användarbetyg", "components.NotificationTypeSelector.mediaautorequested": "Förfrågan automatiskt skickad", "components.NotificationTypeSelector.mediaautorequestedDescription": "Få meddelanden när nya medieförfrågningar automatiskt skickas för objekt på din bevakningslista.", "components.Discover.emptywatchlist": "Media som lagts till i din <PlexWatchlistSupportLink>Plex bevakningslista</PlexWatchlistSupportLink> visas här.", "components.Layout.UserDropdown.requests": "Förfrågningar", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# avsnitt} other {# avsnitt}}", "components.TvDetails.seasonstitle": "Säsonger", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Rensning av bildcache", "components.Settings.SettingsJobsCache.imagecache": "Bildcache", "components.Settings.SettingsJobsCache.imagecachecount": "Bilder cachade", "components.Settings.SettingsJobsCache.imagecachesize": "Total cache-storlek", "components.Settings.experimentalTooltip": "Att aktivera denna inställning kan orsaka oväntade problem i applikationen", "components.TitleCard.tmdbid": "TMDB-ID", "components.PermissionEdit.autorequestDescription": "Ge behörighet att automatiskt skicka in förfrågningar för media som inte är 4K via Plex bevakningslista.", "components.PermissionEdit.autorequestSeriesDescription": "Ge behörighet att automatiskt skicka in förfrågningar för serier som inte är 4K via Plex bevakningslista.", "components.PermissionEdit.autorequestSeries": "Automatisk förfrågan för serier", "components.RequestList.RequestItem.tmdbid": "TMDB-ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB-ID", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Synkronisering av bevakningslista från Plex", "components.Settings.advancedTooltip": "Om du konfigurerar den här inställningen felaktigt kan det leda till att saker inte fungerar korrekt", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON>rr måste startas om för att ändringar som rör den här inställningen ska träda i kraft", "components.TvDetails.reportissue": "Rapportera ett problem", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Automatisk förfrågan för serier", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Begär automatiskt serier på din <PlexWatchlistSupportLink>Plex bevakningslista</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Begär automatiskt filmer på din <PlexWatchlistSupportLink>Plex bevakningslista</PlexWatchlistSupportLink>", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktuell frekvens", "components.PermissionEdit.viewrecentDescription": "Ge tillstånd till att visa listan över nyligen tillagd media.", "components.StatusBadge.managemedia": "Hantera {mediaType}", "components.StatusBadge.playonplex": "<PERSON><PERSON><PERSON> upp på {mediaServerName}", "components.TitleCard.tvdbid": "TheTVDB-ID", "components.Settings.SettingsLogs.viewdetails": "Visa detaljer", "components.Settings.SettingsJobsCache.imagecacheDescription": "När aktiverat i inställningarna kommer Jellyseerr att använda proxy för och cachelagra bilder från förkonfigurerade externa källor. Cachelagrade bilder sparas i din konfigurationsmapp. Du hittar filerna i <code>{appDataPath}/cache/images</code>.", "components.TitleCard.cleardata": "Rensa data", "components.TitleCard.mediaerror": "{mediaType} hittades inte", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.tmdbuserscore": "TMDB-användarbetyg", "components.UserProfile.emptywatchlist": "Media som lagts till i din <PlexWatchlistSupportLink>Plex bevakningslista</PlexWatchlistSupportLink> visas här.", "components.UserProfile.plexwatchlist": "Plex bevakningslista", "components.TvDetails.Season.noepisodes": "Avsnittslistan är inte tillgänglig.", "components.PermissionEdit.viewwatchlistsDescription": "Ge behörighet till att visa andra använda<PERSON> {mediaServerName} bevakningslistor.", "components.PermissionEdit.autorequestMovies": "Automatisk förfrågan för filmer", "components.PermissionEdit.autorequestMoviesDescription": "Ge behörighet att automatiskt skicka in förfrågningar för filmer som inte är 4K via Plex bevakningslista.", "components.PermissionEdit.viewwatchlists": "Visa {mediaServerName} bevakningslista", "components.RequestModal.SearchByNameModal.nomatches": "Vi kunde inte hitta någon matchning för denna serie.", "components.RequestBlock.requestdate": "<PERSON><PERSON> för fö<PERSON>", "components.RequestBlock.requestedby": "Förfrågan av", "components.RequestCard.editrequest": "<PERSON><PERSON><PERSON>", "components.RequestModal.requestcollection4ktitle": "Begär samling i 4K", "components.RequestModal.requestcollectiontitle": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.requestmovie4ktitle": "Begär film i 4K", "components.RequestModal.requestmovietitle": "Begär film", "components.RequestModal.requestseries4ktitle": "Begär serie i 4K", "components.RequestModal.requestseriestitle": "Begär serie", "components.StatusBadge.openinarr": "Öppna i {arr}", "components.TvDetails.Season.somethingwentwrong": "<PERSON><PERSON><PERSON> gick fel när säsongsdata hämtades.", "components.TvDetails.manageseries": "<PERSON><PERSON>", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes Audience Score", "components.TvDetails.seasonnumber": "Säsong {seasonNumber}", "components.TvDetails.status4k": "4K {status}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Automatisk förfrågan för filmer", "components.Discover.DiscoverMovies.discovermovies": "Filmer", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularitet stigande", "components.Discover.DiscoverTv.discovertv": "Serier", "components.Discover.CreateSlider.nooptions": "Inga resultat.", "components.Discover.CreateSlider.searchStudios": "Sök studio…", "components.Discover.CreateSlider.addcustomslider": "Skapa anpassat bläddringsfält", "components.Discover.CreateSlider.addfail": "Det gick inte att skapa nytt bläddringsfält.", "components.Discover.CreateSlider.addSlider": "Lägg till bläddringsfält", "components.Discover.CreateSlider.providetmdbsearch": "Ange en sökfråga", "components.Discover.CreateSlider.validationTitlerequired": "Du måste ange ett namn.", "components.Discover.DiscoverSliderEdit.enable": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "components.Discover.FilterSlideover.clearfilters": "Rensa aktiva filter", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON>", "components.Discover.FilterSlideover.filters": "Filter", "components.Discover.FilterSlideover.firstAirDate": "Första sändningsdatum", "components.Discover.FilterSlideover.from": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle}-filmer", "components.Discover.moviegenres": "Filmgenrer", "components.Discover.networks": "Nätverk", "components.Settings.SettingsMain.apikey": "API-nyckel", "components.Settings.SettingsMain.applicationTitle": "Applikationstitel", "components.Settings.SettingsMain.applicationurl": "Applikations-URL", "components.Settings.SettingsMain.hideAvailable": "Dölj <PERSON>ä<PERSON>lig media", "components.Settings.SettingsMain.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när inställningarna skulle sparas.", "components.Settings.SettingsMain.toastSettingsSuccess": "Inställningarna har sparats!", "components.Settings.SettingsMain.validationApplicationTitle": "Du måste ange en applikationstitel", "components.Settings.SettingsMain.validationApplicationUrl": "Du måste ange en giltig URL", "components.Discover.tmdbmoviestreamingservices": "TMDB-filmstreamingtjänster", "components.Discover.tmdbnetwork": "TMDB-nätverk", "components.Discover.tmdbsearch": "TMDB-sök", "components.Discover.tmdbstudio": "TMDB-studio", "components.Discover.tmdbtvgenre": "TMDB-seriegenre", "components.Discover.tmdbtvkeyword": "TMDB-<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.tmdbtvstreamingservices": "TMDB-seriestreamingtjänster", "components.Layout.Sidebar.browsemovies": "Filmer", "components.Layout.Sidebar.browsetv": "Serier", "components.Selector.searchKeywords": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Selector.nooptions": "Inga resultat.", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON>r…", "components.Selector.searchStudios": "<PERSON><PERSON><PERSON> studior…", "components.Selector.showmore": "Visa mer", "components.Selector.starttyping": "Börja skriva för att söka.", "components.Settings.SettingsJobsCache.availability-sync": "Synkronisering av mediatillgänglighet", "components.Settings.SettingsMain.cacheImages": "Aktivera bildca<PERSON>", "components.Settings.SettingsMain.cacheImagesTip": "Cachelagring av bilder från externa källor (kräver en betydande mängd diskutrymme)", "components.Settings.SettingsMain.general": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.generalsettings": "Allmänna inställningar", "components.Settings.SettingsMain.generalsettingsDescription": "Konfigurera globala och standardinställningar fö<PERSON>.", "components.Settings.SettingsMain.locale": "Visningsspråk", "components.Settings.SettingsMain.originallanguage": "Språk fö<PERSON>", "components.Settings.SettingsMain.originallanguageTip": "Filtrera innehållet efter originalspråk", "components.Settings.SettingsMain.partialRequestsEnabled": "Till<PERSON>t partiella serieförfrågningar", "components.Settings.SettingsMain.toastApiKeyFailure": "<PERSON><PERSON><PERSON> gick fel när <PERSON>-nyckeln skulle genereras.", "components.Settings.SettingsMain.toastApiKeySuccess": "Ny API-nyckel genererades!", "components.Discover.resetwarning": "Återställer alla bläddringsfält till standard. <PERSON>ta raderar också alla användarskapade fält!", "components.Discover.stopediting": "<PERSON><PERSON>a redigera", "components.Discover.tmdbmoviegenre": "TMDB-filmgenre", "components.Discover.studios": "<PERSON>r", "components.Discover.tmdbmoviekeyword": "TMDB-filmnyckelord", "components.Discover.tvgenres": "Seriegenrer", "components.Discover.updatesuccess": "Anpassningsinställningarna för Upptäck har uppdaterats.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Varje {jobScheduleSeconds, plural, one {sekund} other {{jobScheduleSeconds} sekunder}}", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL får inte sluta med ett snedstreck", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# aktivt filter} other {# aktiva filter}}", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularitet fallande", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Utgivningsdatum stigande", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Utgivningsdatum fallande", "components.Discover.DiscoverMovies.sortTitleAsc": "Titel stigande", "components.Discover.DiscoverMovies.sortTitleDesc": "Titel fallande", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB-betyg stigande", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB-betyg fallande", "components.Discover.DiscoverSliderEdit.deletefail": "Det gick inte att ta bort bläddringsfältet.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Bläddringsfältet har tagits bort.", "components.Discover.DiscoverSliderEdit.remove": "<PERSON> bort", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# aktivt filter} other {# aktiva filter}}", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Första sändningsdatum stigande", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Första sändningsdatum fallande", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularitet stigande", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularitet fallande", "components.Discover.DiscoverTv.sortTitleAsc": "Titel stigande", "components.Discover.DiscoverTv.sortTitleDesc": "Titel fallande", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB-betyg stigande", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB-betyg fallande", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle}-serier", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# aktivt filter} other {# aktiva filter}}", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.originalLanguage": "Originalspråk", "components.Discover.FilterSlideover.ratingText": "<PERSON>yg mellan {minValue} och {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Utgivningsdatum", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minuters speltid", "components.Discover.FilterSlideover.runtime": "Speltid", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB-användarbetyg", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Media som lagts till i din <PlexWatchlistSupportLink>Plex bevakningslista</PlexWatchlistSupportLink> visas här.", "components.Discover.resetsuccess": "Anpassningarna för <PERSON> har återställts.", "components.Discover.resettodefault": "Återställ till standardinställningar", "components.Discover.updatefailed": "<PERSON><PERSON><PERSON> gick fel när inställningarna för anpassning av Upptäck skulle uppdateras..", "components.DownloadBlock.formattedTitle": "{titel}: säsong {seasonNumber}, avsnitt {episodeNumber}", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.RequestCard.unknowntitle": "<PERSON><PERSON><PERSON> titel", "components.RequestList.RequestItem.unknowntitle": "<PERSON><PERSON><PERSON> titel", "components.Discover.FilterSlideover.streamingservices": "Streamingtjänster", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.to": "<PERSON>", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON>yligen <PERSON>", "components.Discover.createnewslider": "Skapa nytt bläddringsfält", "components.Discover.customizediscover": "Anpass<PERSON>ptä<PERSON>", "components.Discover.resetfailed": "<PERSON><PERSON><PERSON> gick fel när anpassningarna för <PERSON> skulle återställas.", "components.Selector.showless": "Visa mindre", "components.Discover.CreateSlider.addsuccess": "Skapade nytt bläddringsfält och sparade inställningarna för anpassning av Upptäck.", "components.Discover.CreateSlider.editSlider": "Redigera bläddringsfält", "components.Discover.CreateSlider.editfail": "Det gick inte att redigera bläddringsfältet.", "components.Discover.CreateSlider.editsuccess": "Redigerat bläddringsfält och sparade inställningarna för anpassning av Upptäck.", "components.Discover.CreateSlider.needresults": "Du måste ha minst 1 resultat.", "components.Discover.CreateSlider.providetmdbgenreid": "Ange ett TMDB Genre-ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Ange ett TMDB nyckelords-ID", "components.Discover.CreateSlider.providetmdbnetwork": "Ange TMDB nätverks-ID", "components.Discover.CreateSlider.providetmdbstudio": "Ange TMDB Studio-ID", "components.Discover.CreateSlider.searchGenres": "Sök genre…", "components.Discover.CreateSlider.searchKeywords": "<PERSON><PERSON><PERSON><PERSON>…", "components.Discover.CreateSlider.slidernameplaceholder": "Bläddringsfältets namn", "components.Discover.CreateSlider.starttyping": "Börja skriva för att söka.", "components.Discover.CreateSlider.validationDatarequired": "Du måste ange ett värde.", "components.Discover.FilterSlideover.tmdbuservotecount": "Antal röster från TMDB-användare", "components.Discover.FilterSlideover.voteCount": "<PERSON><PERSON> r<PERSON><PERSON> mellan {minValue} och {maxValue}", "components.Settings.RadarrModal.tagRequests": "Tagga förfrågningar", "components.MovieDetails.imdbuserscore": "IMDB-användarbetyg", "components.Settings.SonarrModal.tagRequests": "Tagga förfrågningar", "components.Settings.RadarrModal.tagRequestsInfo": "Lägg automatiskt till en extra tagg med användarens ID och visningsnamn för den som gjort förfrågan", "components.Settings.SonarrModal.tagRequestsInfo": "Lägg automatiskt till en extra tagg med användarens ID och visningsnamn för den som gjort förfrågan", "i18n.collection": "<PERSON><PERSON>", "components.Layout.UserWarnings.passwordRequired": "Lösenord krävs.", "components.Login.description": "Eftersom det här är första gången du loggar in på {applicationName}, krävs det att en giltig e-postadress läggs till.", "components.Layout.UserWarnings.emailInvalid": "Ogiltig e-postadress.", "components.Login.initialsignin": "<PERSON><PERSON><PERSON>", "components.Login.initialsigningin": "An<PERSON><PERSON><PERSON>…", "components.Login.save": "<PERSON><PERSON><PERSON> till", "components.Login.saving": "<PERSON><PERSON><PERSON> till…", "components.Login.signinwithjellyfin": "<PERSON><PERSON><PERSON><PERSON> ditt {mediaServerName}-konto", "components.Login.title": "<PERSON><PERSON><PERSON> till e-postadress", "components.Login.username": "Användarnamn", "components.Login.validationEmailFormat": "Ogiltig e-postadress", "components.Login.validationemailformat": "Giltig e-postadress krävs", "components.Login.validationhostformat": "Giltig URL krävs", "components.Login.validationhostrequired": "URL för {mediaServerName} krävs", "components.Login.validationusernamerequired": "Användarnamn krävs", "components.ManageSlideOver.removearr4k": "Ta bort från 4K {arr}", "components.MovieDetails.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.openradarr": "Öppna film i Radarr", "components.MovieDetails.openradarr4k": "Öppna film i 4K Radarr", "components.MovieDetails.play4k": "Spela upp i 4K på {mediaServerName}", "components.Settings.Notifications.NotificationsPushover.sound": "Meddelandeljud", "components.Settings.jellyfinsettings": "{mediaServerName}-inställningar", "components.Settings.jellyfinSettings": "{mediaServerName}-inställningar", "components.Settings.jellyfinSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när {mediaServerName}-inställningarna sparades.", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName}-inställningarna sparades!", "components.Settings.jellyfinlibraries": "{mediaServerName}-bibliotek", "components.Settings.manualscanJellyfin": "Manuell biblioteksskanning", "components.Settings.save": "<PERSON><PERSON>", "components.Settings.saving": "<PERSON><PERSON>…", "components.Settings.syncing": "Synkroniserar", "components.Settings.timeout": "Timeout", "components.Setup.signin": "Logga in", "components.Setup.signinWithPlex": "<PERSON><PERSON> dina uppgifter för <PERSON>", "components.TitleCard.addToWatchList": "Lägg till i bevakningslistan", "components.TitleCard.watchlistCancel": "Bevaknings<PERSON><PERSON> fö<PERSON> <strong>{title}</strong> av<PERSON><PERSON><PERSON>.", "components.TitleCard.watchlistError": "Något gick fel. Försök igen.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong>  har lagts till i bevakningslistan!", "components.TvDetails.play4k": "Spela upp i 4K på {mediaServerName}", "components.UserList.mediaServerUser": "{mediaServerName}-användare", "components.UserList.noJellyfinuserstoimport": "Det finns inga {mediaServerName}-användare att importera.", "components.UserProfile.UserSettings.UserGeneralSettings.save": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "<PERSON><PERSON>…", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Meddelandeljud", "components.Login.credentialerror": "Användarnamnet eller lösenordet är felaktigt.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* <PERSON>ta kommer oåterkalleligt att ta bort denna {mediaType} fr<PERSON>n {arr}, inklusive alla filer.", "components.ManageSlideOver.removearr": "Ta bort från {arr}", "components.Settings.jellyfinsettingsDescription": "Konfigurera inställningarna för din {mediaServerName}-server. {mediaServerName} skannar dina {mediaServerName}-bibliotek för att se vilket innehåll som är tillgängligt.", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON>", "components.UserList.importfromJellyfin": "Importera {mediaServerName}-användare", "components.Layout.UserWarnings.emailRequired": "En e-postadress krävs.", "components.Login.emailtooltip": "<PERSON><PERSON><PERSON> behöver inte vara kopplad till din {mediaServerName}-instans.", "components.Login.validationEmailRequired": "Du måste ange en e-postadress", "components.Settings.jellyfinlibrariesDescription": "{mediaServerName}-biblioteken skannas efter titlar. Klicka på knappen nedan om inga bibliotek visas.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Enhetsstandard", "components.Settings.SonarrModal.seriesType": "Serie-typ", "components.Setup.signinWithJellyfin": "<PERSON><PERSON> dina uppgifter för <PERSON>", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> har tagits bort från bevakningslistan!", "components.TvDetails.play": "<PERSON><PERSON><PERSON> upp på {mediaServerName}", "components.UserList.importfromJellyfinerror": "<PERSON><PERSON><PERSON> gick fel när {mediaServerName}-anv<PERSON><PERSON><PERSON> skulle importeras.", "components.UserList.newJellyfinsigninenabled": "Inställningen <strong>Aktivera ny {mediaServerName}-inloggning</strong> är för närvarande aktiverad. {mediaServerName}-användare med biblioteksåtkomst behöver inte importeras för att kunna logga in.", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName}-användare", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Enhetens standard", "components.MovieDetails.play": "<PERSON><PERSON><PERSON> upp på {mediaServerName}", "components.Settings.manualscanDescriptionJellyfin": "Normalt kommer detta endast att köras en gång var 24:e timme. Je<PERSON>seerr kommer att kontrollera din {mediaServerName}-servers nyligen tillagda innehåll mer intensivt. Om detta är första gången du konfigurerar Je<PERSON><PERSON><PERSON>, rekommenderas en engångsskanning av hela biblioteket!", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.syncJellyfin": "Synkronisera bibliotek", "components.Setup.configuremediaserver": "Konfigurera Media Server", "components.UserProfile.localWatchlist": "Bevakningslista för {username}", "components.Settings.Notifications.userEmailRequired": "Kräv användarens e-postadress", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-post", "components.Login.back": "Gå tillbaka", "components.Login.hostname": "{mediaServerName}-URL", "components.Login.validationHostnameRequired": "Du måste ange ett giltigt värdnamn eller en IP-adress", "components.Login.invalidurlerror": "Kunde inte ansluta till {mediaServerName} server.", "components.Login.port": "Port", "components.Login.servertype": "Typ av server", "components.Login.urlBase": "Bas-URL", "components.Login.validationPortRequired": "Du måste ange ett giltigt portnummer", "components.Login.validationUrlBaseLeadingSlash": "Bas-URL måste börja med ett ledande snedstreck", "components.Login.validationUrlBaseTrailingSlash": "Bas-URL får inte sluta med ett avslutande snedstreck", "components.Login.adminerror": "Du måste använda ett administratörskonto för att logga in.", "components.Login.enablessl": "Använd SSL", "components.Discover.FilterSlideover.status": "Status", "component.BlacklistBlock.blacklistdate": "Svartlistad datum", "component.BlacklistBlock.blacklistedby": "Svartlistad av", "component.BlacklistModal.blacklisting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.DiscoverTvUpcoming.upcomingtv": "Kommande serier", "components.Selector.inProduction": "Under produktion", "components.Settings.OverrideRuleModal.conditionsDescription": "Specificerar villkor innan parameterändringar tillämpas. Varje fält måste valideras för att reglerna ska tillämpas (AND-operation). Ett fält anses vara verifierat om någon av dess egenskaper matchar (OR-operation).", "components.Settings.SonarrModal.animeSeriesType": "Anime-serietyp", "components.Settings.jellyfinSyncFailedGenericError": "Något gick fel vid synkronisering av bibliotek", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "<PERSON>e dina uppgifter för {mediaServerName} för att länka med {applicationName}.", "components.PermissionEdit.viewblacklistedItemsDescription": "Ge behörighet att visa svartlistad media.", "components.Settings.Notifications.validationWebhookRoleId": "Du måste ange ett giltigt Discord-roll-ID", "components.Settings.OverrideRuleModal.ruleUpdated": "Åsidosättningsregel uppdaterad!", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "Du har inte några externa konton länkade till ditt konto.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "Tråd-/ämnes-ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "Tråd-/ämnes-ID måste vara ett positivt heltal", "i18n.blacklistError": "Något gick fel. Försök igen.", "components.PermissionEdit.viewblacklistedItems": "Visa svartlistad media.", "components.Selector.canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.validationMessageThreadId": "Tråd-/ämnes-ID måste vara ett positivt heltal", "components.Settings.OverrideRuleModal.settingsDescription": "Anger vilka inställningar som kommer att ändras när ovanstående villkor uppfylls.", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Filtrera innehåll efter regional tillgänglighet", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "En annan användare har detta användarnamn. Du måste ange en e-postadress", "components.Setup.librarieserror": "Valideringen misslyckades. För att fortsätta, slå av/på biblioteken igen.", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "<PERSON><PERSON><PERSON> att använda IPv4-ad<PERSON><PERSON> först, istället för IPv6", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Inställningarna sparade!", "components.UserList.validationUsername": "Du måste ange ett användarnamn", "i18n.addToBlacklist": "Lägg till i svartlista", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> togs bort från svar<PERSON>.", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> togs bort från bevakningslistan!", "components.PermissionEdit.manageblacklistDescription": "Ge behörighet till att hantera svartlistad media.", "components.Settings.SettingsNetwork.networksettingsDescription": "Konfigurera nätverksinställningar för din Jellyseerr-instans.", "components.Settings.SettingsNetwork.trustProxyTip": "<PERSON><PERSON><PERSON> att korrekt registrera klienters IP-adresser när Je<PERSON> körs bakom en proxy", "components.Settings.apiKey": "API-nyckel", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Streamingregion", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Om din gruppchatt har ämnen aktiverade kan du ange ett tråd-/ämnes-ID här", "components.Settings.jellyfinSettingsDescription": "Val<PERSON>ritt: Konfigurera de interna och externa slutpunkterna för din {mediaServerName}-server. I de flesta fall skiljer sig den externa URL:en från den interna URL:en. En anpassad URL för återställning av lösenord kan också anges för {mediaServerName}-inloggning, om du vill omdirigera till en annan återställningssida för lösenord. Du kan även ändra API-nyckeln som tidigare genererades.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> har lagts till i bevakningslistan!", "components.Blacklist.mediaName": "<PERSON><PERSON>", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> är inte svartlistad.", "components.Blacklist.blacklistSettingsDescription": "Hantera svartlistad media.", "components.Blacklist.blacklistdate": "datum", "components.Blacklist.blacklistedby": "{date} av {user}", "components.Blacklist.blacklistsettings": "Inställningar för <PERSON>", "components.Blacklist.mediaTmdbId": "tmdb-id", "components.Blacklist.mediaType": "<PERSON><PERSON>", "components.PermissionEdit.blacklistedItems": "Svartlista media.", "components.RequestList.RequestItem.profileName": "Profil", "components.Settings.SettingsMain.discoverRegion": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "components.Layout.Sidebar.blacklist": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.loginwithapp": "Logga in med {appName}", "components.Login.noadminerror": "Ingen användare med adminrättigheter hittades på servern.", "components.Login.orsigninwith": "eller logga in med", "components.Login.validationUrlTrailingSlash": "URL får inte sluta med ett avslutande snedstreck", "components.Login.validationservertyperequired": "Välj typ av server", "components.MovieDetails.addtowatchlist": "Lägg till i bevakningslistan", "components.MovieDetails.watchlistError": "Något gick fel. Försök igen.", "components.MovieDetails.removefromwatchlist": "Ta bort från bevakning<PERSON>listan", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> har lagts till i bevakningslistan!", "components.PermissionEdit.blacklistedItemsDescription": "Ge behörighet till att svartlista media.", "components.PermissionEdit.manageblacklist": "Hantera s<PERSON>tl<PERSON>", "components.RequestList.RequestItem.removearr": "Ta bort från {arr}", "components.RequestList.sortDirection": "<PERSON><PERSON><PERSON><PERSON> sorteringsordning", "components.Selector.ended": "Avslutad", "components.Selector.pilot": "Pilot", "components.Selector.planned": "Planerad", "components.Selector.returningSeries": "Återkommande serie", "components.Selector.searchStatus": "Välj status...", "components.Selector.searchUsers": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.OverrideRuleModal.conditions": "Villkor", "components.Settings.Notifications.messageThreadIdTip": "Om din gruppchatt har ämnen aktiverade kan du ange ett tråd-/ämnes-ID här", "components.Settings.Notifications.messageThreadId": "Tråd-/ämnes-ID", "components.Settings.Notifications.webhookRoleId": "Meddelanderoll-ID", "components.Settings.Notifications.webhookRoleIdTip": "Roll-ID att nämna i webhookmeddelandet. Lämna tomt för att inaktivera omnämnanden", "components.Settings.OverrideRuleModal.create": "<PERSON><PERSON><PERSON> regel", "components.Settings.OverrideRuleModal.createrule": "Ny åsidosättningsregel", "components.Settings.OverrideRuleModal.editrule": "Redigera åsidosättningsregel", "components.Settings.OverrideRuleModal.genres": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.keywords": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.languages": "Språk", "components.Settings.OverrideRuleModal.notagoptions": "<PERSON>ga taggar.", "components.Settings.OverrideRuleModal.qualityprofile": "Kvalitetsprofil", "components.Settings.OverrideRuleModal.rootfolder": "Rotmapp", "components.Settings.OverrideRuleModal.ruleCreated": "Åsidosättningsregel skapad!", "components.Settings.OverrideRuleModal.selectQualityProfile": "Välj kvalitetsprofil", "components.Settings.OverrideRuleModal.selectRootFolder": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.selectService": "<PERSON><PERSON><PERSON><PERSON> tj<PERSON>nst", "components.Settings.OverrideRuleModal.selecttags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.service": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.serviceDescription": "<PERSON><PERSON><PERSON><PERSON> denna regel på den valda tjänsten.", "components.Settings.OverrideRuleModal.settings": "Inställningar", "components.Settings.OverrideRuleModal.tags": "Taggar", "components.Settings.OverrideRuleModal.users": "Användare", "components.Settings.OverrideRuleTile.conditions": "Villkor", "components.Settings.OverrideRuleTile.genre": "Genre", "components.Settings.OverrideRuleTile.keywords": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.language": "Språk", "components.Settings.OverrideRuleTile.qualityprofile": "Kvalitetsprofil", "components.Settings.OverrideRuleTile.rootfolder": "Rotmapp", "components.Settings.OverrideRuleTile.settings": "Inställningar", "components.Settings.OverrideRuleTile.tags": "Taggar", "components.Settings.OverrideRuleTile.users": "Användare", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex uppdateringstoken", "components.Settings.SettingsJobsCache.usersavatars": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> avatarer", "components.Settings.SettingsMain.discoverRegionTip": "Filtrera innehåll efter regional tillgänglighet", "components.Settings.SettingsMain.enableSpecialEpisodes": "<PERSON><PERSON><PERSON> förfrågningar om specialavsnitt", "components.Settings.SettingsMain.streamingRegion": "Streamingregion", "components.Settings.SettingsMain.streamingRegionTip": "Visa streamingtjänster efter regional tillgänglighet", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Avancerade nätverksinställningar", "components.Settings.SettingsNetwork.csrfProtection": "Aktivera CSRF-skydd", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "Aktivera INTE denna inställning om du inte vet vad du gör!", "components.Settings.SettingsNetwork.csrfProtectionTip": "Ställ in skrivskyddad åtkomst till externt API (kräver HTTPS)", "components.Settings.SettingsNetwork.docs": "dokumentation", "components.Settings.SettingsNetwork.forceIpv4First": "Tvinga IPv4 först", "components.Settings.SettingsNetwork.network": "Nätverk", "components.Settings.SettingsNetwork.networkDisclaimer": "Nätverksparametrar från container/system bör användas istället för dessa inställningar. Se {docs} för mer information.", "components.Settings.SettingsNetwork.networksettings": "Nätverksinställningar", "components.Settings.SettingsNetwork.proxyBypassFilter": "Adresser som ignoreras av proxyn", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Använd ',' som avgränsare och '*.' som jokertecken för subdomäner", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Kringgå proxy fö<PERSON> lo<PERSON>a adresser", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S)-proxy", "components.Settings.SettingsNetwork.proxyHostname": "Proxy-värdnamn", "components.Settings.SettingsNetwork.proxyPassword": "Proxy-lösenord", "components.Settings.SettingsNetwork.proxyPort": "Proxy-port", "components.Settings.SettingsNetwork.proxySsl": "Använd SSL för proxy", "components.Settings.SettingsNetwork.proxyUser": "Proxy-användarnamn", "components.Settings.SettingsNetwork.toastSettingsFailure": "<PERSON><PERSON><PERSON> gick fel när inställningarna skulle sparas.", "components.Settings.SettingsNetwork.trustProxy": "Aktivera proxystöd", "components.Settings.SettingsNetwork.validationProxyPort": "Du måste ange en giltig port", "components.Settings.SettingsUsers.loginMethods": "Inloggningsmetoder", "components.Settings.SettingsUsers.atLeastOneAuth": "Minst en inloggningsmetod måste väljas.", "components.Settings.SettingsUsers.loginMethodsTip": "Konfigurera inloggningsmetoder för användare.", "components.Settings.SettingsUsers.mediaServerLogin": "Aktivera inloggning för {mediaServerName}", "components.Settings.SettingsUsers.mediaServerLoginTip": "<PERSON><PERSON>t användare att logga in med sitt {mediaServerName}-konto", "components.Settings.addrule": "Ny åsidosättningsregel", "components.Settings.invalidurlerror": "<PERSON>nde inte ansluta till {mediaServerName}-server.", "components.Settings.jellyfinForgotPasswordUrl": "Glömt-lösenord-URL", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Inga bibliotek hittades", "components.Settings.menuNetwork": "Nätverk", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Anpassad inloggning stöds inte med automatisk gruppering av mappar", "components.Settings.overrideRules": "Åsidosättningsregler", "components.Settings.overrideRulesDescription": "Åsidosättningsregler låter dig specificera egenskaper som kommer att ersättas om en förfråga matchar regeln.", "components.Settings.scanbackground": "Skanningen kommer att köras i bakgrunden. Du kan fortsätta med installationen under tiden.", "components.Settings.tip": "Tips", "components.Setup.back": "Gå tillbaka", "components.Setup.configemby": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.configplex": "Konfigurera Plex", "components.Setup.servertype": "Välj typ av server", "components.Setup.signinWithEmby": "<PERSON><PERSON> dina uppgi<PERSON> fö<PERSON>", "components.Setup.subtitle": "Börja genom att välja din mediaserver", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> togs bort från bevakningslistan!", "components.TvDetails.watchlistError": "Något gick fel. Försök igen.", "components.TvDetails.addtowatchlist": "Lägg till i bevakningslistan", "components.TvDetails.removefromwatchlist": "Ta bort från bevakning<PERSON>listan", "components.UserList.username": "Användarnamn", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "<PERSON>ta konto är redan länkat till en {applicationName}-användare", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Kunde inte ansluta till {mediaServerName} med dina uppgifter", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "<PERSON><PERSON> okänt fel uppstod", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "L<PERSON>senord", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "Du måste ange ett lösenord", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "<PERSON><PERSON><PERSON> till…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "<PERSON><PERSON>nka {mediaServerName}-konto", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Användarnamn", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "Du måste ange ett användarnamn", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Region för Upptäck", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Visa streamingtjänster efter regional tillgänglighet", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Denna e-postadress används redan!", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Giltig e-postadress krävs", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "E-postadress krävs", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Kunde inte radera länkat konto.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "<PERSON><PERSON> okänt fel uppstod", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Länkade konton", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Dessa externa konton är länkade till ditt {applicationName}-konto.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "Du har inte behörighet till att ändra denna användares länkade konton.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Detta konto är redan länkat till ett Plex-konto", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Kunde inte ansluta till Plex med dina uppgifter", "components.UserProfile.UserSettings.menuLinkedAccounts": "Länkade konton", "i18n.blacklist": "<PERSON><PERSON><PERSON><PERSON>", "i18n.blacklistDuplicateError": "<strong>{title}</strong> finns redan i svartlistan.", "i18n.blacklistSuccess": "<strong>{title}</strong> har lagts till i svartlistan.", "i18n.blacklisted": "Svartlistad", "i18n.removefromBlacklist": "Ta bort från svar<PERSON>", "i18n.specials": "Specialare", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {användare} other {användare}} importerade!"}