name: Deploy to GitHub Pages

on:
  push:
    branches:
      - develop
    paths:
      - 'docs/**'
      - 'gen-docs/**'

jobs:
  build:
    name: Build Docusaurus
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Pnpm Setup
        uses: pnpm/action-setup@v4
        with:
          version: 9

      - name: Get pnpm store directory
        shell: sh
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: |
          cd gen-docs
          pnpm install --frozen-lockfile

      - name: Build website
        run: |
          cd gen-docs
          pnpm build

      - name: Upload Build Artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: gen-docs/build

  deploy:
    name: Deploy to GitHub Pages
    needs: build
    concurrency: build-deploy-pages

    # Grant GITHUB_TOKEN the permissions required to make a Pages deployment
    permissions:
      pages: write # to deploy to Pages
      id-token: write # to verify the deployment originates from an appropriate source

    # Deploy to the github-pages environment
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    runs-on: ubuntu-latest
    steps:
      # - name: Download Build Artifact
      #   uses: actions/download-artifact@v4
      #   with:
      #     name: docusaurus-build
      #     path: gen-docs/build

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
