import defineMessages from '@app/utils/defineMessages';

const globalMessages = defineMessages('i18n', {
  available: 'Available',
  partiallyavailable: 'Partially Available',
  deleted: 'Deleted',
  processing: 'Processing',
  unavailable: 'Unavailable',
  notrequested: 'Not Requested',
  requested: 'Requested',
  requesting: 'Requesting…',
  request: 'Request',
  request4k: 'Request in 4K',
  failed: 'Failed',
  pending: 'Pending',
  declined: 'Declined',
  approved: 'Approved',
  completed: 'Completed',
  movie: 'Movie',
  movies: 'Movies',
  collection: 'Collection',
  tvshow: 'Series',
  tvshows: 'Series',
  cancel: 'Cancel',
  canceling: 'Canceling…',
  approve: 'Approve',
  decline: 'Decline',
  delete: 'Delete',
  retry: 'Retry',
  retrying: 'Retrying…',
  view: 'View',
  deleting: 'Deleting…',
  test: 'Test',
  testing: 'Testing…',
  save: 'Save Changes',
  saving: 'Saving…',
  import: 'Import',
  importing: 'Importing…',
  close: 'Close',
  edit: 'Edit',
  areyousure: 'Are you sure?',
  back: 'Back',
  next: 'Next',
  previous: 'Previous',
  status: 'Status',
  all: 'All',
  experimental: 'Experimental',
  advanced: 'Advanced',
  restartRequired: 'Restart Required',
  loading: 'Loading…',
  settings: 'Settings',
  usersettings: 'User Settings',
  delimitedlist: '{a}, {b}',
  showingresults:
    'Showing <strong>{from}</strong> to <strong>{to}</strong> of <strong>{total}</strong> results',
  resultsperpage: 'Display {pageSize} results per page',
  noresults: 'No results.',
  open: 'Open',
  resolved: 'Resolved',
  blacklist: 'Blacklist',
  blacklisted: 'Blacklisted',
  blacklistSuccess: '<strong>{title}</strong> was successfully blacklisted.',
  blacklistError: 'Something went wrong. Please try again.',
  blacklistDuplicateError:
    '<strong>{title}</strong> has already been blacklisted.',
  removeFromBlacklistSuccess:
    '<strong>{title}</strong> was successfully removed from the Blacklist.',
  addToBlacklist: 'Add to Blacklist',
  removefromBlacklist: 'Remove from Blacklist',
  specials: 'Specials',
});

export default globalMessages;
