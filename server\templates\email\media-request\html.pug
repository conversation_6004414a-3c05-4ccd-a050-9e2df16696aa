doctype html
head
  meta(charset='utf-8')
  meta(name='x-apple-disable-message-reformatting')
  meta(http-equiv='x-ua-compatible' content='ie=edge')
  meta(name='viewport' content='width=device-width, initial-scale=1')
  meta(name='format-detection' content='telephone=no, date=no, address=no, email=no')
  link(href='https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap' rel='stylesheet' media='screen')
  style.
    .title:hover * {
    text-decoration: underline;
    }
    @media only screen and (max-width:600px) {
    table {
    font-size: 20px !important;
    width: 100% !important;
    }
    }
div(style='display: block; background-color: #111827; padding: 2.5rem 0;')
  table(style='margin: 0 auto; font-family: Inter, Arial, sans-serif; color: #fff; font-size: 16px; width: 26rem;')
    tr
      td(style="text-align: center;")
        if applicationUrl
          a(href=applicationUrl style='margin: 0 1rem;')
            img(src=applicationUrl +'/logo_full.png' style='width: 26rem; image-rendering: crisp-edges; image-rendering: -webkit-optimize-contrast;')
        else
          div(style='margin: 0 1rem 2.5rem; font-size: 3em; font-weight: 700;')
            | #{applicationTitle}
    if recipientName !== recipientEmail
      tr
        td(style='text-align: center;')
          div(style='margin: 1rem 0 0; font-size: 1.25em;')
            | Hi, #{recipientName.replace(/\.|@/g, ((x) => x + '\ufeff'))}!
    tr
      td(style='text-align: center;')
        div(style='margin: 1rem 0 0; font-size: 1.25em;')
          | #{body}
    tr
      td
        div(style='box-sizing: border-box; margin: 1.5rem 0 0; width: 100%; color: #fff; border-radius: .75rem; padding: 1rem; border: 1px solid rgb(100,100,100); background: linear-gradient(135deg, rgba(17,24,39,0.47) 0%, rgb(17,24,39) 75%), url(' + imageUrl + ') center 25%/cover')
          table(style='color: #fff; width: 100%;')
            tr
              td(style='vertical-align: top;')
                a(href=actionUrl style='display: block; max-width: 20rem; color: #fff; font-weight: 700; text-decoration: none; margin: 0 1rem 0.25rem 0; font-size: 1.3em; line-height: 1.25em; margin-bottom: 5px;' class='title')
                  | #{mediaName}
                div(style='overflow: hidden; text-overflow: ellipsis; white-space: nowrap; color: #d1d5db; font-size: .975em; line-height: 1.45em; padding-top: .25rem; padding-bottom: .25rem;')
                  span(style='display: block;')
                    b(style='color: #9ca3af; font-weight: 700;')
                      | Requested By&nbsp;
                    | #{requestedBy.replace(/\.|@/g, ((x) => x + '\ufeff'))}
                  each extra in mediaExtra
                    span(style='display: block;')
                      b(style='color: #9ca3af; font-weight: 700;')
                        | #{extra.name}&nbsp;
                      | #{extra.value}
              td(rowspan='2' style='width: 7rem;')
                a(style='display: block; width: 7rem; overflow: hidden; border-radius: .375rem;' href=actionUrl)
                  div(style='overflow: hidden; box-sizing: border-box; margin: 0px;')
                    img(alt='' src=imageUrl style='box-sizing: border-box; padding: 0px; border: none; margin: auto; display: block; min-width: 100%; max-width: 100%; min-height: 100%; max-height: 100%;')
            tr
              td(style='font-size: .85em; color: #9ca3af; line-height: 1em; vertical-align: bottom; margin-right: 1rem')
                span
                  | #{timestamp}
    if actionUrl
      tr
        td
          a(href=actionUrl style='display: block; margin: 1.5rem 3rem 0; text-decoration: none; font-size: 1.0em; line-height: 2.25em;')
            span(style='padding: 0.2rem; font-weight: 500; text-align: center; border-radius: 10px; background-color: rgb(99,102,241); color: #fff; display: block; border: 1px solid rgba(255,255,255,0.2);')
              | View Media in #{applicationTitle}
