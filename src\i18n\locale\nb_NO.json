{"components.Discover.popularmovies": "Populære Filmer", "components.Discover.populartv": "Populære Serier", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON> lagt til", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON>", "components.Discover.trending": "Trender", "components.Discover.upcoming": "Kommende Filmer", "components.Discover.upcomingmovies": "Kommende Filmer", "components.Layout.SearchInput.searchPlaceholder": "Søk i Filmer & TV-serier", "components.Layout.Sidebar.dashboard": "Utforsk", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Innstillinger", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Logg ut", "components.MovieDetails.budget": "Budsjett", "components.MovieDetails.cast": "Roller", "components.MovieDetails.originallanguage": "Originalspråk", "components.MovieDetails.overview": "Oversikt", "components.MovieDetails.overviewunavailable": "Informasjon utilgjengelig.", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Utgivelsesdato} other {Utgivelsesdatoer}}", "components.MovieDetails.revenue": "Inntekter", "components.MovieDetails.runtime": "{minutes} minutter", "components.MovieDetails.similar": "Lignende titler", "components.PersonDetails.appearsin": "<PERSON><PERSON> i", "components.PersonDetails.ascharacter": "som {character}", "components.RequestBlock.seasons": "{seasonCount, plural, one {Sesong} other {Sesonger}}", "components.RequestCard.seasons": "{seasonCount, plural, one {Sesong} other {Sesonger}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Sesong} other {Sesonger}}", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.numberofepisodes": "Antal<PERSON> episoder", "components.RequestModal.pendingrequest": "Ventende forespørsel", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> kanse<PERSON><PERSON>.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> forespurt!", "components.RequestModal.requestadmin": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON> vil bli godkjent automagisk.", "components.RequestModal.requestfrom": "{username} sin forespørsel venter på godk<PERSON>.", "components.RequestModal.requestseasons": "Fores<PERSON>ør {seasonCount} {seasonCount, plural, one {Sesong} other {Sesonger}}", "components.RequestModal.season": "Sesong", "components.RequestModal.seasonnumber": "Sesong {number}", "components.RequestModal.selectseason": "Velg sesong(er)", "components.Search.searchresults": "Søkeresultater", "components.Settings.Notifications.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.authPass": "SMTP Passord", "components.Settings.Notifications.authUser": "SMTP brukernavn", "components.Settings.Notifications.emailsender": "Avsenderadresse", "components.Settings.Notifications.smtpHost": "SMTP-vert", "components.Settings.Notifications.smtpPort": "SMTP-port", "components.Settings.Notifications.validationSmtpHostRequired": "Du må oppgi et gyldig vertsnavn eller en IP-adresse", "components.Settings.Notifications.validationSmtpPortRequired": "Du må oppgi et gyldig port-nummer", "components.Settings.Notifications.webhookUrl": "Webhook-nettadresse", "components.Settings.RadarrModal.add": "<PERSON><PERSON> til tjener", "components.Settings.RadarrModal.apiKey": "API-nøkkel", "components.Settings.RadarrModal.baseUrl": "URL Base", "components.Settings.RadarrModal.createradarr": "Legg til ny Radarr-tjener", "components.Settings.RadarrModal.defaultserver": "Standard tjener", "components.Settings.RadarrModal.editradarr": "Rediger Radarr-tjener", "components.Settings.RadarrModal.hostname": "Vertsnavn eller IP-adresse", "components.Settings.RadarrModal.minimumAvailability": "Minimumstilgjengelighet", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "Kvalitetsprofil", "components.Settings.RadarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectMinimumAvailability": "Velg minimumstilgjengelighet", "components.Settings.RadarrModal.selectQualityProfile": "Velg en kvalitetsprofil", "components.Settings.RadarrModal.selectRootFolder": "Legg til en grunn<PERSON>ppe", "components.Settings.RadarrModal.server4k": "4K-tjener", "components.Settings.RadarrModal.servername": "Tjenernavn", "components.Settings.RadarrModal.ssl": "Aktiver SSL", "components.Settings.RadarrModal.toastRadarrTestFailure": "Klarte ikke å koble til Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "<PERSON><PERSON><PERSON> til Radarr!", "components.Settings.RadarrModal.validationApiKeyRequired": "Du må oppgi en API-nøkkel", "components.Settings.RadarrModal.validationHostnameRequired": "Du må oppgi et gyldig vertsnavn eller IP-adresse", "components.Settings.RadarrModal.validationPortRequired": "Du må oppgi et gyldig port-nummer", "components.Settings.RadarrModal.validationProfileRequired": "Du må velge en kvalitetsprofil", "components.Settings.RadarrModal.validationRootFolderRequired": "Du må velge en grunn<PERSON>ppe", "components.Settings.SonarrModal.add": "<PERSON><PERSON> til tjener", "components.Settings.SonarrModal.apiKey": "API-nøkkel", "components.Settings.SonarrModal.baseUrl": "URL Base", "components.Settings.SonarrModal.createsonarr": "Legg til ny Sonarr-tjener", "components.Settings.SonarrModal.defaultserver": "Standard tjener", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON>r-t<PERSON>", "components.Settings.SonarrModal.hostname": "Vertsnavn eller IP-adresse", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.qualityprofile": "Kvalitetsprofil", "components.Settings.SonarrModal.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectQualityProfile": "Velg en kvalitetsprofil", "components.Settings.SonarrModal.selectRootFolder": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.server4k": "4K-tjener", "components.Settings.SonarrModal.servername": "Tjenernavn", "components.Settings.SonarrModal.ssl": "Aktiver SSL", "components.Settings.SonarrModal.validationApiKeyRequired": "Du må oppgi en API-nøkkel", "components.Settings.SonarrModal.validationHostnameRequired": "Du må oppgi et gyldig vertsnavn eller en IP-adresse", "components.Settings.SonarrModal.validationPortRequired": "Du må oppgi et gyldig port-nummer", "components.Settings.SonarrModal.validationProfileRequired": "Du må velge en kvalitetsprofil", "components.Settings.SonarrModal.validationRootFolderRequired": "Du må velge en grunn<PERSON>ppe", "components.Settings.activeProfile": "Aktiv profil", "components.Settings.addradarr": "<PERSON><PERSON> til <PERSON>-t<PERSON><PERSON>", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "<PERSON><PERSON> til Sonarr-tjener", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON><PERSON><PERSON> skanning", "components.Settings.copied": "API-n<PERSON>k<PERSON> kop<PERSON>t til utklippstavle.", "components.Settings.currentlibrary": "Nåværende bibliotek: {name}", "components.Settings.default": "Standard", "components.Settings.default4k": "Standard 4K", "components.Settings.deleteserverconfirm": "Er du sikker på at du vil slette denne tjeneren?", "components.Settings.hostname": "Vertsnavn eller IP-adresse", "components.Settings.librariesRemaining": "Bibliotek som gjenstår: {count}", "components.Settings.manualscan": "<PERSON>l skanning av Biblioteket", "components.Settings.manualscanDescription": "Normalt vil dette kjøres kun én gang i døgnet. <PERSON><PERSON><PERSON><PERSON> vil sjekke Plex oftere etter nylige tillagte titler. Hvis dette er din første gang du konfigurerer Plex vil en enkelt manuell biblioteksskanning anbefales!", "components.Settings.menuAbout": "Om", "components.Settings.menuGeneralSettings": "Generelt", "components.Settings.menuJobs": "Oppgaver & Mellomlager", "components.Settings.menuLogs": "<PERSON><PERSON>", "components.Settings.menuNotifications": "<PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "T<PERSON>nes<PERSON>", "components.Settings.notificationsettings": "V<PERSON>el-innstillinger", "components.Settings.notrunning": "K<PERSON><PERSON>rer ikke", "components.Settings.plexlibraries": "Plex-bibliotek", "components.Settings.plexlibrariesDescription": "Bibliotekene Jellyseerr skanner for media. Sett opp og lagre dine Plex-tilkoblingsinnstillinger; klikk deretter på knappen under hvis ingen bibliotek vises.", "components.Settings.plexsettings": "Plex-innstillinger", "components.Settings.plexsettingsDescription": "Sett opp innstillingene for din Plex-tjener. Jellyseerr vil skanne de valgte bibliotekene på din Plex-tjener med jevne mello<PERSON>rom for å se hvilket innhold som er tilgjengelig.", "components.Settings.port": "Port", "components.Settings.radarrsettings": "Radarr-innstillinger", "components.Settings.sonarrsettings": "Sonarr-innstillinger", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Start skanning", "components.Setup.configureservices": "<PERSON>t opp tjenester", "components.Setup.continue": "Fortsett", "components.Setup.finish": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON>…", "components.Setup.signinMessage": "Start med å logge inn på din Plex-konto", "components.Setup.welcome": "Velkommen til Jellyseerr", "components.TvDetails.cast": "Roller", "components.TvDetails.originallanguage": "Originalspråk", "components.TvDetails.overview": "Oversikt", "components.TvDetails.overviewunavailable": "Informasjon utilgjengelig.", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.similar": "Lignende Serier", "components.UserList.admin": "Administrator", "components.UserList.created": "Opprettet", "components.UserList.plexuser": "Plex-bruker", "components.UserList.role": "<PERSON><PERSON>", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserList.userlist": "Brukerliste", "i18n.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.approved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "Tilgjengelig", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.decline": "Avslå", "i18n.declined": "Avslått", "i18n.delete": "<PERSON><PERSON>", "i18n.movies": "Filmer", "i18n.partiallyavailable": "<PERSON><PERSON>", "i18n.pending": "Venter", "i18n.processing": "<PERSON><PERSON><PERSON>", "i18n.tvshows": "Serier", "i18n.unavailable": "Utilgjengelig", "pages.oops": "Oida", "pages.returnHome": "<PERSON><PERSON> hjem", "pages.errormessagewithcode": "{statusCode} - {error}", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "V<PERSON>el-innstillinger", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>ID-nummeret</FindDiscordIdLink> til din brukerkonto", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Bruker ID", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Innstillingene ble lagret!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Noe gikk galt ved lagring av innstillingene.", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrer innhold basert på regiontilgjengelighet", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Utforskelsesregion", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex-bruker", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrer innhold basert på originalspråk", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Utforskelsesspråk", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Lokal bruker", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "<PERSON><PERSON><PERSON> inn<PERSON>ill<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Generelt", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Visningsnavn", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Kontotype", "components.UserList.userssaved": "Brukertillatelsene ble lagret!", "components.UserList.users": "<PERSON><PERSON><PERSON>", "components.UserList.importfromplexerror": "Noe gikk galt ved importering av brukere fra Plex.", "components.UserList.importfromplex": "Importer brukere fra Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {ny bruker} other {nye brukere}} ble importert fra Plex!", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigurer globale og standard brukerinnstillinger.", "components.Settings.SettingsUsers.userSettings": "Brukerinnstillinger", "components.Settings.SettingsUsers.toastSettingsSuccess": "Brukerinnstillingene ble lagret!", "components.Settings.SettingsUsers.toastSettingsFailure": "Noe gikk galt ved lagring av innstillingene.", "components.Settings.SettingsUsers.localLogin": "Aktiver lokal innlogging", "components.Settings.SettingsUsers.defaultPermissions": "Standard tilganger", "components.PermissionEdit.viewrequestsDescription": "<PERSON>i tillatelse til å vise forespø<PERSON><PERSON> sendt av andre brukere.", "components.PermissionEdit.usersDescription": "Gi tilgang til å administrere brukere. Brukere med dette tilgangsnivået har ikke mulighet til å endre brukertilganger eller gi administratorrettigheter.", "components.PermissionEdit.users": "Administrer <PERSON>", "components.Layout.UserDropdown.settings": "Innstillinger", "components.Layout.UserDropdown.myprofile": "Profil", "components.Discover.upcomingtv": "Kommende TV-serier", "components.Discover.discover": "Utforsk", "components.Discover.TvGenreSlider.tvgenres": "TV-s<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "TV-s<PERSON><PERSON><PERSON>", "components.Discover.StudioSlider.studios": "Studio", "components.Discover.NetworkSlider.networks": "TV-nettverk", "components.Discover.MovieGenreSlider.moviegenres": "Filmsjan<PERSON><PERSON>", "components.Discover.MovieGenreList.moviegenres": "Filmsjan<PERSON><PERSON>", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Serier", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Serier", "components.Discover.DiscoverStudio.studioMovies": "{studio} Filmer", "components.Discover.DiscoverNetwork.networkSeries": "{network} Serier", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Filmer", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Filmer", "components.CollectionDetails.requestcollection4k": "Forespør samling i 4K", "components.CollectionDetails.requestcollection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.CollectionDetails.overview": "Oversikt", "components.CollectionDetails.numberofmovies": "{count} <PERSON>er", "components.UserProfile.ProfileHeader.profile": "Vis profil", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Nullstill", "components.TvDetails.TvCast.fullseriescast": "Alle Roller", "components.MovieDetails.MovieCast.fullcast": "Alle Roller", "components.TvDetails.viewfullcrew": "Vis hele <PERSON>", "components.TvDetails.TvCrew.fullseriescrew": "<PERSON><PERSON>", "components.MovieDetails.viewfullcrew": "Vis hele <PERSON>", "components.MovieDetails.MovieCrew.fullcrew": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "<PERSON><PERSON> ikke lagre instillingene for Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Innstillingene for Pushbullet ble lagret!", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Tilgangsnøkkel", "components.Search.search": "<PERSON><PERSON><PERSON>", "components.ResetPassword.validationpasswordrequired": "Du må oppgi et passord", "components.ResetPassword.validationpasswordminchars": "Passordet er for kort; det må bestå av minimum 8 tegn", "components.ResetPassword.validationpasswordmatch": "Passord må være like", "components.ResetPassword.validationemailrequired": "Du må oppgi en gyldig E-postadresse", "components.ResetPassword.resetpasswordsuccessmessage": "Passord nullstilt!", "components.ResetPassword.resetpassword": "<PERSON><PERSON><PERSON><PERSON> passord", "components.ResetPassword.requestresetlinksuccessmessage": "En passordnullstillingslenke vil bli sendt til E-postadressen dersom den er koblet mot en gyldig bruker.", "components.ResetPassword.passwordreset": "Nullstill Passord", "components.ResetPassword.password": "Passord", "components.ResetPassword.gobacklogin": "Gå tilbake til innloggingssiden", "components.ResetPassword.emailresetlink": "Send E-post med gjenopprettelseslink", "components.ResetPassword.email": "E-postadresse", "components.ResetPassword.confirmpassword": "Verifiser passord", "components.RequestModal.requesterror": "<PERSON>e gikk galt ved sending av forespø<PERSON>l.", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> for <strong>{title}</strong> kanse<PERSON><PERSON>.", "components.RequestModal.pending4krequest": "Ventende forespørsel i 4K", "components.RequestModal.errorediting": "Noe gikk galt under endring av forespørse<PERSON>.", "components.RequestModal.autoapproval": "Automagisk Godkjenning", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON><PERSON><PERSON><PERSON> som", "components.RequestModal.AdvancedRequester.qualityprofile": "Kvalitetsprofil", "components.RequestModal.AdvancedRequester.languageprofile": "Språk-profil", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (Standard)", "components.RequestModal.AdvancedRequester.animenote": "* Dette er en anime-serie.", "components.RequestModal.AdvancedRequester.advancedoptions": "Avansert", "components.RequestList.sortModified": "<PERSON><PERSON>", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON> til", "components.RequestList.showallrequests": "<PERSON><PERSON> <PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} av {user}", "components.RequestList.RequestItem.modified": "<PERSON><PERSON>", "components.RequestList.RequestItem.failedretry": "Noe gikk galt mens du prøvde forespørselen på nytt.", "components.RequestButton.viewrequest4k": "Vis forespørsel i 4K", "components.RequestButton.viewrequest": "<PERSON><PERSON>", "components.RequestButton.requestmore4k": "Forespør mer i 4K", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.declinerequests": "Avvis {requestCount, plural, one {<PERSON>espørsel} other {{requestCount} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>}}", "components.RequestButton.declinerequest4k": "<PERSON><PERSON>vis forespørsel i 4K", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.decline4krequests": "Avvis {requestCount, plural, one {Forespørsel i 4K} other {{requestCount} Forespørsler i 4K}}", "components.RequestButton.approverequests": "Godkjenn {requestCount, plural, one {Forespørsel} other {{requestCount} For<PERSON><PERSON><PERSON><PERSON><PERSON>}}", "components.RequestButton.approverequest4k": "Godkjenn forespørsel i 4K", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.approve4krequests": "Godkjenn {requestCount, plural, one {Forespørsel i 4K} other {{requestCount} Forespørsler i 4K}}", "components.RequestBlock.server": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.rootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.requestoverrides": "Overstyrer forespørsel", "components.RequestBlock.profilechanged": "Kvalitetsprofil", "components.RegionSelector.regionServerDefault": "Standard {{region}}", "components.RegionSelector.regionDefault": "Alle Regioner", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.crewmember": "Stab", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.alsoknownas": "Også kjent som: {names}", "components.PermissionEdit.viewrequests": "<PERSON><PERSON>", "components.PermissionEdit.requestDescription": "Gi tilgang til å forespørre matriale som ikke er i 4K.", "components.PermissionEdit.request4kTvDescription": "Gi tilgang til å forespørre serier i 4K.", "components.PermissionEdit.request4kTv": "Forespør Serier i 4K", "components.PermissionEdit.request4kMoviesDescription": "Gi tilgang til å forespørre filmer i 4K.", "components.PermissionEdit.request4kMovies": "Forespør Filmer i 4K", "components.PermissionEdit.request4kDescription": "Gi tillatelse til å forespørre matriale i 4K.", "components.PermissionEdit.request4k": "Forespør i 4K", "components.PermissionEdit.request": "Forespør", "components.PermissionEdit.managerequestsDescription": "Gi tilgang til å administrere forespørsler. Alle forespørsler utført av en bruker med denne tilgangen vil automagisk bli godkjent.", "components.PermissionEdit.managerequests": "Admini<PERSON><PERSON>", "components.PermissionEdit.autoapproveSeriesDescription": "Godkjenn serieforespørsler som ikke er i 4K automagisk.", "components.PermissionEdit.autoapproveSeries": "God<PERSON><PERSON>nn serieforespørsler automagisk", "components.PermissionEdit.autoapproveMoviesDescription": "Godkjenn filmforespørsler som ikke er i 4K automagisk.", "components.PermissionEdit.autoapproveMovies": "Godkjenn filmforespørsler automagisk", "components.PermissionEdit.autoapproveDescription": "Godkjenn alle forespørsler som ikke er i 4K automagisk.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Godkjenn serieforespørsler i 4K automagisk.", "components.PermissionEdit.autoapprove4kSeries": "Automagisk godkjenning av serier i 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Godkjenn filmforespørsler i 4K automagisk.", "components.PermissionEdit.autoapprove4kMovies": "Automagisk godkjenning av filmer i 4K", "components.PermissionEdit.autoapprove4kDescription": "Godkjenn alle forespørsler av 4K automagisk.", "components.PermissionEdit.autoapprove4k": "Godkjenn 4K Automagisk", "components.PermissionEdit.autoapprove": "Godkjenn Automagisk", "components.PermissionEdit.advancedrequestDescription": "<PERSON>i tilgang til å endre avanserte forespørsler.", "components.PermissionEdit.advancedrequest": "Avanserte Forespørsler", "components.PermissionEdit.adminDescription": "Full administrator tilgang. Overstyrer alle andre till<PERSON>.", "components.PermissionEdit.admin": "Administrator", "components.NotificationTypeSelector.mediafailed": "Forespø<PERSON><PERSON>", "components.NotificationTypeSelector.mediadeclinedDescription": "<PERSON><PERSON> var<PERSON>t når en forespørsel om nytt matriale blir avvist.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaavailable": "Forespørsel Tilgjengelig", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaAutoApproved": "Forespørsel Automagisk Godkjent", "components.MovieDetails.watchtrailer": "<PERSON>", "components.MovieDetails.markavailable": "Merk som Tilgjengelig", "components.MovieDetails.mark4kavailable": "Marker som Tilgjengelig i 4K", "components.MediaSlider.ShowMoreCard.seemore": "Vis mer", "components.Login.validationpasswordrequired": "Du må skrive et passord", "components.Login.validationemailrequired": "Du må bruke en gyldig E-postadresse", "components.Login.signinwithplex": "Bruk din Plex-konto", "components.Login.signinwithoverseerr": "Bruk {applicationTitle}-konto", "components.Login.signinheader": "Logg inn for å fortsette", "components.Login.signingin": "Logger inn…", "components.Login.signin": "Logg inn", "components.Login.password": "Passord", "components.Login.loginerror": "Noe gikk galt under innlogging.", "components.Login.forgotpassword": "Glemt passord?", "components.Login.email": "E-postadresse", "components.AppDataWarning.dockerVolumeMissingDescription": "Volum Mount <code>{appDataPath}</code> er ikke konfigurert korrekt. All data vil slettes når containeren stoppes eller startes på nytt.", "i18n.requested": "Forespurt", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON> fore<PERSON><PERSON><PERSON>l for <strong>{title}</strong>!", "components.RequestModal.alreadyrequested": "Allerede forespurt", "components.RequestList.RequestItem.requested": "Forespurt", "components.NotificationTypeSelector.mediarequestedDescription": "<PERSON><PERSON> varslet når nytt matriale blir forespurt og krever godkjenning.", "components.NotificationTypeSelector.mediarequested": "Fores<PERSON>ø<PERSON><PERSON> venter <PERSON>", "components.NotificationTypeSelector.mediafailedDescription": "<PERSON><PERSON> varslet når forespurt matriale ikke kan bli lagt til i Radarr eller Sonarr.", "components.NotificationTypeSelector.mediaavailableDescription": "<PERSON><PERSON> <PERSON><PERSON>t når forespurt matriale blir til<PERSON>.", "components.NotificationTypeSelector.mediaapprovedDescription": "<PERSON><PERSON> <PERSON><PERSON>t når forespurt matriale blir manuelt godkjent.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "<PERSON><PERSON> var<PERSON>t når nytt matriale blir forespurt og forespørselen blir automagisk godkjent.", "i18n.delimitedlist": "{a}, {b}", "i18n.tvshow": "Serie", "components.UserProfile.seriesrequest": "Seriefores<PERSON>ø<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Begrensning av Serieforespørseler", "components.TvDetails.showtype": "Serietype", "i18n.notrequested": "<PERSON><PERSON><PERSON> forespurt", "components.QuotaSelector.unlimited": "Ubegrenset", "components.MovieDetails.originaltitle": "Originaltittel", "i18n.all": "Alle", "components.UserList.deleteconfirm": "Er du sikker på at du ønsker å slette denne brukeren? All forespørseldata for denne brukeren vil bli slettet.", "components.UserList.autogeneratepassword": "Generer passord automagisk", "components.Settings.SettingsLogs.showall": "Vis all logg", "components.Settings.SettingsJobsCache.cacheDescription": "<PERSON><PERSON><PERSON><PERSON> mellomlagrer forespørsler til eksterne APIer for å optimalisere ytelsen samt unngå unødvendige API-kall.", "components.Settings.Notifications.allowselfsigned": "<PERSON><PERSON> selvsignerte sertifikater", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Vi klarte ikke å koble denne serien automagisk. Vennligst velg riktig serie under.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON>ne brukeren har tillatelse til å forespørre <strong>{limit}</strong> {type} hver <strong>{days}.</strong> dag.", "components.RequestModal.QuotaDisplay.allowedRequests": "Du har tillatelse til å forespørre <strong>{limit}</strong> {type} hver <strong>{days}.</strong> dag.", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Global begrensning av Serieforespørseler", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Denne brukeren trenger minst <strong>{seasons}</strong> gjenvæ<PERSON>de {seasons, plural, one {sesongforespørsel} other {sesongforespø<PERSON><PERSON>}} for å sende en forespørsel for denne serien.", "components.RequestModal.QuotaDisplay.requiredquota": "<PERSON> trenger minst <strong>{seasons}</strong> gjenv<PERSON><PERSON><PERSON> {seasons, plural, one {sesongforespørsel} other {sesongforespø<PERSON><PERSON>}} for å sende en forespørsel for denne serien.", "i18n.advanced": "Avansert", "i18n.experimental": "Eksperimentelt", "i18n.movie": "Film", "components.UserProfile.movierequests": "Filmfores<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Begrensning av Filmforespørseler", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Global begrensning av Filmforespørseler", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {filmer}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studio}}", "components.Settings.SettingsJobsCache.jobs": "Oppgaver", "components.Settings.SettingsJobsCache.jobname": "Oppgave", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} stanset.", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "Navn på mellomlager", "components.Settings.SettingsJobsCache.cachemisses": "Bo<PERSON>", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "Slettet mellomlager for {cachename}.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "Versjon", "components.Settings.SettingsAbout.totalmedia": "<PERSON>t antall <PERSON>", "components.Settings.SettingsAbout.totalrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> totalt", "components.Settings.SettingsAbout.timezone": "Tidssone", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Foretrukket", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "<PERSON><PERSON><PERSON><PERSON> <PERSON> betale for kaffe", "components.Settings.SettingsAbout.githubdiscussions": "Diskuter på GitHub", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON> hjelp", "components.Settings.SettingsAbout.documentation": "Dokumentasjon", "components.Settings.SettingsAbout.about": "Om", "components.Settings.SettingsAbout.Releases.viewongithub": "<PERSON><PERSON> p<PERSON>", "components.Settings.SettingsAbout.Releases.viewchangelog": "<PERSON>is <PERSON>", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Endringslogg", "components.Settings.SettingsAbout.Releases.releases": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Utgivelsesdata er for øyeblikket utilgjengelig.", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON>ste versjon", "components.Settings.SettingsAbout.Releases.currentversion": "Nåværende versjon", "components.Settings.RadarrModal.validationNameRequired": "Du må oppgi et tjenernavn", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Du må velge en minimum tilgjengelighet", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Base URL kan ikke slutte med en skråstrek", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Nettadressen må starte med en skråstrek", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Nettadressen kan ikke slutte med en skråstrek", "components.Settings.RadarrModal.validationApplicationUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.RadarrModal.testFirstRootFolders": "Test tilkoblingen for å laste inn grunnmapper", "components.Settings.RadarrModal.testFirstQualityProfiles": "Test tilkoblingen for å laste inn kvalitetsprofiler", "components.Settings.RadarrModal.syncEnabled": "Aktiver skanning", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON>er <PERSON>…", "components.Settings.RadarrModal.loadingprofiles": "Laster <PERSON>ro<PERSON>…", "components.Settings.RadarrModal.externalUrl": "Ekstern nettadresse", "components.Settings.Notifications.validationUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.Notifications.validationEmail": "Du må oppgi en gyldig E-postadresse", "components.Settings.Notifications.validationChatIdRequired": "Du må oppgi en gyldig prat ID", "components.Settings.Notifications.senderName": "Avsendernavn", "components.Settings.Notifications.sendSilentlyTip": "Send varsler uten lyd", "components.Settings.Notifications.sendSilently": "<PERSON> lydløst", "components.Settings.Notifications.pgpPrivateKey": "Privat PGP-nøkkel", "components.Settings.Notifications.pgpPassword": "PGP-passord", "components.Settings.Notifications.chatId": "Chat ID", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsPushover.accessToken": "Applikasjon/API-nøkkel", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Du må oppgi en tilgangsnøkkel", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sesong} other {sesonger}}", "components.RequestModal.QuotaDisplay.season": "sesong", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {Ingen} other {<strong>#</strong>}} gjenværende {type} {remaining, plural, one {forespørsel} other {foresp<PERSON><PERSON><PERSON>}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Du kan se en oppsummering av denne brukerens forespørselbegrensninger på <ProfileLink>profilsiden deres</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Du kan se en oppsummering av dine forespørselbegrensninger på <ProfileLink>profilsiden</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Ikke nok gjenværende sesongforespørsler", "components.UserList.owner": "<PERSON><PERSON>", "components.UserList.sortDisplayName": "Visningsnavn", "components.UserList.sortRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.sortCreated": "Opprettelsesdato", "components.UserList.createlocaluser": "<PERSON><PERSON><PERSON><PERSON> lokal bruker", "components.UserList.accounttype": "Kontotype", "i18n.edit": "<PERSON><PERSON>", "components.UserProfile.ProfileHeader.settings": "Rediger innstillinger", "components.UserList.edituser": "<PERSON>iger brukertilganger", "components.UserList.bulkedit": "<PERSON>iger flere", "components.Settings.SettingsLogs.extraData": "Mer data", "components.Settings.SettingsLogs.copyToClipboard": "Kopier til utklippstavlen", "components.Settings.SettingsLogs.copiedLogMessage": "Kopierte loggmelding til utklippstavlen.", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>-skann", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON>r-skann", "components.Settings.SettingsJobsCache.process": "Prosess", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Sjekk for nye titler i Plex-biblioteket", "components.Settings.SettingsJobsCache.plex-full-scan": "Full Skanning av Plex-biblioteket", "components.Settings.SettingsJobsCache.nextexecution": "<PERSON><PERSON><PERSON><PERSON> i<PERSON>n", "components.Settings.SettingsJobsCache.jobtype": "Type", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} startet.", "components.Settings.SettingsJobsCache.jobsandcache": "Oppgaver & Mellomlager", "components.Settings.SettingsJobsCache.download-sync-reset": "Nullstill nedlastningssynkronisering", "components.Settings.SettingsJobsCache.download-sync": "Nedlastningssynkronisering", "components.Settings.SettingsJobsCache.command": "Kommando", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.scanning": "Synkron<PERSON>er…", "components.Settings.scan": "Synkroniser bibliotek", "components.Settings.plex": "Plex", "components.Settings.notifications": "<PERSON><PERSON><PERSON>", "components.Settings.enablessl": "Aktiver SSL", "components.Settings.email": "E-post", "components.Settings.SonarrModal.validationNameRequired": "Du må oppgi et tjenernavn", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Du må velge en språkprofil", "components.Settings.SonarrModal.toastSonarrTestSuccess": "<PERSON><PERSON>t til Sonarr!", "components.Settings.SonarrModal.testFirstTags": "Test tilkobling for å laste inn merker", "components.Settings.SonarrModal.testFirstRootFolders": "Test tilkobling for å laste inn grunnmapper", "components.Settings.SonarrModal.testFirstQualityProfiles": "Test tilkobling for å laste inn kvalitetsprofiler", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Test tilkobling for å laste inn språkprofiler", "components.Settings.SonarrModal.tags": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.syncEnabled": "Aktiver skanning", "components.Settings.SonarrModal.selecttags": "<PERSON><PERSON><PERSON> merker", "components.Settings.SonarrModal.selectLanguageProfile": "V<PERSON>g språkprofil", "components.Settings.SonarrModal.notagoptions": "Ingen merker.", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON>er <PERSON>…", "components.Settings.SonarrModal.loadingprofiles": "Laster <PERSON>ro<PERSON>…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Last<PERSON>…", "components.Settings.SonarrModal.loadingTags": "Laster merker…", "components.Settings.SonarrModal.languageprofile": "Språkprofil", "components.Settings.SonarrModal.externalUrl": "Ekstern nettadresse", "components.Settings.SonarrModal.edit4ksonarr": "Rediger 4K Sonarr-tjener", "components.Settings.SonarrModal.default4kserver": "Standard 4K-tjener", "components.Settings.SonarrModal.create4ksonarr": "Legg til ny 4K Sonarr-tjener", "components.Settings.SonarrModal.animerootfolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animequalityprofile": "Anime-kvalitetsprofil", "components.Settings.SonarrModal.animelanguageprofile": "Anime-språkprofil", "components.Settings.SonarrModal.animeTags": "Anime-merker", "components.Settings.SettingsLogs.time": "Tidspunkt", "components.Settings.SettingsLogs.resumeLogs": "Fortsett", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON> på pause", "components.Settings.SettingsLogs.message": "Rapport", "components.Settings.SettingsLogs.logsDescription": "Du kan og<PERSON>å se disse loggene direkte i <code>stdout</code>, eller i <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.logDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.level": "Alvorlighetsgrad", "components.Settings.SettingsLogs.label": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterDebug": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterWarn": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.RadarrModal.testFirstTags": "Test tilkobling for å laste inn merker", "components.Settings.RadarrModal.tags": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selecttags": "<PERSON><PERSON><PERSON> merker", "components.Settings.RadarrModal.notagoptions": "Ingen merker.", "components.Settings.RadarrModal.loadingTags": "Laster merker…", "components.Settings.RadarrModal.edit4kradarr": "Rediger 4K Radarr-tjener", "components.Settings.RadarrModal.default4kserver": "Standard tjener for 4K", "components.Settings.Notifications.telegramsettingssaved": "Innstillingene for Telegram ble lagret!", "components.Settings.Notifications.emailsettingssaved": "Innstillingene for E-post ble lagret!", "components.Settings.Notifications.discordsettingssaved": "Innstillingene for Discord ble lagret!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Innstillingene for Webhook ble lagret!", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook-nettadresse", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Innstillingene for Slack ble lagret!", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Innstillingene for Pushover ble lagret!", "components.RequestModal.AdvancedRequester.tags": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.selecttags": "<PERSON><PERSON><PERSON> merker", "components.RequestModal.AdvancedRequester.notagoptions": "Ingen merker.", "components.LanguageSelector.originalLanguageDefault": "<PERSON>e språk", "components.LanguageSelector.languageServerDefault": "Standard ({language})", "i18n.areyousure": "<PERSON>r du sikker?", "i18n.failed": "Mislykket", "components.UserList.usercreatedfailed": "Noe gikk galt ved oppretting av brukeren.", "components.Settings.toastPlexRefreshFailure": "<PERSON><PERSON> ikke hente listen over Plex-t<PERSON><PERSON><PERSON>.", "components.Settings.toastPlexConnectingFailure": "<PERSON>nne ikke koble til Plex.", "components.Settings.SonarrModal.toastSonarrTestFailure": "<PERSON>nne ikke koble til Sonarr.", "components.Settings.Notifications.telegramsettingsfailed": "<PERSON>nne ikke lagre instillingene for Telegram.", "components.Settings.Notifications.emailsettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for E-post.", "components.Settings.Notifications.discordsettingsfailed": "<PERSON>nne ikke lagre instillingene for Discord.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Webhook.", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "<PERSON>nne ikke lagre instillingene for Slack.", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Pushover.", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.request4k": "Forespør i 4K", "i18n.request": "Forespør", "components.UserProfile.totalrequests": "<PERSON>t antall forespø<PERSON><PERSON>", "components.UserProfile.requestsperdays": "{limit} g<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON>", "components.Settings.noDefaultServer": "Minst én {serverType} server må være definert som standard for at {mediaType}-fore<PERSON><PERSON><PERSON><PERSON> skal kunne bli hånd<PERSON>t.", "components.RequestModal.pendingapproval": "Forespørselen din avventer godkjenning.", "components.RequestList.RequestItem.mediaerror": "{mediaType} <PERSON><PERSON>ke", "components.RequestCard.deleterequest": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.mediaerror": "{mediaType} <PERSON><PERSON>ke", "components.Settings.SettingsAbout.betawarning": "Dette er beta-programvare. Funksjoner kan slutte og fungerer og/eller være ustabile. Reporter gjerne problemer på GitHub!", "components.Settings.noDefaultNon4kServer": "Dersom du har én {serverType} server for både ikke-4K og 4K-media (eller hvis du kun laster ned 4K-innhold), skal {serverType} server <strong>IKKE</strong> være huket av som en 4K server.", "components.Settings.noDefault4kServer": "En 4K {serverType} server må være satt som standard for at brukere skal kunne forspørre 4K {mediaType}.", "components.Settings.SettingsUsers.defaultPermissionsTip": "Standard tilganger gitt til nye brukere", "components.Settings.Notifications.encryptionDefault": "Bruk STARTTLS om tilgjengelig", "components.UserProfile.UserSettings.menuGeneralSettings": "Generelt", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Standard ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Overstyr globale begrensninger", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Visningsspråk", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Du må oppgi en gyldig bruker ID", "components.UserProfile.ProfileHeader.userid": "BrukerID: {userid}", "components.UserProfile.unlimited": "Ubegrenset", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Gå til Pushbullet sine<PushbulletSettingsLink>kontoinnstillinger</PushbulletSettingsLink> for opprette en tilgangsnøkkel", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<PERSON><PERSON> bruker- el<PERSON> enhetsbaserte <LunaSeaLink>webhook-nettadresse for varsler</LunaSeaLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook-nettadresse", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Du må velge minst én varseltype", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Test-var<PERSON> ble sendt med LunaSea!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea sender test-varsel…", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "<PERSON><PERSON> ikke lagre instillingene for LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Innstillingene for LunaSea ble lagret!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea mislykkes med å sende test-varsel.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Kun nødvendig dersom en annen profil enn <code>default</code> er i bruk", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profilnavn", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aktiver Tjeneste", "components.RequestModal.edit": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requesteddate": "Forespurt", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{sesonger} per {quotaDays} {dager}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {sesong} other {sesonger}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {filmer}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{filmer} per {quotaDays} {dager}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {dag} other {dager}}", "components.PermissionEdit.requestTvDescription": "Gi tilgang til å forespørre serier som ikke er i 4K.", "components.PermissionEdit.requestTv": "Forespør Serier", "components.PermissionEdit.requestMoviesDescription": "<PERSON>i tilgang til å forespørre filmer som ikke er i 4K.", "components.PermissionEdit.requestMovies": "Forespør Filmer", "components.NotificationTypeSelector.usermediarequestedDescription": "<PERSON><PERSON> var<PERSON>t når andre brukere forespør nytt matriale hvor forespørselen krever godkjenning.", "components.NotificationTypeSelector.usermediafailedDescription": "<PERSON><PERSON> var<PERSON>t når dine forespørsler ikke kan bli lagt til Radarr eller Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> når dine fore<PERSON><PERSON><PERSON><PERSON> bli<PERSON> avvist.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON> varslet når andre brukere forespør nytt matriale og forespørselen er automagisk godkjent.", "components.NotificationTypeSelector.usermediaapprovedDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> når dine fore<PERSON><PERSON><PERSON><PERSON> bli<PERSON>.", "components.NotificationTypeSelector.usermediaavailableDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> når dine fore<PERSON><PERSON><PERSON><PERSON> blir til<PERSON>.", "components.NotificationTypeSelector.notificationTypes": "Varselstyper", "components.MovieDetails.showmore": "Vis mer", "components.MovieDetails.showless": "Vis mindre", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr Stable", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.outofdate": "Utdatert", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} etter", "components.Layout.LanguagePicker.displaylanguage": "Visningsspråk", "components.RequestCard.failedretry": "Noe gikk galt mens du prøvde forespørselen på nytt.", "components.DownloadBlock.estimatedtime": "Estimert {time}", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "<PERSON><PERSON><PERSON><PERSON> ble lagret!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Passord laget!", "i18n.save": "Lagre", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Du må velge minst én varseltype", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Du må velge minst én varseltype", "components.Settings.Notifications.validationTypes": "Du må velge minst én varseltype", "components.UserProfile.UserSettings.menuNotifications": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Du må velge minst én varseltype", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Du må oppgi en gyldig tilgan<PERSON>nøkkel", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Du må velge minst én varseltype", "components.StatusBadge.status4k": "4K {status}", "components.StatusBadge.status": "{status}", "components.IssueDetails.closeissueandcomment": "Lukk med Kommentar", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "Administrer {mediaType}", "components.ManageSlideOver.mark4kavailable": "Marker som Tilgjengelig i 4K", "components.ManageSlideOver.openarr": "Vis i {arr}", "components.MovieDetails.streamingproviders": "Tilgjengelig på", "components.NotificationTypeSelector.adminissuereopenedDescription": "<PERSON><PERSON> var<PERSON>t når avvik blir gje<PERSON> av andre brukere.", "components.NotificationTypeSelector.adminissueresolvedDescription": "<PERSON><PERSON> var<PERSON>t når avvik blir utbedret av andre brukere.", "components.NotificationTypeSelector.issuecomment": "Avvikskommentar", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Avviksrapport for <strong>{title}</strong> er nå sendt inn!", "components.IssueModal.issueAudio": "Lyd", "components.IssueDetails.IssueDescription.edit": "<PERSON><PERSON>", "components.IssueModal.issueVideo": "<PERSON><PERSON>", "components.ManageSlideOver.tvshow": "serier", "components.PermissionEdit.viewissuesDescription": "<PERSON>i tillatelse til å vise avvik som er rapportert av andre brukere.", "components.IssueDetails.allepisodes": "Alle Episoder", "components.IssueDetails.playonplex": "Spill av med Plex", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON>", "components.IssueDetails.reopenissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.commentplaceholder": "Legg til kommentar…", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Episode} other {Episoder}}", "components.IssueDetails.reopenissueandcomment": "Åpne Avvik med kommentar", "components.IssueDetails.toasteditdescriptionsuccess": "Beskrivelsen av avviket ble regdigert!", "components.IssueDetails.toastissuedeleted": "A<PERSON><PERSON><PERSON> ble slettet!", "components.IssueDetails.toaststatusupdated": "Statusen til <PERSON>vviket ble endret!", "components.IssueModal.CreateIssueModal.toastviewissue": "Se Avvik", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Du må skrive en forklaring av problemet", "components.IssueModal.CreateIssueModal.whatswrong": "Hva er problemet?", "components.IssueModal.issueSubtitles": "Undertekster", "components.Layout.Sidebar.issues": "Avvik", "components.ManageSlideOver.downloadstatus": "Nedlastningsstatus", "components.ManageSlideOver.manageModalClearMedia": "Fjern all Info", "components.ManageSlideOver.manageModalIssues": "Åpne et Avvik", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON> forespø<PERSON>.", "components.ManageSlideOver.markavailable": "<PERSON><PERSON> som Tilg<PERSON>nge<PERSON>g", "components.ManageSlideOver.movie": "film", "components.ManageSlideOver.openarr4k": "Åpne i 4K {arr}", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON> varslet når avviket får en ny kommentar.", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON> <PERSON><PERSON>t når avvik blir rapportert.", "components.NotificationTypeSelector.issuereopened": "Avvik gjenåpnet", "components.PermissionEdit.viewissues": "<PERSON><PERSON>", "components.NotificationTypeSelector.issuecreated": "Avvik Rapportert", "components.IssueDetails.IssueComment.areyousuredelete": "Vil du slette denne kommentaren?", "components.IssueDetails.IssueComment.delete": "Slett Kommentar", "components.IssueDetails.IssueComment.edit": "Rediger Kommentar", "components.IssueDetails.IssueComment.postedbyedited": "Publisert {relativeTime} av {username} (Endret)", "components.IssueDetails.IssueDescription.deleteissue": "Slett Avviket", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.deleteissue": "Slett Avvik", "components.IssueDetails.deleteissueconfirm": "Vil du slette de<PERSON>?", "components.IssueDetails.episode": "Episode {episodeNumber}", "components.IssueDetails.issuepagetitle": "Avvik", "components.IssueDetails.lastupdated": "Sist oppdatert", "components.IssueDetails.leavecomment": "Kommentar", "components.IssueDetails.openinarr": "Vis i {arr}", "components.IssueDetails.season": "Sesong {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Noe gikk galt under redigering av avviksbeskrivelsen.", "components.IssueDetails.toastissuedeletefailed": "Noe gikk galt under sletting av avviket.", "components.IssueDetails.toaststatusupdatefailed": "Noe gikk galt under endring av statusen til Avviket.", "components.IssueDetails.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "Type", "components.IssueDetails.nocomments": "Ingen Kommentar.", "components.IssueDetails.openedby": "#{issueId} ble registert {relativeTime} av {username}", "components.IssueDetails.openin4karr": "Vis i 4K {arr}", "components.IssueDetails.play4konplex": "Spill av i 4K med Plex", "components.IssueDetails.problemepisode": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Sessong} other {Sessonger}}", "components.NotificationTypeSelector.adminissuecommentDescription": "<PERSON><PERSON> varslet når andre brukere kommenterer på avvik.", "components.IssueDetails.closeissue": "Lukk Avvik", "components.IssueModal.CreateIssueModal.allseasons": "Alle Sesonger", "components.NotificationTypeSelector.userissueresolvedDescription": "<PERSON><PERSON> varslet når avviket du sendte inn er utbedret.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON> var<PERSON>t når andre brukere rapporterer avvik.", "components.PermissionEdit.createissuesDescription": "Gi tillatelse til å rapportere avvik.", "components.RequestModal.requestmovies4k": "For<PERSON><PERSON><PERSON>r {count} {count, plural, one {Film} other {Filmer}} i 4K", "components.UserList.email": "E-postadresse", "components.UserList.validationpasswordminchars": "Passordet er for kort; det må bestå av minimum 8 tegn", "i18n.status": "Status", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aktiver Tjeneste", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Kunne ikke lagre instillingene for Gotify.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Innstillingene for Gotify ble lagret!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify mislykkes med å sende test-varsel.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify sender test-varsel…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Test-var<PERSON> ble sendt med Gotify!", "components.Settings.Notifications.NotificationsGotify.token": "Applikasjon/API-nøkkel", "components.Settings.Notifications.NotificationsGotify.url": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Du må oppgi en gyldig nettadresse", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "Nettadressen kan ikke slutte med en skråstrek", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Du må velge minst én varseltype", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Register en tjeneste</ApplicationRegistrationLink> til bruk sammen med Jellyseerr", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet mislykkes med å sende test-varsel.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet sender test-varsel…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Test-<PERSON><PERSON> ble sendt med Pushover!", "components.Settings.Notifications.encryptionTip": "I de fleste tilfeller bruker Implisitt TLS port 465 og STARTTLS bruker port 587", "components.Settings.RadarrModal.inCinemas": "<PERSON><PERSON>", "components.Settings.validationHostnameRequired": "Du må oppgi et gyldig vertsnavn eller IP-adresse", "components.UserProfile.pastdays": "{type} (siste {days} dager)", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Du må oppgi en gyldig prat ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Du må oppgi en gyldig bruker- eller gruppe nøkkel", "i18n.back": "Tilbake", "i18n.close": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.canceling": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.showingresults": "Viser <strong>{from}</strong> til <strong>{to}</strong> av <strong>{total}</strong> resultater", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Du må oppgi en applikasjon/API-nøkkel", "i18n.next": "Neste", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON>", "components.TvDetails.firstAirDate": "F<PERSON>rste gang sendt", "i18n.deleting": "Sletter…", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Innstillingene for E-post ble lagret!", "components.UserList.creating": "<PERSON><PERSON><PERSON><PERSON>…", "components.Settings.is4k": "4K", "pages.somethingwentwrong": "<PERSON>e gikk galt", "pages.serviceunavailable": "T<PERSON>nst<PERSON> Utilg<PERSON>nglig", "components.UserList.create": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.encryptionOpportunisticTls": "Alltid bruk STARTTLS", "components.RequestModal.requestmovies": "For<PERSON><PERSON><PERSON>r {count} {count, plural, one {Film} other {<PERSON>er}}", "components.Settings.Notifications.encryption": "Krypteringsmetode", "i18n.import": "Importer", "i18n.importing": "Importerer…", "components.Setup.setup": "<PERSON><PERSON><PERSON>", "components.TvDetails.network": "TV-nettverk", "components.Settings.validationPortRequired": "Du må oppgi et gyldig port-nummer", "components.PermissionEdit.manageissuesDescription": "Gi tillatelse til å administre avvik.", "i18n.retrying": "Prøver på nytt…", "components.Settings.RadarrModal.enableSearch": "Aktiver Automagisk Søk", "components.Settings.Notifications.validationPgpPrivateKey": "Du må oppgi en gyldig PGP-privatnøkkel", "components.Settings.SettingsJobsCache.cachekeys": "<PERSON>t antall n<PERSON>", "components.MovieDetails.productioncountries": "Produksjonsland", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Nettadressen kan ikke slutte med en skråstrek", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Register en tjeneste</ApplicationRegistrationLink> til bruk sammen med {applicationTitle}", "components.Settings.serverRemote": "eksternt", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverpresetLoad": "Trykk for å laste tilgjenglige tjenere", "components.Settings.serverpresetManualMessage": "<PERSON><PERSON>", "components.Settings.serverpresetRefreshing": "<PERSON><PERSON> tje<PERSON>…", "components.Settings.settingUpPlexDescription": "For å sette opp Plex, kan du enten fylle inn instillingene manuelt eller du kan velge en tjener hentet fra <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Trykk på knappen til høyre for å hente oversikten over tilgjenglige tjenere.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover mislykkes å sende test-varsel.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover sender test-varsel…", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "Type", "components.IssueList.IssueItem.opened": "Registert", "components.IssueModal.CreateIssueModal.allepisodes": "Alle Episoder", "components.IssueModal.CreateIssueModal.episode": "Episode {episodeNumber}", "components.IssueModal.CreateIssueModal.problemepisode": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON>", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON> til", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.providedetail": "Vennligst gi en forklaring av problemet som oppsto, gjerne så detaljert som mulig.", "components.IssueModal.CreateIssueModal.season": "Sesong {seasonNumber}", "components.IssueModal.CreateIssueModal.reportissue": "Rapporter Avvik", "components.IssueModal.CreateIssueModal.submitissue": "Registrer Av<PERSON>", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Noe gikk galt under registering av avviket.", "components.NotificationTypeSelector.issuereopenedDescription": "<PERSON><PERSON> varslet hvis avviket blir gje<PERSON>.", "components.NotificationTypeSelector.issueresolved": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON> varslet når avviket er utbedret.", "components.NotificationTypeSelector.userissuecommentDescription": "<PERSON><PERSON> varslet når ditt innsendte avvik får nye kommentarer.", "components.NotificationTypeSelector.userissuereopenedDescription": "<PERSON><PERSON> varslet når avvik du sendte inn blir gjenåpnet.", "components.PermissionEdit.createissues": "Rapporter Avvik", "components.RequestModal.requestseasons4k": "Forspør {seasonCount} {seasonCount, plural, one {Sesong} other {Sesonger}} i 4K", "components.Settings.Notifications.NotificationsPushover.userToken": "Bruker- eller Grup<PERSON>-n<PERSON>k<PERSON>", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook-adresse", "components.Settings.Notifications.encryptionImplicitTls": "Bruk Implisitt TLS", "components.Settings.RadarrModal.create4kradarr": "Legg til ny Radarr-tjener for 4K", "components.Settings.SettingsAbout.outofdate": "Utdatert", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Base URL må starte med en skråstrek", "components.Settings.services": "T<PERSON>nes<PERSON>", "components.Settings.toastPlexRefreshSuccess": "Listen med Plex-tjenere ble hentet!", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutter", "components.TvDetails.nextAirDate": "Neste Sendedato", "components.TvDetails.watchtrailer": "<PERSON><PERSON>", "components.TvDetails.streamingproviders": "Tilgjengelig på", "components.UserList.deleteuser": "<PERSON><PERSON> Bruker", "components.UserList.localLoginDisabled": "Muligheten for <strong>Aktiver lokal innlogging </strong> er deaktivert.", "components.UserList.userfail": "Noe gikk galt ved endring av bruker-tillatels<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Innstillingene for Discord ble lagret!", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Innstillingene for Pushbullet ble lagret!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Send varsler uten lyd", "i18n.loading": "Laster…", "i18n.saving": "<PERSON><PERSON><PERSON>…", "components.UserProfile.ProfileHeader.joindate": "Opprettet {joindate}", "components.Settings.serverLocal": "lokal", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Tilgangsnøkkel", "components.IssueDetails.IssueComment.validationComment": "Du må skrive en melding", "components.IssueDetails.IssueComment.postedby": "Publisert {relativeTime} av {username}", "components.IssueList.issues": "Avvik", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON><PERSON> kjører enkelte vedlikeholdsoppgaver med fast tidsintervall, men de kan også velges å kjøres manuelt under. En oppgave som blir trigget manuelt forstyrrer ikke det faste tidsintervalet.", "components.UserProfile.UserSettings.menuPermissions": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.description": "Beskrivelse", "components.IssueDetails.allseasons": "Alle Sesonger", "components.PermissionEdit.manageissues": "Administrer <PERSON>", "components.RequestModal.approve": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.requestApproved": "Forespørsel av <strong>{title}</strong> er godkjent!", "components.RequestModal.selectmovies": "Velg Film(er)", "components.Settings.Notifications.validationPgpPassword": "Du må oppgi et PGP passord", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "i18n.testing": "Tester…", "components.Settings.serverSecure": "sikker", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.uptodate": "Oppdatert", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "<PERSON><PERSON><PERSON><PERSON> ble endret!", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Hver {jobScheduleHours}. time", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Hvert {jobScheduleMinutes}. minutt", "components.Settings.SettingsUsers.localLoginTip": "Tilllat brukere å logge på med e-postadresse og passord, istedenfor med {mediaServerName} OAuth", "components.Settings.SettingsUsers.newPlexLoginTip": "Tillat {mediaServerName} brukere å logge inn uten å være importert på forhånd", "components.Settings.SonarrModal.validationApplicationUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Base URL kan ikke slutte med en skråstrek", "components.Settings.mediaTypeMovie": "film", "components.Settings.mediaTypeSeries": "serier", "components.Settings.toastPlexConnecting": "Forsøker å koble til Plex…", "components.Settings.toastPlexConnectingSuccess": "Forbindel<PERSON> til Plex ble opprettet!", "components.Settings.toastPlexRefresh": "<PERSON><PERSON> listen over tje<PERSON>e fra <PERSON>…", "components.TvDetails.episodeRuntime": "S<PERSON><PERSON><PERSON>d Episode", "components.TvDetails.productioncountries": "Produksjonsland", "components.TvDetails.seasons": "{seasonCount, plural, one {# Sesong} other {# Sesonger}}", "components.TvDetails.originaltitle": "Originaltittel", "components.TvDetails.anime": "Animasjon", "components.UserList.localuser": "Lokal bruker", "components.UserList.password": "Passord", "components.UserList.nouserstoimport": "Det er ingen brukere å importere fra Plex.", "components.UserList.userdeleteerror": "Noe gikk galt under sletting av brukeren.", "components.UserList.passwordinfodescription": "Sett opp en nettadresse for Jellyseerr og aktiver E-postvarsler for å kunne sende ut automagisk genererte passord.", "components.UserList.usercreatedfailedexisting": "<PERSON>ne E-postadressen er allerede i bruk av en annen bruker.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "<PERSON>nne ikke lagre instillingene for Discord.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-post", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for E-post.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Applikasjon/API-nøkkel", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Gå til Pushbullet <PushbulletSettingsLink>kontoinnstillinger</PushbulletSettingsLink> for opprette en tilgangsnøkkel", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Bruker- eller Grup<PERSON>-n<PERSON>k<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Innstillingene for Pushover ble lagret!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON> lydløst", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Du må oppgi en gyldig offentlig PGP-nøkkel", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Innstillingene for Telegram ble lagret!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Du må oppgi en gyldig applikasjon/API-nøkkel", "components.UserProfile.UserSettings.UserPasswordChange.password": "Passord", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Du har ikke tillatelse til å endre denne brukerens passord.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Web Push.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Innstillingene for Web Push ble lagret!", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "N<PERSON>t <PERSON>ord", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Nåværende Passord", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Noe gikk galt under lagring av passordet.", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Du må bekrefte det nye passordet", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Passordene må stemme overens", "components.UserProfile.UserSettings.UserPermissions.permissions": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.limit": "{remaining} av {limit}", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Du må oppgi et nytt passord", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Du må oppgi ditt nåværende passord", "components.UserProfile.UserSettings.menuChangePass": "Passord", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Passordet er for kort; det må bestå av minimum 8 tegn", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Du kan ikke endre dine egne tillatelser.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Noe gikk galt under lagring av instillingene.", "i18n.noresults": "Ingen Resultater.", "i18n.open": "<PERSON><PERSON>", "i18n.previous": "<PERSON><PERSON><PERSON>", "i18n.resolved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.retry": "<PERSON><PERSON><PERSON><PERSON> på nytt", "i18n.view": "Vis", "pages.pagenotfound": "Side ble ikke funnet", "i18n.test": "Test", "i18n.usersettings": "<PERSON><PERSON><PERSON>-instillinger", "pages.internalservererror": "Intern Tjenerfeil", "i18n.resultsperpage": "Vis {pageSize} resultater per side", "i18n.settings": "Innstillinger", "components.IssueList.IssueItem.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON>", "components.IssueList.showallissues": "Vis alle A<PERSON>", "components.IssueList.sortModified": "<PERSON><PERSON>", "components.IssueList.IssueItem.openeduserdate": "{date} av {user}", "components.IssueList.IssueItem.problemepisode": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Test-var<PERSON> ble sendt med Pushbullet!", "components.Settings.Notifications.encryptionNone": "Ingen", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "<PERSON>nne ikke lagre instillingene for Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat ID", "components.Settings.SettingsUsers.newPlexLogin": "Aktiver ny {mediaServerName}-pålogging", "components.Settings.SonarrModal.enableSearch": "Aktiver Automagisk Søk", "components.Settings.notificationAgentSettingsDescription": "Konfigurer og aktiver varslingstjenester.", "components.UserList.autogeneratepasswordTip": "Send et automagisk generet passord til bruken på E-post", "components.UserList.usercreatedsuccess": "Bruker opprettet!", "components.UserList.userdeleted": "<PERSON><PERSON><PERSON> slettet!", "components.UserList.validationEmail": "Du må oppgi en gyldig E-postadresse", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Du må oppgi en tilgangsnøkkel", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web push mislykkes med å sende test-varsel.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Lag en <WebhookLink>inkommende Webhook</WebhookLink> integrasjon", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "<PERSON>lack mislykkes med å sende test-varsel.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Slack sender test-varsel…", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Innstillingene for Web Push ble lagret!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web Push sender test-varsel…", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "<PERSON><PERSON><PERSON> bruke <PERSON>", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Test-<PERSON><PERSON> ble sendt med Webhook!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Du må oppgi en gyldig JSON payload", "components.Settings.Notifications.pgpPasswordTip": "Bruk<OpenPgpLink>OpenPGP</OpenPgpLink> til å signere krypterte E-postmeldinger", "components.Settings.Notifications.toastDiscordTestFailed": "Discord mislykkes med å sende test-varsel.", "components.Settings.webAppUrl": "<WebAppLink>Web-tjeneste</WebAppLink> nettadresse", "components.Settings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Offentlig PGP-nøkkel", "components.UserProfile.UserSettings.unauthorizedDescription": "Du har ikke tillatelse til å endre innstillingene til denne brukeren.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Test-var<PERSON> ble sendt med Web Push!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "<PERSON><PERSON> ikke lagre instillingene for Web Push.", "components.Settings.Notifications.webhookUrlTip": "Lag en <DiscordWebhookLink>webhook integasjon</DiscordWebhookLink> med din tjener", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Lag en bot</CreateBotLink> til bruk sammen med Je<PERSON>seerr", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Test-<PERSON><PERSON> ble sendt med Slack!", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Bruk <OpenPgpLink>OpenPGP</OpenPgpLink> til å kryptere E-post", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook mislykkes med å sende test-varsel.", "components.Settings.Notifications.toastTelegramTestSending": "Telegram sender test-varsel…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Webhook sender test-varsel…", "components.Settings.Notifications.pgpPrivateKeyTip": "Bruk<OpenPgpLink>OpenPGP</OpenPgpLink> til å signere krypterte E-postmeldinger", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Du må oppgi en gyldig bruker- eller gruppe nøkkel", "components.Settings.webAppUrlTip": "Alternativt send brukerne direkte til Web-Tjenesten på din tjener istedenfor Plex sin Web-tjener", "components.Settings.webhook": "Webhook", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "For å kunne sende Web Push varsler, må <PERSON><PERSON><PERSON>rr kommunisere over HTTPS.", "components.Settings.Notifications.toastTelegramTestSuccess": "Test-var<PERSON> ble sendt med Telegram!", "components.Settings.Notifications.toastEmailTestFailed": "Mislykkes å sende test-varsel med E-post.", "components.Settings.Notifications.toastEmailTestSending": "Sender test-varsel med E-post…", "components.Settings.Notifications.toastDiscordTestSending": "Discord sender test-varsel…", "components.Settings.Notifications.toastDiscordTestSuccess": "Test-var<PERSON> ble sendt med Discord!", "components.Settings.Notifications.toastEmailTestSuccess": "Test-var<PERSON> ble sendt med E-post!", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram mislykkes med å sende test-varsel.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Noe gikk galt under lagring av oppgaven.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Din konto har ikke noe passord på nåværende tidspunkt. Lag et passord under for å kunne logge inn med din E-postadresse som \"lokal-bruker\".", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Noe gikk galt under lagring av passordet. Var ditt nåværende passord skrevet korrekt?", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload tilbakestilt!", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Din 30-tegns <UsersGroupsLink>bruker- el<PERSON> gruppe-nø<PERSON><PERSON></UsersGroupsLink>", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.serviceSettingsDescription": "<PERSON>n<PERSON><PERSON><PERSON> dine {serverType}tjener(e) nedenfor. Du kan koble til flere forskellige {serverType}tjenere men kun to av dem kan markeres som standard (en som ikke er 4K og en 4K). Administratorer kan endre hvilken tjener som brukes før godkjennelse av nye forespørsler.", "components.ManageSlideOver.manageModalClearMediaWarning": "* Dette vil slette all data for denne tittelen uten mulighet for å bli gjennopprettet, det inkluderer alle forespørsler, avvik osv. Hvis denne tittelen finnes i ditt {mediaServerName} bibliotek vil medieinformasjon bli opprettet på nytt under neste skanning.", "components.Settings.Notifications.NotificationsWebhook.authheader": "Autorisasjonshode", "components.Settings.SettingsJobsCache.cacheksize": "Nøkkelstørrelse", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload", "components.Settings.Notifications.botAPI": "\"Bot\" Autorisasjonsnøkkel", "components.Settings.Notifications.botUsername": "\"Bot\" Brukernavn", "components.Settings.Notifications.botAvatarUrl": "Nettdresse for \"Bot\" avatar", "components.Settings.Notifications.chatIdTip": "Start en prat med din \"Bot\", legg til <GetIdBotLink>@get_id_bot</GetIdBotLink> og send kommandoen <code>/my_id</code>", "components.Settings.Notifications.validationBotAPIRequired": "Du må oppgi en gyldig autorisasjonsnøkkel for \"Boten\"", "components.Settings.Notifications.botUsernameTip": "Tillat brukere å kunne starte en prat med din \"Bot\" for å konfigurere deres egne varsler", "components.UserList.newplexsigninenabled": "Innstillingen <strong>Aktiver ny Plex-pålogging</strong> er nå aktivert. Plex-brukere med tilgang til biblioteket trenger ikke å importeres for å kunne logge på første gang.", "components.Settings.Notifications.enableMentions": "Aktiver \"Mentions\"", "components.Settings.SettingsAbout.runningDevelop": "Du bruker nå <code>develop</code> utgaven av Jellyseerr. Denne er kun anbefalt for dem som bidrar i utviklingen eller assisterer med testing av nye funksjoner.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Din 30-tegns <UsersGroupsLink>bruker- eller gruppe-ID</UsersGroupsLink>", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Denne brukerkontoen har for øyeblikket ikke et passord. Konfigurer et passord nedenfor så denne kontoen kan logge inn som en \"lokal bruker.\"", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Start en prat</TelegramBotLink>, legg til <GetIdBotLink>@get_id_bot</GetIdBotLink> og benytt <code>/my_id</code> kommandoen", "components.ManageSlideOver.manageModalAdvanced": "Avansert", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "4K Media", "components.ManageSlideOver.markallseasonsavailable": "<PERSON>er alle sesonger som Tilgjenglig", "components.ManageSlideOver.opentautulli": "Åpne i Tautulli", "components.ManageSlideOver.pastdays": "Siste {days, number} <PERSON><PERSON>", "components.Settings.toastTautulliSettingsFailure": "Noe gikk galt under lagring av innstillingene for Tautulli.", "components.Settings.urlBase": "URL Base", "components.Settings.validationApiKey": "Du må oppgi en API-nøkkel", "components.Settings.validationUrl": "Du må oppgi en gyldig nettadresse", "components.Settings.validationUrlBaseLeadingSlash": "Nettadressen må starte med en skråstrek", "components.Settings.toastTautulliSettingsSuccess": "Innstillingene for Tautulli ble lagret!", "components.UserProfile.recentlywatched": "<PERSON><PERSON> Sett", "components.ManageSlideOver.markallseasons4kavailable": "Marker alle sesonger som Tilgjenglig i 4K", "components.ManageSlideOver.playedby": "Sett av", "components.Settings.externalUrl": "Ekstern nettadresse", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON> innstillinger", "components.Settings.tautulliApiKey": "API-nøkkel", "components.Settings.validationUrlTrailingSlash": "Nettadressen kan ikke slutte med en skråstrek", "components.Settings.validationUrlBaseTrailingSlash": "Base URL kan ikke slutte med en skråstrek", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {avspilling} other {avspillinger}}", "components.ManageSlideOver.alltime": "Totalt", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "<PERSON><PERSON>-<PERSON>rke", "components.Settings.tautulliSettingsDescription": "<PERSON><PERSON>seerr kan hente avspillingshistorikk for Plex fra Tautulli. Fyll inn innstilingene under for å etablere kommunikasjon med din Tautulli-tjener.", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord Bruker ID", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Du må oppgi en gyldig Bruker ID for Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>ID-nummeret</FindDiscordIdLink> til din brukerkonto", "components.Settings.SettingsAbout.appDataPath": "Datakatalog", "components.RequestBlock.languageprofile": "Språkprofil", "components.MovieDetails.digitalrelease": "Digital utgivelse", "components.MovieDetails.physicalrelease": "Fysisk Utgivelse", "components.MovieDetails.theatricalrelease": "Kinopremiere", "components.StatusChecker.appUpdated": "{applicationTitle} Oppdatert", "components.PermissionEdit.viewrecent": "Vis nylig lag til", "components.PermissionEdit.viewrecentDescription": "Gi tillatelse til å vise nylig lagt til.", "components.Settings.deleteServer": "<PERSON><PERSON> {serverType} tjener", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.StatusChecker.appUpdatedDescription": "Vennligst klikk her for å laste applikasjonen på nytt.", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON>", "components.Discover.plexwatchlist": "<PERSON>", "components.NotificationTypeSelector.mediaautorequestedDescription": "<PERSON><PERSON> varsler når nye medieforspørsler automatisk sendes inn for elementer på Din Visningsliste.", "components.PermissionEdit.autorequestSeries": "Forespør Serier Automagisk", "components.PermissionEdit.autorequestMoviesDescription": "Gi tilgang til å automatisk sende film forespørsler som ikke er i 4K via Din Visningsliste.", "components.PermissionEdit.autorequest": "Auto-Forespørsel", "components.PermissionEdit.autorequestDescription": "Gi tilgang til å automatisk sende forespørsler som ikke er i 4K via Din Plex Visningsliste.", "components.PermissionEdit.autorequestMovies": "Forespør Serier Automagisk", "components.Discover.DiscoverWatchlist.watchlist": "Plex <PERSON>", "components.MovieDetails.managemovie": "Administrer Film", "components.MovieDetails.reportissue": "Rapporter Avvik", "components.NotificationTypeSelector.mediaautorequested": "Forespørsel Automagisk Forespurt", "components.PermissionEdit.autorequestSeriesDescription": "Gi tilgang til å automatisk sende serie forespørsler som ikke er i 4K via Din Visningsliste.", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON>", "components.AirDateBadge.airedrelative": "Sendt {relativeTime}", "components.AirDateBadge.airsrelative": "Sendes {relativeTime}", "components.MovieDetails.rtaudiencescore": "Publikumspoeng for Rotten Tomatoes", "components.PermissionEdit.viewwatchlists": "<PERSON><PERSON>", "components.PermissionEdit.viewwatchlistsDescription": "<PERSON>i tilgang til å vise andre brukere sin Visningsliste.", "components.RequestBlock.requestdate": "Tidspunkt for Forespørsel", "components.RequestBlock.requestedby": "Forespurt av", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.editrequest": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Filmfores<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Seriefores<PERSON>ø<PERSON><PERSON>", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.tmdbuserscore": "<PERSON><PERSON><PERSON><PERSON><PERSON> for TMDb", "components.MovieDetails.rtcriticsscore": "Tomatometer for Rotten Tomatoes", "components.RequestBlock.edit": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.RequestBlock.lastmodifiedby": "Sist endret av", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.delete": "<PERSON><PERSON> foresp<PERSON><PERSON><PERSON>", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON>", "components.TitleCard.mediaerror": "{mediaType} <PERSON><PERSON>ke", "components.RequestList.RequestItem.tmdbid": "TMDb ID", "i18n.restartRequired": "Omstart Nødvendig", "components.TitleCard.cleardata": "Fjern all Info", "components.TitleCard.tmdbid": "TMDb ID", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Forespør Filmer Automagisk", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Synkronisering av Plex Visningsliste", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON>seerr må startes på nytt for at de nye innstillingene skal tre i kraft", "components.TvDetails.reportissue": "Rapporter Avvik", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automagisk forespør serier som ligger på <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Forespør Serier Automagisk", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automagisk forespør filmer som ligger på <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.plexwatchlist": "Plex <PERSON>", "components.RequestList.RequestItem.tvdbid": "TheTVDb ID", "components.StatusChecker.restartRequiredDescription": "Start tjeneren på nytt for å ta i bruk de nye innstillingene.", "components.StatusChecker.restartRequired": "Omstart av tjener nødvendig", "components.TitleCard.tvdbid": "TheTVDb ID", "components.Settings.experimentalTooltip": "Aktivering av denne innstillingen kan føre til uventet oppførsel av programmet", "components.Settings.advancedTooltip": "Feil konfigurering av denne innstillingen kan føre til defekt funksjonalitet", "components.TvDetails.Season.somethingwentwrong": "Noe gikk galt under henting av data for denne sesongen.", "components.StatusChecker.reloadApp": "Last inn {applicationTitle} på nytt", "components.StatusBadge.playonplex": "Spill av med {mediaServerName}", "components.StatusBadge.openinarr": "Vis i {arr}", "components.StatusBadge.managemedia": "Administrer {mediaType}", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episode} other {# Episoder}}", "components.TvDetails.rtcriticsscore": "Tomatometer for Rotten Tomatoes", "components.TvDetails.manageseries": "Administrer Serie", "components.TvDetails.rtaudiencescore": "Publikumspoeng for Rotten Tomatoes", "components.TvDetails.seasonstitle": "Sesonger", "components.TvDetails.seasonnumber": "Sesong {seasonNumber}", "components.RequestModal.requestseriestitle": "Forespør Serier", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.tmdbuserscore": "<PERSON><PERSON><PERSON><PERSON><PERSON> for TMDb", "components.RequestModal.requestseries4ktitle": "Forespør Serier i 4K", "components.RequestModal.requestmovietitle": "Forespør Film", "components.RequestModal.requestcollection4ktitle": "Forespør hele samlingen i 4K", "components.RequestModal.requestmovie4ktitle": "Forespør Film i 4K", "components.RequestModal.requestcollectiontitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> hele sa<PERSON>en", "components.Discover.emptywatchlist": "<PERSON><PERSON><PERSON> som du legger til via <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> vil duk<PERSON> opp her.", "components.UserProfile.emptywatchlist": "<PERSON><PERSON><PERSON> som du legger til via <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> vil duk<PERSON> opp her.", "components.RequestModal.SearchByNameModal.nomatches": "Vi klarte ikke å koble denne serien med et søkbart treff.", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Nåværende frekvens", "components.Settings.SettingsMain.generalsettings": "<PERSON><PERSON><PERSON>", "components.Layout.UserWarnings.emailInvalid": "E-postadressen er ugyldig.", "components.Layout.UserWarnings.emailRequired": "En e-postadresse er påkrevd.", "components.Layout.UserWarnings.passwordRequired": "Et passord er påkrevd.", "components.Settings.SettingsMain.general": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Denne e-postadressen er allerede i bruk!", "components.Discover.DiscoverSliderEdit.deletefail": "Feil ved sletting av skyveknappen.", "components.Discover.DiscoverTv.sortTitleAsc": "Tittel (A-Å) Stigende", "components.Discover.FilterSlideover.originalLanguage": "Originalspråk", "components.TitleCard.watchlistError": "<PERSON>e gikk galt, prøv på nytt.", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> er ikke svartelistet.", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON>", "components.MovieDetails.addtowatchlist": "Legg til i Visningsliste", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON> til Skyveknapp", "components.Discover.resetwarning": "Tilbakestill alle skyveknapper til standardinnstillinger. Dette sletter også egendefinerte skyveknapper!", "components.Layout.Sidebar.browsetv": "Serier", "components.Discover.CreateSlider.providetmdbsearch": "Ang<PERSON> et søkeord", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortPopularityAsc": "Populæritet Stigende", "components.UserProfile.localWatchlist": "{username}'s <PERSON><PERSON>nings<PERSON>e", "components.Discover.FilterSlideover.releaseDate": "Utgivelsesdato", "components.Layout.Sidebar.blacklist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.browsemovies": "Filmer", "components.Settings.SettingsMain.validationApplicationTitle": "Du må oppgi en applikasjonstittel", "components.TitleCard.addToWatchList": "Legg til i Visningsliste", "components.TvDetails.watchlistError": "<PERSON>e gikk galt, prøv på nytt.", "components.Discover.CreateSlider.addcustomslider": "Lag Egendefinert Skyveknapp", "components.Discover.CreateSlider.addfail": "Feil ved opprettelse av ny skyveknapp.", "components.Discover.CreateSlider.editSlider": "Rediger Skyveknapp", "components.Discover.FilterSlideover.keywords": "Nøkkelord", "components.Discover.resettodefault": "Tilbakestill til standard", "components.Login.username": "Brukernavn", "components.MovieDetails.removefromwatchlist": "Fjern fra Visningsliste", "components.MovieDetails.watchlistError": "<PERSON>e gikk galt, prøv på nytt.", "components.Settings.SettingsMain.applicationTitle": "Applikasjonstittel", "components.TvDetails.addtowatchlist": "Legg til i Visningsliste", "components.TvDetails.removefromwatchlist": "<PERSON><PERSON><PERSON> Visningsliste", "components.UserList.importfrommediaserver": "Importer {mediaServerName} Brukere", "components.Blacklist.blacklistSettingsDescription": "Administrer svartelistede medier.", "components.Blacklist.blacklistdate": "dato", "components.Blacklist.blacklistedby": "{date} av {user}", "components.Blacklist.blacklistsettings": "Innstillinger for Svarteliste", "components.Blacklist.mediaName": "Navn", "components.Discover.CreateSlider.editfail": "Feil ved redigering av skyveknapp.", "components.Discover.CreateSlider.nooptions": "Ingen treff.", "components.Discover.CreateSlider.searchGenres": "Søk i sjangre…", "components.Discover.CreateSlider.searchKeywords": "<PERSON><PERSON><PERSON> etter nø<PERSON>…", "components.Discover.CreateSlider.starttyping": "Begynn å skrive for å søke.", "components.Discover.CreateSlider.validationTitlerequired": "Du må oppgi en tittel.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filmer", "components.Discover.DiscoverMovies.discovermovies": "Filmer", "components.Discover.DiscoverMovies.sortPopularityDesc": "Populæritet Fallende", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Utgivelsesdato Stigende", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Utgivelsesdato <PERSON>", "components.Discover.DiscoverMovies.sortTitleAsc": "Tittel (A-Å) Stigende", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON> (A-Å) Fallende", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB-vurdering Stigende", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB-vur<PERSON>", "components.Discover.DiscoverSliderEdit.deletesuccess": "Sletting av skyveknappen var vellykket.", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverTv.discovertv": "Serier", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "<PERSON><PERSON><PERSON><PERSON> sendingsdato Stigende", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "<PERSON><PERSON><PERSON><PERSON>dato <PERSON>", "components.Discover.DiscoverTv.sortPopularityAsc": "Populæritet Stigende", "components.Discover.DiscoverTv.sortPopularityDesc": "Populæritet Fallende", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON> (A-Å) Fallende", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB-vurdering Stigende", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB-vur<PERSON>", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Serier", "components.Discover.FilterSlideover.clearfilters": "Tøm Aktive Filtre", "components.Discover.FilterSlideover.filters": "Filtre", "components.Discover.FilterSlideover.firstAirDate": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.from": "<PERSON>a", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON>"}