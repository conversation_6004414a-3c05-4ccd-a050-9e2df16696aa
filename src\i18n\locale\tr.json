{"components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMBD Derecelendirmesi (Artan)", "components.Discover.moviegenres": "Film Türleri", "components.Discover.CreateSlider.editsuccess": "<PERSON><PERSON>r düzenlendi ve keşfet özelleştirme ayarları kaydedildi.", "components.Discover.FilterSlideover.studio": "Stü<PERSON><PERSON>", "components.CollectionDetails.numberofmovies": "{count} Film", "components.Discover.PlexWatchlistSlider.emptywatchlist": "<PlexWatchlistSupportLink>Plex İzleme Listenize</PlexWatchlistSupportLink> eklenen içerikler burada gözükeceklerdir.", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON>n <PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.ratingText": "{minValue} ile {maxValue} aras<PERSON>nda ki de<PERSON>er", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "<PERSON>lk <PERSON> (Yeni)", "components.AppDataWarning.dockerVolumeMissingDescription": "Bağlanmış <code>{appDataPath}</code> dizini dü<PERSON>ün bir şekilde yapılandırılmamış. Tüm veriler konteyner yeniden başlatıldığında veya durdurulduğunda temizlenecektir.", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Di<PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortPopularityDesc": "<PERSON><PERSON><PERSON><PERSON> (Azalan)", "components.Discover.StudioSlider.studios": "Stüdyolar", "components.AirDateBadge.airsrelative": "Yayınlanıyor {relativeTime}", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMBD <PERSON>ecele<PERSON>esi (Azalan)", "components.Discover.customizediscover": "Keşfet'i Özelleştir", "components.Discover.emptywatchlist": "<PlexWatchlistSupportLink>Plex İzleme Listenize</PlexWatchlistSupportLink> eklenen içerikler burada gözükeceklerdir.", "components.Discover.populartv": "<PERSON><PERSON><PERSON>", "components.CollectionDetails.overview": "Özet", "components.Discover.DiscoverMovies.activefilters": "Filtrele", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMBD Derecelendirmesi (Artan)", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Dizileri", "components.AirDateBadge.airedrelative": "Yayınlandı {relativeTime}", "components.Discover.FilterSlideover.clearfilters": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.searchStudios": "Stüdyolarda Ara.…", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Azalan)", "components.Discover.CreateSlider.providetmdbnetwork": "TMDB Ağ Kimliğini Sağlayın", "components.Discover.MovieGenreSlider.moviegenres": "Film Türleri", "components.Discover.networks": "İnternet Yayınları", "components.Discover.CreateSlider.addfail": "<PERSON><PERSON><PERSON>.", "components.CollectionDetails.requestcollection": "Koleksiyonu Talep Et", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON>", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Filmleri", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} <PERSON>ler", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON>", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Filtre} other {# Filtre}}", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} <PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortPopularityAsc": "<PERSON><PERSON><PERSON><PERSON> (Artan)", "components.Discover.NetworkSlider.networks": "İnternet Yayınları", "components.Discover.FilterSlideover.streamingservices": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.needresults": "Seçtiğin etiketler en az 1 adet sonuç göstermelidir.", "components.Discover.popularmovies": "Popüler Filmler", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtre} other {# Filtre}}", "components.Discover.plexwatchlist": "İzleme Listeniz", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON>lide<PERSON>", "components.Discover.FilterSlideover.tmdbuserscore": "TMBD Kullanıcı Skoru", "components.Discover.DiscoverTv.sortPopularityAsc": "<PERSON><PERSON><PERSON><PERSON> (Artan)", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON><PERSON><PERSON><PERSON> düzenle", "components.Discover.DiscoverTv.sortTitleAsc": "İsim (A-Z) (Artan)", "components.Discover.CreateSlider.validationDatarequired": "Bir veri değeri sağlamalısınız.", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "<PERSON>lk <PERSON> (Eski)", "components.Discover.DiscoverWatchlist.discoverwatchlist": "İzleme Listeniz", "components.Discover.FilterSlideover.releaseDate": "Çıkış Tarihi", "components.Discover.DiscoverTv.discovertv": "<PERSON><PERSON><PERSON>", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON>n <PERSON>", "components.Discover.DiscoverSliderEdit.deletefail": "Slide<PERSON> silinemedi.", "components.Discover.CreateSlider.providetmdbstudio": "TMBD'den Stüdyo etiketi seç", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.from": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortTitleDesc": "İsim (Z-A) (Azalan)", "components.Discover.DiscoverStudio.studioMovies": "{studio} Filmleri", "components.Discover.DiscoverTv.sortPopularityDesc": "<PERSON><PERSON><PERSON><PERSON> (Azalan)", "components.Discover.CreateSlider.searchGenres": "<PERSON><PERSON><PERSON><PERSON> ara…", "components.Discover.CreateSlider.editfail": "<PERSON><PERSON><PERSON> hata o<PERSON>.", "components.Discover.CreateSlider.starttyping": "Ara<PERSON>k i<PERSON> ya<PERSON> ba<PERSON>.", "components.Discover.createnewslider": "<PERSON><PERSON>", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverWatchlist.watchlist": "Plex İzleme Listen", "components.Discover.discover": "Keşfet", "components.Discover.DiscoverSliderEdit.enable": "Görünürlüğü Değiştir", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.CollectionDetails.requestcollection4k": "Koleksiyonu 4K Kalitesiyle Talep Et", "components.Discover.FilterSlideover.firstAirDate": "<PERSON>lk <PERSON>", "components.Discover.CreateSlider.providetmdbsearch": "<PERSON>ir arama sorgusu girin", "components.Discover.DiscoverNetwork.networkSeries": "{network} Dizileri", "components.Discover.CreateSlider.providetmdbkeywordid": "TMBD'den Anahtar <PERSON>", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filmleri", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} da<PERSON><PERSON>", "components.Discover.CreateSlider.validationTitlerequired": "Bir başlık belirtmeniz gerekmektedir.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "İzleme Listeniz", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Artan)", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.originalLanguage": "Orijinal Dili", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON>ç Bulunamadı.", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMBD <PERSON>ecele<PERSON>esi (Azalan)", "components.Discover.CreateSlider.searchKeywords": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.tmdbuservotecount": "TMBD Oy Kullanan Kullanıcı Sayısı", "components.Discover.CreateSlider.addsuccess": "Yeni slider oluşturuldu ve keşfet sekmesinin ayarları kaydedildi.", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.Discover.DiscoverMovies.discovermovies": "<PERSON><PERSON>", "components.Discover.MovieGenreList.moviegenres": "Film Türleri", "components.Discover.DiscoverMovies.sortTitleAsc": "İsim (A-Z) (Artan)", "components.Discover.FilterSlideover.voteCount": "{minValue} ile {maxValue} say<PERSON><PERSON><PERSON> a<PERSON>ında oya sa<PERSON> olanlar", "components.Discover.CreateSlider.providetmdbgenreid": "TMBD'den Tür etiketi seç", "components.Discover.DiscoverTv.sortTitleDesc": "İsim (Z-A) (Azalan)", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverSliderEdit.remove": "Kaldır", "components.Discover.FilterSlideover.to": "Ta<PERSON><PERSON>", "components.Discover.resetfailed": "Keşfet özelleştirme ayarlarını sıfırlarken bir sorun oluştu.", "components.Discover.resetsuccess": "Keşfet ayarları başarıyla sıfırlandı.", "components.Discover.resettodefault": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.resetwarning": "Tüm Slider'ları var<PERSON>ılana sıfırla. Bu aynı zamanda tüm özel Slider'ları da silecektir!", "components.Discover.stopediting": "Vazgeç", "components.Discover.studios": "Stüdyolar", "components.Discover.tmdbmoviegenre": "TMBD Film Türleri", "components.Discover.tmdbmoviekeyword": "TMDB Film <PERSON><PERSON><PERSON>", "components.Discover.tmdbmoviestreamingservices": "TMDB Dijital Platform Filmleri", "components.Discover.tmdbnetwork": "TMBD İnternet Yayınları", "components.Discover.tmdbsearch": "TMBD Arama", "components.Discover.tmdbstudio": "TMBD Stüdyolar", "components.Discover.tmdbtvkeyword": "TMBD Dizi <PERSON>", "components.Discover.tmdbtvstreamingservices": "TMBD Dijital Platform Dizileri", "components.Discover.trending": "<PERSON><PERSON><PERSON>", "components.Discover.tvgenres": "<PERSON><PERSON>", "components.Discover.upcoming": "Gösterime Girecek Filmler", "components.Discover.upcomingmovies": "Gösterime Girecek Filmler", "components.Discover.upcomingtv": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.updatesuccess": "Keşfet ayarları güncellendi.", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON>: {time}", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON> {seasonNumber} Bölüm {episodeNumber}", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.postedby": "{relativeTime} <PERSON><PERSON><PERSON><PERSON>, {username} tara<PERSON><PERSON><PERSON><PERSON> g<PERSON>ildi", "components.IssueDetails.IssueComment.postedbyedited": "{relativeTime} <PERSON><PERSON><PERSON><PERSON>, {username} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Düzenlenmiş)", "components.IssueDetails.IssueComment.validationComment": "Bir mesaj gir<PERSON>", "components.IssueDetails.IssueDescription.deleteissue": "<PERSON><PERSON>", "components.IssueDetails.IssueDescription.description": "<PERSON><PERSON>ı<PERSON><PERSON>", "components.IssueDetails.IssueDescription.edit": "Açıklamayı Düzenle", "components.IssueDetails.allepisodes": "<PERSON><PERSON><PERSON>", "components.IssueDetails.allseasons": "<PERSON><PERSON><PERSON>", "components.IssueDetails.closeissue": "<PERSON><PERSON>", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON> ekle…", "components.IssueDetails.comments": "<PERSON><PERSON><PERSON>", "components.IssueDetails.deleteissue": "<PERSON><PERSON>", "components.IssueDetails.episode": "<PERSON><PERSON><PERSON><PERSON><PERSON> {episodeNumber}", "components.IssueDetails.issuepagetitle": "<PERSON><PERSON>", "components.IssueDetails.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueDetails.lastupdated": "En Son Güncelle<PERSON>", "components.IssueDetails.leavecomment": "<PERSON><PERSON>", "components.IssueDetails.nocomments": "<PERSON><PERSON> yok.", "components.IssueDetails.openin4karr": "{arr} ile 4K Aç", "components.IssueDetails.openinarr": "{arr} ile <PERSON>", "components.IssueDetails.play4konplex": "{mediaServerName} ile 4K Oynat", "components.IssueDetails.playonplex": "{mediaServerName} ile <PERSON>", "components.IssueDetails.problemepisode": "<PERSON><PERSON><PERSON> Etkilenen Bölümler", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON> Sezon<PERSON>", "components.IssueDetails.reopenissue": "Sorun Bildirisini Yeniden Aç", "components.IssueDetails.season": "Sezon {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "<PERSON><PERSON>masını düzenlerken beklenmedik bir hata oluştu.", "components.IssueDetails.toasteditdescriptionsuccess": "Sorun açıklaması başarıyla düzenlendi!", "components.IssueDetails.toastissuedeletefailed": "<PERSON><PERSON> a<PERSON> silinirken beklenmedik bir hata oluştu.", "components.IssueDetails.toaststatusupdated": "<PERSON>run ba<PERSON><PERSON><PERSON><PERSON> güncellen<PERSON>!", "components.IssueDetails.unknownissuetype": "Bilinmiyor", "components.IssueList.IssueItem.episodes": "{episodeCount} <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.issuestatus": "Durum", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.opened": "Açıldı", "components.IssueList.IssueItem.openeduserdate": "{user } tara<PERSON><PERSON><PERSON><PERSON>, {date}", "components.IssueList.IssueItem.problemepisode": "<PERSON><PERSON><PERSON> Etkilenen Bölümler", "components.IssueList.IssueItem.seasons": "{seasonCount} Sezon", "components.IssueList.IssueItem.unknownissuetype": "Bilinmiyor", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON>", "components.IssueList.issues": "<PERSON><PERSON><PERSON>", "components.IssueList.showallissues": "<PERSON><PERSON>m Sorunları Göster", "components.IssueList.sortAdded": "En Son <PERSON>", "components.IssueList.sortModified": "En Son Düzenlenen", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "<PERSON><PERSON><PERSON><PERSON><PERSON> {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Ekstralar", "components.IssueModal.CreateIssueModal.problemepisode": "<PERSON><PERSON><PERSON> Etkilenen Bölümler", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON> Sezon<PERSON>", "components.IssueModal.CreateIssueModal.season": "Sezon {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<strong>{title}</strong> i<PERSON><PERSON> hata bildirisi gönderilmiştir!", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Lütfen açıklamayı doldurunuz", "components.IssueModal.CreateIssueModal.whatswrong": "<PERSON><PERSON>?", "components.IssueModal.issueAudio": "Ses", "components.IssueModal.issueOther": "<PERSON><PERSON><PERSON>", "components.IssueModal.issueSubtitles": "Altyazı", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "Varsayılan ({language})", "components.LanguageSelector.originalLanguageDefault": "<PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Film & Dizi Ara", "components.Layout.Sidebar.browsemovies": "<PERSON><PERSON>", "components.Layout.Sidebar.browsetv": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.dashboard": "Keşfet", "components.Layout.Sidebar.issues": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.requests": "<PERSON><PERSON>", "components.Layout.Sidebar.settings": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Film Talepleri", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "<PERSON><PERSON>", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.UserDropdown.requests": "<PERSON><PERSON>", "components.Layout.UserDropdown.settings": "<PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Çıkış Yap", "components.Layout.UserWarnings.emailRequired": "Bir e-mail adresi gereklidir.", "components.Layout.UserWarnings.passwordRequired": "Bir şifre girmeniz gerekmektedir.", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> Geliştirme Sürümü", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> Stabil Sürüm", "components.Login.credentialerror": "Kullanıcı adı ya da şifre yanlış.", "components.Login.description": "{applicationName} uygulamasına ilk defa giriş yaptığınız için geçerli bir e-mail adresi eklemeniz gerekmektedir.", "components.Login.email": "E-mail Adresi", "components.Login.forgotpassword": "Şifrenizi mi unuttunuz?", "components.Login.initialsignin": "Bağlan", "components.Login.initialsigningin": "Bağlanıyor…", "components.Login.password": "Şifre", "components.Login.save": "<PERSON><PERSON>", "components.Login.saving": "Ekleni<PERSON>r…", "components.Login.signin": "Oturum Aç", "components.Login.signingin": "Oturum Açılıyor…", "components.Login.signinwithjellyfin": "{mediaServerName} he<PERSON>bı<PERSON><PERSON> kullan", "components.Login.signinwithoverseerr": "{applicationTitle} he<PERSON>b<PERSON><PERSON><PERSON> kullan", "components.Login.signinwithplex": "Plex hesabını kullan", "components.Login.title": "E-mail Ekle", "components.Login.username": "Kullanıcı Adı", "components.Login.validationEmailFormat": "Geçersiz e-mail", "components.Login.validationemailformat": "Geçerli bir e-mail adresi gereklidir", "components.Login.validationemailrequired": "Geçerli bir e-mail adresi sağlamalısınız", "components.Login.validationhostformat": "Geçerli bir URl gereklidir", "components.Login.validationhostrequired": "{mediaServerName} URL'si gereklidir", "components.Login.validationusernamerequired": "Kullanıcı Adı girmeniz gereklidir", "components.ManageSlideOver.alltime": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.downloadstatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalAdvanced": "Gelişmiş", "components.ManageSlideOver.manageModalClearMedia": "<PERSON><PERSON><PERSON>", "components.Discover.tmdbtvgenre": "TMBD Dizi Türleri", "components.Discover.updatefailed": "Keşfet ayarları güncellenirken beklenmedik bir hata oluştu.", "components.IssueDetails.IssueComment.areyousuredelete": "<PERSON>u yo<PERSON>u si<PERSON> istediğinize emin misiniz?", "components.IssueDetails.closeissueandcomment": "Yorum ekleyerek Ka<PERSON>", "components.IssueDetails.deleteissueconfirm": "Bu hata bildirisini silmek istediğinize emin misiniz?", "components.IssueDetails.openedby": "#{issueId} {relativeTime} tarihinde {username} tarafı<PERSON>n açıldı", "components.IssueDetails.reopenissueandcomment": "<PERSON><PERSON> <PERSON><PERSON><PERSON> be<PERSON> Ye<PERSON>den Aç", "components.IssueDetails.toastissuedeleted": "<PERSON>run Bildirisi başarıyla silindi!", "components.IssueDetails.toaststatusupdatefailed": "<PERSON><PERSON> güncellenirken beklenmedik bir hata oluştu.", "components.IssueModal.CreateIssueModal.allepisodes": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.providedetail": "Lütfen karşılaştığınız hatayı detaylı bir biçimde açıklayınız.", "components.IssueModal.CreateIssueModal.reportissue": "<PERSON><PERSON>", "components.Layout.LanguagePicker.displaylanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.validationpasswordrequired": "Bir ş<PERSON>re girmeniz gere<PERSON>lid<PERSON>", "components.IssueModal.CreateIssueModal.toastFailedCreate": "<PERSON><PERSON><PERSON> bildirirken beklenmedik bir hata o<PERSON>.", "components.Layout.UserWarnings.emailInvalid": "E-mail adresi geçersiz.", "components.Login.signinheader": "<PERSON><PERSON> etmek için oturum aç", "components.Login.validationEmailRequired": "Bir e-mail adresi girmelisi<PERSON>z", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} behind", "components.Login.emailtooltip": "E-mail adresi {mediaServerName} uygulamasıyla ilişik olmak zorunda değildir.", "components.Login.loginerror": "Oturum açarken bir hata <PERSON>.", "components.ManageSlideOver.manageModalClearMediaWarning": "* Bu işlem geri döndürülemez bir biçimde {mediaType} ile alakalı her türlü talepte dahil olmak üzere tüm verileri silecektir. Eğer ki bu öğe {mediaServerName} kütüphanenizde bulunuyorsa, tüm medya bilgileri bir sonra ki taramada yeniden oluşturulucaktır.", "components.ManageSlideOver.manageModalIssues": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia": "İçerik", "components.ManageSlideOver.manageModalMedia4k": "4K İçerik", "components.ManageSlideOver.manageModalNoRequests": "Hiç talep yok.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Bu işlem geri döndürülemez bir biçimde {arr}'dan {mediaType} öğesini ve dosyalarını kaldıracaktır.", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "{mediaType} içeriğini yönet", "components.ManageSlideOver.markallseasons4kavailable": "Tüm Sezonları 4K Oynatılabilir Olarak İşaretle", "components.ManageSlideOver.mark4kavailable": "4K Oynatılabilir Olarak İşaretle", "components.ManageSlideOver.markallseasonsavailable": "Tüm Sezonları Oynatılabilir Olarak İşaretle", "components.ManageSlideOver.markavailable": "Oynatılabilir Olarak İşaretle", "components.ManageSlideOver.movie": "Film", "components.ManageSlideOver.openarr": "{arr}'da <PERSON><PERSON>", "components.ManageSlideOver.openarr4k": "{arr}'da 4K Olarak A<PERSON>", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.pastdays": "{days, number} <PERSON><PERSON><PERSON>", "components.ManageSlideOver.playedby": "Tarafından Oynatıldı", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> oynatma", "components.ManageSlideOver.removearr": "{arr}'dan <PERSON>", "components.ManageSlideOver.tvshow": "dizi", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON>", "components.MovieDetails.MovieCast.fullcast": "<PERSON><PERSON><PERSON>", "components.MovieDetails.MovieCrew.fullcrew": "<PERSON><PERSON><PERSON>", "components.MovieDetails.budget": "Bütçe", "components.MovieDetails.cast": "Oyuncular", "components.MovieDetails.digitalrelease": "Dijital Sürüm", "components.MovieDetails.downloadstatus": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.managemovie": "<PERSON><PERSON>", "components.MovieDetails.mark4kavailable": "4K Oynatılabilir Olarak İşaretle", "components.MovieDetails.markavailable": "Oynatılabilir Olarak İşaretle", "components.MovieDetails.openradarr": "Filmi Radarr'da <PERSON>", "components.MovieDetails.openradarr4k": "Filmi 4K Radarr'da Aç", "components.MovieDetails.originallanguage": "Orijinal Dil", "components.MovieDetails.originaltitle": "Orijinal Başlık", "components.MovieDetails.overview": "Özet", "components.MovieDetails.physicalrelease": "Fiziksel Sürüm", "components.MovieDetails.play": "İçeriği {mediaServerName} üzerinde oynat", "components.MovieDetails.play4k": "İçeriği {mediaServerName} üzerinde 4K oynat", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.MovieDetails.reportissue": "<PERSON><PERSON>", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes İzleyici Puanı", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.runtime": "{minutes} dakika", "components.MovieDetails.showmore": "<PERSON><PERSON>", "components.MovieDetails.showless": "<PERSON><PERSON>", "components.MovieDetails.similar": "Benzer İçerikler", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {Stüdyolar}}", "components.MovieDetails.theatricalrelease": "Sinemaya Çıkışı", "components.MovieDetails.tmdbuserscore": "TMBD Kullanıcı Skoru", "components.MovieDetails.viewfullcrew": "<PERSON><PERSON><PERSON>", "components.MovieDetails.watchtrailer": "Fragmanı İzle", "components.NotificationTypeSelector.adminissuereopenedDescription": "<PERSON>run bildirileri kullanıcılar tarafından tekrardan açıldığında bildirim gönder.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Sorun bildirileri kullanıcılar tarafından çözüldüğünde bildirim gönder.", "components.NotificationTypeSelector.issuecomment": "<PERSON><PERSON>", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON><PERSON><PERSON> bildirildiğinde bildirim gönder.", "components.NotificationTypeSelector.issuereopened": "<PERSON>run <PERSON> Açıldı", "components.NotificationTypeSelector.issueresolved": "<PERSON><PERSON>", "components.NotificationTypeSelector.issueresolvedDescription": "Sorunlar çözüldüğünde bildirim gönder.", "components.NotificationTypeSelector.mediaAutoApproved": "Otomatik Kabul Edilen <PERSON>", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Kullanıcıların içerik talepleri otomatik olarak kabul edilenler için bildirim gönder.", "components.NotificationTypeSelector.mediaapproved": "Kabul Edilen <PERSON>", "components.NotificationTypeSelector.mediaautorequested": "Talep Otomatik Olarak Gönderildi", "components.NotificationTypeSelector.mediaautorequestedDescription": "Kullanıcıların otomatik içerik talepleri eğer ki sizin İzleme Listenizden bir içerik içeriyor ise bildirim gönder.", "components.NotificationTypeSelector.mediaavailable": "İzlenebilir İçerik", "components.NotificationTypeSelector.mediaavailableDescription": "İçerik izlenebilir olduğunda bildirim gönder.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediafailed": "İşlemdeyken İptal Olan Talepler", "components.NotificationTypeSelector.mediafailedDescription": "İçerik talepleri Sonarr'a ya da Radarr'a eklenirken hata oluşursa bildirim gönder.", "components.NotificationTypeSelector.mediarequested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.notificationTypes": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.userissuecommentDescription": "Gönderdiğiniz hata bildirimine yorum geldiğinde bildirim gönder.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON><PERSON> hata bildirdiklerinde bildirim gönder.", "components.NotificationTypeSelector.userissuereopenedDescription": "Gönderdiğiniz hata bildirimi yeniden açıldığında bildirim gönder.", "components.NotificationTypeSelector.userissueresolvedDescription": "Gönderdiğiniz hata bildirimi çözüldüğünde bildirim gönder.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Gönderdiğiniz içerik talepleri kabul edildiğinde bildirim gönder.", "components.NotificationTypeSelector.usermediaavailableDescription": "Gönderdiğiniz içerik talepleri izlenebilir olduğunda bildirim gönder.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Gönderdiğiniz içerik talepleri reddedildiğinde bildirim gönder.", "components.NotificationTypeSelector.usermediarequestedDescription": "<PERSON><PERSON><PERSON> k<PERSON>anıcılar doğrulama gerektiren içerik talepleri gönderdiğinde bildirim gönder.", "components.PermissionEdit.admin": "Yönetici", "components.PermissionEdit.adminDescription": "Tam yönetici erişimi. <PERSON><PERSON><PERSON> tüm izin denetlemelerini görmezden gelir.", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON>ş<PERSON><PERSON>", "components.PermissionEdit.advancedrequestDescription": "Gelişmiş talep ayarlarını düzenleyebilme izni ver.", "components.PermissionEdit.autoapprove": "Otomatik Onay", "components.PermissionEdit.autoapprove4kMovies": "4K Filmler İçin Otomatik Onay", "components.PermissionEdit.autoapprove4kMoviesDescription": "4K Film taleplerinin otomatik onaylanmasına izin ver.", "components.PermissionEdit.autoapprove4kSeries": "4K Diziler İçin Otomatik Onay", "components.PermissionEdit.autoapprove4kSeriesDescription": "4K Dizi taleplerinin otomatik onaylanmasına izin ver.", "components.PermissionEdit.autoapproveDescription": "4K olmayan içerik taleplerinin otomatik onaylanması iznini ver.", "components.PermissionEdit.autoapproveMovies": "Filmler <PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapproveSeries": "Diziler İçin O<PERSON>atik Onay", "components.PermissionEdit.autoapproveSeriesDescription": "4K olmayan dizi taleplerinin otomatik onaylanması iznini ver.", "components.PermissionEdit.autorequest": "Otomatize Talep", "components.PermissionEdit.autorequestMovies": "Filmleri Otomatize Talep Ett", "components.PermissionEdit.autorequestMoviesDescription": "Kullanıcının Plex İzleme listesinde olan ve 4K olmayan filmler için otomatik olarak talep etme iznini ver.", "components.PermissionEdit.autorequestSeries": "Dizileri Otomatize Talep Ett", "components.PermissionEdit.autorequestSeriesDescription": "Kullanıcının Plex İzleme listesinde olan ve 4K olmayan diziler için otomatik olarak talep etme iznini ver.", "components.PermissionEdit.createissues": "<PERSON><PERSON>", "components.PermissionEdit.manageissues": "Sorunları Yönetme", "components.PermissionEdit.manageissuesDescription": "İçeriklerde ki hataları yönetme iznini ver.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.request": "<PERSON><PERSON>", "components.PermissionEdit.request4k": "4K Talep Etme", "components.PermissionEdit.request4kDescription": "4K içerik talep edilmesine izin ver.", "components.PermissionEdit.request4kMovies": "4K Filmleri Talep Etme", "components.PermissionEdit.request4kMoviesDescription": "4K film talep edilmesine izin ver.", "components.PermissionEdit.request4kTv": "4K Dizi Talep Etme", "components.PermissionEdit.request4kTvDescription": "4K dizi talep edilmesine izin ver.", "components.PermissionEdit.requestDescription": "4K olmayan içeriklerin talep edilmesine izin ver.", "components.PermissionEdit.requestMovies": "Film Talep Etme", "components.PermissionEdit.requestMoviesDescription": "4K olmayan filmlerin talep edilmesine izin ver.", "components.PermissionEdit.requestTv": "<PERSON><PERSON> Etme", "components.PermissionEdit.requestTvDescription": "4K olmayan dizilerin talep edilmesine izin ver.", "components.PermissionEdit.users": "Kullanıcıları Yönetme", "components.PermissionEdit.viewissues": "Sorunları Görme", "components.PermissionEdit.viewissuesDescription": "İçeriklerle alakalı dieğr kullanıcıların raporladığı hataları görme iznini ver.", "components.PermissionEdit.viewrequests": "Tale<PERSON><PERSON> Görme", "components.PermissionEdit.viewrequestsDescription": "<PERSON><PERSON><PERSON> k<PERSON>anıcılar tarafından istenen içerikleri görme iznini ver.", "components.PermissionEdit.viewwatchlists": "{mediaServerName} <PERSON>zle<PERSON> Listelerini Görme", "components.PersonDetails.alsoknownas": "<PERSON><PERSON><PERSON> Diğer: {names}", "components.PersonDetails.appearsin": "<PERSON>r Aldığı İçerikler", "components.PersonDetails.ascharacter": "{character} r<PERSON><PERSON><PERSON>", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.crewmember": "Bilinen İşleri", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count} gün", "components.QuotaSelector.movies": "{count} film", "components.QuotaSelector.seasons": "{count} sezon", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{sezon} per {quotaDays} {gün}</quotaUnits>", "components.QuotaSelector.unlimited": "Sınırsız", "components.RegionSelector.regionDefault": "<PERSON><PERSON><PERSON>", "components.RegionSelector.regionServerDefault": "Varsayılan ({region})", "components.RequestBlock.approve": "Talebi Kabul Et", "components.RequestBlock.decline": "<PERSON><PERSON>", "components.RequestBlock.delete": "Tale<PERSON>", "components.RequestBlock.edit": "<PERSON><PERSON>", "components.RequestBlock.languageprofile": "<PERSON><PERSON>", "components.RequestBlock.profilechanged": "<PERSON><PERSON>", "components.RequestBlock.requestdate": "Talep Etme Ta<PERSON>hi", "components.RequestBlock.requestedby": "<PERSON><PERSON>", "components.RequestBlock.requestoverrides": "Geçersiz Talep", "components.RequestBlock.rootfolder": "<PERSON>", "components.RequestBlock.seasons": "{seasonCount, plural, one {Sezon} other {Sezon}}", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestButton.approverequest": "Talebi Kabul Et", "components.RequestButton.approverequest4k": "4K Talebi Kabul Et", "components.RequestButton.approverequests": "{requestCount, plural, one {<PERSON><PERSON>} other {{requestCount} <PERSON><PERSON>}} Kabul Et", "components.RequestButton.declinerequest": "<PERSON><PERSON>", "components.RequestButton.declinerequest4k": "4K Talebi Reddet", "components.RequestButton.declinerequests": "{requestCount, plural, one {<PERSON><PERSON>} other {{requestCount} <PERSON><PERSON>}} Reddet", "components.RequestButton.requestmore4k": "Daha Fazla 4K Talep Et", "components.RequestButton.viewrequest": "<PERSON><PERSON><PERSON> Gö<PERSON>", "components.RequestButton.viewrequest4k": "4K Talepleri Gör", "components.RequestCard.approverequest": "Talebi Kabul Et", "components.RequestCard.cancelrequest": "Talebi İptal Et", "components.RequestCard.declinerequest": "<PERSON><PERSON>", "components.RequestCard.deleterequest": "Tale<PERSON>", "components.RequestCard.failedretry": "Tekrardan talep gönderirken bir hata o<PERSON>.", "components.RequestCard.mediaerror": "{mediaType} Bulunamadı", "components.RequestCard.seasons": "{seasonCount} Sezon", "components.RequestCard.tmdbid": "TMDB ID'si", "components.RequestCard.tvdbid": "TheTVDB ID'si", "components.RequestCard.unknowntitle": "Bilinmeyen İçerik", "components.RequestList.RequestItem.deleterequest": "Tale<PERSON>", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON>", "components.RequestList.RequestItem.failedretry": "Tekrardan talep gönderirken bir hata o<PERSON>.", "components.RequestList.RequestItem.mediaerror": "{mediaType} Bulunamadı", "components.RequestList.RequestItem.modified": "<PERSON>:", "components.RequestList.RequestItem.modifieduserdate": "{user } tara<PERSON><PERSON><PERSON><PERSON>, {date}", "components.RequestList.RequestItem.requested": "<PERSON><PERSON> Edilme:", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Sezon} other {Sezon}}", "components.RequestList.RequestItem.tmdbid": "TMDB ID'si", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID'si", "components.RequestList.RequestItem.unknowntitle": "Bilinmeyen İçerik", "components.RequestList.requests": "<PERSON><PERSON>", "components.RequestList.showallrequests": "<PERSON><PERSON><PERSON>", "components.RequestList.sortAdded": "En Son <PERSON>", "components.RequestModal.AdvancedRequester.advancedoptions": "Gelişmiş", "components.RequestModal.AdvancedRequester.animenote": "* Bu dizi bir animedir.", "components.RequestModal.AdvancedRequester.default": "{name} (Varsayılan)", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON><PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "Etiket yok.", "components.RequestModal.AdvancedRequester.qualityprofile": "<PERSON><PERSON>", "components.RequestModal.AdvancedRequester.rootfolder": "<PERSON>", "components.RequestModal.AdvancedRequester.selecttags": "Etiketleri seç", "components.RequestModal.AdvancedRequester.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.movie": "Film", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {Film} other {Film}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Sezon isteteme limitiniz yetmemektedir", "components.RequestModal.QuotaDisplay.quotaLink": "Talep limitlerinizin özetini <ProfileLink>profil <PERSON><PERSON></ProfileLink> görüntüleyebilirsiniz.", "components.RequestModal.QuotaDisplay.requestsremaining": "{type} {remaining, plural, one {talep} other {talep}} limitin {remaining, plural, =0 {kalmadı} other {<strong>#</strong>}}", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Bu kullanıcının bu dizinin <strong>{sezonlarını}</strong> talep edebilmesi için en az {seasons, plural, one {sezon talebi} other {sezon talebi}} kadar talep göndermesi gerekiyor.", "components.RequestModal.QuotaDisplay.season": "Sezon", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezon} other {sezon}}", "components.RequestModal.SearchByNameModal.nomatches": "<PERSON>u dizi için uygun bir eşleşme bulamadık.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Bu diziyi otomatik olarak eşleştiremedik, lütfen aşağıdan bir eşleşme seçin.", "components.RequestModal.alreadyrequested": "Zaten Talep Edildi", "components.RequestModal.approve": "Talebi Kabul Et", "components.RequestModal.autoapproval": "Otomatik Kabul", "components.RequestModal.cancel": "Talebi İptal Et", "components.RequestModal.edit": "<PERSON><PERSON>", "components.RequestModal.errorediting": "Talep düzenlenirken beklenmedik bir hatayla karşılaşıldı.", "components.RequestModal.numberofepisodes": "# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.pending4krequest": "4K <PERSON><PERSON>", "components.RequestModal.pendingapproval": "Gönderdiğiniz talep onaylama aşamasında.", "components.RequestModal.pendingrequest": "<PERSON><PERSON>", "components.RequestModal.requestCancel": "<strong>{title}</strong> i<PERSON><PERSON> reddedildi.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> ba<PERSON><PERSON><PERSON><PERSON> talep edildi!", "components.RequestModal.requestcancelled": "<strong>{title}</strong> i<PERSON><PERSON> reddedildi.", "components.RequestModal.requestcollection4ktitle": "Koleksiyonu 4K Kalitesiyle Talep Et", "components.RequestModal.requestcollectiontitle": "Koleksiyonu Talep Et", "components.RequestModal.requestedited": "<strong>{title}</strong> i<PERSON><PERSON> g<PERSON>n talep başar<PERSON><PERSON> düzenlendi!", "components.RequestModal.requestfrom": "{username} kullanıcısının talepleri onay bekliyor.", "components.RequestModal.requestmovie4ktitle": "Filmi 4K Talep Et", "components.RequestModal.requestmovies": "{count} Filmi Talep Et", "components.RequestModal.requestmovietitle": "Filmi Talep Et", "components.RequestModal.requestseasons4k": "{seasonCount} Sezonu 4K Talep Et", "components.RequestModal.requestseries4ktitle": "Diziyi 4K Talep Et", "components.RequestModal.requestseriestitle": "<PERSON><PERSON>yi Talep Et", "components.RequestModal.season": "Sezon", "components.RequestModal.seasonnumber": "{number}. <PERSON><PERSON>", "components.RequestModal.selectmovies": "Film(leri) Seç", "components.RequestModal.selectseason": "<PERSON><PERSON>(leri) Seç", "components.ResetPassword.confirmpassword": "<PERSON>if<PERSON><PERSON>", "components.ResetPassword.email": "E-mail Adresi", "components.ResetPassword.emailresetlink": "E-mail <PERSON><PERSON>", "components.ResetPassword.password": "Şifre", "components.ResetPassword.passwordreset": "Şifreyi <PERSON>", "components.ResetPassword.resetpassword": "Şifrenizi sıfırlayın", "components.ResetPassword.resetpasswordsuccessmessage": "Şifre sıfırlama başarılı!", "components.ResetPassword.validationemailrequired": "Geçerli bir e-mail adresi sağlamalısınız", "components.ResetPassword.validationpasswordmatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uyuşmuyor", "components.ResetPassword.validationpasswordminchars": "Girdiğiniz şifre çok kısa, en az 8 karakterden oluşmalıdır", "components.ResetPassword.validationpasswordrequired": "Bir ş<PERSON>re girmeniz gere<PERSON>lid<PERSON>", "components.Search.search": "Ara", "components.Search.searchresults": "<PERSON>ma <PERSON>", "components.Selector.nooptions": "<PERSON><PERSON>ç Bulunamadı.", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>…", "components.Selector.searchKeywords": "<PERSON><PERSON><PERSON> k<PERSON> a<PERSON>…", "components.Selector.searchStudios": "Stüdyolarda ara…", "components.Selector.showless": "<PERSON><PERSON>", "components.Selector.showmore": "<PERSON><PERSON>", "components.Selector.starttyping": "Ara<PERSON>k i<PERSON> ya<PERSON> ba<PERSON>.", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify bildirim ayarları kaydedildi!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify deneme bildirimi g<PERSON>i.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify deneme bildirimi gö<PERSON>ildi!", "components.Settings.Notifications.NotificationsGotify.token": "Uygulama <PERSON>i", "components.Settings.Notifications.NotificationsGotify.url": "Sunucu URL'si", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Bir uygulama Token'i girmeniz gerekmektedir", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Geçerli bir URL girmelisiniz", "components.ManageSlideOver.removearr4k": "4K {arr}'dan <PERSON>", "components.NotificationTypeSelector.adminissuecommentDescription": "<PERSON><PERSON><PERSON> k<PERSON> hata bildirilerine yorum yaptıklarında bildirim gönder.", "components.NotificationTypeSelector.mediadeclinedDescription": "Talep reddedildiğinde bildirim gö<PERSON>.", "components.PermissionEdit.createissuesDescription": "İçeriklerde ki hataları bildirme iznini ver.", "components.PermissionEdit.viewrecentDescription": "Yakın zamanda eklenen içerikleri görme iznini ver.", "components.RequestBlock.lastmodifiedby": "<PERSON>", "components.RequestButton.decline4krequests": "{requestCount, plural, one {4K Talebi} Reddet other {{requestCount} 4K Requests}}", "components.RequestButton.requestmore": "Daha Fazla Talep Et", "components.RequestCard.editrequest": "<PERSON><PERSON>", "components.RequestModal.requestApproved": "<strong>{title}</strong> i<PERSON><PERSON> tale<PERSON> onaylandı!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsGotify.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.MovieDetails.imdbuserscore": "IMDB Kullanıcı Puanı", "components.MovieDetails.overviewunavailable": "Özet mevcut değil.", "components.MovieDetails.productioncountries": "Ya<PERSON><PERSON>m<PERSON>ı {countryCount, plural, one {<PERSON>lk<PERSON>} other {<PERSON><PERSON><PERSON>r}}", "components.MovieDetails.streamingproviders": "İçeriğin Erişilebilir Olduğu Platformlar", "components.NotificationTypeSelector.issuecommentDescription": "<PERSON><PERSON> bildirilerine yeni yorum geldiğinde bildirim gönder.", "components.NotificationTypeSelector.issuecreated": "<PERSON><PERSON>", "components.NotificationTypeSelector.issuereopenedDescription": "Sorunlar yeniden açıldığında bildirim gönder.", "components.NotificationTypeSelector.mediaapprovedDescription": "Kullanıcıların içerik talepleri elle kabul edildiğinde bildirim gönder.", "components.NotificationTypeSelector.mediarequestedDescription": "Kullanıcılar doğrulama gerektiren içerik talepleri gönderdiğinde bildirim gönder.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON><PERSON>ıcılar otomatize içerik talepleri gönderdiğinde bildirim gönder.", "components.NotificationTypeSelector.usermediafailedDescription": "Gönderdiğiniz içerik talepleri Sonarr ya da Radarr'a eklenemediğinde bildirim gönder.", "components.PermissionEdit.autoapprove4k": "4K İçin Otomatik Onay", "components.PermissionEdit.autoapprove4kDescription": "4K İçerik taleplerinin otomatik onaylanması iznini ver.", "components.PermissionEdit.autoapproveMoviesDescription": "4K olmayan film tale<PERSON><PERSON>n otomatik onaylanması iznini ver.", "components.PermissionEdit.autorequestDescription": "Kullanıcının Plex İzleme listesinde olan içerikler için otomatik olarak talep etme iznini ver.", "components.PermissionEdit.managerequestsDescription": "İçerik taleplerini yönetme iznini ver. Bu izne sahip kullanıcının gönderdiği tüm talepler otomatik olarak kabul edilir.", "components.PermissionEdit.viewrecent": "Yakın Zamanda Eklenenleri Görme", "components.RequestModal.AdvancedRequester.requestas": "Talep Eden:", "components.RequestModal.QuotaDisplay.allowedRequests": "<strong>{limit}</strong> {type} talep etme limitin var, her <strong>{days}</strong> günde bir bu limit yenilenecektir.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <strong>{limit}</strong> {type} talep etme limiti var, her <strong>{days}</strong> günde bir bu limit yenilenecektir.", "components.PermissionEdit.usersDescription": "Kullanıcıları yönetme iznini ver. Bu izne sahip kullanıcılar Yönetici yetkisine sahip kişileri düzenleyemezler ya da Yönetici yetkisi veremezler.", "components.PermissionEdit.viewwatchlistsDescription": "<PERSON><PERSON><PERSON> {mediaServerName} İzleme <PERSON>elerini görme iznini ver.", "components.RequestList.RequestItem.cancelRequest": "Talebi İptal Et", "components.RequestList.sortModified": "En Son Düzenlenen", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Bu kullanıcının talep limitlerinin özetini <ProfileLink>profil sayfası<PERSON></ProfileLink> görüntüleyebilirsiniz.", "components.RequestModal.requesterror": "Talebi gönderirken beklenmedik bir hata oluştu.", "components.RequestModal.requestseasons": "{seasonCount} Se<PERSON><PERSON>p Et", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aracıyı Etkinleştir", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{Film} per {quotaDays} {gün}</quotaUnits>", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON>", "components.RequestModal.requestadmin": "Bu talep otomatik olarak onaylanacaktır.", "components.RequestButton.approve4krequests": "{requestCount, plural, one {4K Talebi} Kabul Et other {{requestCount} 4K Requests}}", "components.RequestModal.QuotaDisplay.requiredquota": "<PERSON><PERSON> dizinin <strong>{sezonlarını}</strong> istetebilmek için en azından {seasons, plural, one {sezon talebi} other {sezon talepleri}} kadar talep göndermeniz gerekiyor.", "components.RequestModal.requestmovies4k": "{count} Filmi 4K Talep Et", "components.ResetPassword.gobacklogin": "Oturum Açma Sayfasına Dön", "components.ResetPassword.requestresetlinksuccessmessage": "Eğer ki girilen e-mail adresi gerçek bir kişiyle ilişkiliyse şifre sıfırlama linki gönderilecektir.", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL'nizin sonunda slash (eğik <PERSON>iz<PERSON>) olmamalıdır", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsLunaSea.profileName": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<PERSON><PERSON><PERSON> ki <code>varsay<PERSON>lan</code> profil kullanılmıyorsa gereklidir", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea deneme bildirimi <PERSON>.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea deneme bildirimi g<PERSON>ildi!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Geçerli bir URL girmelisiniz", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL'si", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "<PushbulletSettingsLink>Hesap <PERSON>ını<PERSON>n</PushbulletSettingsLink> bir token oluşturun", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Kanal Etiketi", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet bildirim ayarları kaydedildi!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet deneme bildirimi gö<PERSON>ildi!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Bir erişim token'i sağlamalısınız", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.NotificationsPushover.accessToken": "Uygulama API'sinin <PERSON>'i", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea bildirim ayarları kaydedildi!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet deneme bildirimi <PERSON>.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Kullanıcı ya da cihaz tabanlı <LunaSeaLink>webhook bildirimi URL'niz</LunaSeaLink>", "components.Settings.RadarrModal.released": "Yayınlandı", "components.Settings.RadarrModal.rootfolder": "<PERSON>", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Cihaz Varsayılanı", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover bildirim ayarları kaydedildi!", "components.Settings.Notifications.NotificationsPushover.sound": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "<PERSON>ush<PERSON> deneme bildirimi <PERSON>.", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "JJellyseerr ile kullanılmak üzere <ApplicationRegistrationLink>bir uygulama kaydedin</ApplicationRegistrationLink>", "components.Settings.SettingsAbout.helppaycoffee": "Bize Bir <PERSON>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover deneme bildirimi g<PERSON>il<PERSON>!", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Geçerli bir uygulama token'i sağlamalısınız", "components.Settings.Notifications.NotificationsPushover.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.NotificationsPushover.userToken": "Kullanıcı ya da Grup Kimliği", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Geçerli bir kullanıcı ya da grup kimliği sağlamalısınız", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slacxk deneme bildirimi g<PERSON>.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "<PERSON>lack deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack deneme bildirimi g<PERSON>il<PERSON>!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Geçerli bir URL girmelisiniz", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL'si", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Web push bildirimlerini alabilmek iç<PERSON>'in HTTPS üzerinden sunulması gerekir.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web deneme bildirimi gö<PERSON>ilemedi.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web deneme bildirimi gönderiliyor…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web deneme bildirimi gönderildi!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web bildirim ayarları kaydedildi!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.NotificationsWebhook.authheader": "Yetkilendirme Üstbilgisi (Header'ı)", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload'I", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Varsayılan Ayarlara Sıfırla", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON Payload'ı başarıyla sıfırlandı!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Şablon Değişkeni İçin Yardım", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Webhook deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook deneme bildirimi gönderildi!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Geçerli bir JSON Payload'ı sağlamalısınız", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL'si", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook bildirim ayarları kaydedildi!", "components.Settings.Notifications.agentenabled": "Aracıyı Etkinleştir", "components.Settings.Notifications.allowselfsigned": "Kendinden İmzalı Sertifikaları Kabul Et", "components.Settings.Notifications.authPass": "SMPT Şifresi", "components.Settings.Notifications.botAPI": "Bot Yetkilendirme Token'i", "components.Settings.Notifications.botApiTip": "<PERSON><PERSON><PERSON>rr ile kullanılmak üzere <CreateBotLink>bir bot oluşturun</CreateBotLink>", "components.Settings.Notifications.botAvatarUrl": "Bot Profil Resmi URL'si", "components.Settings.Notifications.botUsername": "Bot Kullanıcı Adı", "components.Settings.Notifications.chatId": "Sohbet ID'si", "components.Settings.Notifications.chatIdTip": "Botunuzla sohbet etmek için <GetIdBotLink>@get_id_bot</GetIdBotLink> is<PERSON><PERSON><PERSON>, <code>/my_id</code> komutunu kullanın ve sohbeti başlatın", "components.Settings.Notifications.discordsettingsfailed": "Discord bildirim a<PERSON>ları kaydedilemedi.", "components.Settings.Notifications.emailsender": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.emailsettingsfailed": "E-mail bildirim a<PERSON>ları kaydedilemedi.", "components.Settings.Notifications.emailsettingssaved": "E-mail bildirim ayarları kaydedildi!", "components.Settings.Notifications.enableMentions": "Bahsetmeleri Etkinleştirin", "components.Settings.Notifications.encryption": "Şifreleme <PERSON>", "components.Settings.Notifications.encryptionDefault": "Mümkünse STARTTLS kullan", "components.Settings.Notifications.encryptionImplicitTls": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Implict) TLS Kullan", "components.Settings.Notifications.encryptionNone": "Hiç<PERSON>i", "components.Settings.Notifications.encryptionTip": "Genel olarak Örtülü (Implict) TLS 465 numaralı portu kullanır, STARTTLS ise 587 numaralı portu kullanır", "components.Settings.Notifications.pgpPassword": "PGP Şifresi", "components.Settings.Notifications.pgpPrivateKey": "PGP Gizli Anahtar", "components.Settings.Notifications.pgpPrivateKeyTip": "<OpenPgpLink>OpenPGP</OpenPgpLink> kull<PERSON>rak e-mailleri imzala ve şifrele", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.sendSilentlyTip": "Bildirimler sessiz <PERSON>", "components.Settings.Notifications.senderName": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.smtpHost": "SMPT Sunucu Adresi", "components.Settings.Notifications.smtpPort": "SMPT Port'u", "components.Settings.Notifications.telegramsettingsfailed": "Telegram bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.telegramsettingssaved": "Telegram bildirim ayarları kaydedildi!", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord deneme bildirimi gö<PERSON>ildi!", "components.Settings.Notifications.toastEmailTestFailed": "E-mail deneme bildirimi <PERSON>.", "components.Settings.Notifications.toastEmailTestSuccess": "E-mail deneme bildirimi gö<PERSON>ildi!", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram test bild<PERSON><PERSON>.", "components.Settings.Notifications.toastTelegramTestSending": "Telegram deneme bildirimi g<PERSON>…", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram deneme bildirimi gönderildi!", "components.Settings.Notifications.userEmailRequired": "Kullanıcı e-mail'ini zorunlu tut", "components.Settings.Notifications.validationChatIdRequired": "Geçerli bir sohbet ID'si sağlamalısın", "components.Settings.Notifications.validationEmail": "Geçerli bir e-mail adresi sağlamalısın", "components.Settings.Notifications.validationPgpPassword": "Bir PGP şifresi sağlamalısın", "components.Settings.Notifications.validationPgpPrivateKey": "Geçerli bir PGP gizli anahtarı sağlamalısın", "components.Settings.Notifications.validationSmtpPortRequired": "Geçerli bir port numarası girmelisin", "components.Settings.Notifications.validationTypes": "En azından bir adet bildirim türü seçmelisiniz", "components.Settings.Notifications.validationUrl": "Geçerli bir URL girmelisiniz", "components.Settings.Notifications.webhookUrl": "Webhook URL'si", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.announced": "Duyurulm<PERSON>ş", "components.Settings.RadarrModal.apiKey": "API Anahtarı", "components.Settings.RadarrModal.baseUrl": "Temel URL Adresi", "components.Settings.RadarrModal.create4kradarr": "Yeni Bir 4K Radarr Sunucusu <PERSON>", "components.Settings.RadarrModal.createradarr": "<PERSON><PERSON>", "components.Settings.RadarrModal.default4kserver": "Varsayılan 4K Sunucu", "components.Settings.RadarrModal.defaultserver": "Varsayı<PERSON>", "components.Settings.RadarrModal.edit4kradarr": "4K Radarr <PERSON>uc<PERSON>", "components.Settings.RadarrModal.editradarr": "<PERSON><PERSON>", "components.Settings.RadarrModal.enableSearch": "Otomatik Aramayı Etkinleştir", "components.Settings.RadarrModal.externalUrl": "Harici URL", "components.Settings.RadarrModal.hostname": "Domain ya da IP Adresi", "components.Settings.RadarrModal.inCinemas": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.loadingTags": "Etiketler yükleniyor…", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON> k<PERSON>…", "components.Settings.RadarrModal.minimumAvailability": "Asgari Erişilebilirlik Ayarı", "components.Settings.RadarrModal.notagoptions": "Etiket yok.", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.RadarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectRootFolder": "<PERSON> k<PERSON> seç", "components.Settings.RadarrModal.selecttags": "Etiketleri seç", "components.Settings.RadarrModal.server4k": "4K Sunucu", "components.Settings.RadarrModal.servername": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.ssl": "SSL Kullan", "components.Settings.RadarrModal.syncEnabled": "Taramayı Etkinleştir", "components.Settings.RadarrModal.tagRequests": "Etiket Talepleri", "components.Settings.RadarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.testFirstQualityProfiles": "<PERSON><PERSON>rini görüntülemek için bağlantıyı test et", "components.Settings.RadarrModal.testFirstRootFolders": "Ana klasörleri görüntülemek için bağlantıyı test et", "components.Settings.RadarrModal.testFirstTags": "Etiketleri görüntülemek için bağlantıyı test et", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr'a bağlanılamadı.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr'a başarıyla bağlanıldı!", "components.Settings.RadarrModal.validationApplicationUrl": "Geçerli bir URL adresi girmelisiniz", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL'nizin sonunda slash (eğik <PERSON>iz<PERSON>) olmamalıdır", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL'nizin başında slash (eğik çizgi) olmalıdır", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Temel URL slash (eğik çizgi) ile bitmemelidir", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Asgari erişilebilirlik ayarı seçmelisin", "components.Settings.RadarrModal.validationNameRequired": "<PERSON><PERSON> <PERSON>ucu ismi girmel<PERSON>n", "components.Settings.RadarrModal.validationPortRequired": "Geçerli bir port numarası girmelisin", "components.Settings.RadarrModal.validationProfileRequired": "Bir kalite seçeneği seçmelisin", "components.Settings.RadarrModal.validationRootFolderRequired": "Bir ana klasör seçmelisin", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releases": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.viewchangelog": "S<PERSON><PERSON><PERSON>m Notlarını Gör", "components.Settings.SettingsAbout.Releases.viewongithub": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.about": "Hakkında", "components.Settings.SettingsAbout.appDataPath": "<PERSON><PERSON>", "components.Settings.SettingsAbout.documentation": "Dökümantasyon", "components.Settings.SettingsAbout.gettingsupport": "Destek Al", "components.Settings.SettingsAbout.githubdiscussions": "GitHub Tartışmaları", "components.Settings.SettingsAbout.preferredmethod": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "Overseerr'<PERSON> Destekle", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Destekle", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON>", "components.Settings.SettingsAbout.totalmedia": "Toplam İçerik", "components.Settings.SettingsAbout.totalrequests": "Toplam Talep", "components.Settings.SettingsAbout.uptodate": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "S<PERSON>r<PERSON><PERSON>", "components.Settings.SettingsJobsCache.availability-sync": "İçerik Kullanılabilirliğini Eşitle", "components.Settings.SettingsJobsCache.cache": "Önbelleğe Al", "components.Settings.SettingsJobsCache.cacheDescription": "<PERSON><PERSON><PERSON><PERSON>, performansı optimize etmek ve gereksiz API çağrıları yapmaktan kaçınmak için harici API uç noktalarına gelen istekleri önbelleğe alır.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} önbelleği temizlendi.", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachemisses": "Kayıplar", "components.Settings.SettingsJobsCache.cachename": "Önbellek İsmi", "components.Settings.SettingsJobsCache.canceljob": "İşlemi Du<PERSON>ur", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync": "İndirilenleri Eşitle", "components.Settings.SettingsJobsCache.download-sync-reset": "İndirilenler Eşitlemesini Sıfırla", "components.Settings.SettingsJobsCache.editJobSchedule": "İşlemi Düzenle", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Mevcut <PERSON>", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Her {jobScheduleMinutes, plural, one {dakika<PERSON>} other {{jobScheduleMinutes} dakika}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Her {jobScheduleSeconds, plural, one {saniyede} other {{jobScheduleSeconds} saniye}}", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.image-cache-cleanup": "G<PERSON>r<PERSON><PERSON><PERSON> Önbellek Temizleme", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecachecount": "Önbelleğe Alınan <PERSON>", "components.Settings.SettingsJobsCache.imagecachesize": "Toplam Önbellek Boyutu", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "<PERSON>", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "<PERSON><PERSON><PERSON> En Son Eklenenler Taraması", "components.Settings.SonarrModal.animerootfolder": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "İşlem başarıyla düzenlendi!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} iptal edildi.", "components.Settings.SettingsJobsCache.jobname": "İşlem İsmi", "components.Settings.SettingsJobsCache.jobsandcache": "İşlemler & Önbellek", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} ba<PERSON><PERSON><PERSON>.", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.nextexecution": "Bir Sonraki İşlem", "components.Settings.SettingsJobsCache.plex-full-scan": "Tam Plex <PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex En Son Eklenenler Taraması", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex İzleme Listesi Senkronizasyonu", "components.Settings.SettingsJobsCache.process": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.unknownJob": "Bilinmeyen İşlem", "components.Settings.SettingsLogs.copiedLogMessage": "Loglar panoya kopyalandı.", "components.Settings.SettingsLogs.copyToClipboard": "<PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "İlave Veri", "components.Settings.SettingsLogs.filterDebug": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Bilgilendirme", "components.Settings.SettingsLogs.filterWarn": "Uyarı", "components.Settings.SettingsLogs.label": "Etiket", "components.Settings.SettingsLogs.level": "<PERSON>ne<PERSON>", "components.Settings.SettingsLogs.logDetails": "Log Detayları", "components.Settings.SettingsLogs.logs": "Loglar", "components.Settings.SettingsLogs.message": "<PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.showall": "Tüm Logları Göster", "components.Settings.SettingsLogs.time": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.viewdetails": "Detayları Görüntüle", "components.Settings.SettingsMain.apikey": "API Anahtarı", "components.Settings.SettingsMain.applicationTitle": "Uygulama Başlığı", "components.Settings.SettingsMain.applicationurl": "Uygulama URL'si", "components.Settings.SettingsMain.cacheImages": "<PERSON>sim <PERSON>eğe Alma'yı Etkinleştir", "components.Settings.SonarrModal.apiKey": "API Anahtarı", "components.Settings.SettingsMain.general": "<PERSON><PERSON>", "components.Settings.SettingsMain.generalsettings": "<PERSON><PERSON>", "components.Settings.SettingsMain.hideAvailable": "Kullanılabilir İçerikleri Gizle", "components.Settings.SettingsMain.locale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.originallanguage": "Keşfet Dili", "components.Settings.SettingsMain.originallanguageTip": "İçerikleri orijinal dillerine göre filtrele", "components.Settings.SettingsMain.partialRequestsEnabled": "<PERSON><PERSON><PERSON><PERSON> Kabul Et", "components.Settings.SettingsMain.toastApiKeyFailure": "Yeni API anahtarı denenirken beklenmedik bir hata oluştu.", "components.Settings.SettingsMain.toastSettingsFailure": "<PERSON><PERSON><PERSON> kaydedilirken beklenmedik bir hata o<PERSON>.", "components.Settings.SettingsMain.toastSettingsSuccess": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "components.Settings.SettingsMain.validationApplicationTitle": "Bir uygulama başlığı girmelisiniz", "components.Settings.SettingsMain.validationApplicationUrl": "Geçerli bir URL adresi girmelisiniz", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL'nizin sonunda slash (eğik <PERSON>iz<PERSON>) olmamalıdır", "components.Settings.SettingsUsers.defaultPermissions": "Öntanımlı İzinler", "components.Settings.SettingsUsers.defaultPermissionsTip": "<PERSON><PERSON> kayıt yapmış kullanıcılara verilen izinler", "components.Settings.SettingsUsers.localLogin": "Local (Yerel) Oturum Açmayı Etkinleştir", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Genel Film Talep Etme Sınırı", "components.Settings.SettingsUsers.newPlexLogin": "{mediaServerName}'den Kayıtsız Oturum Açmayı Etkinleştir", "components.Settings.SettingsUsers.newPlexLoginTip": "{mediaServerName} kullanıcılarının içe aktarılmadan oturum açmalarına izin ver", "components.Settings.SettingsUsers.toastSettingsFailure": "<PERSON><PERSON><PERSON> kaydedilirken beklenmedik bir hata o<PERSON>.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Kullanıcı ayarları başarıyla kaydedildi!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Genel Dizi Talep Etme Sınırı", "components.Settings.SettingsUsers.userSettings": "Kullanıcı Ayarları", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animeSeriesType": "<PERSON><PERSON>", "components.Settings.SonarrModal.animeTags": "<PERSON><PERSON>", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.animequalityprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.create4ksonarr": "Yeni 4K Sonarr Sunuc<PERSON>u <PERSON>", "components.Settings.SonarrModal.createsonarr": "<PERSON><PERSON>", "components.Settings.SonarrModal.default4kserver": "Varsayılan 4K Sunucu", "components.Settings.SonarrModal.defaultserver": "Varsayı<PERSON>", "components.Settings.SonarrModal.edit4ksonarr": "4K <PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.enableSearch": "Otomatik Aramayı Etkinleştir", "components.Settings.SonarrModal.externalUrl": "Harici URL", "components.Settings.SonarrModal.hostname": "Domain ya da IP Adresi", "components.Settings.SonarrModal.languageprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.loadingTags": "Etiketler yükleniyor…", "components.Settings.SonarrModal.loadingprofiles": "<PERSON><PERSON>…", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON> k<PERSON>…", "components.Settings.SonarrModal.notagoptions": "Etiket yok.", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.rootfolder": "<PERSON>", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON>", "components.Settings.SonarrModal.selectLanguageProfile": "<PERSON><PERSON> seç<PERSON>ğini seç", "components.Settings.SonarrModal.selectQualityProfile": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.selectRootFolder": "<PERSON> k<PERSON> seç", "components.Settings.SonarrModal.selecttags": "Etiketleri seç", "components.Settings.SonarrModal.seriesType": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.server4k": "4K Sunucu", "components.Settings.SonarrModal.ssl": "SSL Kullan", "components.Settings.SonarrModal.syncEnabled": "Taramayı Etkinleştir", "components.Settings.SonarrModal.tagRequests": "Etiket Talepleri", "components.Settings.SonarrModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.testFirstLanguageProfiles": "<PERSON><PERSON> seçeneklerini görmek için bağlantıyı test et", "components.Settings.SonarrModal.testFirstQualityProfiles": "<PERSON><PERSON> görmek için bağlantıyı test et", "components.Settings.SonarrModal.testFirstRootFolders": "Ana klasörleri yüklemek için bağlantıyı test et", "components.Settings.SonarrModal.testFirstTags": "Etiketleri yüklemek için bağlantıyı test et", "components.Settings.SonarrModal.toastSonarrTestFailure": "Sonarr'a bağlanılamadı.", "components.Settings.SonarrModal.validationApiKeyRequired": "Bir API anahtarı girmelisiniz", "components.Settings.SonarrModal.validationApplicationUrl": "Geçerli bir URL adresi girmelisiniz", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL'nizin sonunda slash (eğik <PERSON>iz<PERSON>) olmamalıdır", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Temel URL'nizin başında slash (eğik çizgi) olmalıdır", "components.Settings.SonarrModal.validationHostnameRequired": "Geçerli bir sunucu adresi girmel<PERSON>n", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Bir dil seçeneği seçmelisin", "components.Settings.SonarrModal.validationPortRequired": "Geçerli bir port numarası girmelisin", "components.Settings.SonarrModal.validationProfileRequired": "Bir kalite seçeneği girmelisin", "components.Settings.SonarrModal.validationRootFolderRequired": "Bir ana klasör seçmelisin", "components.Settings.activeProfile": "Etkin Profil", "components.Settings.addradarr": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "components.Settings.address": "<PERSON><PERSON>", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON>", "components.Settings.cancelscan": "Taramayı İptal Et", "components.Settings.copied": "API Anahtarı panoya kopyalandı.", "components.Settings.currentlibrary": "<PERSON><PERSON><PERSON>: {name}", "components.Settings.default": "Öntanımlı", "components.Settings.default4k": "Öntanımlı 4K", "components.Settings.deleteServer": "{serverType} Sunucusunu Sil", "components.Settings.deleteserverconfirm": "<PERSON>u sunucuyu silmek istediğinize emin misiniz?", "components.Settings.email": "E-mail", "components.Settings.enablessl": "SSL Kullan", "components.Settings.externalUrl": "Harici URL", "components.Settings.hostname": "Domain ya da IP Adresi", "components.Settings.is4k": "4K", "components.Settings.jellyfinSettings": "{mediaServerName} Ayarları", "components.Settings.jellyfinSettingsFailure": "{mediaServerName} ayarları kaydedilirken beklenmedik bir hata oluştu.", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName} ayarları başarıyla kaydedildi!", "components.Settings.jellyfinlibraries": "{mediaServerName} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.jellyfinlibrariesDescription": "{mediaServerName} medya sunucunuzda içerik taraması yapılacak. Eğer hiç bir kütüphane listelenmediyse 'Kütüphaneleri Eşitle' butonunu kullanın.", "components.Settings.jellyfinsettings": "{mediaServerName} Ayarları", "components.Settings.jellyfinsettingsDescription": "{mediaServerName} sunucunuz için a<PERSON>lar<PERSON> düzenleyin. {mediaServerName} sunucunuzun kütüphaneleri içlerinde ki içerikleri tespiti için tarana<PERSON>ktır.", "components.Settings.librariesRemaining": "<PERSON><PERSON>: {count}", "components.Settings.manualscan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.manualscanDescriptionJellyfin": "Normalde rutin olarak bu işlem 24 saatte bir yapılır. Je<PERSON><PERSON>rr {mediaServerName} sunucunuzun en son eklenenlerini sıklıkla kontrol eder. Eğer ki bu Jellyseerr'ı ilk yapılandırışınız ise tek seferlik manuel kütüphane taraması yapmanız önerilir!", "components.Settings.mediaTypeMovie": "Film", "components.Settings.menuAbout": "Hakkında", "components.Settings.menuGeneralSettings": "<PERSON><PERSON>", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.menuLogs": "Loglar", "components.Settings.menuNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.mediaTypeSeries": "dizi", "components.Settings.noDefaultServer": "Talebin işlenebilmesi için en azından bir {serverType} sunucusunun {mediaType} içerikleri için öntanımlı olarak işaretlenmesi gerekmektedir.", "components.Settings.notificationAgentSettingsDescription": "Bildirim aracılarını yapılandırın ve etkinleştirin", "components.Settings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.notificationsettings": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.notrunning": "Çalışmıyor", "components.Settings.plex": "Plex", "components.Settings.plexlibraries": "<PERSON><PERSON>", "components.Settings.plexlibrariesDescription": "Jellyseerr'in başlıklar için taradığı kütüphaneler. Plex bağlantı ayarlarınızı kurun ve kaydedin, ardından hiçbir kütüphane listelenmemişse aşağıdaki düğmeye tıklayın.", "components.Settings.plexsettings": "Plex Ayarları", "components.Settings.port": "Port", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.restartrequiredTooltip": "Bu ayarda yapılan değişikliklerin etkili olması için Je<PERSON>'in yeniden başlatılması gerekir", "components.Settings.save": "Değişiklikleri Kaydet", "components.Settings.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Settings.scan": "Kütüphaneleri Eşitle", "components.Settings.scanning": "Eşitleniyor…", "components.Settings.serverLocal": "local (yerel)", "components.Settings.serverRemote": "remote (uzak)", "components.Settings.serverSecure": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverpresetLoad": "Kullanılabilir suncuuları yüklemek için butona bas", "components.Settings.serverpresetManualMessage": "<PERSON><PERSON>", "components.Settings.serverpresetRefreshing": "Sunucular getiriliyor…", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack bildirim ayarları kaydedildi!", "components.Settings.Notifications.authUser": "SMPT Kullanıcı Adı", "components.Settings.Notifications.pgpPasswordTip": "<OpenPgpLink>OpenPGP</OpenPgpLink> kull<PERSON>rak e-mailleri imzala ve şifrele", "components.Settings.Notifications.toastDiscordTestFailed": "Discord test bild<PERSON><PERSON>.", "components.Settings.RadarrModal.selectMinimumAvailability": "Asgari erişilebilirlik ayarını seç", "components.Settings.RadarrModal.validationHostnameRequired": "Geçerli bir sunucu adresi girmel<PERSON>n", "components.Settings.SettingsAbout.betawarning": "Bu BETA yazılımdır. Özellikler bozuk ve/veya dengesiz olabilir. Karşılaştığınız herhangi bir sorunu lütfen GitHub'da bildirin!", "components.Settings.SettingsMain.cacheImagesTip": "Dış kaynaklardan alınan resimleri önbelleğe al (depolama kullanımını arttıracaktır)", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "<WebhookLink>Bir Webhook</WebhookLink> entegrasyonu oluştur", "components.Settings.Notifications.discordsettingssaved": "Discord bildirim ayarları kaydedildi!", "components.Settings.Notifications.encryptionOpportunisticTls": "Her daim STARTTLS kullan", "components.Settings.Notifications.toastDiscordTestSending": "Discord deneme bildirimi g<PERSON>…", "components.Settings.Notifications.webhookUrlTip": "Sunucunuz için bir <DiscordWebhookLink>Webhook</DiscordWebhookLink> entegrasyonu oluştur", "components.Settings.SettingsAbout.outofdate": "<PERSON><PERSON>", "components.Settings.SettingsAbout.runningDevelop": "Jellyseerr'in yalnızca geliştirmeye katkıda bulunan veya en son testlere yardımcı olan kişiler için önerilen <code>develop</code> dalını çalıştırıyorsunuz.", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.imagecacheDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rr önceden yapılandırılmış harici kaynaklardan gelen görüntüleri proxy'leyecek ve önbelleğe alacaktır. Önbelleğe alınan görüntüler yapılandırma klasörünüze kaydedilir. Dosyaları <code>{appDataPath}/cache/images</code> konumunda bulabilirsiniz.", "components.Settings.SettingsUsers.userSettingsDescription": "Genel ve varsayılan kullanıcı ayarlarını yapılandırın.", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.tagRequestsInfo": "Otomatik olarak isteyenin kullanıcı adını ek etiketlere ekle", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr'a başarıyla bağlanıldı!", "components.Settings.experimentalTooltip": "Bu ayarı etkinleştirmek uygulamanın beklenmedik davranışlarda bulunmasına sebep olabilir", "components.Settings.noDefaultNon4kServer": "<PERSON><PERSON><PERSON> ki hem 4K hem de 4K-olmayan içerikler için tek tip bir {serverType} sunucusu kullanıyorsan (ya da sadece 4K içerikleri indiriyorsan) {serverType} sunucunu <strong>KESİNLİKLE</strong> 4K olarak ayarlamaman gerekmektedir.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web bildirim ayarları kaydedilemedi.", "components.Settings.Notifications.validationBotAPIRequired": "Bir bot yetkilendirme token'i sağlamalısın", "components.Settings.RadarrModal.loadingprofiles": "<PERSON><PERSON>…", "components.Settings.RadarrModal.tagRequestsInfo": "Otomatik olarak isteyenin kullanıcı adını ek etiketlere ekle", "components.Settings.RadarrModal.validationApiKeyRequired": "Bir API anahtarı girmelisiniz", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Çıkışıyla ilgili veri henüz mevcut değildir.", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Her {jobScheduleHours, plural, one {saatte} other {{jobScheduleHours} hours}}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "İşlem kaydedilirken beklenmedik bir hata oluştu.", "components.Settings.SettingsJobsCache.jobs": "İşlemler", "components.Settings.SettingsMain.generalsettingsDescription": "<PERSON><PERSON><PERSON><PERSON> için genel ve varsayılan ayarları yapılandırın.", "components.Settings.SonarrModal.validationNameRequired": "<PERSON><PERSON> <PERSON>ucu ismi girmel<PERSON>n", "components.Settings.jellyfinSettingsDescription": "Talep Etğe bağlı olarak {mediaServerName} medya sunucunuz için dahili ve harici uç noktaları yapılandırabilirsiniz. <PERSON><PERSON><PERSON><PERSON> durumda, harici URL dahili URL'den farklıdır. Farklı bir parola sıfırlama sayfasına yönlendirmek istemeniz durumunda {mediaServerName} medya sunucunuz için özel bir parola sıfırlama URL'si de ayarlanabilir. Ayrıca, daha önce otomatik olarak oluşturulan Jellyfin API anahtarını da değiştirebilirsiniz..", "components.Settings.noDefault4kServer": "Kullanıcıların 4K {mediaType} talepleri yapabilmesi için 4K {serverType} sunucusu öntanımlı olarak işaretlenmelidir.", "components.Settings.plexsettingsDescription": "Plex sunucunuz için a<PERSON>ları yapılandırın. <PERSON><PERSON><PERSON><PERSON>, içerik kullanılabilirliğini belirlemek için Plex kitaplıklarınızı tarar.", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Geçerli bir URL girmelisiniz", "components.Settings.Notifications.botUsernameTip": "Kullanıcıların botunuzla sohbet başlatmalarına ve kendi bildirimlerini yapılandırmalarına izin verin", "components.Settings.Notifications.validationSmtpHostRequired": "Geçerli bir sunucu adresi girmel<PERSON>n", "components.Settings.SettingsUsers.localLoginTip": "Kullanıcıların e-posta adreslerini ve parolalarını kullanarak oturum açmalarına izin verin", "components.Settings.advancedTooltip": "Bu ayarın yanlış yapılandırılması işlevselliğin bozulmasına neden olabilir", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover deneme bildirimi g<PERSON>…", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "30 karakterlik <UsersGroupsLink>kullanıcı ya da grup kimliği</UsersGroupsLink>", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook deneme bildirimi <PERSON>.", "components.Settings.Notifications.toastEmailTestSending": "E-mail deneme bildirimi g<PERSON>…", "components.Settings.SettingsMain.toastApiKeySuccess": "Yeni API anahtarı başarıyla oluşturuldu!", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON><PERSON>, be<PERSON><PERSON><PERSON> bakım görevlerini düzenli olarak zamanlanmış işler olarak gerçekleştirir, anca<PERSON> bunlar aşağıda manuel olarak da tetiklenebilir. Bir işi manuel olarak çalıştırmak, zamanlamasını değiştirmez.", "components.Settings.SettingsLogs.logsDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <code>stdout</code> üzerinden veya <code>{appDataPath}/logs/jellyseerr.log</code> dizininde görüntüleyebilirsiniz.", "components.Settings.SonarrModal.baseUrl": "Temel URL Adresi", "components.Settings.SonarrModal.loadinglanguageprofiles": "<PERSON><PERSON> se<PERSON><PERSON><PERSON>ri y<PERSON>…", "components.Settings.SonarrModal.servername": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Temel URL slash (eğik çizgi) ile bitmemelidir", "components.Settings.manualscanDescription": "<PERSON>de, bu yaln<PERSON><PERSON><PERSON> her 24 saatte bir çalıştırıl<PERSON>r. <PERSON><PERSON><PERSON><PERSON>, Plex sunucunuzun son eklenenlerini sık sık kontrol edecektir. Plex'i ilk kez yapılandırıyorsanız, tek seferlik tam manuel kütüphane taraması önerilir!", "components.Settings.manualscanJellyfin": "<PERSON><PERSON><PERSON><PERSON><PERSON>neleri Elle Tara", "components.Settings.menuJobs": "İşlemler & Önbellek", "components.Settings.serviceSettingsDescription": "{serverType} sunucu(lar)ınızı aşağıdan yapılandırın. <PERSON>en fazla {serverType} sunucusuna bağlanabilirsiniz, ancak sadece iki tanesi varsayılan olarak tanımlanabilir (bir adet 4K ve bir adet 4K dışı). Yöneticiler sunucuları her daim mutlak bir biçimde yönetebilirler.", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.settingUpPlexDescription": "Plex'i kurmak için, ayrıntıları manuel olarak girebilir veya <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink> adresinden alınan bir sunucuyu seçebilirsiniz. Kullanılabilir sunucuların listesini almak için açılır menünün sağındaki düğmeyi kullanın.", "components.Settings.ssl": "SSL", "components.Settings.startscan": "<PERSON><PERSON><PERSON>", "components.Settings.syncJellyfin": "Kütüphaneleri Eşitle", "components.Settings.tautulliApiKey": "API Anahtarı", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.timeout": "Zaman Aşımı", "components.Settings.toastPlexConnecting": "Plex'e yeniden bağlanılıyor…", "components.Settings.toastPlexConnectingFailure": "Plex'e bağlanılamadı.", "components.Settings.toastPlexConnectingSuccess": "Plex'e başarıyla bağlanıldı!", "components.Settings.toastPlexRefresh": "Plex'den sunucu listesi alınıyor…", "components.Settings.toastPlexRefreshFailure": "Plex'ten sunucu listesi alınamadı.", "components.Settings.toastPlexRefreshSuccess": "Plex'ten sunucu listesi başarıyla alınıldı!", "components.Settings.toastTautulliSettingsFailure": "Tautulli ayarlarınızı kaydederken beklenmedik bir hatayla karşılaşıldı.", "components.Settings.toastTautulliSettingsSuccess": "Tautulli ayarları başarıyla kaydedildi!", "components.Settings.urlBase": "Temel URL Adresi", "components.Settings.validationHostnameRequired": "Geçerli bir sunucu adresi girmel<PERSON>n", "components.Settings.validationPortRequired": "Geçerli bir port numarası girmelisin", "components.Settings.validationUrl": "Geçerli bir URL adresi girmelisiniz", "components.Settings.validationUrlBaseTrailingSlash": "Temel URL slash (eğik çizgi) ile bitmemelidir", "components.Settings.validationUrlTrailingSlash": "URL'nizin sonunda slash (eğik <PERSON>iz<PERSON>) olmamalıdır", "components.Settings.webAppUrl": "<WebAppLink>Web Uygulaması</WebAppLink> Linki", "components.Settings.webhook": "Webhook", "components.Settings.webpush": "Web Bildirimi", "components.Setup.configuremediaserver": "<PERSON><PERSON><PERSON>", "components.Setup.configureservices": "Servisleri Düzenleyin", "components.Setup.continue": "<PERSON><PERSON>", "components.Setup.finish": "Kurulumu Bitir", "components.Setup.finishing": "<PERSON>…", "components.Setup.setup": "<PERSON><PERSON><PERSON>", "components.Setup.signin": "Oturum Açın", "components.Setup.signinMessage": "Oturum açarak devam edin", "components.Setup.signinWithPlex": "Plex bilgilerinizi girin", "components.Setup.welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.StatusBadge.managemedia": "{mediaType} içeriğini yönet", "components.StatusBadge.openinarr": "{arr}'da <PERSON><PERSON>", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}B{episodeNumber}", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.StatusChecker.reloadApp": "{applicationTitle}'<PERSON>", "components.StatusChecker.restartRequired": "Sunucuyu Yeniden Başlatmalısınız", "components.StatusChecker.restartRequiredDescription": "Güncellenen a<PERSON>ların etkinleşmesi için sunucuyu yeniden başlatın.", "components.TitleCard.addToWatchList": "<PERSON><PERSON><PERSON> list<PERSON> e<PERSON>", "components.TitleCard.cleardata": "<PERSON><PERSON><PERSON>", "components.TitleCard.mediaerror": "{mediaType} Bulunamadı", "components.TitleCard.tmdbid": "TMDB ID'si", "components.TitleCard.watchlistCancel": "<strong>{title}</strong> i<PERSON><PERSON> olan i<PERSON> listesi iptal edildi.", "components.TitleCard.watchlistError": "Bir şeyler ters gitti. Lütfen tekrar deneyin.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> i<PERSON><PERSON> listesine başarı<PERSON> e<PERSON>!", "components.TvDetails.Season.noepisodes": "B<PERSON><PERSON>üm listesi mevcut değil.", "components.TvDetails.Season.somethingwentwrong": "Sezon verisi alınırken bir şeyler ters gitti.", "components.TvDetails.TvCast.fullseriescast": "<PERSON><PERSON><PERSON>", "components.TvDetails.TvCrew.fullseriescrew": "<PERSON><PERSON><PERSON>", "components.TvDetails.anime": "Anime", "components.TvDetails.cast": "Oyuncular", "components.TvDetails.episodeRuntime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.episodeRuntimeMinutes": "{runtime} dakika", "components.TvDetails.firstAirDate": "<PERSON>lk <PERSON>", "components.TvDetails.manageseries": "<PERSON><PERSON><PERSON>", "components.TvDetails.nextAirDate": "Sonraki Çıkış tarihi", "components.TvDetails.originallanguage": "Orijinal Dili", "components.TvDetails.originaltitle": "Orijinal Başlık", "components.TvDetails.overview": "Özet", "components.TvDetails.play": "{mediaServerName} ile <PERSON>", "components.TvDetails.play4k": "İçeriği {mediaServerName} içinde 4K oynat", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.reportissue": "<PERSON><PERSON>", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes İzleyici Puanı", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.seasonnumber": "Sezon {seasonNumber}", "components.TvDetails.network": "{networkCount} Yayın Platformu", "components.TvDetails.seasons": "{seasonCount} # Sezon", "components.TvDetails.tmdbuserscore": "TMBD Kullanıcı Skoru", "components.TvDetails.viewfullcrew": "<PERSON><PERSON><PERSON>", "components.TvDetails.watchtrailer": "Fragmanı İzle", "components.UserList.accounttype": "<PERSON><PERSON><PERSON>", "components.UserList.admin": "Yönetici", "components.UserList.autogeneratepassword": "Rastgele Bir Parola Oluştur", "components.UserList.bulkedit": "<PERSON>lu <PERSON>", "components.UserList.create": "Oluştur", "components.UserList.created": "<PERSON><PERSON><PERSON>", "components.UserList.createlocaluser": "<PERSON><PERSON> Oluştur", "components.UserList.creating": "Oluşturuluyor…", "components.UserList.deleteuser": "Kullanıcıyı Sil", "components.UserList.edituser": "Kullanıcının İzinlerini Düzenle", "components.UserList.email": "E-mail Adresi", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} kullanıcısı başarıyla içeri aktarıldı!", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex kullanıcısı başarıyla içeri aktarıldı!", "components.UserList.importfromJellyfin": "{mediaServerName} Kullanıcılarını İçe Aktar", "components.UserList.importfrommediaserver": "{mediaServerName} Kullanıcılarını İçeri Aktar", "components.UserList.importfromplex": "Plex Kullanıcılarını İçeri Aktar", "components.UserList.importfromplexerror": "Plex kullanıcıları içeri aktarılırken bir şeyler ters gitti.", "components.UserList.localLoginDisabled": "<strong><PERSON><PERSON></strong> se<PERSON><PERSON>ğ<PERSON>u anda etin de<PERSON>.", "components.UserList.localuser": "<PERSON><PERSON>", "components.UserList.mediaServerUser": "{mediaServerName} Kullanıcısı", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> İzleme listenizden başarıyla çıkarıldı!", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.syncing": "Eşitleniyor", "components.Settings.tautulliSettingsDescription": "İsteğe bağlı olarak Tautulli sunucunuz için ayarları yapılandırın. <PERSON><PERSON><PERSON><PERSON>, Plex medyanız için izleme geçmişi verilerini Tautulli'den alır.", "components.Settings.validationApiKey": "Bir API anahtarı girmelisiniz", "components.Settings.validationUrlBaseLeadingSlash": "URL'nizin başında slash (eğik çizgi) olmalıdır", "components.Settings.webAppUrlTip": "İsteğe bağlı olarak kullanıcıları \"hosted\" web uygulaması yerine sunucunuzdaki web uygulamasına yönlendirin", "components.Setup.signinWithJellyfin": "Jellyfin bilgilerinizi girin", "components.StatusBadge.playonplex": "{mediaServerName} ile <PERSON>", "components.StatusChecker.appUpdatedDescription": "Lütfen aşağıda ki butona tıklayarak uygulamayı yenileyin.", "components.TitleCard.tvdbid": "TheTVDB ID'si", "components.TvDetails.similar": "<PERSON><PERSON>", "components.TvDetails.overviewunavailable": "Özet mevcut değil.", "components.TvDetails.seasonstitle": "Sezonlar", "components.UserList.autogeneratepasswordTip": "Kullanıcıya otomatik oluşturulmuş parolayı e-mail olarak gönder", "components.TvDetails.episodeCount": "{episodeCount} # <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.importfromJellyfinerror": "{mediaServerName} kullanıcıları içeri aktarılırken bir şeyler ters gitti.", "components.TvDetails.showtype": "<PERSON><PERSON><PERSON>", "components.TvDetails.productioncountries": "Ya<PERSON><PERSON>m<PERSON>ı {countryCount, plural, one {<PERSON>lk<PERSON>} other {<PERSON><PERSON><PERSON>r}}", "components.TvDetails.status4k": "4K {status}", "components.UserList.deleteconfirm": "Bu kullanıcıyı silmek istediğinizden emin misiniz? Tüm talep verisi temelli olarak silinecektir.", "components.TvDetails.streamingproviders": "İçertiğin Erişilebilir Olduğu Platformlar", "components.UserList.newplexsigninenabled": "<strong>Yeni Plex Kullanıcılarının Oturum Açmasına İzin Ver</strong> ayarı etkin durumdadır. Plex kullanıcılarından kütüphane erişimine sahip olanlarının oturum açabilmesi için içeri aktarılması gerekmez.", "components.UserList.newJellyfinsigninenabled": "<strong><PERSON><PERSON> {mediaServerName} Kullanıcılarının Oturum Açmasına İzin Ver</strong> ayarı etkin durumdadır. {mediaServerName} kullanıcılarından kütüphane erişimine sahip olanlarının oturum açabilmesi için içeri aktarılması gerekmez.", "components.UserList.noJellyfinuserstoimport": "İçeri aktarılacak {mediaServerName} kullanıcısı mevcut değil.", "components.UserList.nouserstoimport": "İçeri aktarılacak Plex kullanıcısı mevcut değil.", "components.UserList.owner": "Sahip", "components.UserList.password": "Şifre", "components.UserList.passwordinfodescription": "Bir uygulama URL'si ayarlayın ve otomatik şifre oluşturulabilmesi için e-mail bildirimlerini aktif edin.", "components.UserList.plexuser": "Plex <PERSON>ı", "components.UserList.role": "Rol", "components.UserList.sortCreated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.sortDisplayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.sortRequests": "Talep Sayı<PERSON>ı", "components.UserList.totalrequests": "<PERSON><PERSON>", "components.UserList.user": "Kullanıcı", "components.UserList.usercreatedsuccess": "Kullanıcı başarıyla oluşturuldu!", "components.UserList.userdeleted": "Kullanıcı başarıyla silindi!", "components.UserList.userdeleteerror": "Kullanıcıyı silerken bir şeyler ters gitti.", "components.UserList.userfail": "Kullanıcının izinleri kaydedilirken bir şeyler ters gitti.", "components.UserList.userlist": "Kullanıcı Listesi", "components.UserList.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.userssaved": "Kullanıcının izni başarıyla kaydedildi!", "components.UserList.validationpasswordminchars": "Girdiğiniz şifre çok kısa, en az 8 karakterden oluşmalıdır", "components.UserProfile.ProfileHeader.joindate": "{joindate} ta<PERSON><PERSON><PERSON> katıldı", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserProfile.ProfileHeader.settings": "Ayarları Düzenle", "components.UserProfile.ProfileHeader.userid": "Kullanı<PERSON>ı Kimliği: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "<PERSON>sabın <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Yönetici", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord Kullanıcı Kimliği", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-mail", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Genel Limiti Geçersiz Kıl", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Varsayılan ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} Kullanıcısı", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Film Talep Etme Limiti", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Keşfet Dili", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "İçerikleri orijinal dillerine göre filtrele", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Sahip", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex <PERSON>ı", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Filmleri Otomatize Talep Ett", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Dizileri Otomatize Talep Ett", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "<PlexWatchlistSupportLink>Plex İzleme Listenizde</PlexWatchlistSupportLink>'ki dizileri otomatik olarak istet", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Keşfet Bölgesi", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "İçerikleri bölgesel erişilebilirliklerine göre filtrele", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Değişiklikleri Kaydet", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Dizi Talep Etme Limiti", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "<PERSON><PERSON><PERSON> kaydedilirken beklenmedik bir hata o<PERSON>.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Kullanıcı", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Cihaz Varsayılanı", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Kullanıcı Kimliği", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord bildirim a<PERSON>ları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "E-mail bildirim a<PERSON>ları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "E-mail bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "<PERSON><PERSON><PERSON><PERSON> (Public) PGP Anahtarı", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "E-mail mesajlarını <OpenPgpLink>OpenPGP</OpenPgpLink> kullanarak şifrele", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "<PushbulletSettingsLink>Hesap <PERSON>ını<PERSON>n</PushbulletSettingsLink> bir token oluşturun", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet bildirim ayarları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Uygulama API'sinin <PERSON>'i", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "{applicationTitle} ile kullanabilmeniz için bir uygulam<PERSON> <ApplicationRegistrationLink>oluşturun</ApplicationRegistrationLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Kullanıcı ya da Grup Kimliği", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover bildirim ayarları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Bildirimler sessiz <PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Sohbet ID'si", "components.UserProfile.UserSettings.menuNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram bildirim ayarları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Geçerli bir kullanıcı kimliği sağlamalısınız", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Geçerli bir PGP açık (public) anahtarı sağlamalısın", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Bir erişim token'i sağlamalısınız", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Geçerli bir kullanıcı ya da grup kimliği sağlamalısınız", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Geçerli bir sohbet ID'si sağlamalısın", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Bildirimi", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Web bildirim ayarları kaydedilemedi.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Web bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "<PERSON>if<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Kullandığınız Şifre", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Hesabın a<PERSON>lanmış bir şifreye sahip değil. Aşağıdan e-mail adresinizi kullanarak bir şifre belirlerseniz \"yerel kullanıcı\" olarak oturum açabilirsiniz.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Bu kullanıcının şifresini değiştirme yetkisine sa<PERSON> değilsin.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Şifre", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "<PERSON><PERSON>re kaydedilirken bir hata <PERSON>, <PERSON><PERSON> anda k<PERSON>ınız şifreyi doğru girdiğinizden emin olun.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Şifre başarıyla kaydedildi!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Yeni şifreyi doğrulamanız gerekmektedir", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uyuşmuyor", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Mevcut olarak kullandığınız şifrenizi girmediniz", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "<PERSON><PERSON> bir <PERSON><PERSON><PERSON> gir<PERSON>", "components.UserProfile.UserSettings.UserPermissions.permissions": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "<PERSON><PERSON><PERSON> kaydedilirken beklenmedik bir hata o<PERSON>.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "İzinler başarıyla kaydedildi!", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "<PERSON><PERSON>.", "components.UserProfile.UserSettings.menuChangePass": "Şifre", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON>", "components.UserProfile.UserSettings.unauthorizedDescription": "<PERSON>u kullanıcının ayarlarını düzenleme yetkin yok.", "components.UserProfile.limit": "{limit} limitinizden {remaining} adet kaldı", "components.UserProfile.localWatchlist": "{username} kullanıcısının İzleme Listesi", "components.UserProfile.movierequests": "Film Talepleri", "components.UserProfile.pastdays": "{type} (Ye<PERSON><PERSON><PERSON>ine {days} gün kaldı)", "components.UserProfile.plexwatchlist": "Plex İzleme Listen", "components.UserProfile.recentlywatched": "Yakın Z<PERSON>da İzlendi", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.requestsperdays": "{limit}ı", "components.UserProfile.seriesrequest": "<PERSON><PERSON>", "components.UserProfile.totalrequests": "Toplam Talep", "components.UserProfile.unlimited": "Sınırsız", "i18n.advanced": "Gelişmiş", "i18n.all": "<PERSON><PERSON><PERSON>", "i18n.approve": "Kabul Et", "i18n.areyousure": "Emin misiniz?", "i18n.available": "Kullanılabilir", "i18n.back": "<PERSON><PERSON>", "i18n.cancel": "İptal Et", "i18n.canceling": "İptal Ediliyor…", "i18n.close": "Ka<PERSON><PERSON>", "i18n.collection": "Koleksiyon", "i18n.decline": "<PERSON><PERSON>", "i18n.declined": "Reddedildi", "i18n.delete": "Sil", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "<PERSON><PERSON><PERSON><PERSON>", "i18n.experimental": "Den<PERSON>sel", "i18n.failed": "Başarısız", "i18n.import": "İçe Aktar", "i18n.importing": "İçe Aktarılıyor…", "i18n.loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.movie": "Film", "i18n.movies": "<PERSON><PERSON>", "i18n.next": "<PERSON><PERSON><PERSON>", "i18n.noresults": "<PERSON><PERSON>ç Bulunamadı.", "i18n.open": "Açık", "i18n.partiallyavailable": "<PERSON><PERSON><PERSON><PERSON>", "i18n.pending": "Beklemede", "i18n.previous": "<PERSON><PERSON><PERSON>", "i18n.processing": "İşleniyor", "i18n.request": "Talep Et", "i18n.request4k": "4K Talep Et", "i18n.requested": "<PERSON><PERSON>", "i18n.requesting": "<PERSON><PERSON>…", "i18n.resolved": "Çözüldü", "i18n.resultsperpage": "<PERSON><PERSON><PERSON><PERSON> {pageSize} sonuç gösteriliyor", "i18n.retry": "<PERSON><PERSON><PERSON>", "i18n.retrying": "<PERSON><PERSON><PERSON>…", "i18n.save": "Değişiklikleri Kaydet", "i18n.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.settings": "<PERSON><PERSON><PERSON>", "i18n.showingresults": "<strong>{from}</strong> ile <strong>{to}</strong> a<PERSON><PERSON><PERSON>n toplam <strong>{total}</strong> sonuç var", "i18n.status": "Durum", "i18n.test": "Test Et", "i18n.testing": "Test Ediliyor…", "i18n.tvshow": "<PERSON><PERSON>", "i18n.tvshows": "<PERSON><PERSON><PERSON>", "i18n.unavailable": "<PERSON><PERSON><PERSON>", "i18n.usersettings": "Kullanıcı Ayarları", "i18n.view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "<PERSON><PERSON><PERSON>", "pages.oops": "<PERSON><PERSON><PERSON><PERSON>", "pages.pagenotfound": "Sayfa Bulunamadı", "pages.returnHome": "<PERSON><PERSON><PERSON><PERSON>", "pages.serviceunavailable": "Hizmet Erişilebilir Değil", "pages.somethingwentwrong": "Bir <PERSON>ler Ters Gitti", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Geçerli bir Discord ID'si sunmalısın", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "<PERSON><PERSON><PERSON> ka<PERSON>ilirken bir şeyler ters gitti.", "components.UserProfile.UserSettings.menuPermissions": "<PERSON><PERSON><PERSON>", "i18n.approved": "Kabul Edildi", "i18n.deleting": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.notrequested": "<PERSON><PERSON>", "components.UserList.usercreatedfailed": "Kullanıcı oluşturulurken bir şeyler ters gitti.", "components.UserList.usercreatedfailedexisting": "Verilen e-mail adresi ha<PERSON> başka bir kulllanıcı tarafından kullanılmaktadır.", "components.UserList.validationEmail": "E-posta gere<PERSON>li", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "<PlexWatchlistSupportLink>Plex İzleme Listenizde</PlexWatchlistSupportLink>'ki filmleri otomatik olarak talep Et", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rolü", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Hesabın<PERSON>z ile bağıntılı <FindDiscordIdLink>numaralardan oluşan kullanıcı ID'niz</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "30 karakterlik <UsersGroupsLink>kullanıcı ya da grup kimliği</UsersGroupsLink>", "components.UserProfile.emptywatchlist": "<PlexWatchlistSupportLink>Plex İzleme Listenize</PlexWatchlistSupportLink> eklenen içerikler burada gözükeceklerdir.", "i18n.restartRequired": "Yeniden Başlatma Gereklidir", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Discord kullanıcı hesabınızla ilişkili <FindDiscordIdLink>çok haneli kimlik numarası</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet bildirim ayarları kaydedildi!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Bir sohbet başlatın</TelegramBotLink> ve <GetIdBotLink>@get_id_bot</GetIdBotLink> ID'li botunuzu ekleyin. Son olar<PERSON> <code>/my_id</code> komutunu kullanın", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Geçerli bir uygulama token'i sağlamalısınız", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Bu kullanıcının henüz ayarlanmış bir şifresi yok. Aşağıdan bir şifre ayarlayarak bu hesabın \"yerel kullanıcı\" olarak oturum açabilmesini sağlayın.", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Girdiğiniz şifre çok kısa, en az 8 karakterden oluşmalıdır", "components.MovieDetails.addtowatchlist": "<PERSON><PERSON><PERSON>", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> İzleme listesinden başarıyla kaldırıldı!", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> i<PERSON><PERSON> listesine başarı<PERSON> e<PERSON>!", "components.MovieDetails.removefromwatchlist": "İzleme Listesinden Kaldır", "components.TvDetails.addtowatchlist": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Geçerli bir e-posta gerekli", "components.RequestList.RequestItem.profileName": "Profil", "components.TvDetails.removefromwatchlist": "İzleme Listesinden Kaldır", "components.Login.adminerror": "Oturum açmak için bir yönetici hesabı kullanmalısınız.", "components.Login.enablessl": "SSL Kullan", "components.Login.hostname": "{mediaServerName} URL", "components.Login.invalidurlerror": "{mediaServerName} sun<PERSON><PERSON>una bağlanılamıyor.", "components.Login.port": "Port", "components.Login.urlBase": "URL Tabanı", "components.Login.validationHostnameRequired": "Geçerli bir ana bilgisayar adı veya IP adresi sağlamalısınız", "components.Login.validationPortRequired": "Geçerli bir port numarası sağlamalısınız", "components.Login.validationUrlBaseLeadingSlash": "URL tabanının başında eğik çizgi olmalıdır", "components.Login.validationUrlBaseTrailingSlash": "URL tabanı eğik çizgiyle bitmemelidir", "components.Login.validationUrlTrailingSlash": "URL, eğik çizgiyle bitmemelidir", "components.MovieDetails.watchlistError": "Bir şeyler ters gitti. Lütfen tekrar deneyin.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> i<PERSON><PERSON> listesine başarı<PERSON> e<PERSON>!", "components.Settings.invalidurlerror": "{mediaServerName} sun<PERSON><PERSON>una bağlanılamıyor.", "components.Settings.jellyfinForgotPasswordUrl": "Parola Sıfırlama URL'si", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Otomatik Kitaplık Gruplandırması ile özel kimlik doğrulaması desteklenmiyor", "components.Settings.jellyfinSyncFailedGenericError": "<PERSON><PERSON><PERSON>ü<PERSON>neler senkronize edilirken bir hata o<PERSON>", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "<PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> İzleme listesinden başarıyla kaldırıldı!", "components.TvDetails.watchlistError": "Bir şeyler ters gitti. Lütfen tekrar deneyin.", "components.UserList.username": "Kullanıcı adı", "components.UserList.validationUsername": "Kullanıcı adınızı sağlamalısınız", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "E-posta gere<PERSON>li", "components.Login.back": "<PERSON><PERSON> git", "components.Login.servertype": "<PERSON><PERSON><PERSON>", "components.Setup.configemby": "Emby İle Devam Et", "components.Login.validationservertyperequired": "Lütfen bir sunucu türü <PERSON>", "components.Selector.canceled": "İptal Edilen", "components.Selector.returningSeries": "<PERSON><PERSON>", "components.Setup.back": "<PERSON><PERSON>", "components.Selector.ended": "Biten", "components.Selector.inProduction": "Çekimde", "components.Selector.pilot": "Pilot", "components.Selector.planned": "<PERSON><PERSON><PERSON>", "components.Selector.searchStatus": "Durum seçin...", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON>", "components.Setup.configplex": "Plex İle Devam Et", "components.Setup.servertype": "<PERSON><PERSON><PERSON>", "components.Setup.signinWithEmby": "Emby bilgilerinizi girin", "components.Setup.subtitle": "<PERSON><PERSON>a <PERSON>uzu seçerek başlayın", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.Discover.FilterSlideover.status": "Durum", "component.BlacklistBlock.blacklistedby": "<PERSON>", "components.Settings.Notifications.webhookRoleId": "Bildirim Rol Kimliği", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex Token'ını Yenile", "components.Settings.SettingsMain.discoverRegion": "Keşfet Bölgesi", "components.Settings.SettingsMain.discoverRegionTip": "İçeriği bölgesel kullanılabilirliğe göre filtrele", "components.Settings.SettingsMain.streamingRegion": "Dijital Ya<PERSON>ı<PERSON>", "components.Settings.SettingsMain.streamingRegionTip": "Bölgesel kullanılabilirliğe göre dijital yayınları göster", "components.Settings.apiKey": "API anahtarı", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Keşfet Bölgesi", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Dijital Ya<PERSON>ı<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Bölgesel kullanılabilirliğe göre dijital yayınları göster", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Başka bir kullanıcı zaten bu kullanıcı adına sahip. Bir e-posta ayarlamalısınız", "i18n.specials": "<PERSON><PERSON>", "components.Settings.Notifications.webhookRoleIdTip": "Webhook mesajında bahsedilecek rol kimliği. Bahsetmeleri devre dışı bırakmak için boş bırakın", "component.BlacklistBlock.blacklistdate": "Kara <PERSON> Alın<PERSON>", "component.BlacklistModal.blacklisting": "Kara Listeye Alındı", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> kara listeye alınmadı.", "components.Blacklist.blacklistSettingsDescription": "Kara listeye alınmış medyayı yönetin.", "components.Blacklist.blacklistdate": "tarih", "components.Blacklist.blacklistedby": "{user } tara<PERSON><PERSON><PERSON><PERSON>, {date}", "components.Blacklist.blacklistsettings": "Kara Liste Ayarları", "components.Blacklist.mediaName": "İsim", "components.Blacklist.mediaTmdbId": "tmdb kimliği", "components.Blacklist.mediaType": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.blacklist": "<PERSON>", "components.PermissionEdit.blacklistedItems": "<PERSON> liste medyası.", "components.PermissionEdit.blacklistedItemsDescription": "Medyayı kara listeye alma izni ver.", "components.PermissionEdit.manageblacklist": "<PERSON>", "components.PermissionEdit.manageblacklistDescription": "Kara listeye alınmış medyayı yönetme izni ver.", "components.PermissionEdit.viewblacklistedItems": "Kara listeye alınmış medyayı görüntüle.", "components.PermissionEdit.viewblacklistedItemsDescription": "Kara listeye alınmış medyayı görüntüleme izni ver.", "components.RequestList.RequestItem.removearr": "{arr}'dan kaldır", "components.Settings.SettingsJobsCache.usersavatars": "Kullanıcıların Ava<PERSON>ları", "components.Settings.scanbackground": "Tarama arka planda devam edecek. Kurulum sürecine devam edebilirsiniz.", "components.Settings.tip": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Bu e-posta zaten alınmış!", "i18n.addToBlacklist": "<PERSON>", "i18n.blacklist": "<PERSON>", "i18n.blacklistDuplicateError": "<strong>{title</strong> kara listede mevcut.", "i18n.blacklistError": "Bir şeyler ters gitti. Lütfen tekrar deneyin.", "i18n.blacklistSuccess": "<strong>{title</strong> ba<PERSON><PERSON><PERSON><PERSON> kara listeye alındı.", "i18n.blacklisted": "Kara listeye alındı", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> <PERSON> <PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON> kaldırıldı.", "i18n.removefromBlacklist": "<PERSON>", "components.RequestList.sortDirection": "Sıralama Yönünü Değiştir", "components.Settings.Notifications.validationWebhookRoleId": "Geçerli bir Discord Rol Kimliği sağlamalısınız", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "İçeriği bölgesel kullanılabilirliğe göre filtrele", "components.Settings.SettingsNetwork.toastSettingsFailure": "<PERSON><PERSON><PERSON> kaydedili<PERSON>en bir so<PERSON>.", "components.Settings.OverrideRuleModal.conditionsDescription": "Parametre değişikliklerini uygulamadan önce koşulları belirtir. Kuralların uygulanması için her alan <PERSON>ğ<PERSON>ı<PERSON> (VE işlem). B<PERSON> <PERSON><PERSON>, özelliklerinden herhangi biri eşleşirse doğrulanmış kabul edilir (VEYA işlem).", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Kullanılan kimlik bilgileriniz ile Plex'e bağlanılamıyor", "components.Settings.Notifications.messageThreadIdTip": "Grup sohbetinizde konular etkinleştirilmişse, burada bir konu başlığı/konu kimliği belirtebilirsiniz", "components.Settings.SettingsNetwork.trustProxyTip": "Jellyseerr'in proxy arkasındaki istemci IP adreslerini doğru şekilde kaydetmesine izin ver", "components.Selector.searchUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> seçin…", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "<PERSON>u ha<PERSON>i hesaplar {applicationName} hesab<PERSON><PERSON><PERSON><PERSON> bağlıdır.", "components.DiscoverTvUpcoming.upcomingtv": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.loginwithapp": "{appName} ile giri<PERSON> ya<PERSON>n", "components.Login.noadminerror": "Sunucuda yönetici kullanıcısı bulunamadı.", "components.Login.orsigninwith": "veya giriş ya<PERSON>ın", "components.Settings.Notifications.messageThreadId": "<PERSON><PERSON>/<PERSON><PERSON>", "components.Settings.Notifications.validationMessageThreadId": "<PERSON><PERSON>/konu kim<PERSON>i pozitif bir tam sayı olmalıdır", "components.Settings.OverrideRuleModal.conditions": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.create": "Kural oluştur", "components.Settings.OverrideRuleModal.createrule": "Yeni Geçersiz Kılma <PERSON>ı", "components.Settings.OverrideRuleModal.editrule": "Geçersiz Kılma <PERSON> Düzenle", "components.Settings.OverrideRuleModal.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.keywords": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.languages": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.notagoptions": "Etiket yok.", "components.Settings.OverrideRuleModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.rootfolder": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.ruleCreated": "Geçersiz kılma kuralı başarıyla oluşturuldu!", "components.Settings.OverrideRuleModal.ruleUpdated": "Geçersiz kılma kuralı başarıyla güncellendi!", "components.Settings.OverrideRuleModal.selectRootFolder": "Kök klasörü seç", "components.Settings.OverrideRuleModal.selectQualityProfile": "<PERSON><PERSON> pro<PERSON>", "components.Settings.OverrideRuleModal.selecttags": "Etiket seç", "components.Settings.OverrideRuleModal.serviceDescription": "<PERSON><PERSON> kuralı seçili servise uygula.", "components.Settings.OverrideRuleModal.service": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.selectService": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.settings": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.settingsDescription": "Yukarıdaki koşullar sağlandığında hangi ayarların değiştirileceğini belirtir.", "components.Settings.OverrideRuleModal.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.conditions": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.genre": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.keywords": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.language": "Dil", "components.Settings.OverrideRuleTile.qualityprofile": "<PERSON><PERSON>", "components.Settings.OverrideRuleTile.rootfolder": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.settings": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.tags": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.enableSpecialEpisodes": "<PERSON><PERSON>ü<PERSON>plerine İzin Ver", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Gelişmiş Ağ Ayarları", "components.Settings.SettingsNetwork.csrfProtection": "CSRF Korumasını Etkinleştir", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "Ne yaptığınızı bilmiyorsanız bu ayarı ETKİNLEŞTİRMEYİN!", "components.Settings.SettingsNetwork.csrfProtectionTip": "Harici API erişimini salt okunur olarak ayarlayın (HTTPS gerektirir)", "components.Settings.SettingsNetwork.docs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "Jellyseerr'in IPv6 yerine öncelikli olarak IPv4 adreslerine bağlanmasını zorunlu kılın", "components.Settings.SettingsNetwork.forceIpv4First": "Öncelike IPv4 Bağlantısını Zorla", "components.Settings.SettingsNetwork.network": "Ağ", "components.Settings.SettingsNetwork.networkDisclaimer": "Bu ayarlar yerine konteynerinizden/sisteminizden gelen ağ parametreleri kullanılmalıdır. Daha fazla bilgi için {docs}'a bakın.", "components.Settings.SettingsNetwork.networksettings": "<PERSON><PERSON>", "components.Settings.SettingsNetwork.networksettingsDescription": "Jellyseerr örneğiniz için ağ ayarlarını yapılandırın.", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Alt etki alanları için a<PERSON>ırı<PERSON> olarak ',' ve joker karakter olarak '*.' kull<PERSON><PERSON>n", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "<PERSON><PERSON> i<PERSON><PERSON>xy <PERSON>", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) Proxy", "components.Settings.SettingsNetwork.proxyHostname": "Proxy <PERSON>", "components.Settings.SettingsNetwork.proxyPassword": "Proxy <PERSON>", "components.Settings.SettingsNetwork.proxyPort": "Proxy Bağlantı Noktası", "components.Settings.SettingsNetwork.proxySsl": "Proxy İçin SSL Kullanın", "components.Settings.SettingsNetwork.proxyUser": "Proxy Kullanıcı Adı", "components.Settings.SettingsNetwork.proxyBypassFilter": "Proxy Tarafından Yoksay<PERSON>", "components.Settings.SettingsNetwork.toastSettingsSuccess": "<PERSON><PERSON><PERSON> başar<PERSON>yla kaydedildi!", "components.Settings.SettingsNetwork.trustProxy": "Proxy Desteğini Etkinleştir", "components.Settings.SettingsNetwork.validationProxyPort": "Geçerli bir bağlantı noktası sağlamalısınız", "components.Settings.SettingsUsers.atLeastOneAuth": "En az bir kimlik doğrulama yöntemi seçilmelidir.", "components.Settings.SettingsUsers.loginMethods": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.loginMethodsTip": "Kullanıcılar için oturum açma yöntemlerini yapılandırın.", "components.Settings.SettingsUsers.mediaServerLogin": "{mediaServerName} ile Oturum Açmayı Etkinleştir", "components.Settings.SettingsUsers.mediaServerLoginTip": "Kullanı<PERSON>ıların {mediaServerName} hesaplarını kullanarak oturum açmalarına izin ver", "components.Settings.addrule": "Yeni Geçersiz Kılma <PERSON>ı", "components.Settings.menuNetwork": "Ağ", "components.Settings.overrideRules": "Geçersiz Kılma <PERSON>", "components.Settings.overrideRulesDescription": "Geçersiz kılma kural<PERSON>, bir talep kuralla eşleşirse değiştirilecek özellikleri belirtmenize olanak tanır.", "components.Setup.librarieserror": "Doğrulama başarısız oldu. Devam etmek için lütfen kütüphaneleri tekrar değiştirin.", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "Hesabınızı {applicationName} ile bağlamak için {mediaServerName} kimlik bilgilerinizi girin.", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "Bu hesap zaten bir {applicationName} kullanıcısına bağlı", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Kullanılan kimlik bilgileriniz ile {mediaServerName} sunucusuna bağlanılamadı", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Bilinmeyen bir hata o<PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Şifre", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "Bağlantı", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Ekleni<PERSON>r…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "{mediaServerName} Hesabını Bağla", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Kullanıcı adı", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "Kullanıcı adı sağlamalısınız", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Bağlantılı hesap silinemiyor.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Bilinmeyen bir hata o<PERSON>", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Bağlantılı Hesaplar", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "Hesabın<PERSON>za bağlı herhangi bir harici hesabın<PERSON>z bulunmamaktadır.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "Bu kullanıcının bağlantılı hesaplarını değiştirme yetkiniz yok.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Bu hesap zaten bir Plex kullanıcısına bağlı", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "<PERSON><PERSON>/<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Grup sohbetinizde konular etkinleştirilmişse, burada bir konu başlığının/konunun kimliğini belirtebilirsiniz", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "<PERSON><PERSON>/konu kim<PERSON>i pozitif bir tam sayı olmalıdır", "components.UserProfile.UserSettings.menuLinkedAccounts": "Bağlantılı Hesaplar", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "<PERSON><PERSON>re sağlamanız gerekiyor"}