apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "jellyseerr.configPersistenceName" . }}
  labels:
    {{- include "jellyseerr.labels" . | nindent 4 }}
  {{- with .Values.config.persistence.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- with .Values.config.persistence.accessModes }}
  accessModes:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.config.persistence.volumeName }}
  volumeName: {{ .Values.config.persistence.volumeName }}
  {{- end }}
  {{- with .Values.config.persistence.storageClass }}
  storageClassName: {{ if (eq "-" .) }}""{{ else }}{{ . }}{{ end }}
  {{- end }}
  resources:
    requests:
      storage: "{{ .Values.config.persistence.size }}"