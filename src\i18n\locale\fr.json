{"components.AirDateBadge.airedrelative": "Diffusé {relativeTime}", "components.AirDateBadge.airsrelative": "Diffusion {relativeTime}", "components.AppDataWarning.dockerVolumeMissingDescription": "Le montage du volume <code>{appDataPath}</code> n'a pas été configuré correctement. Toutes les données seront effacées lorsque le conteneur sera arrêté ou redémarré.", "components.CollectionDetails.numberofmovies": "{count} Films", "components.CollectionDetails.overview": "Résumé", "components.CollectionDetails.requestcollection": "De<PERSON><PERSON> la collection", "components.CollectionDetails.requestcollection4k": "Demander la collection en 4K", "components.Discover.CreateSlider.addSlider": "Ajouter un slider", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON><PERSON> un slider personnalisé", "components.Discover.CreateSlider.addfail": "Échec de la création du nouveau slider.", "components.Discover.CreateSlider.addsuccess": "Nouveau slider et paramètres de la page Découvrir enregistrés.", "components.Discover.CreateSlider.editSlider": "Modifier un slider", "components.Discover.CreateSlider.editfail": "Échec de la modification du slider.", "components.Discover.CreateSlider.editsuccess": "Slider édité et paramètres de la page Découvrir enregistrés.", "components.Discover.CreateSlider.needresults": "Vous devez avoir au moins 1 résultat.", "components.Discover.CreateSlider.nooptions": "Aucun résultat.", "components.Discover.CreateSlider.providetmdbgenreid": "Fournissez un ID de genre TMBD", "components.Discover.CreateSlider.providetmdbkeywordid": "Fournissez un ID de mot-clé TMBD", "components.Discover.CreateSlider.providetmdbnetwork": "Fournissez un ID de diffuseur TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Fournissez une requête de recherche", "components.Discover.CreateSlider.providetmdbstudio": "Fournissez un ID de studio TMDB", "components.Discover.CreateSlider.searchGenres": "Recherche par genres…", "components.Discover.CreateSlider.searchKeywords": "Recherche par mots-clés…", "components.Discover.CreateSlider.searchStudios": "Recherche par studios…", "components.Discover.CreateSlider.slidernameplaceholder": "Nom du slider", "components.Discover.CreateSlider.starttyping": "Commencez à taper pour rechercher.", "components.Discover.CreateSlider.validationDatarequired": "V<PERSON> devez fournir une valeur de données.", "components.Discover.CreateSlider.validationTitlerequired": "Vous devez fournir un titre.", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Films", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Films", "components.Discover.DiscoverMovieLanguage.languageMovies": "Films en {language}", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# filtre actif} other {# filtres actifs}}", "components.Discover.DiscoverMovies.discovermovies": "Films", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularité croissante", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularité décroissante", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Date de sortie croissante", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Date de sortie décroissante", "components.Discover.DiscoverMovies.sortTitleAsc": "Titre (A-Z) croissant", "components.Discover.DiscoverMovies.sortTitleDesc": "Titre (Z-A) décroissant", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Note TMDB croissante", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Note TMDB décroissante", "components.Discover.DiscoverNetwork.networkSeries": "Séries {network}", "components.Discover.DiscoverSliderEdit.deletefail": "Échec de la suppression du slider.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Slider supprimé avec succès.", "components.Discover.DiscoverSliderEdit.enable": "Basculer la visibilité", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverStudio.studioMovies": "Films {studio}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Filtre actif} other {# Filtres actifs}}", "components.Discover.DiscoverTv.discovertv": "Séries", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Première diffusion croissante", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Première diffusion décroissante", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularité croissante", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularité décroissante", "components.Discover.DiscoverTv.sortTitleAsc": "Titre (A-Z) croissant", "components.Discover.DiscoverTv.sortTitleDesc": "Titre (Z-A) décroissant", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Note TMDB croissante", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Note TMDB décroissante", "components.Discover.DiscoverTvGenre.genreSeries": "Séries {genre}", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Séries", "components.Discover.DiscoverTvLanguage.languageSeries": "Séries en {language}", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Votre watchlist", "components.Discover.DiscoverWatchlist.watchlist": "Liste de lecture Plex", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtre actif} other {# Filtres actifs}}", "components.Discover.FilterSlideover.clearfilters": "Effacer les filtres actifs", "components.Discover.FilterSlideover.filters": "Filtres", "components.Discover.FilterSlideover.firstAirDate": "Première diffusion", "components.Discover.FilterSlideover.from": "De", "components.Discover.FilterSlideover.genres": "Genre", "components.Discover.FilterSlideover.keywords": "Mots-clés", "components.Discover.FilterSlideover.originalLanguage": "Langue originale", "components.Discover.FilterSlideover.ratingText": "Notes entre {minValue} et {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Date de sortie", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "<PERSON><PERSON><PERSON> entre {minValue} et {maxValue} minutes", "components.Discover.FilterSlideover.streamingservices": "Services de streaming", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuserscore": "Note utilisateur TMDB", "components.Discover.FilterSlideover.tmdbuservotecount": "Nombre de votes utilisateur TMDB", "components.Discover.FilterSlideover.to": "À", "components.Discover.FilterSlideover.voteCount": "Nombre de votes entre {minValue} et {maxValue}", "components.Discover.MovieGenreList.moviegenres": "Films par genres", "components.Discover.MovieGenreSlider.moviegenres": "Films par genres", "components.Discover.NetworkSlider.networks": "Diffuseurs", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Les médias ajoutés à votre <PlexWatchlistSupportLink>liste de lecture Plex</PlexWatchlistSupportLink> apparaîtront ici.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Votre liste de lecture", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "components.Discover.StudioSlider.studios": "Studios", "components.Discover.TvGenreList.seriesgenres": "Séries par genres", "components.Discover.TvGenreSlider.tvgenres": "Séries par genres", "components.Discover.createnewslider": "<PERSON><PERSON><PERSON> un nouveau slider", "components.Discover.customizediscover": "Personnaliser Découvrir", "components.Discover.discover": "Découvrir", "components.Discover.emptywatchlist": "Les médias ajoutés à votre <PlexWatchlistSupportLink>liste de lecture Plex</PlexWatchlistSupportLink> apparaîtront ici.", "components.Discover.moviegenres": "Films par genres", "components.Discover.networks": "Diffuseurs", "components.Discover.plexwatchlist": "Votre liste de lecture", "components.Discover.popularmovies": "Films populaires", "components.Discover.populartv": "Séries populaires", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON> r<PERSON>s", "components.Discover.recentrequests": "De<PERSON><PERSON> ré<PERSON>es", "components.Discover.resetfailed": "Une erreur s'est produite lors de la réinitialisation des paramètres de Découvrir.", "components.Discover.resetsuccess": "Réinitialisation réussie des paramètres de Découvrir.", "components.Discover.resettodefault": "Réinitialiser par défaut", "components.Discover.resetwarning": "Réinitialisez tous les sliders par défaut. Cela supprimera également tous les sliders personnalisés !", "components.Discover.stopediting": "Arrêter l'édition", "components.Discover.studios": "Studios", "components.Discover.tmdbmoviegenre": "Genre de film TMDB", "components.Discover.tmdbmoviekeyword": "Mot-clé de film TMDB", "components.Discover.tmdbmoviestreamingservices": "Services de diffusion en continu de films TMDB", "components.Discover.tmdbnetwork": "Diffuseur TMDB", "components.Discover.tmdbsearch": "Recherche TMDB", "components.Discover.tmdbstudio": "Studio TMDB", "components.Discover.tmdbtvgenre": "Genre de la série TMDB", "components.Discover.tmdbtvkeyword": "Mot-clé de la série TMDB", "components.Discover.tmdbtvstreamingservices": "Services de streaming TMDB TV", "components.Discover.trending": "Tendances", "components.Discover.tvgenres": "Séries par genres", "components.Discover.upcoming": "Films à venir", "components.Discover.upcomingmovies": "Films à venir", "components.Layout.SearchInput.searchPlaceholder": "Rechercher un film ou une série", "components.Layout.Sidebar.dashboard": "Découvrir", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Paramètres", "components.Layout.Sidebar.users": "Utilisateurs", "components.Layout.UserDropdown.signout": "Se déconnecter", "components.MovieDetails.budget": "Budget", "components.MovieDetails.cast": "Casting", "components.MovieDetails.originallanguage": "Langue originale", "components.MovieDetails.overview": "Résumé", "components.MovieDetails.overviewunavailable": "Résumé indisponible.", "components.MovieDetails.recommendations": "Recommandations", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Date de sortie} other {Date de sorties}}", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.runtime": "{minutes} minutes", "components.MovieDetails.similar": "<PERSON>it<PERSON> similaires", "components.PersonDetails.appearsin": "Apparitions", "components.PersonDetails.ascharacter": "Rôle : {character}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {Saisons}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {Saisons}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {Saisons}}", "components.RequestList.requests": "<PERSON><PERSON><PERSON>", "components.RequestModal.cancel": "Annuler la demande", "components.RequestModal.numberofepisodes": "Nombre d'épisodes", "components.RequestModal.pendingrequest": "Demande en attente", "components.RequestModal.requestCancel": "De<PERSON>e pour <strong>{title}</strong> refusée.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> demandé avec succès !", "components.RequestModal.requestadmin": "Cette demande sera validée automatiquement.", "components.RequestModal.requestfrom": "La demande de {username} est en attente de validation.", "components.RequestModal.requestseasons": "Demander {seasonCount} {seasonCount, plural, one {saison} other {saisons}}", "components.RequestModal.season": "<PERSON><PERSON>", "components.RequestModal.seasonnumber": "<PERSON><PERSON> {number}", "components.RequestModal.selectseason": "Sélectionner la/les saison(s)", "components.Search.searchresults": "Résultats de la recherche", "components.Settings.Notifications.agentenabled": "Activer l'agent", "components.Settings.Notifications.authPass": "Mot de passe SMTP", "components.Settings.Notifications.authUser": "Nom d'utilisateur SMTP", "components.Settings.Notifications.emailsender": "<PERSON><PERSON><PERSON> de l'expéditeur", "components.Settings.Notifications.smtpHost": "Hôte SMTP", "components.Settings.Notifications.smtpPort": "Port SMTP", "components.Settings.Notifications.validationSmtpHostRequired": "V<PERSON> devez fournir un nom d'hôte ou une adresse IP valide", "components.Settings.Notifications.validationSmtpPortRequired": "Vous devez fournir un numéro de port valide", "components.Settings.Notifications.webhookUrl": "URL de webhook", "components.Settings.RadarrModal.add": "Ajouter un serveur", "components.Settings.RadarrModal.apiKey": "Clé d'API", "components.Settings.RadarrModal.baseUrl": "URL de base", "components.Settings.RadarrModal.createradarr": "Ajouter un nouveau serveur Radarr", "components.Settings.RadarrModal.defaultserver": "Serveur par défaut", "components.Settings.RadarrModal.editradarr": "Modifier le serveur Radarr", "components.Settings.RadarrModal.hostname": "Nom d'hôte ou adresse IP", "components.Settings.RadarrModal.minimumAvailability": "Disponibilité minimale", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "Profil de qualité", "components.Settings.RadarrModal.rootfolder": "Dossier racine", "components.Settings.RadarrModal.selectMinimumAvailability": "Sélectionner une disponibilté minimale", "components.Settings.RadarrModal.selectQualityProfile": "Sélectionner un profil qualité", "components.Settings.RadarrModal.selectRootFolder": "Sélectionner un dossier racine", "components.Settings.RadarrModal.server4k": "Serveur 4K", "components.Settings.RadarrModal.servername": "Nom du serveur", "components.Settings.RadarrModal.ssl": "Utiliser SSL", "components.Settings.RadarrModal.toastRadarrTestFailure": "Échec de la connexion à Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Connexion avec le Serveur Radarr établie avec succès !", "components.Settings.RadarrModal.validationApiKeyRequired": "<PERSON><PERSON> de<PERSON> fournir une clé d'API", "components.Settings.RadarrModal.validationHostnameRequired": "V<PERSON> devez fournir un nom d'hôte ou une adresse IP valide", "components.Settings.RadarrModal.validationPortRequired": "Vous devez fournir un numéro de port valide", "components.Settings.RadarrModal.validationProfileRequired": "<PERSON><PERSON> de<PERSON> sélectionner un profil", "components.Settings.RadarrModal.validationRootFolderRequired": "<PERSON><PERSON> de<PERSON> sélectionner un dossier racine", "components.Settings.SonarrModal.add": "Ajouter un serveur", "components.Settings.SonarrModal.apiKey": "Clé API", "components.Settings.SonarrModal.baseUrl": "URL de base", "components.Settings.SonarrModal.createsonarr": "Ajouter un nouveau serveur Sonarr", "components.Settings.SonarrModal.defaultserver": "Serveur par défaut", "components.Settings.SonarrModal.editsonarr": "Modifier le serveur Sonarr", "components.Settings.SonarrModal.hostname": "Nom d'hôte ou adresse IP", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.qualityprofile": "Profil de qualité", "components.Settings.SonarrModal.rootfolder": "Dossier racine", "components.Settings.SonarrModal.seasonfolders": "Dossier saison", "components.Settings.SonarrModal.selectQualityProfile": "Sélectionner un profil qualité", "components.Settings.SonarrModal.selectRootFolder": "Sélectionner un dossier racine", "components.Settings.SonarrModal.server4k": "Serveur 4K", "components.Settings.SonarrModal.servername": "Nom du serveur", "components.Settings.SonarrModal.ssl": "Utiliser SSL", "components.Settings.SonarrModal.validationApiKeyRequired": "<PERSON><PERSON> de<PERSON> fournir une clé d'API", "components.Settings.SonarrModal.validationHostnameRequired": "V<PERSON> devez fournir un nom d'hôte ou une adresse IP valide", "components.Settings.SonarrModal.validationPortRequired": "Vous devez fournir un numéro de port valide", "components.Settings.SonarrModal.validationProfileRequired": "<PERSON><PERSON> de<PERSON> sélectionner un profil qualité", "components.Settings.SonarrModal.validationRootFolderRequired": "<PERSON><PERSON> de<PERSON> sélectionner un dossier racine", "components.Settings.activeProfile": "Profil actif", "components.Settings.addradarr": "Ajouter un serveur Radarr", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "Ajouter un serveur Sonarr", "components.Settings.cancelscan": "<PERSON><PERSON>r le scan", "components.Settings.copied": "Clé d'API copiée dans le presse-papier.", "components.Settings.currentlibrary": "Bibliothèque actuelle : {name}", "components.Settings.default": "<PERSON><PERSON> <PERSON><PERSON>", "components.Settings.default4k": "4K par défaut", "components.Settings.deleteserverconfirm": "Êtes-vous sûr(e) de vouloir supprimer ce serveur ?", "components.Settings.hostname": "Nom d'hôte ou adresse IP", "components.Settings.librariesRemaining": "Bibliothèques restantes : {count}", "components.Settings.manualscan": "Scan manuel des bibliothèques", "components.Settings.manualscanDescription": "Normalement, le scan sera effectué une fois toutes les 24 heures seulement. <PERSON><PERSON><PERSON><PERSON> vérifiera les ajouts récents de votre serveur Plex de façon proactive. Si c'est la première fois que vous configurez <PERSON>lex, un scan complet de la bibliothèque est recommandé !", "components.Settings.menuAbout": "À propos", "components.Settings.menuGeneralSettings": "Général", "components.Settings.menuJobs": "Tâches et cache", "components.Settings.menuLogs": "<PERSON><PERSON><PERSON>", "components.Settings.menuNotifications": "Notifications", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "Applications", "components.Settings.notificationsettings": "Paramètres de notification", "components.Settings.notrunning": "Pas en exécution", "components.Settings.plexlibraries": "Bibliothèques Plex", "components.Settings.plexlibrariesDescription": "Les bibliothèques Jellyseerr recherchent les titres. Configurez et sauvegardez vos paramètres de connexion Plex, puis cliquez sur le bouton ci-dessous si aucune bibliothèque n'est listée.", "components.Settings.plexsettings": "Paramètres Plex", "components.Settings.plexsettingsDescription": "Configurer les paramètres de votre serveur Plex. Jellyseerr scanne vos librairies Plex pour déterminer les contenus disponibles.", "components.Settings.port": "Port", "components.Settings.radarrsettings": "Paramètres Radarr", "components.Settings.sonarrsettings": "Paramètres Sonarr", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Commencer le <PERSON>", "components.Setup.configureservices": "Configurer les Services", "components.Setup.continue": "<PERSON><PERSON><PERSON>", "components.Setup.finish": "Finir la configuration", "components.Setup.finishing": "Finalisation…", "components.Setup.signinMessage": "Commencez en vous connectant avec votre compte Plex", "components.Setup.welcome": "Bienvenue sur Jellyseerr", "components.TvDetails.cast": "Casting", "components.TvDetails.originallanguage": "Langue originale", "components.TvDetails.overview": "Résumé", "components.TvDetails.overviewunavailable": "Résumé indisponible.", "components.TvDetails.recommendations": "Recommandations", "components.TvDetails.similar": "Séries similaires", "components.UserList.admin": "Admin", "components.UserList.created": "A rejoint", "components.UserList.plexuser": "Utilisateur Plex", "components.UserList.role": "R<PERSON><PERSON>", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON>", "components.UserList.user": "Utilisa<PERSON>ur", "components.UserList.userlist": "Liste des utilisateurs", "i18n.approve": "Valider", "i18n.approved": "<PERSON><PERSON><PERSON>", "i18n.available": "Disponible", "i18n.cancel": "Annuler", "i18n.decline": "Refuser", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "<PERSON><PERSON><PERSON><PERSON>", "i18n.movies": "Films", "i18n.partiallyavailable": "Partiellement disponible", "i18n.pending": "En attente", "i18n.processing": "En cours de traitement", "i18n.tvshows": "Séries", "i18n.unavailable": "Indisponible", "pages.oops": "Oups", "pages.returnHome": "Retourner à l'accueil", "components.TvDetails.TvCast.fullseriescast": "Casting complet de la série", "components.MovieDetails.MovieCast.fullcast": "Casting complet", "components.Settings.Notifications.emailsettingssaved": "Paramètres de notification par e-mail enregistrés avec succès !", "components.Settings.Notifications.emailsettingsfailed": "Les paramètres de notification par e-mail n'ont pas pu être enregistrés.", "components.Settings.Notifications.discordsettingssaved": "Paramètres de notification Discord enregistrés avec succès !", "components.Settings.Notifications.discordsettingsfailed": "Les paramètres de notification Discord n'ont pas pu être enregistrés.", "components.Settings.validationPortRequired": "Vous devez fournir un numéro de port valide", "components.Settings.validationHostnameRequired": "V<PERSON> devez fournir un nom d'hôte valide ou une adresse IP", "components.Settings.SonarrModal.validationNameRequired": "V<PERSON> devez fournir un nom de serveur", "components.Settings.SettingsAbout.version": "Version", "components.Settings.SettingsAbout.totalrequests": "Total des demandes", "components.Settings.SettingsAbout.totalmedia": "Total des médias", "components.Settings.SettingsAbout.overseerrinformation": "À propos de Je<PERSON>rr", "components.Settings.SettingsAbout.githubdiscussions": "Discussions GitHub", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON><PERSON> <PERSON> l'aide", "components.Settings.RadarrModal.validationNameRequired": "V<PERSON> devez fournir un nom de serveur", "i18n.deleting": "Suppression…", "components.UserList.userdeleteerror": "Une erreur s'est produite lors de la suppression de l'utilisateur.", "components.UserList.userdeleted": "Utilisateur supprimé avec succès !", "components.UserList.deleteuser": "Supprimer l'utilisateur", "components.UserList.deleteconfirm": "Voulez-vous vraiment supprimer cet utilisateur ? Toutes les données de demande de cet utilisateur seront supprimées de façon permanente.", "components.Settings.SonarrModal.testFirstRootFolders": "Testez la connexion pour charger les dossiers racine", "components.Settings.SonarrModal.testFirstQualityProfiles": "Testez la connexion pour charger les profils qualité", "components.Settings.SonarrModal.loadingrootfolders": "Chargement des dossiers racine…", "components.Settings.SonarrModal.loadingprofiles": "Chargement des profils qualité…", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "<PERSON><PERSON> de<PERSON> sélectionner une disponibilité minimale", "components.Settings.RadarrModal.testFirstRootFolders": "Testez la connexion pour charger les dossiers racine", "components.Settings.RadarrModal.testFirstQualityProfiles": "Testez la connexion pour charger les profils qualité", "components.Settings.RadarrModal.loadingrootfolders": "Chargement des dossiers racine…", "components.Settings.RadarrModal.loadingprofiles": "Chargement des profils qualité…", "components.TvDetails.showtype": "Type de séries", "components.TvDetails.network": "{networkCount, plural, one {Diffuseur} other {Diffuseurs}}", "components.TvDetails.anime": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animerootfolder": "Dossier racine pour anime", "components.Settings.SonarrModal.animequalityprofile": "Profil qualité pour anime", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studios}}", "components.Settings.SettingsAbout.supportoverseerr": "Soutenez Overseerr", "i18n.close": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON> ho<PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Aidez-nous à payer le café", "components.Settings.SettingsAbout.Releases.viewongithub": "Voir sur GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Voir le journal des modifications", "components.Settings.SettingsAbout.Releases.versionChangelog": "Journal des modifications de la version {version}", "components.Settings.SettingsAbout.Releases.releases": "Versions", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Les données de version sont actuellement indisponible.", "components.Settings.SettingsAbout.Releases.latestversion": "Dernière version", "components.Settings.SettingsAbout.Releases.currentversion": "Actuelle", "components.UserList.importfromplexerror": "Une erreur s'est produite durant l'importation des utilisateurs de Plex.", "components.UserList.importfromplex": "Importer les utilisateurs de Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {utilisateur} other {utilisateurs}} importé(s) depuis Plex avec succès !", "components.TvDetails.viewfullcrew": "Voir l'équipe complète", "components.TvDetails.TvCrew.fullseriescrew": "Équipe complè<PERSON> de la série", "components.PersonDetails.crewmember": "Équipe", "components.MovieDetails.viewfullcrew": "Voir l'équipe complète", "components.MovieDetails.MovieCrew.fullcrew": "<PERSON><PERSON><PERSON> compl<PERSON>", "components.TvDetails.firstAirDate": "Date de première diffusion", "components.Settings.Notifications.allowselfsigned": "Autoriser les certificats autosignés", "components.TvDetails.watchtrailer": "Regarder la bande-annonce", "components.MovieDetails.watchtrailer": "Regarder la bande-annonce", "i18n.requested": "<PERSON><PERSON><PERSON>", "i18n.retry": "<PERSON><PERSON><PERSON><PERSON>", "i18n.failed": "Échec", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL de webhook", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Paramètres de notification de Slack sauvegardés avec succès !", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Les paramètres de notification Slack n'ont pas pu être enregistrés.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Activer l'agent", "components.RequestList.RequestItem.failedretry": "Une erreur s'est produite lors du renvoi de la demande.", "components.Settings.Notifications.validationChatIdRequired": "V<PERSON> devez fournir un identifiant de discussion valide", "components.Settings.Notifications.botAPI": "Jeton d'autorisation du bot", "components.Settings.Notifications.validationBotAPIRequired": "<PERSON><PERSON> de<PERSON> fournir la clé d'autorisation du bot", "components.Settings.Notifications.telegramsettingssaved": "Paramètres de notification Telegram enregistrés avec succès !", "components.Settings.Notifications.telegramsettingsfailed": "Les paramètres de notification Telegram n'ont pas pu être enregistrés.", "components.Settings.Notifications.senderName": "Nom de l'expéditeur", "components.Settings.Notifications.chatId": "ID discussion", "components.Settings.SettingsAbout.documentation": "Documentation", "components.NotificationTypeSelector.mediarequestedDescription": "Envoyer des notifications lorsque des utilisateurs soumettent une demande de média qui nécessite une validation.", "components.NotificationTypeSelector.mediarequested": "Demande en attente de validation", "components.NotificationTypeSelector.mediafailedDescription": "Envoyer des notifications lorsqu'une demande de média n'a pas pu être ajoutée à Radarr ou Sonarr.", "components.NotificationTypeSelector.mediafailed": "Échec d’ajout de la demande", "components.NotificationTypeSelector.mediaavailableDescription": "Envoyer des notifications lorsque le média demandé devient disponible.", "components.NotificationTypeSelector.mediaavailable": "Demande disponible", "components.NotificationTypeSelector.mediaapprovedDescription": "Envoyer des notifications lorsqu'une demande de média est validée manuellement.", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON><PERSON> valid<PERSON>", "i18n.request": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "V<PERSON> devez fournir une clé d'utilisateur ou de groupe valide", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "V<PERSON> devez fournir un jeton d'application valide", "components.Settings.Notifications.NotificationsPushover.userToken": "Clé d'utilisateur ou de groupe", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Paramètres de notification Pushover enregistrés avec succès !", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Les paramètres de notification Pushover n'ont pas pu être enregistrés.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Activer l'agent", "components.Settings.Notifications.NotificationsPushover.accessToken": "Jeton API d'application", "components.RequestList.sortModified": "Dernière modification", "components.RequestList.sortAdded": "Plus récentes", "components.RequestList.showallrequests": "<PERSON><PERSON><PERSON><PERSON> toutes les demandes", "components.StatusBadge.status4k": "{status} en 4K", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Paramètres de notification Webhook enregistrés avec succès !", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Échec de l'enregistrement des paramètres de notification du webhook.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL de webhook", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Aide sur les variables de modèle", "components.Settings.Notifications.NotificationsWebhook.authheader": "En-tête d'autorisation", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Activer l'agent", "components.RequestModal.pending4krequest": "Demande 4K en attente", "components.RequestButton.viewrequest4k": "Voir la demande 4K", "components.RequestButton.viewrequest": "Voir la demande", "components.RequestButton.requestmore4k": "De<PERSON>er d'autres ajouts en 4K", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON> d'autres ajouts", "components.RequestButton.declinerequests": "Refuser {requestCount, plural, one {Demande} other {{requestCount} demandes}}", "components.RequestButton.declinerequest4k": "Refuser la demande 4K", "components.RequestButton.declinerequest": "Refuser la demande", "components.RequestButton.decline4krequests": "Refuser {requestCount, plural, one {Demande en 4K} other {{requestCount} demandes en 4K}}", "components.RequestButton.approverequests": "Valider {requestCount, plural, one {Demande} other {{requestCount} demandes}}", "components.RequestButton.approverequest4k": "Valider la demande 4K", "components.RequestButton.approverequest": "Valider la demande", "components.RequestButton.approve4krequests": "Valider {requestCount, plural, one {Demande en 4k} other {{requestCount} demandes en 4K}}", "components.Settings.Notifications.NotificationsWebhook.customJson": "Données utiles JSON", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Les données utiles JSON par défaut ont été réinitialisées avec succès !", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Réinitialiser les données par défaut", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "V<PERSON> devez fournir un JSON payload valide", "components.UserList.validationpasswordminchars": "Le mot de passe est trop court ; il doit contenir au moins 8 caractères", "components.UserList.usercreatedsuccess": "L'utilisateur a bien été créé !", "components.UserList.usercreatedfailed": "Une erreur s'est produite lors de la création de l'utilisateur.", "components.UserList.password": "Mot de passe", "components.UserList.localuser": "Utilisateur local", "components.UserList.creating": "Création…", "components.UserList.createlocaluser": "Créer un utilisateur local", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "Générer automatiquement le mot de passe", "components.UserList.passwordinfodescription": "Configurez l'URL de l'application ainsi que les notifications par e-mail pour permettre la génération automatique de mots de passe.", "components.UserList.email": "Adresse e-mail", "components.Login.validationpasswordrequired": "V<PERSON> devez renseigner un mot de passe", "components.Login.validationemailrequired": "V<PERSON> devez fournir un e-mail valide", "components.Login.signinwithoverseerr": "Utilisez votre compte {applicationTitle}", "components.Login.password": "Mot de passe", "components.Login.loginerror": "Une erreur s'est produite lors de la tentative de connexion.", "components.Login.email": "Adresse e-mail", "components.MediaSlider.ShowMoreCard.seemore": "Voir plus", "i18n.edit": "Modifier", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON> pour <strong>{title}</strong> modifiée avec succès !", "components.RequestModal.requestcancelled": "De<PERSON>e pour <strong>{title}</strong> refusée.", "components.RequestModal.errorediting": "Une erreur s'est produite lors de la modification de la demande.", "components.RequestModal.autoapproval": "Validation automatique", "components.RequestModal.AdvancedRequester.rootfolder": "Dossier racine", "components.RequestModal.AdvancedRequester.qualityprofile": "Profil de qualité", "components.RequestModal.AdvancedRequester.destinationserver": "Serveur de destination", "components.RequestModal.AdvancedRequester.default": "{name} (Dé<PERSON>ut)", "components.RequestModal.AdvancedRequester.animenote": "* Cette série est un animé.", "components.RequestModal.AdvancedRequester.advancedoptions": "Options Avancées", "components.RequestBlock.requestoverrides": "Modifications de la demande", "components.RequestBlock.server": "Serveur de destination", "components.RequestBlock.rootfolder": "Dossier racine", "components.RequestBlock.profilechanged": "Profil de qualité", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON> refusée", "components.NotificationTypeSelector.mediadeclinedDescription": "Envoyer des notifications lorsqu'une demande de média est refusée.", "i18n.experimental": "Expérimental", "components.RequestModal.requesterror": "Une erreur s'est produite lors de la demande.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Nous n'avons pas pu associer cette série automatiquement. Veuillez sélectionner l'association correcte dans la liste ci-dessous.", "components.Login.signinwithplex": "Utilisez votre compte Plex", "components.Login.signin": "Connexion", "components.Login.signinheader": "Connectez-vous pour continuer", "components.Login.signingin": "Connexion en cours…", "components.Settings.notificationAgentSettingsDescription": "Configurer et activer les agents de notification.", "components.UserList.userssaved": "Les permissions d'utilisateur ont été enregistrées avec succès !", "components.UserList.bulkedit": "Modification en masse", "components.PermissionEdit.usersDescription": "Autorise à gérer les utilisateurs. Les utilisateurs avec cette autorisation ne peuvent pas modifier les utilisateurs dotés de privilèges d'administrateur ni les accorder.", "components.PermissionEdit.users": "<PERSON><PERSON><PERSON> les utilisateurs", "components.PermissionEdit.requestDescription": "Autorise à demander des médias non-4K.", "components.PermissionEdit.request4kTvDescription": "Autorise à demander des séries en 4K.", "components.PermissionEdit.request4kTv": "Demander des séries en 4K", "components.PermissionEdit.request4kMoviesDescription": "Autorise à demander des films en 4K.", "components.PermissionEdit.request4kMovies": "Demander des films en 4K", "components.PermissionEdit.request4kDescription": "Autorise à demander des médias en 4K.", "components.PermissionEdit.request4k": "De<PERSON><PERSON> en 4K", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.managerequestsDescription": "Autorise à gérer les demandes de média. Toutes les demandes faites par un utilisateur avec cette autorisation seront validées automatiquement.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON> les demandes", "components.PermissionEdit.autoapproveSeriesDescription": "Valider automatiquement les demandes de séries non-4K.", "components.PermissionEdit.autoapproveSeries": "Valider automatiquement les séries", "components.PermissionEdit.autoapproveMoviesDescription": "Valide automatiquement les demandes de films non-4K.", "components.PermissionEdit.autoapproveMovies": "Valider automatiquement les films", "components.PermissionEdit.autoapproveDescription": "Valide automatiquement toutes les demandes de média non-4K.", "components.PermissionEdit.autoapprove": "Valider automatiquement", "components.PermissionEdit.advancedrequestDescription": "Permet de modifier les options de demande de média avancées.", "components.PermissionEdit.advancedrequest": "Demandes avancées", "components.PermissionEdit.adminDescription": "Accès administrateur complet. Con<PERSON>ne toutes les autres permissions (sélectionnées ou non).", "components.PermissionEdit.admin": "Administateur", "components.Settings.toastPlexRefreshSuccess": "Liste des serveurs Plex récupérée avec succès !", "components.Settings.toastPlexRefreshFailure": "Échec de la récupération de la liste des serveurs Plex.", "components.Settings.toastPlexRefresh": "Récupération de la liste des serveurs depuis Plex…", "components.Settings.toastPlexConnectingSuccess": "Connexion Plex établie avec succès !", "components.Settings.toastPlexConnectingFailure": "Échec de connexion à Plex.", "components.Settings.toastPlexConnecting": "Tentative de connexion à Plex…", "components.Settings.settingUpPlexDescription": "Pour configurer Plex, vous pouvez soit entrer les paramètres manuellement ou choisir parmi l'un des serveurs récupérés sur <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Appuyez sur le bouton à droite de la liste déroulante pour actualiser la liste des serveurs disponibles.", "components.Settings.serverpresetRefreshing": "Récupération des serveurs…", "components.Settings.serverpresetManualMessage": "Configuration manuelle", "components.Settings.serverpresetLoad": "Appuyez sur le bouton pour charger les serveurs disponibles", "components.Settings.serverpreset": "Ser<PERSON><PERSON>", "components.Settings.serverRemote": "distant", "components.Settings.serverLocal": "local", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Connexion à Sonarr établie avec succès !", "components.Settings.SonarrModal.toastSonarrTestFailure": "Échec de la connexion à Sonarr.", "components.Settings.SonarrModal.syncEnabled": "Activer les scans", "components.Settings.SonarrModal.externalUrl": "URL externe", "components.Settings.RadarrModal.syncEnabled": "Activer les scans", "components.Settings.RadarrModal.externalUrl": "URL externe", "components.MovieDetails.markavailable": "Marquer comme disponible", "components.MovieDetails.mark4kavailable": "Marquer comme disponible en 4K", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON><PERSON> effectue certaines tâches de maintenance comme des tâches planifiées régulièrement, mais elles peuvent également être déclenchées manuellement ci-dessous. L'exécution manuelle d'une tâche ne modifiera pas sa planification.", "components.Settings.SettingsJobsCache.cachemisses": "Manq<PERSON>s", "components.Settings.SettingsJobsCache.runnow": "Exécuter", "components.Settings.SettingsJobsCache.nextexecution": "Prochaine exécution", "components.Settings.SettingsJobsCache.jobtype": "Type", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} a commencé.", "components.Settings.SettingsJobsCache.jobs": "Tâches", "components.Settings.SettingsJobsCache.jobname": "Nom de la tâche", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} annulé.", "components.Settings.SettingsJobsCache.flushcache": "Vider le cache", "components.Settings.SettingsJobsCache.canceljob": "Annuler la tâche", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON> de <PERSON> valeur", "components.Settings.SettingsJobsCache.cachename": "Nom du cache", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON> de <PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Total des clés", "components.Settings.SettingsJobsCache.cachehits": "Résultats", "components.Settings.SettingsJobsCache.cacheflushed": "<PERSON><PERSON> de {cachename} vidé.", "components.Settings.SettingsJobsCache.cacheDescription": "<PERSON><PERSON><PERSON><PERSON> met en cache les demandes aux points de terminaison d'API externes pour optimiser les performances et éviter de faire des appels d'API inutiles.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "i18n.advanced": "<PERSON><PERSON><PERSON>", "components.UserList.users": "Utilisateurs", "components.Setup.setup": "Configuration", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "L'URL ne doit pas se terminer par une barre oblique finale", "components.Settings.SonarrModal.validationApplicationUrl": "V<PERSON> devez fournir une URL valide", "components.Settings.SettingsAbout.preferredmethod": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "L'URL ne doit pas se terminer par une barre oblique finale", "components.Settings.RadarrModal.validationApplicationUrl": "V<PERSON> devez fournir une URL valide", "components.Search.search": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.requestas": "De<PERSON>er en tant que", "components.PermissionEdit.viewrequestsDescription": "Autorise à afficher les demandes des autres utilisateurs.", "components.PermissionEdit.viewrequests": "Voir les demandes", "components.UserList.validationEmail": "Email requis", "components.TvDetails.nextAirDate": "Prochaine diffusion", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "L'URL de base ne doit pas se terminer par une barre oblique finale", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "L'URL de base doit être précédée d'une barre oblique", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "L'URL de base ne doit pas se terminer par un slash", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "L'URL de base doit être précédée d'un slash", "components.Settings.Notifications.validationEmail": "V<PERSON> devez fournir un e-mail valide", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "V<PERSON> devez fournir une URL valide", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "V<PERSON> devez fournir une URL valide", "components.ResetPassword.validationpasswordrequired": "V<PERSON> devez renseigner un mot de passe", "components.ResetPassword.validationpasswordminchars": "Le mot de passe est trop court ; il doit comporter au moins 8 caractères", "components.ResetPassword.validationpasswordmatch": "Les mots de passe doivent être les mêmes", "components.ResetPassword.validationemailrequired": "V<PERSON> devez fournir un e-mail valide", "components.ResetPassword.resetpasswordsuccessmessage": "Le mot de passe a été réinitialisé avec succès !", "components.ResetPassword.resetpassword": "Réinitialiser votre mot de passe", "components.ResetPassword.requestresetlinksuccessmessage": "Un lien de réinitialisation du mot de passe sera envoyé à l'e-mail fourni si il est associé à un utilisateur valide.", "components.ResetPassword.password": "Mot de passe", "components.ResetPassword.gobacklogin": "Retourner à la page de connexion", "components.ResetPassword.emailresetlink": "Envoyer un lien de récupération par e-mail", "components.ResetPassword.email": "Adresse e-mail", "components.ResetPassword.confirmpassword": "Confirmation du mot de passe", "components.Login.forgotpassword": "Mot de passe oublié ?", "components.Settings.SettingsJobsCache.process": "Processus", "components.Settings.SettingsJobsCache.command": "Commande", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON><PERSON> de<PERSON> sélectionner un profil de langue", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Tester la connexion pour charger les profils de langue", "components.Settings.SonarrModal.selectLanguageProfile": "Sélectionnez le profil de langue", "components.Settings.SonarrModal.loadinglanguageprofiles": "Chargement des profils de langue…", "components.Settings.SonarrModal.languageprofile": "<PERSON>il de langue", "components.Settings.SonarrModal.animelanguageprofile": "Profil de langue d'anime", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON>il de langue", "components.Settings.Notifications.sendSilentlyTip": "Envoyer des notifications sans son", "components.Settings.Notifications.sendSilently": "Envoyer silencieusement", "components.UserList.sortRequests": "Nombre de demandes", "components.UserList.sortDisplayName": "Nom d'Utilisateur affiché", "components.UserList.sortCreated": "Date d'inscription", "components.PermissionEdit.autoapprove4kSeriesDescription": "Valide automatiquement les demandes de séries en 4K.", "components.PermissionEdit.autoapprove4kSeries": "Validation automatique des séries 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Valide automatiquement les demandes de films en 4K.", "components.PermissionEdit.autoapprove4kMovies": "Validation automatique des films 4K", "components.PermissionEdit.autoapprove4kDescription": "Valide automatiquement toutes les demandes de média en 4K.", "components.PermissionEdit.autoapprove4k": "Validation automatique 4K", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Identifiant", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Paramètres de notification", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nouveau mot de passe", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Mot de passe actuel", "components.UserProfile.UserSettings.UserPasswordChange.password": "Mot de passe", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Paramètres enregistrés avec succès !", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Une erreur s'est produite lors de l'enregistrement des paramètres.", "components.UserProfile.recentrequests": "De<PERSON><PERSON> ré<PERSON>es", "components.UserProfile.UserSettings.menuPermissions": "Permissions", "components.UserProfile.UserSettings.menuNotifications": "Notifications", "components.UserProfile.UserSettings.menuGeneralSettings": "Général", "components.UserProfile.UserSettings.menuChangePass": "Mot de passe", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "<PERSON><PERSON> devez fournir un jeton d'accès", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Les paramètres de notification Pushbullet n'ont pas pu être enregistrés.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Activer l'agent", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON>on d'a<PERSON>ès", "components.Layout.UserDropdown.settings": "Paramètres", "components.Layout.UserDropdown.myprofile": "Profil", "components.UserProfile.UserSettings.UserPermissions.permissions": "Permissions", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Le mot de passe est trop court, il doit contenir un minimum de 8 caractères", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Vous devez fournir un nouveau mot de passe", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Vous devez fournir votre mot de passe actuel", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Les mots de passe doivent correspondre", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON><PERSON> de<PERSON> confirmer le nouveau mot de passe", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Mot de passe enregistré avec succès !", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Un problème est survenu lors de l'enregistrement du mot de passe.", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Vous devez fournir un identifiant valide", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "L' <FindDiscordIdLink>ID</FindDiscordIdLink>associé à votre compte utilisateur", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Les paramètres ont été enregistrés avec succès !", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Un problème est survenu pendant l'enregistrement des paramètres.", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Utilisateur Plex", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Utilisateur local", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Paramètres généraux", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Nom affiché", "components.UserProfile.ProfileHeader.settings": "Modifier les paramètres", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON><PERSON><PERSON> le profil", "components.UserList.userfail": "Un problème est survenu lors de l'enregistrement des permissions de l'utilisateur.", "components.UserList.edituser": "Modifier les permissions de l'utilisateur", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Paramètres de notification Pushbullet enregistrés avec succès !", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Confirmez le mot de passe", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrer le contenu par disponibilité régionale", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Région à découvrir", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrer le contenu par langue d’origine", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Langue à découvrir", "components.Discover.upcomingtv": "Séries à venir", "components.RegionSelector.regionDefault": "Toutes les régions", "components.Settings.webhook": "Webhook", "components.Settings.email": "E-mail", "components.RegionSelector.regionServerDefault": "Défaut ({region})", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Vous n'avez l'autorisation de modifier le mot de passe de cet utilisateur.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Utilisa<PERSON>ur", "components.UserProfile.UserSettings.UserGeneralSettings.role": "R<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Administrateur", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Admin", "components.UserList.owner": "Administrateur", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Type de compte", "components.UserList.accounttype": "Type de compte", "components.Settings.SettingsJobsCache.unknownJob": "Tâche inconnue", "components.Settings.SettingsJobsCache.download-sync": "Synchroniser les téléchargements", "components.Settings.SettingsJobsCache.download-sync-reset": "Reset de la synchronisation des téléchargements", "i18n.loading": "Chargement…", "components.TvDetails.seasons": "{seasonCount, plural, one {# Saison} other {# Saisons}}", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Envoyer des notifications sans son", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Démarre une discussion</TelegramBotLink>, ajoute <GetIdBotLink>@get_id_bot</GetIdBotLink>, et utilise la commande <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID de discussion", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Envoie les messages silencieusement", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "V<PERSON> devez fournir un identifiant de chat valide", "components.Settings.Notifications.botUsername": "Pseudonyme du Bot", "components.RequestList.RequestItem.modified": "Modifiée", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} par {user}", "components.Settings.scanning": "Synchronisation en cours…", "components.Settings.scan": "Synchroniser les bibliothèques", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Scan des ajouts récents Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Scan complet des bibliothèques Plex", "components.Settings.Notifications.validationUrl": "V<PERSON> devez fournir une URL valide", "components.Settings.Notifications.botAvatarUrl": "L'URL de l'avatar de votre Bot", "components.Settings.SettingsUsers.userSettingsDescription": "Configurer les paramètres généraux et par défaut de l'utilisateur.", "components.Settings.SettingsUsers.toastSettingsFailure": "Un problème est survenu pendant la sauvegarde des paramètres.", "components.Settings.SettingsUsers.localLogin": "Activer la connexion locale", "components.Settings.SettingsUsers.defaultPermissions": "Permissions par défaut", "components.UserProfile.ProfileHeader.userid": "ID utilisateur : {userid}", "components.UserProfile.ProfileHeader.joindate": "Membre depuis le {joindate}", "components.Settings.menuUsers": "Utilisateurs", "components.Settings.SettingsUsers.userSettings": "Paramètres utilisateur", "components.Settings.SettingsUsers.toastSettingsSuccess": "Les paramètres utilisateur ont été enregistrés avec succès !", "components.NotificationTypeSelector.mediaAutoApproved": "Demande validée automatiquement", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Envoyer des notifications lorsque des utilisateurs soumettent une demande pour un nouveau média qui est validée automatiquement.", "components.UserProfile.UserSettings.unauthorizedDescription": "Vous n'avez pas l'autorisation de modifier les paramètres de cet utilisateur.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Vous ne pouvez pas modifier vos propres permissions.", "components.Settings.Notifications.pgpPrivateKeyTip": "Signer des emails chiffrés en utilisant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPasswordTip": "Signer des emails chiffrés en utilisant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Clé privée PGP", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutes", "components.TvDetails.episodeRuntime": "<PERSON><PERSON>e d'un épisode", "components.Settings.Notifications.pgpPassword": "Mot de passe PGP", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.alreadyrequested": "<PERSON><PERSON><PERSON><PERSON>", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.pagenotfound": "La page n'a pas été trouvée", "pages.serviceunavailable": "Service indisponible", "components.Settings.SettingsLogs.pauseLogs": "Pause", "components.Settings.SettingsLogs.logs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterWarn": "Attention", "components.Settings.SettingsLogs.filterInfo": "Infos", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterDebug": "Débogage", "components.Settings.SettingsAbout.about": "À propos", "pages.somethingwentwrong": "Un problème est survenu", "i18n.usersettings": "Paramètres utilisateur", "i18n.settings": "Paramètres", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notifications", "components.Settings.SettingsLogs.copiedLogMessage": "Le texte des journaux a été ajouté au presse-papiers.", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Général", "components.Settings.services": "Applications", "components.Settings.plex": "Plex", "components.Settings.notifications": "Notifications", "components.Settings.enablessl": "Utiliser SSL", "components.Settings.SettingsUsers.users": "Utilisateurs", "components.Settings.SettingsLogs.showall": "Afficher tous les journaux", "components.Settings.SettingsLogs.logDetails": "Journal détaillé", "components.Settings.SettingsLogs.copyToClipboard": "Copier dans le presse-papiers", "components.ResetPassword.passwordreset": "Réinitialiser le mot de passe", "pages.internalservererror": "<PERSON>rreur interne du serveur", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Un problème est survenu lors de l'enregistrement du mot de passe. Votre mot de passe actuel a-t-il été saisi correctement ?", "components.Settings.SettingsLogs.time": "Horodatage", "components.Settings.SettingsLogs.resumeLogs": "Résumer", "components.Settings.SettingsLogs.message": "Message", "components.Settings.SettingsLogs.logsDescription": "Vous pouvez également afficher ces journaux directement via <code>stdout</code>, ou dans <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.level": "Gravité", "components.Settings.SettingsLogs.label": "Étiquette", "components.Settings.SettingsLogs.extraData": "Données supplémentaires", "components.Settings.SettingsJobsCache.jobsandcache": "Tâches et cache", "components.UserList.nouserstoimport": "Aucun nouvel utilisateur de Plex à importer.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "<PERSON><PERSON>(e) le {birthdate}", "components.PersonDetails.alsoknownas": "Aussi connu sous le(s) nom(s) : {names}", "i18n.delimitedlist": "{a}, {b}", "components.RequestModal.QuotaDisplay.season": "saison", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON>ne} other {<strong>#</strong>}} {remaining, plural, one {demande} other {demandes}} de {type} {remaining, plural, one {restante} other {restantes}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Vous pouvez voir un résumé des limites de demandes de cet utilisateur sur sa <ProfileLink>page de profil</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Vous pouvez voir un résumé de vos limites de demandes sur votre <ProfileLink>page de profil</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Pas assez de demandes de saison restantes", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {films}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Cet utilisateur est autorisé à demander <strong>{limit}</strong> {type} tous les <strong>{days}</strong> jour(s).", "components.RequestModal.QuotaDisplay.allowedRequests": "Vous êtes autorisé à demander <strong>{limit}</strong> {type} tous les <strong>{days}</strong> jour(s).", "components.QuotaSelector.unlimited": "Illimité", "components.UserProfile.unlimited": "Illimité", "components.TvDetails.originaltitle": "Titre original", "components.MovieDetails.originaltitle": "Titre original", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {saison} other {saisons}}", "components.LanguageSelector.originalLanguageDefault": "Toutes les langues", "components.LanguageSelector.languageServerDefault": "({language}) par défaut", "i18n.tvshow": "Série", "i18n.test": "Tester", "i18n.save": "Sauvegarder les changements", "i18n.request4k": "De<PERSON><PERSON> en 4K", "i18n.movie": "Film", "components.UserProfile.totalrequests": "Total des demandes", "components.UserProfile.requestsperdays": "{limit} restantes", "components.UserProfile.limit": "{remaining} sur {limit}", "i18n.view": "Voir", "i18n.testing": "Test en cours…", "i18n.status": "Statut", "i18n.saving": "Sauvegarde en cours…", "i18n.resultsperpage": "Afficher {pageSize} résultats par page", "i18n.requesting": "Demande en cours…", "i18n.previous": "Précédent", "i18n.notrequested": "Non demandé", "i18n.noresults": "Aucun résultat.", "i18n.next": "Suivant", "i18n.canceling": "Annulation…", "i18n.back": "Retour", "i18n.areyousure": "Êtes-vous sûr ?", "i18n.all": "Toutes", "components.UserProfile.seriesrequest": "Demandes de séries", "components.UserProfile.pastdays": "{type} (derniers {days} jours)", "components.UserProfile.movierequests": "Demandes de films", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limite de demandes de films", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limite de demandes de séries", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Contourner la limite globale", "components.Settings.SonarrModal.loadingTags": "Chargement des tags en cours…", "components.Settings.SonarrModal.edit4ksonarr": "Modifier le serveur Sonarr 4K", "components.Settings.SonarrModal.default4kserver": "Serveur 4K par défaut", "components.Settings.SonarrModal.create4ksonarr": "Ajouter un nouveau serveur Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Tags d'animés", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Limite globale de demandes de séries", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Limite globale de demandes de films", "components.Settings.SonarrModal.testFirstTags": "Tester la connexion pour charger les tags", "components.Settings.RadarrModal.testFirstTags": "Tester la connexion pour charger les tags", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.SonarrModal.selecttags": "Sélectionner les tags", "components.Settings.RadarrModal.selecttags": "Sélectionner les tags", "components.Settings.SonarrModal.notagoptions": "Aucun tag.", "components.Settings.RadarrModal.notagoptions": "Aucun tag.", "components.Settings.RadarrModal.loadingTags": "Chargement des tags en cours…", "components.Settings.RadarrModal.edit4kradarr": "Modifier le serveur Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Serveur 4K par défaut", "components.Settings.RadarrModal.create4kradarr": "Ajouter un nouveau serveur Radarr 4K", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.AdvancedRequester.selecttags": "Sélectionner les tags", "components.RequestModal.AdvancedRequester.notagoptions": "Aucun tag.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Chiffrer les emails en utilisant <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Votre compte n’a actuellement aucun mot de passe. Configurez un mot de passe ci-dessous pour activer la connexion en tant qu’ \"utilisateur local\" en utilisant votre adresse e-mail.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Ce compte utilisateur n’a actuellement pas de mot de passe. Configurez un mot de passe ci-dessous pour permettre à ce compte de se connecter en tant \"qu’utilisateur local.\"", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "<PERSON><PERSON> devez fournir une clé publique PGP valide", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Paramètres de notification Telegram enregistrés avec succès !", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Impossible d’enregistrer les paramètres de notification de Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Clé Publique PGP", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Les paramètres de notification Discord n’ont pas pu être enregistrés.", "components.Settings.serviceSettingsDescription": "Configurez votre serveur {serverType} ci-dessous. Vous pouvez connecter plusieurs serveurs {serverType}, mais seulement deux d’entre eux peuvent être marqués par défaut (un non-4K et un 4K). Les administrateurs peuvent modifier le serveur utilisé pour traiter les nouvelles demandes avant la validation.", "components.Settings.mediaTypeSeries": "séries", "components.Settings.mediaTypeMovie": "film", "components.Settings.SettingsAbout.uptodate": "À jour", "components.Settings.SettingsAbout.outofdate": "Obsolète", "components.Settings.Notifications.validationPgpPrivateKey": "Vous devez fournir une clé privée PGP valide si un mot de passe PGP est entré", "components.Settings.Notifications.validationPgpPassword": "V<PERSON> devez fournir un mot de passe PGP", "components.Settings.Notifications.botUsernameTip": "Permet aux utilisateurs de démarrer également une conversation avec votre bot et de configurer leurs propres notifications personnelles", "components.RequestModal.pendingapproval": "Votre demande est en attente de validation.", "components.RequestList.RequestItem.mediaerror": "{mediaType} non trouvé", "components.RequestList.RequestItem.deleterequest": "Supprimer la demande", "components.RequestList.RequestItem.cancelRequest": "Annuler la demande", "components.RequestCard.mediaerror": "{mediaType} non trouvé", "components.RequestCard.deleterequest": "Supprimer la demande", "components.NotificationTypeSelector.notificationTypes": "Types de Notification", "components.Layout.VersionStatus.streamstable": "Jellyseerr stable", "components.Layout.VersionStatus.streamdevelop": "Développement de <PERSON>rr", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} en retard", "components.Layout.VersionStatus.outofdate": "Obsolète", "components.RequestModal.QuotaDisplay.requiredquota": "Vous devez avoir au moins <strong>{seasons}</strong> {seasons, plural, one {demande de saison} other {demandes de saisons}} afin de soumettre une demande pour cette série.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Cet utilisateur doit avoir au moins <strong>{seasons}</strong> {seasons, plural, one {demande de saison} other {demandes de saisons}} afin de soumettre une demande pour cette série.", "components.Settings.noDefaultNon4kServer": "Si vous n’avez qu’un seul serveur {serverType} pour les contenus non-4K et 4K (ou si vous ne téléchargez que du contenu 4K), votre serveur {serverType} ne devrait <strong>PAS</strong> être désigné comme serveur 4K.", "components.Settings.noDefaultServer": "Au moins un serveur {serverType} doit être marqué par défaut pour que les demandes {mediaType} puissent être envoyées.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Paramètres de notification Discord enregistrés avec succès !", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Impossible d’enregistrer les paramètres de notification par E-mail.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Paramètres de notification par E-mail enregistrés avec succès !", "i18n.showingresults": "Affichage de <strong>{from}</strong> à <strong>{to}</strong> pour <strong>{total}</strong> résultats", "components.UserList.autogeneratepasswordTip": "Envoyer par email un mot de passe généré par le serveur à l’utilisateur", "i18n.retrying": "Nouvelle tentative…", "components.Settings.serverSecure": "sé<PERSON>ris<PERSON>", "components.RequestModal.edit": "Modifier la demande", "components.RequestList.RequestItem.editrequest": "Modifier la demande", "components.UserList.usercreatedfailedexisting": "L'adresse électronique fournie est déjà utilisée par un autre utilisateur.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Langue d'affichage", "components.Settings.webpush": "Web Push", "components.Settings.SonarrModal.enableSearch": "Activer la recherche automatique", "components.Settings.RadarrModal.enableSearch": "Activer la recherche automatique", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Les paramètres de la notification Web push ont été enregistrés avec succès !", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Les paramètres de la notification Web push n'ont pas été enregistrés.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Activer l'agent", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL de webhook", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "V<PERSON> devez fournir une URL valide", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Paramètres de notification de LunaSea sauvegardés avec succès !", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Les paramètres de notification LunaSea n'ont pas pu être enregistrés.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Uniquement nécessaire si vous n'utilisez pas le profil <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nom du Profil", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Activer l'agent", "components.PermissionEdit.requestMoviesDescription": "Autorise à demander des films non-4K.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "La notification de test Web Push n’a pas été envoyée.", "components.PermissionEdit.requestMovies": "Demander des films", "components.PermissionEdit.requestTv": "Demander des séries", "components.PermissionEdit.requestTvDescription": "Autorise à demander des séries non-4K.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Notification test LunaSea envoyée !", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Envoi de la notification test LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "L'envoi de la notification test LunaSea a échoué.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Envoi de la notification test Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "L'envoi de la notification test P<PERSON><PERSON>et a échoué.", "components.Settings.Notifications.toastTelegramTestFailed": "L'envoi de la notification test à Telegram a échoué.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Notification test Pushbullet envoyée !", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Envoi de la notification test Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "L'envoi de la notification test Pushover a échoué.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Notification test Pushover envoyée !", "components.Settings.Notifications.encryptionImplicitTls": "Utiliser TLS implicite", "components.Settings.Notifications.encryptionTip": "Dans la majorité des cas, TLS implicite utilise le port 465 et STARTTLS utilise le port 587", "components.Settings.Notifications.encryptionNone": "Aucune", "components.Settings.Notifications.encryption": "<PERSON><PERSON><PERSON><PERSON> de chiffrement", "components.Settings.Notifications.encryptionDefault": "Utiliser STARTTLS si disponible", "components.Settings.Notifications.encryptionOpportunisticTls": "Toujours utiliser STARTTLS", "components.DownloadBlock.estimatedtime": "Estimation {time}", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Votre <UsersGroupsLink>identifiant d'utilisateur ou de groupe</UsersGroupsLink> de 30 caractères", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Enregistrer une application</ApplicationRegistrationLink> à utiliser avec <PERSON>rr", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON>", "components.RequestCard.failedretry": "Une erreur s'est produite lors du renvoi de la demande.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Paramètres de notification Web Push enregistrés avec succès !", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Notification de test web push envoyée !", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Envoi d'une notification de test web push…", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Afin de recevoir des notifications push web, <PERSON><PERSON><PERSON>rr doit fonctionner en HTTPS.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Notification de test Webhook envoyée !", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Envoi de notification de test webhook…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "L'envoi de la notification de test Web push a échoué.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Notification test S<PERSON>ck envoyée !", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Envoi de la notification test Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "L'envoi de la notification test Slack a <PERSON><PERSON>.", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "<PERSON><PERSON><PERSON> un jeton à partir de vos <PushbulletSettingsLink>paramètres de compte</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Votre <LunaSeaLink>URL de webhook de notification</LunaSeaLink> basée sur l'utilisateur ou l'appareil", "components.QuotaSelector.seasons": "{count, plural, one {saison} other {saisons}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {films}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} tous les {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {jour} other {jours}}", "components.Settings.SettingsAbout.betawarning": "Ceci est un logiciel BÊTA. Les fonctionnalités peuvent être non opérationnelles ou instables. Veuillez signaler tout problème sur GitHub !", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Échec de l'enregistrement des paramètres de notification Web push.", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Langage par défaut ({language})", "components.Settings.webAppUrlTip": "<PERSON><PERSON>ez éventuellement les utilisateurs vers l'application Web sur votre serveur au lieu de l'application Web « hébergée »", "components.Settings.webAppUrl": "URL <WebAppLink>Application Web</WebAppLink>", "components.Settings.noDefault4kServer": "Un serveur 4K {serverType} doit être marqué par défaut afin de permettre aux utilisateurs de soumettre des requêtes 4K {mediaType}.", "components.Settings.is4k": "4K", "components.Settings.SettingsUsers.newPlexLoginTip": "Autoriser les utilisateurs de {mediaServerName} à se connecter sans être d'abord importés", "components.Settings.SettingsUsers.newPlexLogin": "Autoriser nouvelle connexion {mediaServerName}", "components.Settings.SettingsUsers.localLoginTip": "Permettre aux utilisateurs de se connecter en utilisant leur adresse e-mail et leur mot de passe, au lieu de {mediaServerName} OAuth", "components.Settings.SettingsUsers.defaultPermissionsTip": "Autorisations par défaut attribuées aux nouveaux utilisateurs", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON><PERSON> <DiscordWebhookLink>intégration de webhook</DiscordWebhookLink> dans votre serveur", "components.Settings.Notifications.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.toastTelegramTestSuccess": "Notification de test de télégramme envoyée !", "components.Settings.Notifications.toastTelegramTestSending": "Envoi de la notification de test à Telegram…", "components.Settings.Notifications.toastEmailTestSuccess": "Notification de test par e-mail envoyée !", "components.Settings.Notifications.toastEmailTestSending": "Envoi d'une notification de test par e-mail…", "components.Settings.Notifications.toastEmailTestFailed": "Échec de l'envoi de la notification de test par e-mail.", "components.Settings.Notifications.toastDiscordTestSuccess": "Notification de test à Discord envoyée !", "components.Settings.Notifications.toastDiscordTestSending": "Envoi de la notification de test Discord…", "components.Settings.Notifications.toastDiscordTestFailed": "Échec de l'envoi de la notification de test Discord.", "components.Settings.Notifications.chatIdTip": "<PERSON><PERSON><PERSON><PERSON> une discussion avec votre bot, ajoutez <GetIdBotLink>@get_id_bot</GetIdBotLink> et exécutez la commande <code>/my_id</code>", "components.Settings.Notifications.botApiTip": "<CreateBotLink>C<PERSON><PERSON> un bot</CreateBotLink> à utiliser avec <PERSON>", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.NotificationsSlack.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.NotificationsPushover.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} tous les {quotaDays} {days}</quotaUnits>", "components.NotificationTypeSelector.usermediarequestedDescription": "Être averti(e) lorsque d'autres utilisateurs soumettent une demande de média qui nécessite une validation.", "components.NotificationTypeSelector.usermediafailedDescription": "Être averti(e) lorsqu'une demande de média n'a pas pu être ajoutée à Radarr ou Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Être averti(e) lorsque vos demandes de médias sont refusées.", "components.NotificationTypeSelector.usermediaavailableDescription": "Être averti(e) lorsque vos demandes de médias deviennent disponibles.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Être averti(e) lorsque vos demandes de médias sont validées.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Être averti(e) lorsque des utilisateurs soumettent une demande de média qui est validée automatiquement.", "components.MovieDetails.showmore": "<PERSON><PERSON> plus", "components.MovieDetails.showless": "<PERSON><PERSON> moins", "components.Layout.LanguagePicker.displaylanguage": "Langue d'affichage", "components.UserList.localLoginDisabled": "Le paramètre <strong>Activer la connexion locale</strong> est actuellement désactivé.", "components.TvDetails.streamingproviders": "Disponible en streaming sur", "components.MovieDetails.streamingproviders": "Actuellement diffusé sur", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "<PERSON><PERSON>er une intégration <WebhookLink>Webhook entrante</WebhookLink>", "components.StatusBadge.status": "{status}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Toutes les {jobScheduleHours, plural, one {heure} other {{jobScheduleHours} heures}}", "components.IssueDetails.IssueComment.areyousuredelete": "Êtes-vous sûr de vouloir supprimer ce commentaire ?", "components.IssueDetails.IssueComment.delete": "Supp<PERSON><PERSON> le commentaire", "components.IssueDetails.IssueComment.edit": "É<PERSON>er le commentaire", "components.IssueDetails.IssueComment.postedby": "Ajouté {relativeTime} par {username}", "components.IssueDetails.IssueComment.postedbyedited": "Ajou<PERSON> {relativeTime} par {username} (É<PERSON>é)", "components.IssueDetails.IssueComment.validationComment": "<PERSON><PERSON> devez é<PERSON>rire un message", "components.IssueDetails.IssueDescription.deleteissue": "Supp<PERSON>er le problème", "components.IssueDetails.IssueDescription.description": "Description", "components.IssueDetails.IssueDescription.edit": "Éditer la description", "components.IssueDetails.allepisodes": "Tous les épisodes", "components.IssueDetails.allseasons": "Toutes les saisons", "components.IssueDetails.closeissue": "<PERSON>lore le problème", "components.IssueDetails.closeissueandcomment": "Clore avec un commentaire", "components.IssueDetails.commentplaceholder": "Ajouter un commentaire…", "components.IssueDetails.comments": "Commentaires", "components.IssueDetails.deleteissue": "Supp<PERSON>er le problème", "components.IssueDetails.deleteissueconfirm": "Êtes-vous sûr de vouloir supprimer ce problème ?", "components.IssueDetails.episode": "Épisode {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problème", "components.IssueDetails.issuetype": "Type", "components.IssueDetails.lastupdated": "Dernière mise à jour", "components.IssueDetails.leavecomment": "<PERSON><PERSON><PERSON>", "components.IssueDetails.nocomments": "Aucun commentaire.", "components.IssueDetails.openedby": "#{issueId} ouvert {relativeTime} par {username}", "components.IssueDetails.openin4karr": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans {arr} 4K", "components.IssueDetails.openinarr": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans {arr}", "components.IssueDetails.play4konplex": "Lire en 4K sur {mediaServerName}", "components.IssueDetails.playonplex": "Lire sur {mediaServerName}", "components.IssueDetails.problemepisode": "Épisode concern<PERSON>", "components.IssueDetails.problemseason": "Saison concernée", "components.IssueDetails.reopenissue": "<PERSON><PERSON><PERSON><PERSON><PERSON> le problème", "components.IssueDetails.reopenissueandcomment": "Rouvrir avec un commentaire", "components.IssueDetails.season": "<PERSON><PERSON> {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Un problème est survenu lors de l'édition de la description du problème.", "components.IssueDetails.toasteditdescriptionsuccess": "La description du problème a été éditée avec succès !", "components.IssueDetails.toastissuedeleted": "Le problème a été supprimé avec succès !", "components.IssueDetails.toastissuedeletefailed": "Un problème est survenu lors de la suppression du problème.", "components.IssueDetails.toaststatusupdated": "Le statut du problème a été mis à jour avec succès !", "components.IssueDetails.toaststatusupdatefailed": "Un problème est survenu lors de la mise à jour du statut du problème.", "components.IssueDetails.unknownissuetype": "Inconnu", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Épisode} other {Épisodes}}", "components.IssueList.IssueItem.issuestatus": "Statut", "components.IssueList.IssueItem.issuetype": "Type", "components.IssueList.IssueItem.opened": "Ouvert", "components.IssueList.IssueItem.openeduserdate": "{date} par {user}", "components.IssueList.IssueItem.problemepisode": "Épisode concern<PERSON>", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON>} other {Saisons}}", "components.IssueList.IssueItem.unknownissuetype": "Inconnu", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON><PERSON> le problème", "components.IssueList.issues": "Problèmes", "components.IssueList.showallissues": "Afficher tous les problèmes", "components.IssueList.sortAdded": "Plus récents", "components.IssueList.sortModified": "Dernière modification", "components.IssueModal.CreateIssueModal.allepisodes": "Tous les épisodes", "components.IssueModal.CreateIssueModal.allseasons": "Toutes les saisons", "components.IssueModal.CreateIssueModal.episode": "Épisode {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.problemepisode": "Épisode concern<PERSON>", "components.IssueModal.CreateIssueModal.problemseason": "Saison concernée", "components.IssueModal.CreateIssueModal.providedetail": "Fournissez une explication détaillée du problème.", "components.NotificationTypeSelector.userissueresolvedDescription": "Être averti(e) lorsqu'un problème que vous avez signalé est résolu.", "components.PermissionEdit.manageissues": "<PERSON><PERSON><PERSON> les problèmes", "components.PermissionEdit.viewissues": "Afficher les problèmes", "components.PermissionEdit.viewissuesDescription": "Autorise à consulter les problèmes liés aux médias signalés par d'autres utilisateurs.", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Un problème est survenu lors de la soumission du problème.", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON><PERSON><PERSON> le problème", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Le signalement du problème pour <strong>{title}</strong> a été soumis avec succès !", "components.IssueModal.CreateIssueModal.whatswrong": "Qu’est-ce qui ne va pas ?", "components.Layout.Sidebar.issues": "Problèmes", "components.ManageSlideOver.downloadstatus": "Téléchargement(s)", "components.ManageSlideOver.manageModalNoRequests": "Au<PERSON>ne demande.", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "<PERSON><PERSON><PERSON> {mediaType}", "components.ManageSlideOver.manageModalClearMediaWarning": "* Ceci supprimera de manière irréversible toutes les données de ce(tte) {mediaType}, y compris les demandes éventuelles. Si cet élément existe dans votre bibliothèque {mediaServerName}, les informations sur le média seront recréées lors de la prochaine analyse.", "components.ManageSlideOver.tvshow": "série", "components.NotificationTypeSelector.issuecomment": "Commentaires du problème", "components.NotificationTypeSelector.issuecreatedDescription": "Envoyer des notifications lorsqu'un problème est signalé.", "components.PermissionEdit.createissues": "Signaler des problèmes", "components.PermissionEdit.createissuesDescription": "Autorise à signaler les problèmes liés aux médias.", "i18n.resolved": "R<PERSON>ol<PERSON>", "components.NotificationTypeSelector.userissuecommentDescription": "Être averti(e) lorsqu'un problème que vous avez signalé reçoit de nouveaux commentaires.", "components.ManageSlideOver.manageModalClearMedia": "Efface<PERSON> les données", "components.ManageSlideOver.movie": "film", "components.IssueModal.CreateIssueModal.season": "<PERSON><PERSON> {seasonNumber}", "components.IssueModal.CreateIssueModal.validationMessageRequired": "<PERSON><PERSON> de<PERSON> fournir une description", "components.IssueModal.CreateIssueModal.reportissue": "Signaler un problème", "components.IssueModal.CreateIssueModal.submitissue": "Soumettre le problème", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.ManageSlideOver.mark4kavailable": "Marquer comme disponible en 4K", "components.ManageSlideOver.markavailable": "Marquer comme disponible", "components.Settings.SettingsAbout.runningDevelop": "Vous utilisez la branche <code>develop</code> de <PERSON><PERSON><PERSON><PERSON>, qui n'est recommandée que pour ceux qui contribuent au développement ou qui aident aux tests et correctifs.", "components.ManageSlideOver.openarr": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans {arr}", "components.NotificationTypeSelector.adminissuecommentDescription": "Être averti(e) lorsque d'autres utilisateurs commentent sur un problème.", "components.NotificationTypeSelector.issuecommentDescription": "Envoyer des notifications lorsqu'un problème reçoit de nouveaux commentaires.", "components.NotificationTypeSelector.issueresolved": "Problème résolu", "components.ManageSlideOver.openarr4k": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans {arr} 4K", "components.NotificationTypeSelector.issuecreated": "Problème signalé", "components.NotificationTypeSelector.issueresolvedDescription": "Envoyer des notifications lorsqu'un problème est résolu.", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nouvelle fréquence", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Tâche modifiée avec succès !", "i18n.open": "Ouvert", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifier la tâche", "components.NotificationTypeSelector.userissuecreatedDescription": "Être averti(e) lorsque d’autres utilisateurs signalent des problèmes.", "components.PermissionEdit.manageissuesDescription": "Autorise à gérer les problèmes liés aux médias.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Un problème est survenu lors de l'enregistrement de la tâche.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Toutes les {jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON>on d'a<PERSON>ès", "components.NotificationTypeSelector.adminissuereopenedDescription": "Être averti(e) lorsqu'un problème est ré-ouvert par d'autres utilisateurs.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Être averti(e) lorsqu'un problème est résolu par d'autres utilisateurs.", "components.NotificationTypeSelector.issuereopenedDescription": "Envoyer des notifications lorsqu'un problème est rouvert.", "components.NotificationTypeSelector.userissuereopenedDescription": "Être averti(e) lorsqu'un problème que vous avez signalé a été rouvert.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Les paramètres de notification Pushbullet n'ont pas été sauvegardés correctement.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Les paramètres de notification Pushbullet ont été sauvegardés correctement !", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Jeton API d'application", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Enregistrer une application</ApplicationRegistrationLink> à utiliser avec {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "V<PERSON> devez fournir un jeton d'application valide", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Les paramètres de notification Pushover n'ont pas pu être enregistrés.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Paramètres de notification Pushover enregistrés avec succès !", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "C<PERSON>er un jeton depuis les <PushbulletSettingsLink>paramètres de votre compte</PushbulletSettingsLink>", "components**********************************": "Problèmes ouverts", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "V<PERSON> devez fournir une clé d'utilisateur ou de groupe valide", "components.NotificationTypeSelector.issuereopened": "<PERSON><PERSON><PERSON><PERSON> rouvert", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "<PERSON><PERSON> devez fournir un jeton d'accès", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Clé d'utilisateur ou de groupe", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Votre <UsersGroupsLink>identifiant d'utilisateur ou de groupe</UsersGroupsLink> à 30 caractères", "components.RequestModal.requestmovies4k": "Demander {count} {count, plural, one {film} autre {films}} en 4K", "components.RequestModal.selectmovies": "Sélectionner le(s) film(s)", "components.MovieDetails.productioncountries": "{countryCount, plural, one {Pays} other {Pays}} de production", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON>", "components.RequestModal.approve": "Valider la demande", "components.RequestModal.requestseasons4k": "Demander {seasonCount} {seasonCount, plural, one {saison} other {saisons}} en 4K", "components.RequestModal.requestmovies": "Demander {count} {count, plural, one {film} other {films}}", "components.RequestModal.requestApproved": "De<PERSON>e pour <strong>{title}</strong> validée !", "components.Settings.RadarrModal.inCinemas": "Au cinéma", "components.Settings.RadarrModal.released": "Disponible", "components.TvDetails.productioncountries": "Pays de production", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Activer l'agent", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Les paramètres de notification Gotify n'ont pas pu être enregistrés.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Paramètres de notification de Gotify sauvegardés avec succès !", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Envoi de la notification test Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Notification test Gotify envoyée !", "components.Settings.Notifications.NotificationsGotify.token": "Jeton d'application", "components.Settings.Notifications.NotificationsGotify.url": "URL du serveur", "components.Settings.Notifications.NotificationsGotify.validationTypes": "V<PERSON> devez sélectionner au moins un type de notification", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "V<PERSON> devez fournir une URL valide", "components.Settings.Notifications.enableMentions": "<PERSON>r les <PERSON>", "i18n.import": "Importer", "i18n.importing": "Importation…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "L'envoi de la notification test Gotify a échoué.", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "V<PERSON> devez fournir un jeton d'application", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "L'URL ne doit pas se terminer par un slash", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia4k": "Média(s) 4K", "components.ManageSlideOver.markallseasons4kavailable": "Marquer toutes les saisons comme disponibles en 4K", "components.ManageSlideOver.playedby": "<PERSON><PERSON> par", "components.Settings.validationUrlTrailingSlash": "L'URL ne doit pas ce terminer par un slash", "components.Settings.externalUrl": "URL externe", "components.Settings.tautulliApiKey": "Clé API", "components.Settings.tautulliSettings": "Paramè<PERSON>", "components.Settings.toastTautulliSettingsFailure": "<PERSON><PERSON><PERSON> chose c'est mal passé quand les paramètres Tautulli on été enregistrés.", "components.Settings.toastTautulliSettingsSuccess": "Les paramètres pour Tautulli on bien été sauvegardés !", "components.Settings.urlBase": "URL de base", "components.Settings.validationApiKey": "<PERSON><PERSON> devez fournir une clef API", "components.Settings.validationUrl": "V<PERSON> devez fournir une URL valide", "components.Settings.validationUrlBaseLeadingSlash": "L'URL de base doit avoir un slash", "components.UserProfile.recentlywatched": "Vu récemment", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON><PERSON><PERSON> Tautulli", "components.ManageSlideOver.pastdays": "{days, number} derniers jours", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {lecture} other {lectures}}", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Étiquette de canal", "components.ManageSlideOver.alltime": "Tout le temps", "components.ManageSlideOver.manageModalMedia": "<PERSON><PERSON><PERSON>(s)", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON> toutes les saisons comme disponibles", "components.Settings.validationUrlBaseTrailingSlash": "L'URL de base ne doit pas ce terminer par un slash", "components.Settings.tautulliSettingsDescription": "Configuration optionnelle pour votre serveur Tautulli. Jellyseerr va récupérer l'historique de visionnage de votre Plex depuis <PERSON>.", "components.UserList.newplexsigninenabled": "L'option <strong>Autoriser nouvelle connexion Plex</strong> est actuellement activée. Les utilisateurs Plex disposant d'un accès à la librairie n'ont pas besoin d'être importés pour pouvoir ce connecter.", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID utilisateur Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Le <FindDiscordIdLink>numéro d'identification à plusieurs chiffres</FindDiscordIdLink> est associé avec votre compte Discord", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "V<PERSON> devez fournir un ID utilisateur Discord valide", "components.Settings.SettingsAbout.appDataPath": "Répertoire de données", "components.MovieDetails.digitalrelease": "Sortie numéri<PERSON>", "components.MovieDetails.physicalrelease": "Sortie physique", "components.PermissionEdit.autorequest": "Demande automatique", "components.MovieDetails.theatricalrelease": "Sortie en salles", "components.StatusChecker.reloadApp": "Recharger {applicationTitle}", "components.PermissionEdit.viewrecent": "Voir les ajouts récents", "components.PermissionEdit.viewrecentDescription": "Autorise à voir la liste des médias ajoutés récemment.", "i18n.restartRequired": "Redémarrage nécessaire", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "ID TheTVDB", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "ID TheTVDB", "components.NotificationTypeSelector.mediaautorequested": "Demande soumise automatiquement", "components.PermissionEdit.autorequestMoviesDescription": "Autorise l'envoi de demande automatique pour les vidéos non-4K via la liste de lecture Plex.", "components.PermissionEdit.autorequestSeriesDescription": "Autorise l'envoi de demande automatique pour les séries non-4K via la liste de lecture Plex.", "components.StatusChecker.appUpdatedDescription": "Veuillez cliquer sur le bouton ci-dessous pour recharger l'application.", "components.StatusChecker.restartRequiredDescription": "<PERSON><PERSON><PERSON>z redémarrer le serveur pour appliquer les paramètres mis à jour.", "components.TitleCard.cleardata": "Efface<PERSON> les données", "components.TitleCard.mediaerror": "{mediaType} non trouvé", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "ID TheTVDB", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Demander automatiquement les films", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Demander automatiquement les Séries", "components.PermissionEdit.autorequestDescription": "Autorise l'envoi de demande automatique pour les médias non-4K via la liste de lecture Plex.", "components.PermissionEdit.autorequestMovies": "Demander automatiquement les Films", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Demande automatiquement les séries de votre <PlexWatchlistSupportLink>Watchlist Plex</PlexWatchlistSupportLink>", "components.PermissionEdit.autorequestSeries": "Demander automatiquement les Séries", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Synchronisation de la liste de lecture Plex", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Demande automatiquement les films sur votre <PlexWatchlistSupportLink>Watchlist Plex</PlexWatchlistSupportLink>", "components.NotificationTypeSelector.mediaautorequestedDescription": "Re<PERSON>vez une notification lorsque de nouvelles demandes de médias sont automatiquement soumises pour des éléments de votre liste de lecture.", "components.StatusChecker.restartRequired": "Redémarrage du serveur requis", "components.StatusChecker.appUpdated": "{applicationTitle} mis à jour", "components.Settings.SettingsLogs.viewdetails": "Voir les détails", "components.Settings.advancedTooltip": "Une configuration incorrecte de ce paramètre peut entraîner un dysfonctionnement", "components.Settings.experimentalTooltip": "L'activation de ce paramètre peut entraîner un comportement inattendu de l'application", "components.PermissionEdit.viewwatchlists": "Voir les listes de lecture {mediaServerName}", "components.TvDetails.reportissue": "Signaler un problème", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Demandes de films", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Demandes de séries", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON>", "components.Layout.UserWarnings.emailInvalid": "L'adresse e-mail est invalide.", "components.Layout.UserWarnings.emailRequired": "Une adresse e-mail est requise.", "components.Layout.UserWarnings.passwordRequired": "Un mot de passe est requis.", "components.Login.credentialerror": "Le nom d’utilisateur ou le mot de passe est incorrect.", "components.Login.description": "Comme il s’agit de votre première connexion à {applicationName}, vous devez ajouter une adresse courrielle valide.", "components.Login.initialsignin": "Connexion", "components.Login.initialsigningin": "Connexion en cours…", "components.Login.save": "Ajouter", "components.Login.saving": "<PERSON><PERSON><PERSON>…", "components.Login.signinwithjellyfin": "Utilisez votre compte {mediaServerName}", "components.Login.title": "Ajouter un e-mail", "components.Login.username": "Nom d'utilisateur", "components.Login.validationEmailFormat": "E-mail invalide", "components.Login.validationEmailRequired": "<PERSON><PERSON> devez fournir un e-mail", "components.Login.validationemailformat": "V<PERSON> devez fournir un e-mail valide", "components.Login.validationhostformat": "URL valide requise", "components.Login.validationhostrequired": "{mediaServerName} URL requise", "components.Login.validationusernamerequired": "Nom d'utilisateur requis", "components.MovieDetails.imdbuserscore": "Note des utilisateurs IMDB", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Cela supprimera irréversiblement ce(tte) {mediaType} de {arr}, y compris tous les fichiers.", "components.ManageSlideOver.removearr": "Supprimer de {arr}", "components.ManageSlideOver.removearr4k": "Supprimer de {arr} 4K", "components.MovieDetails.downloadstatus": "Statut du téléchargement", "components.MovieDetails.managemovie": "<PERSON><PERSON><PERSON> le film", "components.MovieDetails.openradarr": "Ou<PERSON><PERSON>r le film dans Radarr", "components.MovieDetails.openradarr4k": "Ouvrir le film dans Radarr 4K", "components.MovieDetails.play": "Lire sur {mediaServerName}", "components.MovieDetails.play4k": "Lire en 4k sur {mediaServerName}", "components.MovieDetails.reportissue": "Signaler un problème", "components.MovieDetails.rtaudiencescore": "Score d’audience Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatomètre Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Note des utilisateurs TMDB", "components.PermissionEdit.viewwatchlistsDescription": "Autorise à voir les listes de lecture {mediaServerName} des autres utilisateurs.", "components.RequestBlock.approve": "Approuver la demande", "components.RequestBlock.decline": "Refuser la demande", "components.RequestBlock.delete": "Supprimer la demande", "components.RequestBlock.edit": "Modifier la demande", "components.RequestBlock.languageprofile": "<PERSON>il de langue", "components.RequestBlock.lastmodifiedby": "Dernière modification par", "components.RequestBlock.requestdate": "Date de la demande", "components.RequestBlock.requestedby": "Demandé par", "components.RequestCard.approverequest": "Approuver la demande", "components.RequestCard.cancelrequest": "Annuler la demande", "components.RequestCard.declinerequest": "Refuser la demande", "components.RequestCard.editrequest": "Modifier la demande", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> doit être redémarré pour que les modifications de ce paramètre prennent effet", "components.Settings.deleteServer": "Supprimer {serverType} serveur", "components.RequestModal.requestcollection4ktitle": "Demander la collection en 4K", "components.RequestModal.requestcollectiontitle": "De<PERSON><PERSON> la collection", "components.UserProfile.emptywatchlist": "Les médias ajoutés à votre <PlexWatchlistSupportLink>Watchlist Plex</PlexWatchlistSupportLink> apparaîtront ici.", "components.RequestModal.SearchByNameModal.nomatches": "Nous n'avons pas pu trouver de correspondance pour cette série.", "components.RequestModal.requestmovie4ktitle": "Demander le film en 4K", "components.RequestModal.requestmovietitle": "<PERSON><PERSON><PERSON> le film", "components.RequestModal.requestseries4ktitle": "De<PERSON>er la série en 4K", "components.RequestModal.requestseriestitle": "<PERSON><PERSON><PERSON> la série", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Fréquence actuelle", "components.RequestList.RequestItem.unknowntitle": "Titre inconnu", "components.Settings.SettingsMain.toastSettingsFailure": "Un problème est survenu lors de l'enregistrement des paramètres.", "components.Settings.SettingsMain.validationApplicationUrl": "V<PERSON> devez fournir une URL valide", "components.TvDetails.Season.noepisodes": "Liste des épisodes non disponible.", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.RequestCard.unknowntitle": "Titre inconnu", "components.Settings.SettingsMain.locale": "Langue d'affichage", "components.Settings.SettingsMain.toastApiKeySuccess": "Nouvelle clé d'API générée avec succès !", "components.Settings.SettingsMain.toastApiKeyFailure": "Un problème est survenu lors de la génération de la nouvelle clé d'API.", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "L'URL ne doit pas se terminer par une barre oblique", "components.Settings.SettingsJobsCache.imagecachesize": "Taille totale du cache", "components.Settings.SettingsMain.apikey": "Clé d'API", "components.Settings.SettingsMain.applicationTitle": "Nom de l'application", "components.Settings.SettingsMain.applicationurl": "URL de l'application", "components.Settings.SettingsMain.cacheImages": "Activer la mise en cache d'image", "components.Settings.SettingsMain.cacheImagesTip": "Met en cache localement et utilise des images optimisées (nécessite une quantité considérable d'espace disque)", "components.Settings.SettingsMain.general": "Général", "components.Settings.SettingsMain.generalsettings": "Paramètres généraux", "components.Settings.SettingsMain.toastSettingsSuccess": "Paramètres sauvegardés avec succès !", "components.DownloadBlock.formattedTitle": "{title} : <PERSON><PERSON> {seasonNumber} épisode {episodeNumber}", "components.Settings.SettingsJobsCache.imagecachecount": "Images mises en cache", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON> d'images", "components.Settings.SettingsJobsCache.imagecacheDescription": "Une fois activé dans les paramètres, <PERSON><PERSON>seerr va récupérer et mettre en cache les images provenant de sources externes pré-configurées. Les images mises en cache sont enregistrées dans votre dossier de configuration. Vous pouvez trouver les fichiers dans le répertoire <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Nettoyage du cache d'images", "components.Settings.SettingsMain.hideAvailable": "Masquer les médias disponibles", "components.Settings.SettingsMain.originallanguageTip": "Filtrer le contenu par langue d’origine", "components.Settings.SettingsMain.validationApplicationTitle": "V<PERSON> devez fournir un nom d'application", "components.Discover.updatefailed": "Une erreur s'est produite lors de la mise à jour des paramètres de Découvrir.", "components.Discover.updatesuccess": "Mise à jour des paramètres de Découvrir.", "components.Layout.Sidebar.browsemovies": "Films", "components.Selector.searchGenres": "Sélectionnez le(s) genre(s)…", "components.Selector.searchKeywords": "Recherchez un ou plusieurs mots-clés…", "components.Selector.searchStudios": "Recherchez un ou plusieurs studios…", "components.Settings.SettingsMain.generalsettingsDescription": "Configurer les paramètres généraux et par défaut pour Jellyseerr.", "components.Settings.SettingsMain.originallanguage": "Langue à découvrir", "components.Settings.SettingsMain.partialRequestsEnabled": "Permettre les demandes partielles des séries", "components.Selector.nooptions": "Aucun résultat.", "components.Layout.Sidebar.browsetv": "Séries", "components.Selector.showmore": "En voir plus", "components.Selector.showless": "En voir moins", "components.Selector.starttyping": "Commencez à taper pour rechercher.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Toutes les {jobScheduleSeconds, plural, one {seconde} other {{jobScheduleSeconds} secondes}}", "components.Settings.SettingsJobsCache.availability-sync": "Synchronisation de la disponibilité des médias", "components.Settings.RadarrModal.tagRequests": "<PERSON><PERSON> les demandes", "components.Settings.SonarrModal.tagRequests": "<PERSON><PERSON> les demandes", "components.Settings.SonarrModal.tagRequestsInfo": "Ajouter automatiquement un tag supplémentaire avec l'ID utilisateur et le nom d'affichage du demandeur", "i18n.collection": "Collection", "components.Settings.RadarrModal.tagRequestsInfo": "Ajouter automatiquement un tag supplémentaire avec l'ID utilisateur et le nom d'affichage du demandeur", "components.IssueModal.issueVideo": "Vidéo", "components.Settings.Notifications.NotificationsPushover.sound": "Son de notification", "components.Settings.jellyfinSettings": "Paramètres {mediaServerName}", "components.Settings.jellyfinSettingsFailure": "Un problème est survenu lors de l'enregistrement des paramètres de {mediaServerName}.", "components.Settings.jellyfinSettingsSuccess": "Les paramètres de {mediaServerName} ont été sauvegardés avec succès !", "components.Settings.jellyfinlibraries": "Bibliothèques {mediaServerName}", "components.Settings.jellyfinlibrariesDescription": "Les bibliothèques de {mediaServerName} sont en cours d'analyse. Cliquez sur le bouton ci-dessous si aucune bibliothèque n'est répertoriée.", "components.Settings.jellyfinsettings": "Paramètres {mediaServerName}", "components.Settings.manualscanJellyfin": "Analy<PERSON> man<PERSON> de la bibliothèque", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.save": "Enregistrer les modifications", "components.Settings.saving": "Sauvegarde en cours…", "components.Settings.syncing": "Synchronisation en cours", "components.Setup.signin": "Se connecter", "components.Setup.signinWithPlex": "Entrez vos identifiants Plex", "components.StatusBadge.managemedia": "<PERSON><PERSON><PERSON> {mediaType}", "components.StatusBadge.openinarr": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans {arr}", "components.StatusBadge.playonplex": "Lire sur {mediaServerName}", "components.TitleCard.addToWatchList": "Ajouter à la liste de surveillance", "components.TitleCard.watchlistCancel": "Watchlist pour <strong>{title}</strong> annulée.", "components.TitleCard.watchlistError": "Un problème est survenu. Veuillez réessayer.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> a été ajouté à votre watchlist avec succès !", "components.TvDetails.Season.somethingwentwrong": "Une erreur est survenue lors de la récupération des données de la saison.", "components.TvDetails.manageseries": "<PERSON><PERSON><PERSON> les séries", "components.TvDetails.play": "Lire sur {mediaServerName}", "components.TvDetails.play4k": "Lire en 4K sur {mediaServerName}", "components.TvDetails.rtcriticsscore": "Tomatometer sur Rotten Tomatoes", "components.TvDetails.seasonnumber": "<PERSON><PERSON> {seasonNumber}", "components.TvDetails.seasonstitle": "Saisons", "components.TvDetails.status4k": "{status} 4K", "components.TvDetails.tmdbuserscore": "Score utilisateur sur TMDB", "components.UserList.importfromJellyfin": "Importer les utilisateurs de {mediaServerName}", "components.UserList.importfromJellyfinerror": "Une erreur est survenue lors de l'importation des utilisateurs de {mediaServerName}.", "components.UserList.importfrommediaserver": "Importer les utilisateurs de {mediaServerName}", "components.UserList.noJellyfinuserstoimport": "Il n'y a aucun utilisateur à importer pour {mediaServerName}.", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Sauvegarde en cours…", "components.UserProfile.plexwatchlist": "Watchlist Plex", "components.Settings.syncJellyfin": "Synchroniser les bibliothèques", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> a été retiré de votre watchlist avec succès !", "components.IssueModal.issueSubtitles": "Sous-titres", "components.Login.emailtooltip": "L'adresse ne nécessite pas d'être associée avec votre instance {mediaServerName}.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Appareil par défaut", "components.Settings.Notifications.userEmailRequired": "Exiger l'adresse e-mail de l'utilisateur", "components.Settings.SettingsAbout.supportjellyseerr": "Soutenir Je<PERSON>rr", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Scan complet des bibliothèques Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Scan des ajouts récents aux bibliothèques Jellyfin", "components.Settings.SonarrModal.animeSeriesType": "Type de série anime", "components.Settings.SonarrModal.seriesType": "Type de série", "components.Settings.jellyfinsettingsDescription": "Configurez les paramètres de votre serveur {mediaServerName}. {mediaServerName} analyse vos bibliothèques {mediaServerName} pour voir quel contenu est disponible.", "components.Settings.jellyfinSettingsDescription": "Configurez facultativement les URL internes et externes pour votre serveur {mediaServerName}. Dans la plupart des cas, l'URL externe est différente de l'URL interne. Vous pouvez également définir une URL de réinitialisation de mot de passe personnalisée pour la connexion à {mediaServerName}, au cas où vous souhaiteriez rediriger vers une page de réinitialisation de mot de passe différente.", "components.Settings.manualscanDescriptionJellyfin": "Normalement, cette tâche est executée qu'une fois toutes les 24 heures. <PERSON><PERSON><PERSON><PERSON> vérifiera plus agressivement les éléments récemment ajoutés à votre serveur {mediaServerName}. Si c'est la première fois que vous configurez <PERSON><PERSON><PERSON><PERSON>, une analyse complète manuelle de la bibliothèque est recommandée !", "components.Settings.timeout": "Temps écoulé", "components.Setup.configuremediaserver": "Configurer le serveur multimédia", "components.TvDetails.rtaudiencescore": "Score de l'audience sur Rotten Tomatoes", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "Utilisateur {mediaServerName}", "components.Setup.signinWithJellyfin": "Entrez vos identifiants Jellyfin", "components.UserList.mediaServerUser": "Utilisateur {mediaServerName}", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Épisode} other {# Épisodes}}", "components.UserList.newJellyfinsigninenabled": "Le paramètre <strong>Activer la nouvelle connexion à {mediaServerName}</strong> est actuellement activé. Les utilisateurs de {mediaServerName} avec accès à la bibliothèque n'ont pas besoin d'être importés pour se connecter.", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-mail", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Enregistrer les modifications", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Appareil par défaut", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {user} other {users}} importé(s) avec succès !", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Son de notification", "components.UserProfile.localWatchlist": "Liste de lecture de {username}", "components.Login.invalidurlerror": "Impossible de se connecter au serveur {mediaServerName}.", "components.MovieDetails.removefromwatchlist": "Supprimer de la liste de surveillance", "components.Login.adminerror": "Vous devez utiliser un compte administrateur pour vous connecter.", "components.MovieDetails.addtowatchlist": "Ajouter à la liste de surveillance", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> a bien été supprimé de la liste de suivi !", "components.Login.validationUrlBaseTrailingSlash": "L'URL de base ne doit pas se terminer par une barre oblique finale", "components.RequestList.RequestItem.profileName": "Profil", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Email valide requis", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> a bien été supprimé de la liste de suivi !", "components.TvDetails.addtowatchlist": "Ajouter à la liste de surveillance", "components.Login.enablessl": "Utilise SSL", "components.Login.hostname": "URL de {mediaServerName}", "components.Login.port": "Port", "components.Login.urlBase": "URL de base", "components.Login.validationHostnameRequired": "V<PERSON> devez fournir un nom d'hôte ou une adresse IP valide", "components.Login.validationPortRequired": "Vous devez fournir un numéro de port valide", "components.Login.validationUrlBaseLeadingSlash": "L'URL de base doit avoir une barre oblique initiale", "components.Login.validationUrlTrailingSlash": "L'URL ne doit pas se terminer par une barre oblique finale", "components.MovieDetails.watchlistError": "Une erreur s'est produite. Merci de réessayez.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> a bien été ajouté à la liste de suivi !", "components.Settings.invalidurlerror": "Impossible de se connecter au serveur {mediaServerName}.", "components.Settings.jellyfinForgotPasswordUrl": "URL de mot de passe oublié", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "L'authentification personnalisée avec le regroupement automatique de bibliothèques n'est pas prise en charge", "components.Settings.jellyfinSyncFailedGenericError": "Une erreur s'est produite lors de la synchronisation des bibliothèques", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Aucune bibliothèque n'a été trouvée", "components.TvDetails.removefromwatchlist": "Supprimer de la liste de surveillance", "components.TvDetails.watchlistError": "Un problème est survenu. Veuillez réessayer.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> a bien été ajouté à la liste de suivi !", "components.UserList.username": "Nom d'utilisateur", "components.UserList.validationUsername": "V<PERSON> devez fournir un nom d'utilisateur", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Email requis", "components.Login.validationservertyperequired": "<PERSON><PERSON><PERSON> de sélectionner un type de serveur", "components.Setup.servertype": "Choisir le type de serveur", "components.Login.back": "Retourner en arrière", "components.Login.servertype": "Type de serveur", "components.Selector.canceled": "<PERSON><PERSON><PERSON>(e)", "components.Selector.ended": "<PERSON><PERSON><PERSON><PERSON>(e)", "components.Selector.inProduction": "En production", "components.Selector.pilot": "<PERSON><PERSON>", "components.Selector.planned": "Planifié(e)", "components.Selector.returningSeries": "Séries de retour", "components.Selector.searchStatus": "Sélectionner statut...", "components.Setup.back": "Retourner en arrière", "components.Setup.configemby": "Confi<PERSON><PERSON>", "components.Setup.configjellyfin": "Configu<PERSON>", "components.Setup.configplex": "Configurer Plex", "components.Setup.signinWithEmby": "Entrez vos identifiants Emby", "components.Setup.subtitle": "Commencez par choisir votre serveur multimédia", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.Discover.FilterSlideover.status": "Statut", "components.Blacklist.mediaType": "Type", "components.Blacklist.mediaTmdbId": "Identifiant tmdb", "components.Blacklist.blacklistdate": "date", "components.Blacklist.blacklistedby": "{date} par {user}", "components.Blacklist.mediaName": "Nom", "component.BlacklistBlock.blacklistdate": "Date de mise en liste noire", "component.BlacklistBlock.blacklistedby": "Mis en liste noire par", "component.BlacklistModal.blacklisting": "Ajout en liste noire", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> n'est pas dans la liste noire.", "components.Blacklist.blacklistSettingsDescription": "<PERSON><PERSON><PERSON> le contenu en liste noire.", "components.Blacklist.blacklistsettings": "Paramètres de la liste noire", "components.Layout.Sidebar.blacklist": "Liste noire", "components.PermissionEdit.viewblacklistedItems": "Voir les médias dans la liste noire.", "i18n.blacklistDuplicateError": "<strong>{title}</strong> est déjà dans la liste noire.", "i18n.blacklistError": "Un problème est survenu. Veuillez réessayer.", "components.PermissionEdit.blacklistedItems": "Ajouter le média à la liste noire.", "components.Settings.Notifications.validationWebhookRoleId": "Vous devez fournir un identifiant Discord valide", "components.Settings.SettingsMain.discoverRegion": "Pays à découvrir", "components.PermissionEdit.manageblacklistDescription": "Accorder la permission de gérer la liste noire.", "components.RequestList.RequestItem.removearr": "Supprimer de {arr}", "components.Settings.SettingsMain.streamingRegionTip": "Afficher les sites de streaming par disponibilité dans les pays", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Afficher les sites de streaming par disponibilité régionale", "components.PermissionEdit.blacklistedItemsDescription": "Accorder la permission de mettre les médias sur liste noire.", "components.PermissionEdit.manageblacklist": "<PERSON><PERSON><PERSON> la liste noire", "components.PermissionEdit.viewblacklistedItemsDescription": "Accorder la permission de voir la liste noire.", "components.RequestList.sortDirection": "Inverser la direction du tri", "components.Settings.SettingsJobsCache.usersavatars": "Avatars des utilisateurs", "components.Settings.SettingsMain.discoverRegionTip": "Filtrer le contenu par disponibilité dans les pays", "components.Settings.SettingsMain.streamingRegion": "Pays de diffusion", "components.Settings.apiKey": "Clé API", "components.Settings.scanbackground": "L'analyse s'exécutera en arrière-plan. Vous pouvez poursuivre la configuration en attendant.", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Pays à découvrir", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Filtrer le contenu par disponibilité régionale", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Pays de diffusion", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Cet email est déjà pris !", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Quelqu'un d'autre possède déjà ce nom d'utilisateur. Vous devez utiliser une adresse e-mail", "i18n.blacklistSuccess": "<strong>{title}</strong> a été ajouté dans la liste noire avec succès.", "i18n.blacklisted": "Sur liste noire", "i18n.addToBlacklist": "Ajouter à la liste noire", "i18n.blacklist": "Liste noire", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> a été retiré de la liste noire avec succès.", "i18n.removefromBlacklist": "Re<PERSON>rer de la liste noire", "i18n.specials": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.webhookRoleIdTip": "L'ID à mentionner dans le message du webhook. Laissez ce champ vide pour désactiver les mentions", "components.Settings.Notifications.webhookRoleId": "ID de rôle de notification", "components.Settings.SettingsJobsCache.plex-refresh-token": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.tip": "Conseil", "components.Login.loginwithapp": "Se connecter avec {appName}", "components.Settings.SettingsNetwork.network": "<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.genres": "Genres", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "N'activez pas ce paramètre à moins que vous ne compreniez ce que vous faites !", "components.Settings.OverrideRuleModal.selectQualityProfile": "<PERSON><PERSON> le profil de qualité", "components.Settings.OverrideRuleModal.selectRootFolder": "Choisir le dossier racine", "components.Settings.SettingsNetwork.networksettings": "Paramètres réseau", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Paramètres réseau avan<PERSON>", "components.DiscoverTvUpcoming.upcomingtv": "Séries à venir", "components.Login.noadminerror": "Aucun compte administrateur trouvé sur ce serveur.", "components.Login.orsigninwith": "Ou se connecter avec", "components.Settings.OverrideRuleModal.keywords": "<PERSON>ts clés", "components.Settings.OverrideRuleModal.qualityprofile": "Profil de qualité", "components.Settings.OverrideRuleModal.rootfolder": "Dossier racine", "components.Settings.OverrideRuleModal.selectService": "Choisir le service", "components.Settings.OverrideRuleModal.settings": "Paramètres", "components.Settings.OverrideRuleModal.selecttags": "Choisir les étiquettes", "components.Settings.OverrideRuleModal.tags": "Étiquettes", "components.Settings.OverrideRuleModal.users": "Utilisateurs", "components.Settings.OverrideRuleTile.genre": "Genre", "components.Settings.OverrideRuleTile.keywords": "<PERSON>ts clés", "components.Settings.OverrideRuleTile.qualityprofile": "Profil de qualité", "components.Settings.OverrideRuleTile.rootfolder": "Dossier racine", "components.Settings.OverrideRuleTile.settings": "Paramètres", "components.Settings.OverrideRuleTile.tags": "Étiquettes", "components.Settings.OverrideRuleTile.users": "Utilisateurs", "components.Settings.SettingsNetwork.docs": "documentation", "components.Selector.searchUsers": "Sélectionner un utilisateur…", "components.Settings.SettingsNetwork.networkDisclaimer": "Les paramètres réseau de votre conteneur/système doivent être utilisés à la place de ces paramètres. Consultez la documentation pour plus d'informations.", "components.Settings.SettingsMain.enableSpecialEpisodes": "Autoriser les demandes d'épisodes spéciaux", "components.Settings.SettingsUsers.loginMethods": "Méthodes de connexion", "components.Settings.menuNetwork": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.mediaServerLoginTip": "Autoriser les utilisateurs à se connecter en utilisant leur compte {mediaServerName}", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "Saisissez vos informations de connexion à {mediaServerName} pour lier votre compte à {applicationName}.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "ID du fil de discussion", "components.Setup.librarieserror": "La validation a échoué. Veuillez réactiver les bibliothèques pour continuer.", "components.Settings.Notifications.messageThreadId": "ID du fil de discussion", "components.Settings.overrideRulesDescription": "Les règles de substitution vous permettent de spécifier les propriétés qui seront remplacées si une demande correspond à la règle.", "components.Settings.Notifications.messageThreadIdTip": "Si votre discussion de groupe prend en charge les fils de discussion, vous pouvez spécifier son identifiant ici", "components.Settings.SettingsUsers.atLeastOneAuth": "Au moins une méthode d'authentification doit être sélectionnée.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Ces comptes externes sont liés à votre compte {applicationName}.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Si votre discussion de groupe prend en charge les fils de discussion, vous pouvez spécifier son identifiant ici", "components.Settings.OverrideRuleModal.conditionsDescription": "Spécifie les conditions avant d'appliquer les modifications des paramètres. Chaque champ doit être validé pour que les règles s'appliquent (opération ET). Un champ est considéré comme vérifié si l'une de ses propriétés correspond (opération OU).", "components.Settings.SettingsNetwork.toastSettingsFailure": "Un problème s'est produit lors de l'enregistrement des paramètres.", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Impossible de se connecter à {mediaServerName} en utilisant vos informations de connexion", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "Vous devez fournir un mot de passe", "components.Settings.addrule": "Nouvelle règle de substitution", "components.Settings.Notifications.validationMessageThreadId": "L'identifiant du fil de discussion doit être un nombre entier positif", "components.Settings.OverrideRuleModal.conditions": "Conditions", "components.Settings.OverrideRuleModal.languages": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.notagoptions": "Pas de tag.", "components.Settings.OverrideRuleModal.editrule": "Modifier la règle de substitution", "components.Settings.OverrideRuleModal.ruleCreated": "La règle de substitution a été créée avec succès !", "components.Settings.OverrideRuleModal.ruleUpdated": "La règle de substitution a été mise à jour avec succès !", "components.Settings.OverrideRuleModal.service": "Service", "components.Settings.OverrideRuleModal.serviceDescription": "Appliquer cette règle au service sélectionné.", "components.Settings.OverrideRuleModal.settingsDescription": "Spécifie les paramètres qui seront modifiés lorsque les conditions ci-dessus sont remplies.", "components.Settings.OverrideRuleTile.conditions": "Conditions", "components.Settings.OverrideRuleTile.language": "<PERSON><PERSON>", "components.Settings.SettingsNetwork.csrfProtection": "Activer la protection CSRF", "components.Settings.SettingsNetwork.csrfProtectionTip": "Définir l'accès à l'API externe en lecture seule (nécessite HTTPS)", "components.Settings.SettingsNetwork.forceIpv4First": "Forcer la résolution IPv4 en premier", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "Forcer <PERSON><PERSON><PERSON><PERSON> à résoudre d'abord les adresses IPv4 au lieu d'IPv6", "components.Settings.SettingsNetwork.networksettingsDescription": "Configurez les paramètres réseau de votre instance Jelly<PERSON>rr.", "components.Settings.SettingsNetwork.proxyBypassFilter": "Adresses ignorées par le proxy", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Utilisez ',' comme séparateur et '*' comme wildcard pour les sous-domaines", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "<PERSON><PERSON><PERSON> le proxy pour les adresses locales", "components.Settings.SettingsNetwork.proxyEnabled": "Proxy HTTP(S)", "components.Settings.SettingsNetwork.proxyHostname": "Nom d'hôte du proxy", "components.Settings.SettingsNetwork.proxyPassword": "Mot de passe du proxy", "components.Settings.SettingsNetwork.proxyPort": "Port du proxy", "components.Settings.SettingsNetwork.proxySsl": "Utiliser SSL pour le proxy", "components.Settings.SettingsNetwork.proxyUser": "Nom d'utilisateur du proxy", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Les paramètres ont été enregistrés avec succès !", "components.Settings.SettingsNetwork.trustProxy": "Activer la prise en charge du proxy", "components.Settings.SettingsNetwork.trustProxyTip": "Permettre à Jellyseerr d'enregistrer correctement les adresses IP des clients derrière un proxy", "components.Settings.SettingsNetwork.validationProxyPort": "V<PERSON> devez indiquer un port valide", "components.Settings.SettingsUsers.loginMethodsTip": "Configurer les méthodes de connexion pour les utilisateurs.", "components.Settings.SettingsUsers.mediaServerLogin": "Activer la connexion à {mediaServerName}", "components.Settings.overrideRules": "R<PERSON>gles de substitution", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "Ce compte est déjà lié à un utilisateur {applicationName}", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Une erreur inconnue est survenue", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Mot de passe", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "<PERSON><PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Ajout en cours…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "<PERSON>r le compte {mediaServerName}", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Nom d'utilisateur", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "V<PERSON> devez fournir un nom d'utilisateur", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Impossible de supprimer le compte associé.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Une erreur inconnue est survenue", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "L'identifiant du fil de discussion doit être un nombre entier positif", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Comptes liés", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "Vous n'avez pas de comptes externes liés à votre compte.", "components.UserProfile.UserSettings.menuLinkedAccounts": "Comptes liés", "components.Settings.OverrideRuleModal.create": "<PERSON><PERSON><PERSON> une règle", "components.Settings.OverrideRuleModal.createrule": "Nouvelle règle de remplacement", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "Vous n'avez pas la permission de modifier les comptes liés de cet utilisateur.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Ce compte est déjà lié à un utilisateur Plex", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Impossible de se connecter à Plex en utilisant vos identifiants"}