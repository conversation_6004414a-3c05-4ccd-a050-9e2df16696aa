---
title: Jobs & Cache
description: Configure jobs and cache settings.
---

# Jobs & Cache

Jellyseerr performs certain maintenance tasks as regularly-scheduled jobs, but they can also be manually triggered on this page.

Je<PERSON>seerr also caches requests to external API endpoints to optimize performance and avoid making unnecessary API calls. If necessary, the cache for any particular endpoint can be cleared by clicking the "Flush Cache" button.

You can also view the current image cache size as well as the total number of cached images.
