{"component.BlacklistBlock.blacklistdate": "Blacklisted date", "component.BlacklistBlock.blacklistedby": "Blacklisted By", "component.BlacklistModal.blacklisting": "Blacklisting", "components.AirDateBadge.airedrelative": "Aired {relativeTime}", "components.AirDateBadge.airsrelative": "Airing {relativeTime}", "components.AppDataWarning.dockerVolumeMissingDescription": "The <code>{appDataPath}</code> volume mount was not configured properly. All data will be cleared when the container is stopped or restarted.", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> is not blacklisted.", "components.Blacklist.blacklistSettingsDescription": "Manage blacklisted media.", "components.Blacklist.blacklistdate": "date", "components.Blacklist.blacklistedby": "{date} by {user}", "components.Blacklist.blacklistsettings": "Blacklist Settings", "components.Blacklist.filterBlacklistedTags": "Blacklisted Tags", "components.Blacklist.filterManual": "Manual", "components.Blacklist.mediaName": "Name", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.Blacklist.mediaType": "Type", "components.Blacklist.showAllBlacklisted": "Show All Blacklisted Media", "components.CollectionDetails.numberofmovies": "{count} Movies", "components.CollectionDetails.overview": "Overview", "components.CollectionDetails.requestcollection": "Request Collection", "components.CollectionDetails.requestcollection4k": "Request Collection in 4K", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "Create Custom Slider", "components.Discover.CreateSlider.addfail": "Failed to create new slider.", "components.Discover.CreateSlider.addsuccess": "Created new slider and saved discover customization settings.", "components.Discover.CreateSlider.editSlider": "<PERSON>", "components.Discover.CreateSlider.editfail": "Failed to edit slider.", "components.Discover.CreateSlider.editsuccess": "Edited slider and saved discover customization settings.", "components.Discover.CreateSlider.needresults": "You need to have at least 1 result.", "components.Discover.CreateSlider.nooptions": "No results.", "components.Discover.CreateSlider.providetmdbgenreid": "Provide a TMDB Genre ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Provide a TMDB Keyword ID", "components.Discover.CreateSlider.providetmdbnetwork": "Provide TMDB Network ID", "components.Discover.CreateSlider.providetmdbsearch": "Provide a search query", "components.Discover.CreateSlider.providetmdbstudio": "Provide TMDB Studio ID", "components.Discover.CreateSlider.searchGenres": "Search genres…", "components.Discover.CreateSlider.searchKeywords": "Search keywords…", "components.Discover.CreateSlider.searchStudios": "Search studios…", "components.Discover.CreateSlider.slidernameplaceholder": "Slider Name", "components.Discover.CreateSlider.starttyping": "Starting typing to search.", "components.Discover.CreateSlider.validationDatarequired": "You must provide a data value.", "components.Discover.CreateSlider.validationTitlerequired": "You must provide a title.", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Movies", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Movies", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Movies", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.DiscoverMovies.discovermovies": "Movies", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularity Ascending", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularity Descending", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Release Date Ascending", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Release Date Descending", "components.Discover.DiscoverMovies.sortTitleAsc": "Title (A-Z) Ascending", "components.Discover.DiscoverMovies.sortTitleDesc": "Title (Z-A) Descending", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB Rating Ascending", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB Rating Descending", "components.Discover.DiscoverNetwork.networkSeries": "{network} Series", "components.Discover.DiscoverSliderEdit.deletefail": "Failed to delete slider.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Sucessfully deleted slider.", "components.Discover.DiscoverSliderEdit.enable": "Toggle Visibility", "components.Discover.DiscoverSliderEdit.remove": "Remove", "components.Discover.DiscoverStudio.studioMovies": "{studio} Movies", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.DiscoverTv.discovertv": "Series", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "First Air Date Ascending", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "First Air Date Descending", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularity Ascending", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularity Descending", "components.Discover.DiscoverTv.sortTitleAsc": "Title (A-Z) Ascending", "components.Discover.DiscoverTv.sortTitleDesc": "Title (Z-A) Descending", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB Rating Ascending", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB Rating Descending", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Series", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Series", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Series", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Your Watchlist", "components.Discover.DiscoverWatchlist.watchlist": "Plex Watchlist", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.FilterSlideover.certification": "Content Rating", "components.Discover.FilterSlideover.clearfilters": "Clear Active Filters", "components.Discover.FilterSlideover.filters": "Filters", "components.Discover.FilterSlideover.firstAirDate": "First Air Date", "components.Discover.FilterSlideover.from": "From", "components.Discover.FilterSlideover.genres": "Genres", "components.Discover.FilterSlideover.keywords": "Keywords", "components.Discover.FilterSlideover.originalLanguage": "Original Language", "components.Discover.FilterSlideover.ratingText": "Ratings between {minValue} and {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Release Date", "components.Discover.FilterSlideover.runtime": "Runtime", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minute runtime", "components.Discover.FilterSlideover.status": "Status", "components.Discover.FilterSlideover.streamingservices": "Streaming Services", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB User Score", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB User Vote Count", "components.Discover.FilterSlideover.to": "To", "components.Discover.FilterSlideover.voteCount": "Number of votes between {minValue} and {maxValue}", "components.Discover.MovieGenreList.moviegenres": "Movie Genres", "components.Discover.MovieGenreSlider.moviegenres": "Movie Genres", "components.Discover.NetworkSlider.networks": "Networks", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Media added to your <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> will appear here.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Your Watchlist", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Recently Added", "components.Discover.StudioSlider.studios": "Studios", "components.Discover.TvGenreList.seriesgenres": "Series Genres", "components.Discover.TvGenreSlider.tvgenres": "Series Genres", "components.DiscoverTvUpcoming.upcomingtv": "Upcoming Series", "components.Discover.createnewslider": "Create New Slider", "components.Discover.customizediscover": "Customize Discover", "components.Discover.discover": "Discover", "components.Discover.emptywatchlist": "Media added to your <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> will appear here.", "components.Discover.moviegenres": "Movie Genres", "components.Discover.networks": "Networks", "components.Discover.plexwatchlist": "Your Watchlist", "components.Discover.popularmovies": "Popular Movies", "components.Discover.populartv": "Popular Series", "components.Discover.recentlyAdded": "Recently Added", "components.Discover.recentrequests": "Recent Requests", "components.Discover.resetfailed": "Something went wrong resetting the discover customization settings.", "components.Discover.resetsuccess": "Sucessfully reset discover customization settings.", "components.Discover.resettodefault": "Reset to De<PERSON>ult", "components.Discover.resetwarning": "Reset all sliders to default. This will also delete any custom sliders!", "components.Discover.stopediting": "Stop Editing", "components.Discover.studios": "Studios", "components.Discover.tmdbmoviegenre": "TMDB Movie Genre", "components.Discover.tmdbmoviekeyword": "TMDB Movie Keyword", "components.Discover.tmdbmoviestreamingservices": "TMDB Movie Streaming Services", "components.Discover.tmdbnetwork": "TMDB Network", "components.Discover.tmdbsearch": "TMDB Search", "components.Discover.tmdbstudio": "TMDB Studio", "components.Discover.tmdbtvgenre": "TMDB Series Genre", "components.Discover.tmdbtvkeyword": "TMDB Series Keyword", "components.Discover.tmdbtvstreamingservices": "TMDB TV Streaming Services", "components.Discover.trending": "Trending", "components.Discover.tvgenres": "Series Genres", "components.Discover.upcoming": "Upcoming Movies", "components.Discover.upcomingmovies": "Upcoming Movies", "components.Discover.upcomingtv": "Upcoming Series", "components.Discover.updatefailed": "Something went wrong updating the discover customization settings.", "components.Discover.updatesuccess": "Updated discover customization settings.", "components.DownloadBlock.estimatedtime": "Estimated {time}", "components.DownloadBlock.formattedTitle": "{title}: Season {seasonNumber} Episode {episodeNumber}", "components.IssueDetails.IssueComment.areyousuredelete": "Are you sure you want to delete this comment?", "components.IssueDetails.IssueComment.delete": "Delete Comment", "components.IssueDetails.IssueComment.edit": "Edit Comment", "components.IssueDetails.IssueComment.postedby": "Posted {relativeTime} by {username}", "components.IssueDetails.IssueComment.postedbyedited": "Posted {relativeTime} by {username} (Edited)", "components.IssueDetails.IssueComment.validationComment": "You must enter a message", "components.IssueDetails.IssueDescription.deleteissue": "Delete Issue", "components.IssueDetails.IssueDescription.description": "Description", "components.IssueDetails.IssueDescription.edit": "Edit Description", "components.IssueDetails.allepisodes": "All Episodes", "components.IssueDetails.allseasons": "All Seasons", "components.IssueDetails.closeissue": "Close Issue", "components.IssueDetails.closeissueandcomment": "Close with Comment", "components.IssueDetails.commentplaceholder": "Add a comment…", "components.IssueDetails.comments": "Comments", "components.IssueDetails.deleteissue": "Delete Issue", "components.IssueDetails.deleteissueconfirm": "Are you sure you want to delete this issue?", "components.IssueDetails.episode": "Episode {episodeNumber}", "components.IssueDetails.issuepagetitle": "Issue", "components.IssueDetails.issuetype": "Type", "components.IssueDetails.lastupdated": "Last Updated", "components.IssueDetails.leavecomment": "Comment", "components.IssueDetails.nocomments": "No comments.", "components.IssueDetails.openedby": "#{issueId} opened {relativeTime} by {username}", "components.IssueDetails.openin4karr": "Open in 4K {arr}", "components.IssueDetails.openinarr": "Open in {arr}", "components.IssueDetails.play4konplex": "Play in 4K on {mediaServerName}", "components.IssueDetails.playonplex": "Play on {mediaServerName}", "components.IssueDetails.problemepisode": "Affected Episode", "components.IssueDetails.problemseason": "Affected Season", "components.IssueDetails.reopenissue": "Reopen Issue", "components.IssueDetails.reopenissueandcomment": "Reopen with Comment", "components.IssueDetails.season": "Season {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Something went wrong while editing the issue description.", "components.IssueDetails.toasteditdescriptionsuccess": "Issue description edited successfully!", "components.IssueDetails.toastissuedeleted": "Issue deleted successfully!", "components.IssueDetails.toastissuedeletefailed": "Something went wrong while deleting the issue.", "components.IssueDetails.toaststatusupdated": "Issue status updated successfully!", "components.IssueDetails.toaststatusupdatefailed": "Something went wrong while updating the issue status.", "components.IssueDetails.unknownissuetype": "Unknown", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Episode} other {Episodes}}", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "Type", "components.IssueList.IssueItem.opened": "Opened", "components.IssueList.IssueItem.openeduserdate": "{date} by {user}", "components.IssueList.IssueItem.problemepisode": "Affected Episode", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.IssueList.IssueItem.unknownissuetype": "Unknown", "components.IssueList.IssueItem.viewissue": "View Issue", "components.IssueList.issues": "Issues", "components.IssueList.showallissues": "Show All Issues", "components.IssueList.sortAdded": "Most Recent", "components.IssueList.sortModified": "Last Modified", "components.IssueModal.CreateIssueModal.allepisodes": "All Episodes", "components.IssueModal.CreateIssueModal.allseasons": "All Seasons", "components.IssueModal.CreateIssueModal.episode": "Episode {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.problemepisode": "Affected Episode", "components.IssueModal.CreateIssueModal.problemseason": "Affected Season", "components.IssueModal.CreateIssueModal.providedetail": "Please provide a detailed explanation of the issue you encountered.", "components.IssueModal.CreateIssueModal.reportissue": "Report an Issue", "components.IssueModal.CreateIssueModal.season": "Season {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Submit Issue", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Something went wrong while submitting the issue.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Issue report for <strong>{title}</strong> submitted successfully!", "components.IssueModal.CreateIssueModal.toastviewissue": "View Issue", "components.IssueModal.CreateIssueModal.validationMessageRequired": "You must provide a description", "components.IssueModal.CreateIssueModal.whatswrong": "What's wrong?", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "Other", "components.IssueModal.issueSubtitles": "Subtitle", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "Default ({language})", "components.LanguageSelector.originalLanguageDefault": "All Languages", "components.Layout.LanguagePicker.displaylanguage": "Display Language", "components.Layout.SearchInput.searchPlaceholder": "Search Movies & TV", "components.Layout.Sidebar.blacklist": "Blacklist", "components.Layout.Sidebar.browsemovies": "Movies", "components.Layout.Sidebar.browsetv": "Series", "components.Layout.Sidebar.dashboard": "Discover", "components.Layout.Sidebar.issues": "Issues", "components.Layout.Sidebar.requests": "Requests", "components.Layout.Sidebar.settings": "Settings", "components.Layout.Sidebar.users": "Users", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Movie Requests", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Series Requests", "components.Layout.UserDropdown.myprofile": "Profile", "components.Layout.UserDropdown.requests": "Requests", "components.Layout.UserDropdown.settings": "Settings", "components.Layout.UserDropdown.signout": "Sign Out", "components.Layout.UserWarnings.emailInvalid": "Email address is invalid.", "components.Layout.UserWarnings.emailRequired": "An email address is required.", "components.Layout.UserWarnings.passwordRequired": "A password is required.", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} behind", "components.Layout.VersionStatus.outofdate": "Out of Date", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr Stable", "components.Login.adminerror": "You must use an admin account to sign in.", "components.Login.back": "Go back", "components.Login.credentialerror": "The username or password is incorrect.", "components.Login.description": "Since this is your first time logging into {applicationName}, you are required to add a valid email address.", "components.Login.email": "Email Address", "components.Login.emailtooltip": "Address does not need to be associated with your {mediaServerName} instance.", "components.Login.enablessl": "Use SSL", "components.Login.forgotpassword": "Forgot Password?", "components.Login.hostname": "{mediaServerName} URL", "components.Login.initialsignin": "Connect", "components.Login.initialsigningin": "Connecting…", "components.Login.invalidurlerror": "Unable to connect to {mediaServerName} server.", "components.Login.loginerror": "Something went wrong while trying to sign in.", "components.Login.loginwithapp": "Login with {appName}", "components.Login.noadminerror": "No admin user found on the server.", "components.Login.orsigninwith": "Or sign in with", "components.Login.password": "Password", "components.Login.port": "Port", "components.Login.save": "Add", "components.Login.saving": "Adding…", "components.Login.servertype": "Server Type", "components.Login.signin": "Sign In", "components.Login.signingin": "Signing In…", "components.Login.signinheader": "Sign in to continue", "components.Login.signinwithjellyfin": "Use your {mediaServerName} account", "components.Login.signinwithoverseerr": "Use your {applicationTitle} account", "components.Login.signinwithplex": "Use your Plex account", "components.Login.title": "Add <PERSON>", "components.Login.urlBase": "URL Base", "components.Login.username": "Username", "components.Login.validationEmailFormat": "Invalid email", "components.Login.validationEmailRequired": "You must provide an email", "components.Login.validationPortRequired": "You must provide a valid port number", "components.Login.validationUrlBaseLeadingSlash": "URL base must have a leading slash", "components.Login.validationUrlBaseTrailingSlash": "URL base must not end in a trailing slash", "components.Login.validationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Login.validationemailformat": "Valid email required", "components.Login.validationemailrequired": "You must provide a valid email address", "components.Login.validationhostformat": "Valid URL required", "components.Login.validationhostrequired": "{mediaServerName} URL required", "components.Login.validationpasswordrequired": "You must provide a password", "components.Login.validationservertyperequired": "Please select a server type", "components.Login.validationusernamerequired": "Username required", "components.ManageSlideOver.alltime": "All Time", "components.ManageSlideOver.downloadstatus": "Downloads", "components.ManageSlideOver.manageModalAdvanced": "Advanced", "components.ManageSlideOver.manageModalClearMedia": "Clear Data", "components.ManageSlideOver.manageModalClearMediaWarning": "* This will irreversibly remove all data for this {mediaType}, including any requests. If this item exists in your {mediaServerName} library, the media information will be recreated during the next scan.", "components.ManageSlideOver.manageModalIssues": "Open Issues", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "4K Media", "components.ManageSlideOver.manageModalNoRequests": "No requests.", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* This will irreversibly remove this {mediaType} from {arr}, including all files.", "components.ManageSlideOver.manageModalRequests": "Requests", "components.ManageSlideOver.manageModalTitle": "Manage {mediaType}", "components.ManageSlideOver.mark4kavailable": "<PERSON> as Available in 4K", "components.ManageSlideOver.markallseasons4kavailable": "<PERSON> Seasons as Available in 4K", "components.ManageSlideOver.markallseasonsavailable": "<PERSON> All Seasons as Available", "components.ManageSlideOver.markavailable": "Mark as Available", "components.ManageSlideOver.movie": "movie", "components.ManageSlideOver.openarr": "Open in {arr}", "components.ManageSlideOver.openarr4k": "Open in 4K {arr}", "components.ManageSlideOver.opentautulli": "Open in Tautulli", "components.ManageSlideOver.pastdays": "Past {days, number} Days", "components.ManageSlideOver.playedby": "Played By", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {play} other {plays}}", "components.ManageSlideOver.removearr": "Remove from {arr}", "components.ManageSlideOver.removearr4k": "Remove from 4K {arr}", "components.ManageSlideOver.tvshow": "series", "components.MediaSlider.ShowMoreCard.seemore": "See More", "components.MovieDetails.MovieCast.fullcast": "Full Cast", "components.MovieDetails.MovieCrew.fullcrew": "Full Crew", "components.MovieDetails.addtowatchlist": "Add To Watchlist", "components.MovieDetails.budget": "Budget", "components.MovieDetails.cast": "Cast", "components.MovieDetails.digitalrelease": "Digital Release", "components.MovieDetails.downloadstatus": "Download Status", "components.MovieDetails.imdbuserscore": "IMDB User Score", "components.MovieDetails.managemovie": "Manage Movie", "components.MovieDetails.mark4kavailable": "<PERSON> as Available in 4K", "components.MovieDetails.markavailable": "Mark as Available", "components.MovieDetails.openradarr": "Open Movie in Radarr", "components.MovieDetails.openradarr4k": "Open Movie in 4K Radarr", "components.MovieDetails.originallanguage": "Original Language", "components.MovieDetails.originaltitle": "Original Title", "components.MovieDetails.overview": "Overview", "components.MovieDetails.overviewunavailable": "Overview unavailable.", "components.MovieDetails.physicalrelease": "Physical Release", "components.MovieDetails.play": "Play on {mediaServerName}", "components.MovieDetails.play4k": "Play 4K on {mediaServerName}", "components.MovieDetails.productioncountries": "Production {countryCount, plural, one {Country} other {Countries}}", "components.MovieDetails.recommendations": "Recommendations", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Release Date} other {Release Dates}}", "components.MovieDetails.removefromwatchlist": "Remove From Watchlist", "components.MovieDetails.reportissue": "Report an Issue", "components.MovieDetails.revenue": "Revenue", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes Audience Score", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.runtime": "{minutes} minutes", "components.MovieDetails.showless": "Show Less", "components.MovieDetails.showmore": "Show More", "components.MovieDetails.similar": "Similar Titles", "components.MovieDetails.streamingproviders": "Currently Streaming On", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studios}}", "components.MovieDetails.theatricalrelease": "Theatrical Release", "components.MovieDetails.tmdbuserscore": "TMDB User Score", "components.MovieDetails.viewfullcrew": "View Full Crew", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> Removed from watchlist successfully!", "components.MovieDetails.watchlistError": "Something went wrong. Please try again.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> added to watchlist successfully!", "components.MovieDetails.watchtrailer": "Watch Trailer", "components.NotificationTypeSelector.adminissuecommentDescription": "Get notified when other users comment on issues.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Get notified when issues are reopened by other users.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Get notified when issues are resolved by other users.", "components.NotificationTypeSelector.issuecomment": "Issue Comment", "components.NotificationTypeSelector.issuecommentDescription": "Send notifications when issues receive new comments.", "components.NotificationTypeSelector.issuecreated": "Issue Reported", "components.NotificationTypeSelector.issuecreatedDescription": "Send notifications when issues are reported.", "components.NotificationTypeSelector.issuereopened": "Issue Reopened", "components.NotificationTypeSelector.issuereopenedDescription": "Send notifications when issues are reopened.", "components.NotificationTypeSelector.issueresolved": "Issue Resolved", "components.NotificationTypeSelector.issueresolvedDescription": "Send notifications when issues are resolved.", "components.NotificationTypeSelector.mediaAutoApproved": "Request Automatically Approved", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Send notifications when users submit new media requests which are automatically approved.", "components.NotificationTypeSelector.mediaapproved": "Request Approved", "components.NotificationTypeSelector.mediaapprovedDescription": "Send notifications when media requests are manually approved.", "components.NotificationTypeSelector.mediaautorequested": "Request Automatically Submitted", "components.NotificationTypeSelector.mediaautorequestedDescription": "Get notified when new media requests are automatically submitted for items on Your Watchlist.", "components.NotificationTypeSelector.mediaavailable": "Request Available", "components.NotificationTypeSelector.mediaavailableDescription": "Send notifications when media requests become available.", "components.NotificationTypeSelector.mediadeclined": "Request Declined", "components.NotificationTypeSelector.mediadeclinedDescription": "Send notifications when media requests are declined.", "components.NotificationTypeSelector.mediafailed": "Request Processing Failed", "components.NotificationTypeSelector.mediafailedDescription": "Send notifications when media requests fail to be added to Radarr or Sonarr.", "components.NotificationTypeSelector.mediarequested": "Request Pending Approval", "components.NotificationTypeSelector.mediarequestedDescription": "Send notifications when users submit new media requests which require approval.", "components.NotificationTypeSelector.notificationTypes": "Notification Types", "components.NotificationTypeSelector.userissuecommentDescription": "Get notified when issues you reported receive new comments.", "components.NotificationTypeSelector.userissuecreatedDescription": "Get notified when other users report issues.", "components.NotificationTypeSelector.userissuereopenedDescription": "Get notified when issues you reported are reopened.", "components.NotificationTypeSelector.userissueresolvedDescription": "Get notified when issues you reported are resolved.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Get notified when other users submit new media requests which are automatically approved.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Get notified when your media requests are approved.", "components.NotificationTypeSelector.usermediaavailableDescription": "Get notified when your media requests become available.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Get notified when your media requests are declined.", "components.NotificationTypeSelector.usermediafailedDescription": "Get notified when media requests fail to be added to Radarr or Sonarr.", "components.NotificationTypeSelector.usermediarequestedDescription": "Get notified when other users submit new media requests which require approval.", "components.PermissionEdit.admin": "Admin", "components.PermissionEdit.adminDescription": "Full administrator access. Bypasses all other permission checks.", "components.PermissionEdit.advancedrequest": "Advanced Requests", "components.PermissionEdit.advancedrequestDescription": "Grant permission to modify advanced media request options.", "components.PermissionEdit.autoapprove": "Auto-Approve", "components.PermissionEdit.autoapprove4k": "Auto-Approve 4K", "components.PermissionEdit.autoapprove4kDescription": "Grant automatic approval for all 4K media requests.", "components.PermissionEdit.autoapprove4kMovies": "Auto-Approve 4K Movies", "components.PermissionEdit.autoapprove4kMoviesDescription": "Grant automatic approval for 4K movie requests.", "components.PermissionEdit.autoapprove4kSeries": "Auto-Approve 4K Series", "components.PermissionEdit.autoapprove4kSeriesDescription": "Grant automatic approval for 4K series requests.", "components.PermissionEdit.autoapproveDescription": "Grant automatic approval for all non-4K media requests.", "components.PermissionEdit.autoapproveMovies": "Auto-Approve Movies", "components.PermissionEdit.autoapproveMoviesDescription": "Grant automatic approval for non-4K movie requests.", "components.PermissionEdit.autoapproveSeries": "Auto-Approve Series", "components.PermissionEdit.autoapproveSeriesDescription": "Grant automatic approval for non-4K series requests.", "components.PermissionEdit.autorequest": "Auto-Request", "components.PermissionEdit.autorequestDescription": "Grant permission to automatically submit requests for non-4K media via Plex Watchlist.", "components.PermissionEdit.autorequestMovies": "Auto-Request Movies", "components.PermissionEdit.autorequestMoviesDescription": "Grant permission to automatically submit requests for non-4K movies via Plex Watchlist.", "components.PermissionEdit.autorequestSeries": "Auto-Request Series", "components.PermissionEdit.autorequestSeriesDescription": "Grant permission to automatically submit requests for non-4K series via Plex Watchlist.", "components.PermissionEdit.blacklistedItems": "Blacklist media.", "components.PermissionEdit.blacklistedItemsDescription": "Grant permission to blacklist media.", "components.PermissionEdit.createissues": "Report Issues", "components.PermissionEdit.createissuesDescription": "Grant permission to report media issues.", "components.PermissionEdit.manageblacklist": "Manage Blacklist", "components.PermissionEdit.manageblacklistDescription": "Grant permission to manage blacklisted media.", "components.PermissionEdit.manageissues": "Manage Issues", "components.PermissionEdit.manageissuesDescription": "Grant permission to manage media issues.", "components.PermissionEdit.managerequests": "Manage Requests", "components.PermissionEdit.managerequestsDescription": "Grant permission to manage media requests. All requests made by a user with this permission will be automatically approved.", "components.PermissionEdit.request": "Request", "components.PermissionEdit.request4k": "Request 4K", "components.PermissionEdit.request4kDescription": "Grant permission to submit requests for 4K media.", "components.PermissionEdit.request4kMovies": "Request 4K Movies", "components.PermissionEdit.request4kMoviesDescription": "Grant permission to submit requests for 4K movies.", "components.PermissionEdit.request4kTv": "Request 4K Series", "components.PermissionEdit.request4kTvDescription": "Grant permission to submit requests for 4K series.", "components.PermissionEdit.requestDescription": "Grant permission to submit requests for non-4K media.", "components.PermissionEdit.requestMovies": "Request Movies", "components.PermissionEdit.requestMoviesDescription": "Grant permission to submit requests for non-4K movies.", "components.PermissionEdit.requestTv": "Request Series", "components.PermissionEdit.requestTvDescription": "Grant permission to submit requests for non-4K series.", "components.PermissionEdit.users": "Manage Users", "components.PermissionEdit.usersDescription": "Grant permission to manage users. Users with this permission cannot modify users with or grant the Admin privilege.", "components.PermissionEdit.viewblacklistedItems": "View blacklisted media.", "components.PermissionEdit.viewblacklistedItemsDescription": "Grant permission to view blacklisted media.", "components.PermissionEdit.viewissues": "View Issues", "components.PermissionEdit.viewissuesDescription": "Grant permission to view media issues reported by other users.", "components.PermissionEdit.viewrecent": "View Recently Added", "components.PermissionEdit.viewrecentDescription": "Grant permission to view the list of recently added media.", "components.PermissionEdit.viewrequests": "View Requests", "components.PermissionEdit.viewrequestsDescription": "Grant permission to view media requests submitted by other users.", "components.PermissionEdit.viewwatchlists": "View {mediaServerName} Watchlists", "components.PermissionEdit.viewwatchlistsDescription": "Grant permission to view other users' {mediaServerName} Watchlists.", "components.PersonDetails.alsoknownas": "Also Known As: {names}", "components.PersonDetails.appearsin": "Appearances", "components.PersonDetails.ascharacter": "as {character}", "components.PersonDetails.birthdate": "Born {birthdate}", "components.PersonDetails.crewmember": "Crew", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {day} other {days}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {movie} other {movies}}", "components.QuotaSelector.seasons": "{count, plural, one {season} other {seasons}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.unlimited": "Unlimited", "components.RegionSelector.regionDefault": "All Regions", "components.RegionSelector.regionServerDefault": "Default ({region})", "components.RequestBlock.approve": "Approve Request", "components.RequestBlock.decline": "Decline Request", "components.RequestBlock.delete": "Delete Request", "components.RequestBlock.edit": "Edit Request", "components.RequestBlock.languageprofile": "Language Profile", "components.RequestBlock.lastmodifiedby": "Last Modified By", "components.RequestBlock.profilechanged": "Quality Profile", "components.RequestBlock.requestdate": "Request Date", "components.RequestBlock.requestedby": "Requested By", "components.RequestBlock.requestoverrides": "Request Overrides", "components.RequestBlock.rootfolder": "Root Folder", "components.RequestBlock.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestBlock.server": "Destination Server", "components.RequestButton.approve4krequests": "Approve {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.approverequest": "Approve Request", "components.RequestButton.approverequest4k": "Approve 4K Request", "components.RequestButton.approverequests": "Approve {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.decline4krequests": "Decline {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.declinerequest": "Decline Request", "components.RequestButton.declinerequest4k": "Decline 4K Request", "components.RequestButton.declinerequests": "Decline {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.requestmore": "Request More", "components.RequestButton.requestmore4k": "Request More in 4K", "components.RequestButton.viewrequest": "View Request", "components.RequestButton.viewrequest4k": "View 4K Request", "components.RequestCard.approverequest": "Approve Request", "components.RequestCard.cancelrequest": "Cancel Request", "components.RequestCard.declinerequest": "Decline Request", "components.RequestCard.deleterequest": "Delete Request", "components.RequestCard.editrequest": "Edit Request", "components.RequestCard.failedretry": "Something went wrong while retrying the request.", "components.RequestCard.mediaerror": "{mediaType} Not Found", "components.RequestCard.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestCard.unknowntitle": "Unknown Title", "components.RequestList.RequestItem.cancelRequest": "Cancel Request", "components.RequestList.RequestItem.deleterequest": "Delete Request", "components.RequestList.RequestItem.editrequest": "Edit Request", "components.RequestList.RequestItem.failedretry": "Something went wrong while retrying the request.", "components.RequestList.RequestItem.mediaerror": "{mediaType} Not Found", "components.RequestList.RequestItem.modified": "Modified", "components.RequestList.RequestItem.modifieduserdate": "{date} by {user}", "components.RequestList.RequestItem.profileName": "Profile", "components.RequestList.RequestItem.removearr": "Remove from {arr}", "components.RequestList.RequestItem.requested": "Requested", "components.RequestList.RequestItem.requesteddate": "Requested", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.unknowntitle": "Unknown Title", "components.RequestList.requests": "Requests", "components.RequestList.showallrequests": "Show All Requests", "components.RequestList.sortAdded": "Most Recent", "components.RequestList.sortDirection": "Toggle Sort Direction", "components.RequestList.sortModified": "Last Modified", "components.RequestModal.AdvancedRequester.advancedoptions": "Advanced", "components.RequestModal.AdvancedRequester.animenote": "* This series is an anime.", "components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.destinationserver": "Destination Server", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "Language Profile", "components.RequestModal.AdvancedRequester.notagoptions": "No tags.", "components.RequestModal.AdvancedRequester.qualityprofile": "Quality Profile", "components.RequestModal.AdvancedRequester.requestas": "Request As", "components.RequestModal.AdvancedRequester.rootfolder": "Root Folder", "components.RequestModal.AdvancedRequester.selecttags": "Select tags", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.QuotaDisplay.allowedRequests": "You are allowed to request <strong>{limit}</strong> {type} every <strong>{days}</strong> days.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "This user is allowed to request <strong>{limit}</strong> {type} every <strong>{days}</strong> days.", "components.RequestModal.QuotaDisplay.movie": "movie", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {movie} other {movies}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Not enough season requests remaining", "components.RequestModal.QuotaDisplay.quotaLink": "You can view a summary of your request limits on your <ProfileLink>profile page</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "You can view a summary of this user's request limits on their <ProfileLink>profile page</ProfileLink>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {request} other {requests}} remaining", "components.RequestModal.QuotaDisplay.requiredquota": "You need to have at least <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} remaining in order to submit a request for this series.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "This user needs to have at least <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} remaining in order to submit a request for this series.", "components.RequestModal.QuotaDisplay.season": "season", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {season} other {seasons}}", "components.RequestModal.SearchByNameModal.nomatches": "We were unable to find a match for this series.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "We were unable to automatically match this series. Please select the correct match below.", "components.RequestModal.alreadyrequested": "Already Requested", "components.RequestModal.approve": "Approve Request", "components.RequestModal.autoapproval": "Automatic Approval", "components.RequestModal.cancel": "Cancel Request", "components.RequestModal.edit": "Edit Request", "components.RequestModal.errorediting": "Something went wrong while editing the request.", "components.RequestModal.numberofepisodes": "# of Episodes", "components.RequestModal.pending4krequest": "Pending 4K Request", "components.RequestModal.pendingapproval": "Your request is pending approval.", "components.RequestModal.pendingrequest": "Pending Request", "components.RequestModal.requestApproved": "Request for <strong>{title}</strong> approved!", "components.RequestModal.requestCancel": "Request for <strong>{title}</strong> canceled.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> requested successfully!", "components.RequestModal.requestadmin": "This request will be approved automatically.", "components.RequestModal.requestcancelled": "Request for <strong>{title}</strong> canceled.", "components.RequestModal.requestcollection4ktitle": "Request Collection in 4K", "components.RequestModal.requestcollectiontitle": "Request Collection", "components.RequestModal.requestedited": "Request for <strong>{title}</strong> edited successfully!", "components.RequestModal.requesterror": "Something went wrong while submitting the request.", "components.RequestModal.requestfrom": "{username}'s request is pending approval.", "components.RequestModal.requestmovie4ktitle": "Request Movie in 4K", "components.RequestModal.requestmovies": "Request {count} {count, plural, one {Movie} other {Movies}}", "components.RequestModal.requestmovies4k": "Request {count} {count, plural, one {Movie} other {Movies}} in 4K", "components.RequestModal.requestmovietitle": "Request Movie", "components.RequestModal.requestseasons": "Request {seasonCount} {seasonCount, plural, one {Season} other {Seasons}}", "components.RequestModal.requestseasons4k": "Request {seasonCount} {seasonCount, plural, one {Season} other {Seasons}} in 4K", "components.RequestModal.requestseries4ktitle": "Request Series in 4K", "components.RequestModal.requestseriestitle": "Request Series", "components.RequestModal.season": "Season", "components.RequestModal.seasonnumber": "Season {number}", "components.RequestModal.selectmovies": "Select Movie(s)", "components.RequestModal.selectseason": "Select Season(s)", "components.ResetPassword.confirmpassword": "Confirm Password", "components.ResetPassword.email": "Email Address", "components.ResetPassword.emailresetlink": "Email Recovery Link", "components.ResetPassword.gobacklogin": "Return to Sign-In Page", "components.ResetPassword.password": "Password", "components.ResetPassword.passwordreset": "Password Reset", "components.ResetPassword.requestresetlinksuccessmessage": "A password reset link will be sent to the provided email address if it is associated with a valid user.", "components.ResetPassword.resetpassword": "Reset your password", "components.ResetPassword.resetpasswordsuccessmessage": "Password reset successfully!", "components.ResetPassword.validationemailrequired": "You must provide a valid email address", "components.ResetPassword.validationpasswordmatch": "Passwords must match", "components.ResetPassword.validationpasswordminchars": "Password is too short; should be a minimum of 8 characters", "components.ResetPassword.validationpasswordrequired": "You must provide a password", "components.Search.search": "Search", "components.Search.searchresults": "Search Results", "components.Selector.CertificationSelector.errorLoading": "Failed to load certifications", "components.Selector.CertificationSelector.maxRating": "Maximum rating", "components.Selector.CertificationSelector.minRating": "Minimum rating", "components.Selector.CertificationSelector.noOptions": "No options available", "components.Selector.CertificationSelector.selectCertification": "Select a certification", "components.Selector.CertificationSelector.selectCountry": "Select a country", "components.Selector.CertificationSelector.starttyping": "Starting typing to search.", "components.Selector.canceled": "Canceled", "components.Selector.ended": "Ended", "components.Selector.inProduction": "In Production", "components.Selector.nooptions": "No results.", "components.Selector.pilot": "Pilot", "components.Selector.planned": "Planned", "components.Selector.returningSeries": "Returning Series", "components.Selector.searchGenres": "Select genres…", "components.Selector.searchKeywords": "Search keywords…", "components.Selector.searchStatus": "Select status...", "components.Selector.searchStudios": "Search studios…", "components.Selector.searchUsers": "Select users…", "components.Selector.showless": "Show Less", "components.Selector.showmore": "Show More", "components.Selector.starttyping": "Starting typing to search.", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify notification settings failed to save.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify notification settings saved successfully!", "components.Settings.Notifications.NotificationsGotify.priority": "Priority", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify test notification failed to send.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Sending Gotify test notification…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify test notification sent!", "components.Settings.Notifications.NotificationsGotify.token": "Application Token", "components.Settings.Notifications.NotificationsGotify.url": "Server URL", "components.Settings.Notifications.NotificationsGotify.validationPriorityRequired": "You must set a priority number", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "You must provide an application token", "components.Settings.Notifications.NotificationsGotify.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "You must provide a valid URL", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.Notifications.NotificationsNtfy.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsNtfy.ntfysettingsfailed": "Ntfy notification settings failed to save.", "components.Settings.Notifications.NotificationsNtfy.ntfysettingssaved": "Ntfy notification settings saved successfully!", "components.Settings.Notifications.NotificationsNtfy.password": "Password", "components.Settings.Notifications.NotificationsNtfy.toastNtfyTestFailed": "Ntfy test notification failed to send.", "components.Settings.Notifications.NotificationsNtfy.toastNtfyTestSending": "Sending ntfy test notification…", "components.Settings.Notifications.NotificationsNtfy.toastNtfyTestSuccess": "Ntfy test notification sent!", "components.Settings.Notifications.NotificationsNtfy.token": "Token", "components.Settings.Notifications.NotificationsNtfy.tokenAuth": "Token authentication", "components.Settings.Notifications.NotificationsNtfy.topic": "Topic", "components.Settings.Notifications.NotificationsNtfy.url": "Server root URL", "components.Settings.Notifications.NotificationsNtfy.username": "Username", "components.Settings.Notifications.NotificationsNtfy.usernamePasswordAuth": "Username + Password authentication", "components.Settings.Notifications.NotificationsNtfy.validationNtfyTopic": "You must provide a topic", "components.Settings.Notifications.NotificationsNtfy.validationNtfyUrl": "You must provide a valid URL", "components.Settings.Notifications.NotificationsNtfy.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Access Token", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Create a token from your <PushbulletSettingsLink>Account Settings</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Enable Agent", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Channel Tag", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet notification settings failed to save.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet notification settings saved successfully!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet test notification failed to send.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Sending Pushbullet test notification…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet test notification sent!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "You must provide an access token", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsPushover.accessToken": "Application API Token", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Register an application</ApplicationRegistrationLink> for use with Jellyseerr", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover notification settings failed to save.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover notification settings saved successfully!", "components.Settings.Notifications.NotificationsPushover.sound": "Notification Sound", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover test notification failed to send.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Sending Pushover test notification…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover test notification sent!", "components.Settings.Notifications.NotificationsPushover.userToken": "User or Group Key", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Your 30-character <UsersGroupsLink>user or group identifier</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "You must provide a valid application token", "components.Settings.Notifications.NotificationsPushover.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "You must provide a valid user or group key", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack notification settings failed to save.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack notification settings saved successfully!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack test notification failed to send.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Sending Slack test notification…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack test notification sent!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "You must provide a valid URL", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Create an <WebhookLink>Incoming Webhook</WebhookLink> integration", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsWebhook.authheader": "Authorization Header", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Reset to De<PERSON>ult", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload reset successfully!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Template Variable Help", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Webhook test notification failed to send.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Sending webhook test notification…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook test notification sent!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "You must provide a valid JSON payload", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "You must provide a valid URL", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Webhook notification settings failed to save.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook notification settings saved successfully!", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Enable Agent", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "In order to receive web push notifications, <PERSON><PERSON><PERSON><PERSON> must be served over HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web push test notification failed to send.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Sending web push test notification…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push test notification sent!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web push notification settings failed to save.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web push notification settings saved successfully!", "components.Settings.Notifications.agentenabled": "Enable Agent", "components.Settings.Notifications.allowselfsigned": "Allow Self-Signed Certificates", "components.Settings.Notifications.authPass": "SMTP Password", "components.Settings.Notifications.authUser": "SMTP Username", "components.Settings.Notifications.botAPI": "Bot Authorization Token", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Create a bot</CreateBotLink> for use with Jellyseerr", "components.Settings.Notifications.botAvatarUrl": "Bot Avatar URL", "components.Settings.Notifications.botUsername": "<PERSON><PERSON>", "components.Settings.Notifications.botUsernameTip": "Allow users to also start a chat with your bot and configure their own notifications", "components.Settings.Notifications.chatId": "Chat ID", "components.Settings.Notifications.chatIdTip": "Start a chat with your bot, add <GetIdBotLink>@get_id_bot</GetIdBotLink>, and issue the <code>/my_id</code> command", "components.Settings.Notifications.discordsettingsfailed": "Discord notification settings failed to save.", "components.Settings.Notifications.discordsettingssaved": "Discord notification settings saved successfully!", "components.Settings.Notifications.emailsender": "Sender Address", "components.Settings.Notifications.emailsettingsfailed": "Email notification settings failed to save.", "components.Settings.Notifications.emailsettingssaved": "Email notification settings saved successfully!", "components.Settings.Notifications.enableMentions": "Enable Mentions", "components.Settings.Notifications.encryption": "Encryption Method", "components.Settings.Notifications.encryptionDefault": "Use STARTTLS if available", "components.Settings.Notifications.encryptionImplicitTls": "Use Implicit TLS", "components.Settings.Notifications.encryptionNone": "None", "components.Settings.Notifications.encryptionOpportunisticTls": "Always use STARTTLS", "components.Settings.Notifications.encryptionTip": "In most cases, Implicit TLS uses port 465 and STARTTLS uses port 587", "components.Settings.Notifications.messageThreadId": "Thread/Topic ID", "components.Settings.Notifications.messageThreadIdTip": "If your group-chat has topics enabled, you can specify a thread/topic's ID here", "components.Settings.Notifications.pgpPassword": "PGP Password", "components.Settings.Notifications.pgpPasswordTip": "Sign encrypted email messages using <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "PGP Private Key", "components.Settings.Notifications.pgpPrivateKeyTip": "Sign encrypted email messages using <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "Send Silently", "components.Settings.Notifications.sendSilentlyTip": "Send notifications with no sound", "components.Settings.Notifications.senderName": "Sender Name", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.Settings.Notifications.smtpPort": "SMTP Port", "components.Settings.Notifications.telegramsettingsfailed": "Telegram notification settings failed to save.", "components.Settings.Notifications.telegramsettingssaved": "Telegram notification settings saved successfully!", "components.Settings.Notifications.toastDiscordTestFailed": "Discord test notification failed to send.", "components.Settings.Notifications.toastDiscordTestSending": "Sending Discord test notification…", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord test notification sent!", "components.Settings.Notifications.toastEmailTestFailed": "Email test notification failed to send.", "components.Settings.Notifications.toastEmailTestSending": "Sending email test notification…", "components.Settings.Notifications.toastEmailTestSuccess": "Email test notification sent!", "components.Settings.Notifications.toastTelegramTestFailed": "Telegram test notification failed to send.", "components.Settings.Notifications.toastTelegramTestSending": "Sending Telegram test notification…", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram test notification sent!", "components.Settings.Notifications.userEmailRequired": "Require user email", "components.Settings.Notifications.validationBotAPIRequired": "You must provide a bot authorization token", "components.Settings.Notifications.validationChatIdRequired": "You must provide a valid chat ID", "components.Settings.Notifications.validationEmail": "You must provide a valid email address", "components.Settings.Notifications.validationMessageThreadId": "The thread/topic ID must be a positive whole number", "components.Settings.Notifications.validationPgpPassword": "You must provide a PGP password", "components.Settings.Notifications.validationPgpPrivateKey": "You must provide a valid PGP private key", "components.Settings.Notifications.validationSmtpHostRequired": "You must provide a valid hostname or IP address", "components.Settings.Notifications.validationSmtpPortRequired": "You must provide a valid port number", "components.Settings.Notifications.validationTypes": "You must select at least one notification type", "components.Settings.Notifications.validationUrl": "You must provide a valid URL", "components.Settings.Notifications.validationWebhookRoleId": "You must provide a valid Discord Role ID", "components.Settings.Notifications.webhookRoleId": "Notification Role ID", "components.Settings.Notifications.webhookRoleIdTip": "The role ID to mention in the webhook message. Leave empty to disable mentions", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.Settings.Notifications.webhookUrlTip": "Create a <DiscordWebhookLink>webhook integration</DiscordWebhookLink> in your server", "components.Settings.OverrideRuleModal.conditions": "Conditions", "components.Settings.OverrideRuleModal.conditionsDescription": "Specifies conditions before applying parameter changes. Each field must be validated for the rules to be applied (AND operation). A field is considered verified if any of its properties match (OR operation).", "components.Settings.OverrideRuleModal.create": "Create rule", "components.Settings.OverrideRuleModal.createrule": "New Override Rule", "components.Settings.OverrideRuleModal.editrule": "Edit Override Rule", "components.Settings.OverrideRuleModal.genres": "Genres", "components.Settings.OverrideRuleModal.keywords": "Keywords", "components.Settings.OverrideRuleModal.languages": "Languages", "components.Settings.OverrideRuleModal.notagoptions": "No tags.", "components.Settings.OverrideRuleModal.qualityprofile": "Quality Profile", "components.Settings.OverrideRuleModal.rootfolder": "Root Folder", "components.Settings.OverrideRuleModal.ruleCreated": "Override rule created successfully!", "components.Settings.OverrideRuleModal.ruleUpdated": "Override rule updated successfully!", "components.Settings.OverrideRuleModal.selectQualityProfile": "Select quality profile", "components.Settings.OverrideRuleModal.selectRootFolder": "Select root folder", "components.Settings.OverrideRuleModal.selectService": "Select service", "components.Settings.OverrideRuleModal.selecttags": "Select tags", "components.Settings.OverrideRuleModal.service": "Service", "components.Settings.OverrideRuleModal.serviceDescription": "Apply this rule to the selected service.", "components.Settings.OverrideRuleModal.settings": "Settings", "components.Settings.OverrideRuleModal.settingsDescription": "Specifies which settings will be changed when the above conditions are met.", "components.Settings.OverrideRuleModal.tags": "Tags", "components.Settings.OverrideRuleModal.users": "Users", "components.Settings.OverrideRuleTile.conditions": "Conditions", "components.Settings.OverrideRuleTile.genre": "Genre", "components.Settings.OverrideRuleTile.keywords": "Keywords", "components.Settings.OverrideRuleTile.language": "Language", "components.Settings.OverrideRuleTile.qualityprofile": "Quality Profile", "components.Settings.OverrideRuleTile.rootfolder": "Root Folder", "components.Settings.OverrideRuleTile.settings": "Settings", "components.Settings.OverrideRuleTile.tags": "Tags", "components.Settings.OverrideRuleTile.users": "Users", "components.Settings.RadarrModal.add": "Add Server", "components.Settings.RadarrModal.announced": "Announced", "components.Settings.RadarrModal.apiKey": "API Key", "components.Settings.RadarrModal.baseUrl": "URL Base", "components.Settings.RadarrModal.create4kradarr": "Add New 4K Radarr Server", "components.Settings.RadarrModal.createradarr": "Add New Radarr Server", "components.Settings.RadarrModal.default4kserver": "Default 4K Server", "components.Settings.RadarrModal.defaultserver": "Default Server", "components.Settings.RadarrModal.edit4kradarr": "Edit 4K Radarr Server", "components.Settings.RadarrModal.editradarr": "Edit Radarr Server", "components.Settings.RadarrModal.enableSearch": "Enable Automatic Search", "components.Settings.RadarrModal.externalUrl": "External URL", "components.Settings.RadarrModal.hostname": "Hostname or IP Address", "components.Settings.RadarrModal.inCinemas": "In Cinemas", "components.Settings.RadarrModal.loadingTags": "Loading tags…", "components.Settings.RadarrModal.loadingprofiles": "Loading quality profiles…", "components.Settings.RadarrModal.loadingrootfolders": "Loading root folders…", "components.Settings.RadarrModal.minimumAvailability": "Minimum Availability", "components.Settings.RadarrModal.notagoptions": "No tags.", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.qualityprofile": "Quality Profile", "components.Settings.RadarrModal.released": "Released", "components.Settings.RadarrModal.rootfolder": "Root Folder", "components.Settings.RadarrModal.selectMinimumAvailability": "Select minimum availability", "components.Settings.RadarrModal.selectQualityProfile": "Select quality profile", "components.Settings.RadarrModal.selectRootFolder": "Select root folder", "components.Settings.RadarrModal.selecttags": "Select tags", "components.Settings.RadarrModal.server4k": "4K Server", "components.Settings.RadarrModal.servername": "Server Name", "components.Settings.RadarrModal.ssl": "Use SSL", "components.Settings.RadarrModal.syncEnabled": "Enable <PERSON>", "components.Settings.RadarrModal.tagRequests": "Tag Requests", "components.Settings.RadarrModal.tagRequestsInfo": "Automatically add an additional tag with the requester's user ID & display name", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.RadarrModal.testFirstQualityProfiles": "Test connection to load quality profiles", "components.Settings.RadarrModal.testFirstRootFolders": "Test connection to load root folders", "components.Settings.RadarrModal.testFirstTags": "Test connection to load tags", "components.Settings.RadarrModal.toastRadarrTestFailure": "Failed to connect to Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr connection established successfully!", "components.Settings.RadarrModal.validationApiKeyRequired": "You must provide an API key", "components.Settings.RadarrModal.validationApplicationUrl": "You must provide a valid URL", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL base must have a leading slash", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL base must not end in a trailing slash", "components.Settings.RadarrModal.validationHostnameRequired": "You must provide a valid hostname or IP address", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "You must select a minimum availability", "components.Settings.RadarrModal.validationNameRequired": "You must provide a server name", "components.Settings.RadarrModal.validationPortRequired": "You must provide a valid port number", "components.Settings.RadarrModal.validationProfileRequired": "You must select a quality profile", "components.Settings.RadarrModal.validationRootFolderRequired": "You must select a root folder", "components.Settings.SettingsAbout.Releases.currentversion": "Current", "components.Settings.SettingsAbout.Releases.latestversion": "Latest", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Release data is currently unavailable.", "components.Settings.SettingsAbout.Releases.releases": "Releases", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Changelog", "components.Settings.SettingsAbout.Releases.viewchangelog": "View Changelog", "components.Settings.SettingsAbout.Releases.viewongithub": "View on GitHub", "components.Settings.SettingsAbout.about": "About", "components.Settings.SettingsAbout.appDataPath": "Data Directory", "components.Settings.SettingsAbout.betawarning": "This is BETA software. Features may be broken and/or unstable. Please report any issues on GitHub!", "components.Settings.SettingsAbout.documentation": "Documentation", "components.Settings.SettingsAbout.gettingsupport": "Getting Support", "components.Settings.SettingsAbout.githubdiscussions": "GitHub Discussions", "components.Settings.SettingsAbout.helppaycoffee": "Help Pay for Coffee", "components.Settings.SettingsAbout.outofdate": "Out of Date", "components.Settings.SettingsAbout.overseerrinformation": "About <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Preferred", "components.Settings.SettingsAbout.runningDevelop": "You are running the <code>develop</code> branch of Jellyseerr, which is only recommended for those contributing to development or assisting with bleeding-edge testing.", "components.Settings.SettingsAbout.supportjellyseerr": "Support <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "Support Overseerr", "components.Settings.SettingsAbout.timezone": "Time Zone", "components.Settings.SettingsAbout.totalmedia": "Total Media", "components.Settings.SettingsAbout.totalrequests": "Total Requests", "components.Settings.SettingsAbout.uptodate": "Up to Date", "components.Settings.SettingsAbout.version": "Version", "components.Settings.SettingsJobsCache.availability-sync": "Media Availability Sync", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr caches requests to external API endpoints to optimize performance and avoid making unnecessary API calls.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} cache flushed.", "components.Settings.SettingsJobsCache.cachehits": "Hits", "components.Settings.SettingsJobsCache.cachekeys": "Total Keys", "components.Settings.SettingsJobsCache.cacheksize": "Key Size", "components.Settings.SettingsJobsCache.cachemisses": "Misses", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Value Size", "components.Settings.SettingsJobsCache.canceljob": "Cancel Job", "components.Settings.SettingsJobsCache.command": "Command", "components.Settings.SettingsJobsCache.download-sync": "Download Sync", "components.Settings.SettingsJobsCache.download-sync-reset": "Download Sync Reset", "components.Settings.SettingsJobsCache.editJobSchedule": "Modify Job", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Current Frequency", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "New Frequency", "components.Settings.SettingsJobsCache.editJobScheduleSelectorDays": "Every {jobScheduleDays, plural, one {day} other {{jobScheduleDays} days}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Every {jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Every {jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Every {jobScheduleSeconds, plural, one {second} other {{jobScheduleSeconds} seconds}}", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Image Cache Cleanup", "components.Settings.SettingsJobsCache.imagecache": "Image Cache", "components.Settings.SettingsJobsCache.imagecacheDescription": "When enabled in settings, <PERSON><PERSON><PERSON><PERSON> will proxy and cache images from pre-configured external sources. Cached images are saved into your config folder. You can find the files in <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.imagecachecount": "Images Cached", "components.Settings.SettingsJobsCache.imagecachesize": "Total Cache <PERSON>", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Je<PERSON>fin Full Library Scan", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "<PERSON><PERSON><PERSON> Recently Added <PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Something went wrong while saving the job.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Job edited successfully!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} canceled.", "components.Settings.SettingsJobsCache.jobname": "Job Name", "components.Settings.SettingsJobsCache.jobs": "Jobs", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON>seerr performs certain maintenance tasks as regularly-scheduled jobs, but they can also be manually triggered below. Manually running a job will not alter its schedule.", "components.Settings.SettingsJobsCache.jobsandcache": "Jobs & Cache", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} started.", "components.Settings.SettingsJobsCache.jobtype": "Type", "components.Settings.SettingsJobsCache.nextexecution": "Next Execution", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex Full Library Scan", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex Recently Added <PERSON>an", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex Refresh Token", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex Watchlist Sync", "components.Settings.SettingsJobsCache.process": "Process", "components.Settings.SettingsJobsCache.process-blacklisted-tags": "Process Blacklisted Tags", "components.Settings.SettingsJobsCache.radarr-scan": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.runnow": "Run Now", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.unknownJob": "Unknown Job", "components.Settings.SettingsJobsCache.usersavatars": "Users' Avatars", "components.Settings.SettingsLogs.copiedLogMessage": "Copied log message to clipboard.", "components.Settings.SettingsLogs.copyToClipboard": "Copy to Clipboard", "components.Settings.SettingsLogs.extraData": "Additional Data", "components.Settings.SettingsLogs.filterDebug": "Debug", "components.Settings.SettingsLogs.filterError": "Error", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterWarn": "Warning", "components.Settings.SettingsLogs.label": "Label", "components.Settings.SettingsLogs.level": "Severity", "components.Settings.SettingsLogs.logDetails": "Log Details", "components.Settings.SettingsLogs.logs": "Logs", "components.Settings.SettingsLogs.logsDescription": "You can also view these logs directly via <code>stdout</code>, or in <code>{appDataPath}/logs/jellyseerr.log</code>.", "components.Settings.SettingsLogs.message": "Message", "components.Settings.SettingsLogs.pauseLogs": "Pause", "components.Settings.SettingsLogs.resumeLogs": "Resume", "components.Settings.SettingsLogs.showall": "Show All Logs", "components.Settings.SettingsLogs.time": "Timestamp", "components.Settings.SettingsLogs.viewdetails": "View Details", "components.Settings.SettingsMain.apikey": "API Key", "components.Settings.SettingsMain.apikeyCopied": "Copied API key to clipboard.", "components.Settings.SettingsMain.applicationTitle": "Application Title", "components.Settings.SettingsMain.applicationurl": "Application URL", "components.Settings.SettingsMain.blacklistedTags": "Blacklist Content with Tags", "components.Settings.SettingsMain.blacklistedTagsLimit": "Limit Content Blacklisted per Tag", "components.Settings.SettingsMain.blacklistedTagsLimitTip": "The \"Process Blacklisted Tags\" job will blacklist this many pages into each sort. Larger numbers will create a more accurate blacklist, but use more space.", "components.Settings.SettingsMain.blacklistedTagsTip": "Automatically add content with tags to the blacklist using the \"Process Blacklisted Tags\" job", "components.Settings.SettingsMain.cacheImages": "Enable Image Caching", "components.Settings.SettingsMain.cacheImagesTip": "Cache externally sourced images (requires a significant amount of disk space)", "components.Settings.SettingsMain.discoverRegion": "Discover Region", "components.Settings.SettingsMain.discoverRegionTip": "Filter content by regional availability", "components.Settings.SettingsMain.enableSpecialEpisodes": "Allow Special Episodes Requests", "components.Settings.SettingsMain.general": "General", "components.Settings.SettingsMain.generalsettings": "General Settings", "components.Settings.SettingsMain.generalsettingsDescription": "Configure global and default settings for Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "Hide Available Media", "components.Settings.SettingsMain.hideAvailableTip": "Hide available media from the discover pages but not search results", "components.Settings.SettingsMain.hideBlacklisted": "Hide Blacklisted Items", "components.Settings.SettingsMain.hideBlacklistedTip": "Hide blacklisted items from discover pages for all users with the \"Manage Blacklist\" permission", "components.Settings.SettingsMain.locale": "Display Language", "components.Settings.SettingsMain.originallanguage": "Discover Language", "components.Settings.SettingsMain.originallanguageTip": "Filter content by original language", "components.Settings.SettingsMain.partialRequestsEnabled": "Allow Partial Series Requests", "components.Settings.SettingsMain.streamingRegion": "Streaming Region", "components.Settings.SettingsMain.streamingRegionTip": "Show streaming sites by regional availability", "components.Settings.SettingsMain.toastApiKeyFailure": "Something went wrong while generating a new API key.", "components.Settings.SettingsMain.toastApiKeySuccess": "New API key generated successfully!", "components.Settings.SettingsMain.toastSettingsFailure": "Something went wrong while saving settings.", "components.Settings.SettingsMain.toastSettingsSuccess": "Setting<PERSON> saved successfully!", "components.Settings.SettingsMain.validationApplicationTitle": "You must provide an application title", "components.Settings.SettingsMain.validationApplicationUrl": "You must provide a valid URL", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.SettingsMain.validationUrl": "You must provide a valid URL", "components.Settings.SettingsMain.validationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.SettingsMain.youtubeUrl": "YouTube URL", "components.Settings.SettingsMain.youtubeUrlTip": "Base URL for YouTube videos if a self-hosted YouTube instance is used.", "components.Settings.SettingsNetwork.csrfProtection": "Enable CSRF Protection", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "Do NOT enable this setting unless you understand what you are doing!", "components.Settings.SettingsNetwork.csrfProtectionTip": "Set external API access to read-only (requires HTTPS)", "components.Settings.SettingsNetwork.docs": "documentation", "components.Settings.SettingsNetwork.forceIpv4First": "Force IPv4 Resolution First", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "Force Jellyseerr to resolve IPv4 addresses first instead of IPv6", "components.Settings.SettingsNetwork.network": "Network", "components.Settings.SettingsNetwork.networkDisclaimer": "Network parameters from your container/system should be used instead of these settings. See the {docs} for more information.", "components.Settings.SettingsNetwork.networksettings": "Network Settings", "components.Settings.SettingsNetwork.networksettingsDescription": "Configure network settings for your Jellyseerr instance.", "components.Settings.SettingsNetwork.proxyBypassFilter": "Proxy Ignored Addresses", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Use ',' as a separator, and '*.' as a wildcard for subdomains", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Bypass Proxy for Local Addresses", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) Proxy", "components.Settings.SettingsNetwork.proxyHostname": "Proxy Hostname", "components.Settings.SettingsNetwork.proxyPassword": "Proxy Password", "components.Settings.SettingsNetwork.proxyPort": "Proxy Port", "components.Settings.SettingsNetwork.proxySsl": "Use SSL For Proxy", "components.Settings.SettingsNetwork.proxyUser": "Proxy Username", "components.Settings.SettingsNetwork.toastSettingsFailure": "Something went wrong while saving settings.", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Setting<PERSON> saved successfully!", "components.Settings.SettingsNetwork.trustProxy": "Enable Proxy Support", "components.Settings.SettingsNetwork.trustProxyTip": "Allow <PERSON><PERSON><PERSON><PERSON> to correctly register client IP addresses behind a proxy", "components.Settings.SettingsNetwork.validationProxyPort": "You must provide a valid port", "components.Settings.SettingsUsers.atLeastOneAuth": "At least one authentication method must be selected.", "components.Settings.SettingsUsers.defaultPermissions": "Default Permissions", "components.Settings.SettingsUsers.defaultPermissionsTip": "Initial permissions assigned to new users", "components.Settings.SettingsUsers.localLogin": "Enable Local Sign-In", "components.Settings.SettingsUsers.localLoginTip": "Allow users to sign in using their email address and password", "components.Settings.SettingsUsers.loginMethods": "Login Methods", "components.Settings.SettingsUsers.loginMethodsTip": "Configure login methods for users.", "components.Settings.SettingsUsers.mediaServerLogin": "Enable {mediaServerName} Sign-In", "components.Settings.SettingsUsers.mediaServerLoginTip": "Allow users to sign in using their {mediaServerName} account", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Global Movie Request Limit", "components.Settings.SettingsUsers.newPlexLogin": "Enable New {mediaServerName} Sign-In", "components.Settings.SettingsUsers.newPlexLoginTip": "Allow {mediaServerName} users to sign in without first being imported", "components.Settings.SettingsUsers.toastSettingsFailure": "Something went wrong while saving settings.", "components.Settings.SettingsUsers.toastSettingsSuccess": "User settings saved successfully!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Global Series Request Limit", "components.Settings.SettingsUsers.userSettings": "User Settings", "components.Settings.SettingsUsers.userSettingsDescription": "Configure global and default user settings.", "components.Settings.SettingsUsers.users": "Users", "components.Settings.SonarrModal.add": "Add Server", "components.Settings.SonarrModal.animeSeriesType": "Anime Series Type", "components.Settings.SonarrModal.animeTags": "Anime Tags", "components.Settings.SonarrModal.animelanguageprofile": "Anime Language Profile", "components.Settings.SonarrModal.animequalityprofile": "Anime Quality Profile", "components.Settings.SonarrModal.animerootfolder": "Anime Root Folder", "components.Settings.SonarrModal.apiKey": "API Key", "components.Settings.SonarrModal.baseUrl": "URL Base", "components.Settings.SonarrModal.create4ksonarr": "Add New 4K Sonarr Server", "components.Settings.SonarrModal.createsonarr": "Add New Sonarr Server", "components.Settings.SonarrModal.default4kserver": "Default 4K Server", "components.Settings.SonarrModal.defaultserver": "Default Server", "components.Settings.SonarrModal.edit4ksonarr": "Edit 4K Sonarr Server", "components.Settings.SonarrModal.editsonarr": "<PERSON> <PERSON>arr Server", "components.Settings.SonarrModal.enableSearch": "Enable Automatic Search", "components.Settings.SonarrModal.externalUrl": "External URL", "components.Settings.SonarrModal.hostname": "Hostname or IP Address", "components.Settings.SonarrModal.languageprofile": "Language Profile", "components.Settings.SonarrModal.loadingTags": "Loading tags…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Loading language profiles…", "components.Settings.SonarrModal.loadingprofiles": "Loading quality profiles…", "components.Settings.SonarrModal.loadingrootfolders": "Loading root folders…", "components.Settings.SonarrModal.notagoptions": "No tags.", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.qualityprofile": "Quality Profile", "components.Settings.SonarrModal.rootfolder": "Root Folder", "components.Settings.SonarrModal.seasonfolders": "Season Folders", "components.Settings.SonarrModal.selectLanguageProfile": "Select language profile", "components.Settings.SonarrModal.selectQualityProfile": "Select quality profile", "components.Settings.SonarrModal.selectRootFolder": "Select root folder", "components.Settings.SonarrModal.selecttags": "Select tags", "components.Settings.SonarrModal.seriesType": "Series Type", "components.Settings.SonarrModal.server4k": "4K Server", "components.Settings.SonarrModal.servername": "Server Name", "components.Settings.SonarrModal.ssl": "Use SSL", "components.Settings.SonarrModal.syncEnabled": "Enable <PERSON>", "components.Settings.SonarrModal.tagRequests": "Tag Requests", "components.Settings.SonarrModal.tagRequestsInfo": "Automatically add an additional tag with the requester's user ID & display name", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Test connection to load language profiles", "components.Settings.SonarrModal.testFirstQualityProfiles": "Test connection to load quality profiles", "components.Settings.SonarrModal.testFirstRootFolders": "Test connection to load root folders", "components.Settings.SonarrModal.testFirstTags": "Test connection to load tags", "components.Settings.SonarrModal.toastSonarrTestFailure": "Failed to connect to Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr connection established successfully!", "components.Settings.SonarrModal.validationApiKeyRequired": "You must provide an API key", "components.Settings.SonarrModal.validationApplicationUrl": "You must provide a valid URL", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Base URL must have a leading slash", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Base URL must not end in a trailing slash", "components.Settings.SonarrModal.validationHostnameRequired": "You must provide a valid hostname or IP address", "components.Settings.SonarrModal.validationLanguageProfileRequired": "You must select a language profile", "components.Settings.SonarrModal.validationNameRequired": "You must provide a server name", "components.Settings.SonarrModal.validationPortRequired": "You must provide a valid port number", "components.Settings.SonarrModal.validationProfileRequired": "You must select a quality profile", "components.Settings.SonarrModal.validationRootFolderRequired": "You must select a root folder", "components.Settings.activeProfile": "Active Profile", "components.Settings.addradarr": "Add Radarr Server", "components.Settings.address": "Address", "components.Settings.addrule": "New Override Rule", "components.Settings.addsonarr": "Add Sonarr Server", "components.Settings.advancedTooltip": "Incorrectly configuring this setting may result in broken functionality", "components.Settings.apiKey": "API key", "components.Settings.blacklistedTagImportInstructions": "Paste blacklist tag configuration below.", "components.Settings.blacklistedTagImportTitle": "Import Blacklisted Tag Configuration", "components.Settings.blacklistedTagsText": "Blacklisted Tags", "components.Settings.cancelscan": "<PERSON>cel Scan", "components.Settings.clearBlacklistedTagsConfirm": "Are you sure you want to clear the blacklisted tags?", "components.Settings.copyBlacklistedTags": "Copied blacklisted tags to clipboard.", "components.Settings.copyBlacklistedTagsEmpty": "Nothing to copy", "components.Settings.copyBlacklistedTagsTip": "Copy blacklisted tag configuration", "components.Settings.currentlibrary": "Current Library: {name}", "components.Settings.default": "<PERSON><PERSON><PERSON>", "components.Settings.default4k": "Default 4K", "components.Settings.deleteServer": "Delete {serverType} Server", "components.Settings.deleteserverconfirm": "Are you sure you want to delete this server?", "components.Settings.email": "Email", "components.Settings.enablessl": "Use SSL", "components.Settings.experimentalTooltip": "Enabling this setting may result in unexpected application behavior", "components.Settings.externalUrl": "External URL", "components.Settings.hostname": "Hostname or IP Address", "components.Settings.importBlacklistedTagsTip": "Import blacklisted tag configuration", "components.Settings.invalidKeyword": "{keywordId} is not a TMDB keyword.", "components.Settings.invalidurlerror": "Unable to connect to {mediaServerName} server.", "components.Settings.is4k": "4K", "components.Settings.jellyfinForgotPasswordUrl": "Forgot Password URL", "components.Settings.jellyfinSettings": "{mediaServerName} Settings", "components.Settings.jellyfinSettingsDescription": "Optionally configure the internal and external endpoints for your {mediaServerName} server. In most cases, the external URL is different to the internal URL. A custom password reset URL can also be set for {mediaServerName} login, in case you would like to redirect to a different password reset page. You can also change the Jellyfin API key, which was automatically generated previously.", "components.Settings.jellyfinSettingsFailure": "Something went wrong while saving {mediaServerName} settings.", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName} settings saved successfully!", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Custom authentication with Automatic Library Grouping not supported", "components.Settings.jellyfinSyncFailedGenericError": "Something went wrong while syncing libraries", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "No libraries were found", "components.Settings.jellyfinlibraries": "{mediaServerName} Libraries", "components.Settings.jellyfinlibrariesDescription": "The libraries {mediaServerName} scans for titles. Click the button below if no libraries are listed.", "components.Settings.jellyfinsettings": "{mediaServerName} Settings", "components.Settings.jellyfinsettingsDescription": "Configure the settings for your {mediaServerName} server. {mediaServerName} scans your {mediaServerName} libraries to see what content is available.", "components.Settings.librariesRemaining": "Libraries Remaining: {count}", "components.Settings.manualscan": "Manual Library Scan", "components.Settings.manualscanDescription": "Normally, this will only be run once every 24 hours. <PERSON><PERSON><PERSON><PERSON> will check your Plex server's recently added more aggressively. If this is your first time configuring Plex, a one-time full manual library scan is recommended!", "components.Settings.manualscanDescriptionJellyfin": "Normally, this will only be run once every 24 hours. <PERSON><PERSON><PERSON><PERSON> will check your {mediaServerName} server's recently added more aggressively. If this is your first time configuring <PERSON><PERSON><PERSON><PERSON>, a one-time full manual library scan is recommended!", "components.Settings.manualscanJellyfin": "Manual Library Scan", "components.Settings.mediaTypeMovie": "movie", "components.Settings.mediaTypeSeries": "series", "components.Settings.menuAbout": "About", "components.Settings.menuGeneralSettings": "General", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.menuJobs": "Jobs & Cache", "components.Settings.menuLogs": "Logs", "components.Settings.menuNetwork": "Network", "components.Settings.menuNotifications": "Notifications", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "Services", "components.Settings.menuUsers": "Users", "components.Settings.no": "No", "components.Settings.noDefault4kServer": "A 4K {serverType} server must be marked as default in order to enable users to submit 4K {mediaType} requests.", "components.Settings.noDefaultNon4kServer": "If you only have a single {serverType} server for both non-4K and 4K content (or if you only download 4K content), your {serverType} server should <strong>NOT</strong> be designated as a 4K server.", "components.Settings.noDefaultServer": "At least one {serverType} server must be marked as default in order for {mediaType} requests to be processed.", "components.Settings.noSpecialCharacters": "Configuration must be a comma delimited list of TMDB keyword ids, and must not start or end with a comma.", "components.Settings.nooptions": "No results.", "components.Settings.notificationAgentSettingsDescription": "Configure and enable notification agents.", "components.Settings.notifications": "Notifications", "components.Settings.notificationsettings": "Notification Settings", "components.Settings.notrunning": "Not Running", "components.Settings.overrideRules": "Override Rules", "components.Settings.overrideRulesDescription": "Override rules allow you to specify properties that will be replaced if a request matches the rule.", "components.Settings.plex": "Plex", "components.Settings.plexlibraries": "Plex Libraries", "components.Settings.plexlibrariesDescription": "The libraries Jellyseerr scans for titles. Set up and save your Plex connection settings, then click the button below if no libraries are listed.", "components.Settings.plexsettings": "Plex Settings", "components.Settings.plexsettingsDescription": "Configure the settings for your Plex server. Jellyseerr scans your Plex libraries to determine content availability.", "components.Settings.port": "Port", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> must be restarted for changes to this setting to take effect", "components.Settings.save": "Save Changes", "components.Settings.saving": "Saving…", "components.Settings.scan": "Sync Libraries", "components.Settings.scanbackground": "Scanning will run in the background. You can continue the setup process in the meantime.", "components.Settings.scanning": "Syncing…", "components.Settings.searchKeywords": "Search keywords…", "components.Settings.serverLocal": "local", "components.Settings.serverRemote": "remote", "components.Settings.serverSecure": "secure", "components.Settings.serverpreset": "Server", "components.Settings.serverpresetLoad": "Press the button to load available servers", "components.Settings.serverpresetManualMessage": "Manual configuration", "components.Settings.serverpresetRefreshing": "Retrieving servers…", "components.Settings.serviceSettingsDescription": "Configure your {serverType} server(s) below. You can connect multiple {serverType} servers, but only two of them can be marked as defaults (one non-4K and one 4K). Administrators are able to override the server used to process new requests prior to approval.", "components.Settings.services": "Services", "components.Settings.settingUpPlexDescription": "To set up Plex, you can either enter the details manually or select a server retrieved from <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Press the button to the right of the dropdown to fetch the list of available servers.", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.ssl": "SSL", "components.Settings.startscan": "<PERSON>an", "components.Settings.starttyping": "Starting typing to search.", "components.Settings.syncJellyfin": "Sync Libraries", "components.Settings.syncing": "Syncing", "components.Settings.tautulliApiKey": "API Key", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.tautulliSettingsDescription": "Optionally configure the settings for your Tautulli server. <PERSON><PERSON><PERSON><PERSON> fetches watch history data for your Plex media from Tautulli.", "components.Settings.timeout": "Timeout", "components.Settings.tip": "Tip", "components.Settings.toastPlexConnecting": "Attempting to connect to Plex…", "components.Settings.toastPlexConnectingFailure": "Failed to connect to Plex.", "components.Settings.toastPlexConnectingSuccess": "Plex connection established successfully!", "components.Settings.toastPlexRefresh": "Retrieving server list from Plex…", "components.Settings.toastPlexRefreshFailure": "Failed to retrieve Plex server list.", "components.Settings.toastPlexRefreshSuccess": "Plex server list retrieved successfully!", "components.Settings.toastTautulliSettingsFailure": "Something went wrong while saving Tautulli settings.", "components.Settings.toastTautulliSettingsSuccess": "Tautulli settings saved successfully!", "components.Settings.urlBase": "URL Base", "components.Settings.validationApiKey": "You must provide an API key", "components.Settings.validationHostnameRequired": "You must provide a valid hostname or IP address", "components.Settings.validationPortRequired": "You must provide a valid port number", "components.Settings.validationUrl": "You must provide a valid URL", "components.Settings.validationUrlBaseLeadingSlash": "URL base must have a leading slash", "components.Settings.validationUrlBaseTrailingSlash": "URL base must not end in a trailing slash", "components.Settings.validationUrlTrailingSlash": "URL must not end in a trailing slash", "components.Settings.valueRequired": "You must provide a value.", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> URL", "components.Settings.webAppUrlTip": "Optionally direct users to the web app on your server instead of the \"hosted\" web app", "components.Settings.webhook": "Webhook", "components.Settings.webpush": "Web Push", "components.Settings.yes": "Yes", "components.Setup.back": "Go back", "components.Setup.configemby": "Configure <PERSON><PERSON>", "components.Setup.configjellyfin": "Configure <PERSON><PERSON><PERSON>", "components.Setup.configplex": "Configure Plex", "components.Setup.configuremediaserver": "Configure Media Server", "components.Setup.configureservices": "Configure Services", "components.Setup.continue": "Continue", "components.Setup.finish": "Finish Setup", "components.Setup.finishing": "Finishing…", "components.Setup.librarieserror": "Validation failed. Please toggle the libraries again to continue.", "components.Setup.servertype": "Choose Server Type", "components.Setup.setup": "Setup", "components.Setup.signin": "Sign In", "components.Setup.signinMessage": "Get started by signing in", "components.Setup.signinWithEmby": "Enter your Emby details", "components.Setup.signinWithJellyfin": "Enter your Jellyfin details", "components.Setup.signinWithPlex": "Enter your Plex details", "components.Setup.subtitle": "Get started by choosing your media server", "components.Setup.welcome": "Welcome to <PERSON><PERSON><PERSON><PERSON>", "components.StatusBadge.managemedia": "Manage {mediaType}", "components.StatusBadge.openinarr": "Open in {arr}", "components.StatusBadge.playonplex": "Play on {mediaServerName}", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} Updated", "components.StatusChecker.appUpdatedDescription": "Please click the button below to reload the application.", "components.StatusChecker.reloadApp": "Reload {applicationTitle}", "components.StatusChecker.restartRequired": "Server <PERSON><PERSON> Required", "components.StatusChecker.restartRequiredDescription": "Please restart the server to apply the updated settings.", "components.TitleCard.addToWatchList": "Add to watchlist", "components.TitleCard.cleardata": "Clear Data", "components.TitleCard.mediaerror": "{mediaType} Not Found", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TitleCard.watchlistCancel": "watchlist for <strong>{title}</strong> canceled.", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> Removed from watchlist  successfully!", "components.TitleCard.watchlistError": "Something went wrong. Please try again.", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> added to watchlist  successfully!", "components.TvDetails.Season.noepisodes": "Episode list unavailable.", "components.TvDetails.Season.somethingwentwrong": "Something went wrong while retrieving season data.", "components.TvDetails.TvCast.fullseriescast": "Full Series Cast", "components.TvDetails.TvCrew.fullseriescrew": "Full Series Crew", "components.TvDetails.addtowatchlist": "Add To Watchlist", "components.TvDetails.anime": "Anime", "components.TvDetails.cast": "Cast", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episode} other {# Episodes}}", "components.TvDetails.episodeRuntime": "Episode Runtime", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutes", "components.TvDetails.firstAirDate": "First Air Date", "components.TvDetails.manageseries": "Manage Series", "components.TvDetails.network": "{networkCount, plural, one {Network} other {Networks}}", "components.TvDetails.nextAirDate": "Next Air Date", "components.TvDetails.originallanguage": "Original Language", "components.TvDetails.originaltitle": "Original Title", "components.TvDetails.overview": "Overview", "components.TvDetails.overviewunavailable": "Overview unavailable.", "components.TvDetails.play": "Play on {mediaServerName}", "components.TvDetails.play4k": "Play 4K on {mediaServerName}", "components.TvDetails.productioncountries": "Production {countryCount, plural, one {Country} other {Countries}}", "components.TvDetails.recommendations": "Recommendations", "components.TvDetails.removefromwatchlist": "Remove From Watchlist", "components.TvDetails.reportissue": "Report an Issue", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes Audience Score", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.TvDetails.seasonnumber": "Season {seasonNumber}", "components.TvDetails.seasons": "{seasonCount, plural, one {# Season} other {# Seasons}}", "components.TvDetails.seasonstitle": "Seasons", "components.TvDetails.showtype": "Series Type", "components.TvDetails.similar": "Similar Series", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.streamingproviders": "Currently Streaming On", "components.TvDetails.tmdbuserscore": "TMDB User Score", "components.TvDetails.viewfullcrew": "View Full Crew", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> Removed from watchlist successfully!", "components.TvDetails.watchlistError": "Something went wrong. Please try again.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> added to watchlist successfully!", "components.TvDetails.watchtrailer": "Watch Trailer", "components.UserList.accounttype": "Type", "components.UserList.admin": "Admin", "components.UserList.autogeneratepassword": "Automatically Generate Password", "components.UserList.autogeneratepasswordTip": "Email a server-generated password to the user", "components.UserList.bulkedit": "Bulk Edit", "components.UserList.create": "Create", "components.UserList.created": "Joined", "components.UserList.createlocaluser": "Create Local User", "components.UserList.creating": "Creating…", "components.UserList.deleteconfirm": "Are you sure you want to delete this user? All of their request data will be permanently removed.", "components.UserList.deleteuser": "Delete User", "components.UserList.edituser": "Edit User Permissions", "components.UserList.email": "Email Address", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {user} other {users}} imported successfully!", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {user} other {users}} imported successfully!", "components.UserList.importfromJellyfin": "Import {mediaServerName} Users", "components.UserList.importfromJellyfinerror": "Something went wrong while importing {mediaServerName} users.", "components.UserList.importfrommediaserver": "Import {mediaServerName} Users", "components.UserList.importfromplex": "Import Plex Users", "components.UserList.importfromplexerror": "Something went wrong while importing Plex users.", "components.UserList.localLoginDisabled": "The <strong>Enable Local Sign-In</strong> setting is currently disabled.", "components.UserList.localuser": "Local User", "components.UserList.mediaServerUser": "{mediaServerName} User", "components.UserList.newJellyfinsigninenabled": "The <strong>Enable New {mediaServerName} Sign-In</strong> setting is currently enabled. {mediaServerName} users with library access do not need to be imported in order to sign in.", "components.UserList.newplexsigninenabled": "The <strong>Enable New Plex Sign-In</strong> setting is currently enabled. Plex users with library access do not need to be imported in order to sign in.", "components.UserList.noJellyfinuserstoimport": "There are no {mediaServerName} users to import.", "components.UserList.nouserstoimport": "There are no Plex users to import.", "components.UserList.owner": "Owner", "components.UserList.password": "Password", "components.UserList.passwordinfodescription": "Configure an application URL and enable email notifications to allow automatic password generation.", "components.UserList.plexuser": "Plex User", "components.UserList.role": "Role", "components.UserList.sortCreated": "Join Date", "components.UserList.sortDisplayName": "Display Name", "components.UserList.sortRequests": "Request Count", "components.UserList.totalrequests": "Requests", "components.UserList.user": "User", "components.UserList.usercreatedfailed": "Something went wrong while creating the user.", "components.UserList.usercreatedfailedexisting": "The provided email address is already in use by another user.", "components.UserList.usercreatedsuccess": "User created successfully!", "components.UserList.userdeleted": "User deleted successfully!", "components.UserList.userdeleteerror": "Something went wrong while deleting the user.", "components.UserList.userfail": "Something went wrong while saving user permissions.", "components.UserList.userlist": "User List", "components.UserList.username": "Username", "components.UserList.users": "Users", "components.UserList.userssaved": "User permissions saved successfully!", "components.UserList.validationEmail": "Email required", "components.UserList.validationUsername": "You must provide an username", "components.UserList.validationpasswordminchars": "Password is too short; should be a minimum of 8 characters", "components.UserProfile.ProfileHeader.joindate": "Joined {joindate}", "components.UserProfile.ProfileHeader.profile": "View Profile", "components.UserProfile.ProfileHeader.settings": "Edit Settings", "components.UserProfile.ProfileHeader.userid": "User ID: {userid}", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "Enter your {mediaServerName} credentials to link your account with {applicationName}.", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "This account is already linked to a {applicationName} user", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Unable to connect to {mediaServerName} using your credentials", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "An unknown error occurred", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Password", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "You must provide a password", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "Link", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Adding…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "Link {mediaServerName} Account", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Username", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "You must provide a username", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Account Type", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Admin", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Display Language", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord User ID", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "The <FindDiscordIdLink>multi-digit ID number</FindDiscordIdLink> associated with your Discord user account", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Discover Region", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Filter content by regional availability", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Display Name", "components.UserProfile.UserSettings.UserGeneralSettings.email": "Email", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Override Global Limit", "components.UserProfile.UserSettings.UserGeneralSettings.general": "General", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "General Settings", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Default ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Local User", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName} User", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Movie Request Limit", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Discover Language", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filter content by original language", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Owner", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex User", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Auto-Request Movies", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatically request movies on your <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Auto-Request Series", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatically request series on your <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Discover Region", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filter content by regional availability", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Role", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Save Changes", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Saving…", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Series Request Limit", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Streaming Region", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Show streaming sites by regional availability", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Something went wrong while saving settings.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "This email is already taken!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Another user already has this username. You must set an email", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Setting<PERSON> saved successfully!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "User", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "You must provide a valid Discord user ID", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Valid email required", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Email required", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Unable to delete linked account.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "An unknown error occurred", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Linked Accounts", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "These external accounts are linked to your {applicationName} account.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "You do not have any external accounts linked to your account.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "You do not have permission to modify this user's linked accounts.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "This account is already linked to a Plex user", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Unable to connect to Plex using your credentials", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.browser": "Browser", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.created": "Created", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.deletesubscription": "Delete Subscription", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.device": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.disablewebpush": "Disable web push", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.disablingwebpusherror": "Something went wrong while disabling web push.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.enablewebpush": "Enable web push", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.enablingwebpusherror": "Something went wrong while enabling web push.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.engine": "Engine", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.managedevices": "Manage Devices", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.nodevicestoshow": "You have no web push subscriptions to show.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.operatingsystem": "Operating System", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.subscriptiondeleted": "Subscription deleted.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.subscriptiondeleteerror": "Something went wrong while deleting the user subscription.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.type": "type", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.unknown": "Unknown", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.webpushhasbeendisabled": "Web push has been disabled.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.webpushhasbeenenabled": "Web push has been enabled.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.webpushsettingsfailed": "Web push notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.UserNotificationsWebPush.webpushsettingssaved": "Web push notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "User ID", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "The <FindDiscordIdLink>multi-digit ID number</FindDiscordIdLink> associated with your user account", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Email notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Email notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notifications", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Notification Settings", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP Public Key", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Encrypt email messages using <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Access Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Create a token from your <PushbulletSettingsLink>Account Settings</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Application API Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Register an application</ApplicationRegistrationLink> for use with {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "User or Group Key", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Your 30-character <UsersGroupsLink>user or group identifier</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Send Silently", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Send notifications with no sound", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Notification Sound", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Start a chat</TelegramBotLink>, add <GetIdBotLink>@get_id_bot</GetIdBotLink>, and issue the <code>/my_id</code> command", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "Thread/Topic ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "If your group-chat has topics enabled, you can specify a thread/topic's ID here", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram notification settings failed to save.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram notification settings saved successfully!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "You must provide a valid user ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "You must provide a valid PGP public key", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "You must provide an access token", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "You must provide a valid application token", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "You must provide a valid user or group key", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "You must provide a valid chat ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "The thread/topic ID must be a positive whole number", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Confirm Password", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Current Password", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "New Password", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "This user account currently does not have a password set. Configure a password below to enable this account to sign in as a \"local user.\"", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Your account currently does not have a password set. Configure a password below to enable sign-in as a \"local user\" using your email address.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "You do not have permission to modify this user's password.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Password", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Something went wrong while saving the password.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Something went wrong while saving the password. Was your current password entered correctly?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Password saved successfully!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "You must confirm the new password", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Passwords must match", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "You must provide your current password", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "You must provide a new password", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Password is too short; should be a minimum of 8 characters", "components.UserProfile.UserSettings.UserPermissions.permissions": "Permissions", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Something went wrong while saving settings.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Permissions saved successfully!", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "You cannot modify your own permissions.", "components.UserProfile.UserSettings.menuChangePass": "Password", "components.UserProfile.UserSettings.menuGeneralSettings": "General", "components.UserProfile.UserSettings.menuLinkedAccounts": "Linked Accounts", "components.UserProfile.UserSettings.menuNotifications": "Notifications", "components.UserProfile.UserSettings.menuPermissions": "Permissions", "components.UserProfile.UserSettings.unauthorizedDescription": "You do not have permission to modify this user's settings.", "components.UserProfile.emptywatchlist": "Media added to your <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> will appear here.", "components.UserProfile.limit": "{remaining} of {limit}", "components.UserProfile.localWatchlist": "{username}'s Watchlist", "components.UserProfile.movierequests": "Movie Requests", "components.UserProfile.pastdays": "{type} (past {days} days)", "components.UserProfile.plexwatchlist": "Plex Watchlist", "components.UserProfile.recentlywatched": "Recently Watched", "components.UserProfile.recentrequests": "Recent Requests", "components.UserProfile.requestsperdays": "{limit} remaining", "components.UserProfile.seriesrequest": "Series Requests", "components.UserProfile.totalrequests": "Total Requests", "components.UserProfile.unlimited": "Unlimited", "i18n.addToBlacklist": "Add to Blacklist", "i18n.advanced": "Advanced", "i18n.all": "All", "i18n.approve": "Approve", "i18n.approved": "Approved", "i18n.areyousure": "Are you sure?", "i18n.available": "Available", "i18n.back": "Back", "i18n.blacklist": "Blacklist", "i18n.blacklistDuplicateError": "<strong>{title}</strong> has already been blacklisted.", "i18n.blacklistError": "Something went wrong. Please try again.", "i18n.blacklistSuccess": "<strong>{title}</strong> was successfully blacklisted.", "i18n.blacklisted": "Blacklisted", "i18n.cancel": "Cancel", "i18n.canceling": "Canceling…", "i18n.close": "Close", "i18n.collection": "Collection", "i18n.completed": "Completed", "i18n.decline": "Decline", "i18n.declined": "Declined", "i18n.delete": "Delete", "i18n.deleted": "Deleted", "i18n.deleting": "Deleting…", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "Edit", "i18n.experimental": "Experimental", "i18n.failed": "Failed", "i18n.import": "Import", "i18n.importing": "Importing…", "i18n.loading": "Loading…", "i18n.movie": "Movie", "i18n.movies": "Movies", "i18n.next": "Next", "i18n.noresults": "No results.", "i18n.notrequested": "Not Requested", "i18n.open": "Open", "i18n.partiallyavailable": "Partially Available", "i18n.pending": "Pending", "i18n.previous": "Previous", "i18n.processing": "Processing", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> was successfully removed from the Blacklist.", "i18n.removefromBlacklist": "<PERSON><PERSON><PERSON> from Blacklist", "i18n.request": "Request", "i18n.request4k": "Request in 4K", "i18n.requested": "Requested", "i18n.requesting": "Requesting…", "i18n.resolved": "Resolved", "i18n.restartRequired": "<PERSON><PERSON> Required", "i18n.resultsperpage": "Display {pageSize} results per page", "i18n.retry": "Retry", "i18n.retrying": "Retrying…", "i18n.save": "Save Changes", "i18n.saving": "Saving…", "i18n.settings": "Settings", "i18n.showingresults": "Showing <strong>{from}</strong> to <strong>{to}</strong> of <strong>{total}</strong> results", "i18n.specials": "Specials", "i18n.status": "Status", "i18n.test": "Test", "i18n.testing": "Testing…", "i18n.tvshow": "Series", "i18n.tvshows": "Series", "i18n.unavailable": "Unavailable", "i18n.usersettings": "User Settings", "i18n.view": "View", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "Internal Server Error", "pages.oops": "Oops", "pages.pagenotfound": "Page Not Found", "pages.returnHome": "Return Home", "pages.serviceunavailable": "Service Unavailable", "pages.somethingwentwrong": "Something Went Wrong"}