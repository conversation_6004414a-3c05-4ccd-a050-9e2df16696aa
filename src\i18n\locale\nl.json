{"components.Discover.popularmovies": "Populaire films", "components.Discover.populartv": "Populaire series", "components.Discover.recentlyAdded": "Onlangs toegevoegd", "components.Discover.recentrequests": "<PERSON><PERSON> a<PERSON>v<PERSON>n", "components.Discover.trending": "Trending", "components.Discover.upcoming": "Verwachte films", "components.Discover.upcomingmovies": "Verwachte films", "components.Layout.SearchInput.searchPlaceholder": "Films en series zoeken", "components.Layout.Sidebar.dashboard": "Ontdekken", "components.Layout.Sidebar.requests": "Aanvragen", "components.Layout.Sidebar.settings": "Instellingen", "components.Layout.Sidebar.users": "Gebruikers", "components.Layout.UserDropdown.signout": "Afmelden", "components.MovieDetails.budget": "Budget", "components.MovieDetails.cast": "Cast", "components.MovieDetails.originallanguage": "Oorspronkelijke taal", "components.MovieDetails.overview": "Overzicht", "components.MovieDetails.overviewunavailable": "Overzicht niet besch<PERSON>.", "components.MovieDetails.recommendations": "Aanbevelingen", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Verschijningsdatum} other {Vers<PERSON>jningsdata}}", "components.MovieDetails.revenue": "Omzet", "components.MovieDetails.runtime": "{minutes} minuten", "components.MovieDetails.similar": "Vergelijkbare titels", "components.PersonDetails.appearsin": "Verschijningen", "components.PersonDetails.ascharacter": "als {character}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.RequestList.requests": "Aanvragen", "components.RequestModal.cancel": "Aanvraag annuleren", "components.RequestModal.numberofepisodes": "Aantal afleveringen", "components.RequestModal.pendingrequest": "Aanvraag in behandeling", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON> voor <strong>{title}</strong> gean<PERSON><PERSON><PERSON>.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> is aangev<PERSON><PERSON>d!", "components.RequestModal.requestadmin": "Deze aanvraag zal automatisch goedgekeurd worden.", "components.RequestModal.requestfrom": "<PERSON> van {user} is in afwachting van goedkeuring.", "components.RequestModal.requestseasons": "{seasonCount} {seasonCount, plural, one {seizoen} other {seizoenen}} aanvragen", "components.RequestModal.season": "Seizoen", "components.RequestModal.seasonnumber": "Seizoen {number}", "components.RequestModal.selectseason": "Seizoenen selecteren", "components.Search.searchresults": "Zoekresultaten", "components.Settings.Notifications.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.Settings.Notifications.authPass": "SMTP-wachtwoord", "components.Settings.Notifications.authUser": "SMTP-gebruikersnaam", "components.Settings.Notifications.emailsender": "E-mailadres afzender", "components.Settings.Notifications.smtpHost": "SMTP-host", "components.Settings.Notifications.smtpPort": "SMTP-poort", "components.Settings.Notifications.validationSmtpHostRequired": "Je moet een geldig(e) hostnaam of IP-adres opgeven", "components.Settings.Notifications.validationSmtpPortRequired": "Je moet een geldig poortnummer opgeven", "components.Settings.Notifications.webhookUrl": "Webhook-URL", "components.Settings.RadarrModal.add": "Server toevoegen", "components.Settings.RadarrModal.apiKey": "API-sleutel", "components.Settings.RadarrModal.baseUrl": "URL-basis", "components.Settings.RadarrModal.createradarr": "Nieuwe Radarr-server toevoegen", "components.Settings.RadarrModal.defaultserver": "Standaardserver", "components.Settings.RadarrModal.editradarr": "Radarr-server wijzigen", "components.Settings.RadarrModal.hostname": "Hostnaam of IP-adres", "components.Settings.RadarrModal.minimumAvailability": "Minimale beschikbaarheid", "components.Settings.RadarrModal.port": "Poort", "components.Settings.RadarrModal.qualityprofile": "Kwaliteitsprofiel", "components.Settings.RadarrModal.rootfolder": "Hoofdmap", "components.Settings.RadarrModal.selectMinimumAvailability": "Minimale beschikbaarheid selecteren", "components.Settings.RadarrModal.selectQualityProfile": "Kwaliteitsprofiel selecteren", "components.Settings.RadarrModal.selectRootFolder": "Hoofdmap selecteren", "components.Settings.RadarrModal.server4k": "4K-server", "components.Settings.RadarrModal.servername": "Servernaam", "components.Settings.RadarrModal.ssl": "SSL gebruiken", "components.Settings.RadarrModal.toastRadarrTestFailure": "<PERSON>n niet verbinden met <PERSON>r.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Succesvol verbonden met Radarr!", "components.Settings.RadarrModal.validationApiKeyRequired": "Je moet een API-sleutel opgeven", "components.Settings.RadarrModal.validationHostnameRequired": "Je moet een geldige hostnaam of geldig IP-adres opgeven", "components.Settings.RadarrModal.validationPortRequired": "Je moet een geldig poortnummer opgeven", "components.Settings.RadarrModal.validationProfileRequired": "Je moet een kwaliteitsprofiel selecteren", "components.Settings.RadarrModal.validationRootFolderRequired": "Je moet een hoofdmap selecteren", "components.Settings.SonarrModal.add": "Server toevoegen", "components.Settings.SonarrModal.apiKey": "API-sleutel", "components.Settings.SonarrModal.baseUrl": "URL-basis", "components.Settings.SonarrModal.createsonarr": "<PERSON><PERSON><PERSON> Sonarr-server toevoegen", "components.Settings.SonarrModal.defaultserver": "Standaardserver", "components.Settings.SonarrModal.editsonarr": "Sonarr-server wijzigen", "components.Settings.SonarrModal.hostname": "Hostnaam of IP-adres", "components.Settings.SonarrModal.port": "Poort", "components.Settings.SonarrModal.qualityprofile": "Kwaliteitsprofiel", "components.Settings.SonarrModal.rootfolder": "Hoofdmap", "components.Settings.SonarrModal.seasonfolders": "Seizoensmappen", "components.Settings.SonarrModal.selectQualityProfile": "Kwaliteitsprofiel selecteren", "components.Settings.SonarrModal.selectRootFolder": "Hoofdmap selecteren", "components.Settings.SonarrModal.server4k": "4K-server", "components.Settings.SonarrModal.servername": "Servernaam", "components.Settings.SonarrModal.ssl": "SSL gebruiken", "components.Settings.SonarrModal.validationApiKeyRequired": "Je moet een API-sleutel opgeven", "components.Settings.SonarrModal.validationHostnameRequired": "Je moet een geldige hostnaam of geldig IP-adres opgeven", "components.Settings.SonarrModal.validationPortRequired": "Je moet een geldig poortnummer opgeven", "components.Settings.SonarrModal.validationProfileRequired": "Je moet een kwaliteitsprofiel selecteren", "components.Settings.SonarrModal.validationRootFolderRequired": "Je moet een hoofdmap selecteren", "components.Settings.activeProfile": "Actief profiel", "components.Settings.addradarr": "Radarr-server toe<PERSON><PERSON>n", "components.Settings.address": "<PERSON><PERSON>", "components.Settings.addsonarr": "Sonarr-server toevoegen", "components.Settings.cancelscan": "<PERSON><PERSON> annuleren", "components.Settings.copied": "API-sleutel gekopieerd naar klembord.", "components.Settings.currentlibrary": "Huidige bibliotheek: {name}", "components.Settings.default": "Standaard", "components.Settings.default4k": "Standaard 4K", "components.Settings.deleteserverconfirm": "Weet je zeker dat je deze server wilt verwijderen?", "components.Settings.hostname": "Hostnaam of IP-adres", "components.Settings.librariesRemaining": "Resterende bibliotheken: {count}", "components.Settings.manualscan": "Handmatige bibliotheekscan", "components.Settings.manualscanDescription": "Normaliter wordt dit eenmaal per 24 uur uitgevoerd. <PERSON><PERSON><PERSON><PERSON> zal <PERSON>ij<PERSON> met onlangs toegevoegde media op je Plex-server vaker controleren. Als dit de eerste keer is dat je Je<PERSON><PERSON><PERSON> instelt, wordt aanbevolen eenmalig een handmatige, volledige bibliotheekscan uit te voeren!", "components.Settings.menuAbout": "Over", "components.Settings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuJobs": "Taken en cache", "components.Settings.menuLogs": "Logboeken", "components.Settings.menuNotifications": "Meldingen", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.notificationsettings": "Meldingsinstellingen", "components.Settings.notrunning": "Niet actief", "components.Settings.plexlibraries": "Plex-bibliotheken", "components.Settings.plexlibrariesDescription": "De bibliotheken die Jellyseerr scant voor titels. Stel je Plex-verbinding in en sla ze op. Klik daarna op de onderstaande knop als er geen bibliotheken staan.", "components.Settings.plexsettings": "Plex-instellingen", "components.Settings.plexsettingsDescription": "Configureer de instellingen voor je Plex-server. <PERSON><PERSON><PERSON><PERSON> scant je Plex-bibliotheken om te zien welke inhoud beschik<PERSON> is.", "components.Settings.port": "Poort", "components.Settings.radarrsettings": "Radarr-instellingen", "components.Settings.sonarrsettings": "Sonarr-instellingen", "components.Settings.ssl": "SSL", "components.Settings.startscan": "<PERSON><PERSON> starten", "components.Setup.configureservices": "<PERSON><PERSON><PERSON> configureren", "components.Setup.continue": "Doorgaan", "components.Setup.finish": "Installatie voltooien", "components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON>…", "components.Setup.signinMessage": "Ga aan de slag door je aan te melden", "components.Setup.welcome": "<PERSON>lk<PERSON> bi<PERSON>", "components.TvDetails.cast": "Cast", "components.TvDetails.originallanguage": "Oorspronkelijke taal", "components.TvDetails.overview": "Overzicht", "components.TvDetails.overviewunavailable": "Overzicht niet besch<PERSON>.", "components.TvDetails.recommendations": "Aanbevelingen", "components.TvDetails.similar": "Vergelijkbare series", "components.UserList.admin": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.created": "<PERSON><PERSON> gew<PERSON>en", "components.UserList.plexuser": "Plex-geb<PERSON>iker", "components.UserList.role": "Rol", "components.UserList.totalrequests": "Aanvragen", "components.UserList.user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.userlist": "Gebruikerslijst", "i18n.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.approved": "Goedgekeurd", "i18n.available": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "Verwijderen", "i18n.movies": "Films", "i18n.partiallyavailable": "<PERSON><PERSON>", "i18n.pending": "In behandeling", "i18n.processing": "Verwerken", "i18n.tvshows": "Series", "i18n.unavailable": "<PERSON><PERSON>", "pages.oops": "Oeps", "pages.returnHome": "Terug naar de startpagina", "components.MovieDetails.MovieCast.fullcast": "Volledige cast", "components.TvDetails.TvCast.fullseriescast": "Volledige cast van de serie", "components.Settings.Notifications.emailsettingssaved": "Instellingen voor e-mailmeldingen opgeslagen!", "components.Settings.Notifications.emailsettingsfailed": "Instellingen voor e-mailmeldingen konden niet opgeslagen worden.", "components.Settings.Notifications.discordsettingssaved": "Instellingen voor Discord-meldingen opgeslagen!", "components.Settings.Notifications.discordsettingsfailed": "Instellingen voor Discord-meldingen konden niet opgeslagen worden.", "components.Settings.validationPortRequired": "Je moet een geldig poortnummer opgeven", "components.Settings.validationHostnameRequired": "Je moet een geldig(e) hostnaam of IP-adres opgeven", "components.Settings.SonarrModal.validationNameRequired": "Je moet een server<PERSON>am opgeven", "components.Settings.RadarrModal.validationNameRequired": "Je moet een server<PERSON>am opgeven", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.totalrequests": "Totaal aantal aanvragen", "components.Settings.SettingsAbout.totalmedia": "Totaal aantal media", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub-discussies", "components.Settings.SonarrModal.testFirstRootFolders": "Test verbinding om hoofdmappen te laden", "components.Settings.SonarrModal.testFirstQualityProfiles": "Test verbinding om kwaliteitsprofielen te laden", "components.Settings.SonarrModal.loadingrootfolders": "Ho<PERSON>dma<PERSON>n laden…", "components.Settings.SonarrModal.loadingprofiles": "Kwaliteitsprofielen laden…", "components.Settings.SettingsAbout.gettingsupport": "Ondersteuning krijgen", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Je moet een minimale beschikbaarheid selecteren", "components.Settings.RadarrModal.testFirstRootFolders": "Test verbinding om hoofdmappen te laden", "components.Settings.RadarrModal.testFirstQualityProfiles": "Test verbinding om kwaliteitsprofielen te laden", "components.Settings.RadarrModal.loadingrootfolders": "Ho<PERSON>dma<PERSON>n laden…", "components.Settings.RadarrModal.loadingprofiles": "Kwaliteitsprofielen laden…", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Versiegegevens zijn momenteel niet be<PERSON>.", "components.Settings.SettingsAbout.Releases.latestversion": "Nieuwste", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON>", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studio's}}", "components.CollectionDetails.overview": "Overzicht", "components.CollectionDetails.numberofmovies": "{count} films", "components.NotificationTypeSelector.mediafailed": "Verwerking van aanvraag mislukt", "components.NotificationTypeSelector.mediaapprovedDescription": "Een melding sturen wanneer media-aanvragen handmatig worden goedgekeurd.", "components.NotificationTypeSelector.mediaavailableDescription": "Een melding sturen wanneer een media-aanvra<PERSON> besch<PERSON> is.", "components.NotificationTypeSelector.mediaapproved": "Aanvraag goedgekeurd", "i18n.retry": "Opnieuw proberen", "i18n.requested": "Aangevraagd", "i18n.failed": "Mislukt", "i18n.deleting": "Verwijderen…", "i18n.close": "Sluiten", "components.UserList.userdeleteerror": "<PERSON>r ging iets mis bij het verwijderen van de gebruiker.", "components.UserList.userdeleted": "Gebruiker verwijderd!", "components.UserList.importfromplexerror": "<PERSON>r is iets misgegaan bij het importeren van Plex-gebruikers.", "components.UserList.importfrommediaserver": "{mediaServerName}-gebruikers importeren", "components.UserList.importfromplex": "Plex-gebruikers importeren", "components.UserList.deleteuser": "Gebruiker verwijderen", "components.UserList.deleteconfirm": "Weet je zeker dat je deze gebruiker wilt verwijderen? Al diens bestaande aanvraaggegevens zullen worden verwijderd.", "components.TvDetails.watchtrailer": "Trailer bekijken", "components.TvDetails.viewfullcrew": "Volledige crew bekijken", "components.TvDetails.showtype": "Type serie", "components.TvDetails.network": "{networkCount, plural, one {Netwerk} other {Netwerken}}", "components.TvDetails.firstAirDate": "Datum eerste uitzending", "components.TvDetails.anime": "Anime", "components.Settings.SonarrModal.animerootfolder": "Hoofdmap anime", "components.Settings.SonarrModal.animequalityprofile": "Kwaliteitsprofiel anime", "components.Settings.SettingsAbout.timezone": "Tijdzone", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON> steunen", "components.Settings.SettingsAbout.helppaycoffee": "Help een koffie te betalen", "components.Settings.SettingsAbout.documentation": "Documentatie", "components.Settings.SettingsAbout.Releases.viewongithub": "Bekijken op GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Changelog bekijken", "components.Settings.Notifications.validationChatIdRequired": "Je moet een geldige chat-ID opgeven", "components.Settings.Notifications.validationBotAPIRequired": "Je moet een bot-author<PERSON><PERSON><PERSON><PERSON> opgeven", "components.Settings.Notifications.telegramsettingssaved": "Instellingen Telegrammeldingen opgeslagen!", "components.Settings.Notifications.telegramsettingsfailed": "De instellingen voor Telegrammeldingen konden niet opgeslagen worden.", "components.Settings.Notifications.senderName": "<PERSON><PERSON>", "components.Settings.Notifications.chatId": "Chat-ID", "components.Settings.Notifications.botAPI": "Bot-autorisatietoken", "components.Settings.Notifications.allowselfsigned": "Self-signed certificaten <PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsSlack.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.RequestList.RequestItem.failedretry": "Er ging op<PERSON>uw iets mis tijdens het aanvragen.", "components.PersonDetails.crewmember": "Crew", "components.NotificationTypeSelector.mediarequested": "<PERSON><PERSON><PERSON><PERSON><PERSON> in afwachting van goedkeuring", "components.NotificationTypeSelector.mediaavailable": "Aanvraag be<PERSON>", "components.MovieDetails.watchtrailer": "Trailer bekijken", "components.MovieDetails.viewfullcrew": "Volledige crew bekijken", "components.MovieDetails.MovieCrew.fullcrew": "Volledige crew", "components.CollectionDetails.requestcollection": "Collectie a<PERSON>vragen", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex-{userCount, plural, one {gebruiker} other {gebruikers}} geïmporteerd!", "components.Settings.SettingsAbout.Releases.versionChangelog": "Changelog voor {version}", "components.Settings.SettingsAbout.Releases.releases": "Versies", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Instellingen voor Slack-meldingen opgeslagen!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Instellingen voor Slack-meldingen konden niet opgeslagen worden.", "components.NotificationTypeSelector.mediarequestedDescription": "Een melding sturen wanneer gebruikers een nieuwe media-aanvraag indienen die goedkeuring vereist.", "components.NotificationTypeSelector.mediafailedDescription": "Een melding sturen wanneer een media-aanvraag niet kan worden toegevoegd aan Radarr of Sonarr.", "components.TvDetails.TvCrew.fullseriescrew": "Volledige crew van de serie", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Instellingen voor Pushover-meldingen opgeslagen!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Instellingen voor Pushover-meldingen konden niet opgeslagen worden.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token toepassings-API", "components.RequestList.sortModified": "Laatst gewijzigd", "components.RequestList.sortAdded": "Meest recent", "components.RequestList.showallrequests": "Alle aanvragen tonen", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Je moet een geldige gebruikers- of groepssleutel opgeven", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Je moet een geldig toepassingstoken opgeven", "components.Settings.Notifications.NotificationsPushover.userToken": "Gebruikers- of groepssleutel", "i18n.request": "Aanvragen", "components.RequestButton.requestmore4k": "<PERSON><PERSON> in 4K aanvragen", "components.RequestButton.approverequests": "{requestCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {{requestCount} Aanvragen}} goedkeuren", "components.RequestButton.approve4krequests": "{requestCount, plural, one {4K-aanvraag} other {{requestCount} 4K-aanvragen}} goed<PERSON>uren", "components.RequestButton.declinerequests": "{requestCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {{requestCount} Aanvragen}} weigeren", "components.RequestButton.decline4krequests": "{requestCount, plural, one {4K-aanvraag} other {{requestCount} 4K-aanvragen}} weigeren", "components.StatusBadge.status4k": "4K {status}", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Instellingen voor webhook-meldingen opgeslagen!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Instellingen voor webhook-meldingen konden niet opgeslagen worden.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Je moet een geldige JSON-payload opgeven", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON-payload teruggezet!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Terugzetten naar standaard", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON-payload", "components.Settings.Notifications.NotificationsWebhook.authheader": "Autorisatie-header", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.RequestModal.pending4krequest": "4K-aanv<PERSON><PERSON> in behandeling", "components.RequestButton.viewrequest4k": "4K-aanvraag bekijken", "components.RequestButton.viewrequest": "Aanvraag bekijken", "components.RequestButton.requestmore": "<PERSON><PERSON>", "components.RequestButton.declinerequest4k": "4K-aanvraag weigeren", "components.RequestButton.declinerequest": "Aanvraag weigeren", "components.RequestButton.approverequest4k": "4K-aanv<PERSON><PERSON> goedkeuren", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON><PERSON><PERSON> go<PERSON>", "components.UserList.autogeneratepassword": "Automatisch wachtwoord genereren", "components.UserList.create": "Aanmaken", "components.UserList.createlocaluser": "Lokale gebruiker aanmaken", "components.UserList.usercreatedfailed": "<PERSON>r ging iets mis bij het aanma<PERSON> van de gebruiker.", "components.UserList.creating": "Aanmaken…", "components.UserList.validationpasswordminchars": "Wachtwoord is te kort; moet minimaal 8 tekens bevatten", "components.UserList.usercreatedsuccess": "Gebruiker aangemaakt!", "components.UserList.passwordinfodescription": "Stel een toepassings-URL in en schakel e-mailmeldingen in om automatische wachtwoordgeneratie mogelijk te maken.", "components.UserList.password": "Wachtwoord", "components.UserList.localuser": "Lokale gebruiker", "components.UserList.email": "E-mailadres", "components.Login.validationpasswordrequired": "Je moet een wachtwoord opgeven", "components.Login.validationemailrequired": "Je moet een geldig e-mailadres opgeven", "components.Login.signinwithoverseerr": "{applicationTitle}-account gebruiken", "components.Login.password": "Wachtwoord", "components.Login.loginerror": "<PERSON>r ging iets mis bij het a<PERSON>melden.", "components.Login.email": "E-mailadres", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON>", "i18n.edit": "Bewerken", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON><PERSON> voor <strong>{title}</strong> aangepast!", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> voor <strong>{title}</strong> gean<PERSON><PERSON><PERSON>.", "components.RequestModal.errorediting": "Er is iets misgegaan bij het aanpassen van de a<PERSON>v<PERSON>ag.", "components.RequestModal.AdvancedRequester.rootfolder": "Hoofdmap", "components.RequestModal.AdvancedRequester.qualityprofile": "Kwaliteitsprofiel", "components.RequestModal.AdvancedRequester.destinationserver": "Doelserver", "components.RequestModal.AdvancedRequester.default": "{name} (Standaard)", "components.RequestModal.AdvancedRequester.animenote": "* Deze serie is anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "Geavanceerd", "components.RequestBlock.server": "Doelserver", "components.RequestBlock.rootfolder": "Hoofdmap", "components.RequestBlock.profilechanged": "Kwaliteitsprofiel", "components.RequestBlock.requestoverrides": "Overschrijvingen van aanvraag", "components.NotificationTypeSelector.mediadeclinedDescription": "Een melding sturen wanneer een media-aanvraag wordt afgewezen.", "components.NotificationTypeSelector.mediadeclined": "Aanvraag geweigerd", "components.RequestModal.autoapproval": "Automatische goedkeuring", "i18n.experimental": "Experimenteel", "components.RequestModal.requesterror": "<PERSON>r ging iets mis bij het aanvragen.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Er is geen automatische overeenkomst voor deze serie gevonden. Selecteer hieronder de overeenkomstige serie.", "components.Login.signinwithplex": "Plex-account gebruiken", "components.Login.signinheader": "Log in om verder te gaan", "components.Login.signingin": "Aanmelden…", "components.Login.signin": "Aanmelden", "components.Settings.notificationAgentSettingsDescription": "Meldingsdiensten configureren en inschakelen.", "components.PermissionEdit.advancedrequest": "Geavanceerde aanvragen", "components.PermissionEdit.admin": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.userssaved": "Gebruikersrechten opgeslagen!", "components.Settings.toastPlexRefreshSuccess": "<PERSON><PERSON><PERSON><PERSON> van Plex opgehaald!", "components.Settings.toastPlexRefresh": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>…", "components.Settings.toastPlexConnecting": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON>…", "components.UserList.bulkedit": "Meerdere bewerken", "components.Settings.toastPlexRefreshFailure": "<PERSON><PERSON><PERSON><PERSON> van Plex niet ophalen.", "components.Settings.toastPlexConnectingSuccess": "Succesvol verbonden met Plex!", "components.Settings.toastPlexConnectingFailure": "Kan geen verbinding maken met Plex.", "components.Settings.settingUpPlexDescription": "Om Plex in te stellen, kan je de gegevens handmatig invoeren of een server selecteren die is op<PERSON><PERSON><PERSON> van <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Druk op de knop rechts van de vervolgkeuzelijst om de lijst van beschikbare servers op te halen.", "components.Settings.serverpresetRefreshing": "Servers ophalen…", "components.Settings.serverpresetManualMessage": "Handmatige configuratie", "components.Settings.serverpresetLoad": "Klik op de knop om de beschikbare servers te laden", "components.Settings.serverpreset": "Server", "components.Settings.serverRemote": "extern", "components.Settings.serverLocal": "lokaal", "components.PermissionEdit.usersDescription": "Toestemming geven om gebruikers te beheren. Gebruikers met deze toestemming kunnen gebruikers met beheerders<PERSON>en niet wijzigen of die rechten verlenen.", "components.PermissionEdit.users": "Gebruike<PERSON> beheren", "components.PermissionEdit.requestDescription": "Toestemming geven om niet-4K-media aan te vragen.", "components.PermissionEdit.request4kTvDescription": "Toestemming geven om series in 4K aan te vragen.", "components.PermissionEdit.request4kTv": "4K-series aanvragen", "components.PermissionEdit.request4kMoviesDescription": "Toestemming geven om films in 4K aan te vragen.", "components.PermissionEdit.request4k": "4K aanvragen", "components.PermissionEdit.request": "Aanvragen", "components.PermissionEdit.request4kMovies": "4K-films aanvragen", "components.PermissionEdit.request4kDescription": "Toestemming geven om 4K-media aan te vragen.", "components.PermissionEdit.managerequestsDescription": "Toestemming geven om media-aanvragen te beheren. Alle aanvragen die door een gebruiker met deze toestemming worden gedaan, worden automatisch goedgekeurd.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON><PERSON><PERSON> beheren", "components.PermissionEdit.autoapproveSeriesDescription": "Serie-aanvragen (niet-4K) automatisch goedkeuren.", "components.PermissionEdit.autoapproveMovies": "Films automatisch goedkeuren", "components.PermissionEdit.autoapproveSeries": "Series automatisch goedkeuren", "components.PermissionEdit.autoapproveMoviesDescription": "Filmaanvragen (niet-4K) automatisch goedkeuren.", "components.PermissionEdit.autoapproveDescription": "Alle media-aanvragen (niet-4K) automatisch goedkeuren.", "components.PermissionEdit.autoapprove": "Automatische goedkeuring", "components.PermissionEdit.advancedrequestDescription": "Toestemming geven om geavanceerde aanvraagopties voor media te wijzigen.", "components.PermissionEdit.adminDescription": "Volledige beheerderstoegang. Omzeilt alle andere machtigingscontroles.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Succesvol verbonden met Sonarr!", "components.Settings.SonarrModal.toastSonarrTestFailure": "<PERSON><PERSON> niet verbinden met <PERSON><PERSON><PERSON>.", "components.Settings.SonarrModal.syncEnabled": "<PERSON>an inschakelen", "components.Settings.SonarrModal.externalUrl": "Externe URL", "components.Settings.RadarrModal.syncEnabled": "<PERSON>an inschakelen", "components.Settings.RadarrModal.externalUrl": "Externe URL", "components.MovieDetails.mark4kavailable": "<PERSON><PERSON> in 4K markeren", "components.MovieDetails.markavailable": "<PERSON><PERSON> besch<PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} cache leeggemaakt.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachemisses": "Ontbreekt", "components.Settings.SettingsJobsCache.jobsDescription": "<PERSON><PERSON><PERSON><PERSON> voert bepaalde onderhoudstaken uit als regelmatig ingeplande taken, maar ze kunnen hieronder ook handmatig worden gestart. Het handmatig uitvoeren van een taak verandert de planning niet.", "i18n.advanced": "Geavanceerd", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON> u<PERSON>n", "components.Settings.SettingsJobsCache.process": "Proces", "components.Settings.SettingsJobsCache.nextexecution": "Volgende uitvoering", "components.Settings.SettingsJobsCache.jobtype": "Type", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} gestart.", "components.Settings.SettingsJobsCache.jobs": "Taken", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} gean<PERSON><PERSON><PERSON>.", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON> wissen", "components.Settings.SettingsJobsCache.command": "Commando", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON> annu<PERSON>en", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "Cachenaam", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Totaal aantal sleutels", "components.Settings.SettingsJobsCache.cachehits": "Resultaten", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr cachet verzoeken aan externe API-eindpunten om prestatie te optimaliseren en onnodige API-aanroepen te vermijden.", "components.Settings.SettingsAbout.preferredmethod": "Voorkeur", "components.UserList.users": "Gebruikers", "components.Search.search": "<PERSON><PERSON>", "components.Setup.setup": "Configuratie", "components.RequestModal.AdvancedRequester.requestas": "Aanvragen als", "components.Discover.discover": "Ontdekken", "components.AppDataWarning.dockerVolumeMissingDescription": "De <PERSON> <code>{appDataPath}</code> is niet correct geconfigureerd. Alle gegevens zullen worden gewist wanneer de container wordt gestopt of opnieuw wordt gestart.", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL mag niet eindigen op een schuine streep", "components.Settings.SonarrModal.validationApplicationUrl": "Je moet een geldige URL opgeven", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL mag niet eindigen op een schuine streep", "components.Settings.RadarrModal.validationApplicationUrl": "Je moet een geldige URL opgeven", "components.PermissionEdit.viewrequestsDescription": "Toestemming geven om media-aanvragen van andere gebruikers te bekijken.", "components.PermissionEdit.viewrequests": "Aanvragen bekijken", "components.UserList.validationEmail": "E-mailadres vereist", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Basis-URL mag niet eindigen op een schuine streep", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Basis-U<PERSON> moet met een schuine streep beginnen", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL-basis mag niet eindigen op een schuine streep", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL-basis moe<PERSON> met een schuine streep beginnen", "components.Settings.Notifications.validationEmail": "Je moet een geldig e-mailadres opgeven", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Je moet een geldige URL opgeven", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Je moet een geldige URL opgeven", "components.ResetPassword.resetpassword": "Wachtwoord herstellen", "components.ResetPassword.email": "E-mailadres", "components.TvDetails.nextAirDate": "Volgende uitzenddatum", "components.ResetPassword.validationpasswordrequired": "Je moet een wachtwoord opgeven", "components.ResetPassword.validationpasswordminchars": "Wachtwoord is te kort; moet minimaal 8 tekens bevatten", "components.ResetPassword.validationpasswordmatch": "Wachtwoorden moeten overeenkomen", "components.ResetPassword.validationemailrequired": "Je moet een geldig e-mailadres opgeven", "components.ResetPassword.resetpasswordsuccessmessage": "Wachtwoord is opnieuw ingesteld!", "components.ResetPassword.requestresetlinksuccessmessage": "Er wordt een link om het wachtwoord te resetten naar het opgegeven e-mailadres gestuurd als dat gekoppeld is aan een geldige gebruiker.", "components.ResetPassword.password": "Wachtwoord", "components.ResetPassword.gobacklogin": "Terug naar aanmeldpagina", "components.ResetPassword.emailresetlink": "Herstellink e-mailen", "components.ResetPassword.confirmpassword": "Wachtwoord bevestigen", "components.Login.forgotpassword": "Wachtwoord vergeten?", "components.RequestModal.AdvancedRequester.languageprofile": "Taalprofiel", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Je moet een taalprofiel selecteren", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Verbinding testen en taalprofielen laden", "components.Settings.SonarrModal.selectLanguageProfile": "Taalprofiel selecteren", "components.Settings.SonarrModal.loadinglanguageprofiles": "Taalprofielen laden…", "components.Settings.SonarrModal.languageprofile": "Taalprofiel", "components.Settings.SonarrModal.animelanguageprofile": "Taalprofiel anime", "components.Settings.Notifications.sendSilentlyTip": "Meldingen versturen zonder geluid", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON> verz<PERSON>en", "components.UserList.sortRequests": "Aantal aanvragen", "components.UserList.sortDisplayName": "Weergavenaam", "components.UserList.sortCreated": "Aanmeldingsdatum", "components.PermissionEdit.autoapprove4kSeriesDescription": "Serie-aanvragen in 4K automatisch goedkeuren.", "components.PermissionEdit.autoapprove4kSeries": "Series automatisch goedkeuren", "components.PermissionEdit.autoapprove4kMoviesDescription": "Filmaanvragen in 4K automatisch goedkeuren.", "components.PermissionEdit.autoapprove4kMovies": "Automatische goedkeuring van films in 4K", "components.PermissionEdit.autoapprove4kDescription": "Alle 4K-media-aanvragen automatisch goedkeuren.", "components.PermissionEdit.autoapprove4k": "Automatische goedkeuring 4K", "components.UserProfile.recentrequests": "<PERSON><PERSON> a<PERSON>v<PERSON>n", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Algemene instellingen", "components.UserProfile.UserSettings.menuNotifications": "Meldingen", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Gebruikers-ID", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Wachtwoord is te kort; moet minimaal 8 tekens bevatten", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Je moet een nieuw wachtwoord opgeven", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Je moet jouw huidige wachtwoord opgeven", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Wachtwoorden moeten overeenkomen", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Je moet het nieuwe wachtwoord bevestigen", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Wachtwoord gewijzigd!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Er ging iets mis bij het opslaan van het wachtwoord.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Wachtwoord", "components.UserProfile.UserSettings.menuChangePass": "Wachtwoord", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON><PERSON> wa<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON> wa<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Wachtwoord bevestigen", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Instellingen opgeslagen!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Machtigingen opgeslagen!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "<PERSON>r ging iets mis bij het opsla<PERSON> van de instellingen.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "<PERSON>r ging iets mis bij het opsla<PERSON> van de instellingen.", "components.UserProfile.UserSettings.menuPermissions": "Machtigingen", "components.UserProfile.UserSettings.UserPermissions.permissions": "Machtigingen", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex-geb<PERSON>iker", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Meldingsinstellingen", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Lokale gebruiker", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Weergavenaam", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserList.userfail": "<PERSON>r ging iets mis bij het opsla<PERSON> van de gebruikersrechten.", "components.UserList.edituser": "Gebruikersrechten bewerken", "components.UserProfile.ProfileHeader.settings": "Instellingen bewerken", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Je moet een toegangstoken opgeven", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Instellingen voor Pushbullet-meldingen opgeslagen!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Instellingen voor Pushbullet-meldingen konden niet opgeslagen worden.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "<PERSON><PERSON> ins<PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Toegangstoken", "components.Layout.UserDropdown.settings": "Instellingen", "components.Layout.UserDropdown.myprofile": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Je moet een geldige gebruikers-ID opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Het <FindDiscordIdLink>meercijferige ID-nummer</FindDiscordIdLink> van je gebruikersaccount", "components.CollectionDetails.requestcollection4k": "Col<PERSON><PERSON> a<PERSON>v<PERSON>n in 4K", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Inhoud filteren op regionale beschikbaarheid", "components.UserProfile.UserSettings.UserGeneralSettings.region": "<PERSON><PERSON> van Ontdekken", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Inhoud filteren op oorspronkelijke taal", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Taal voor Ontdekken", "components.Settings.webhook": "Webhook", "components.Settings.email": "E-mail", "components.RegionSelector.regionDefault": "Alle regio’s", "components.Discover.upcomingtv": "Verwachte series", "components.RegionSelector.regionServerDefault": "Standaard ({region})", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Je bent niet gemachtigd om het wachtwoord van deze gebruiker te wijzigen.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rol", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Accounttype", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.accounttype": "Type", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON><PERSON> taak", "components.Settings.SettingsJobsCache.download-sync-reset": "Reset download sync", "components.Settings.SettingsJobsCache.download-sync": "Synchronisatie downloads", "components.TvDetails.seasons": "{seasonCount, plural, one {# seizoen} other {# seizoenen}}", "i18n.loading": "<PERSON>den…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Je moet een geldige chat-ID opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Een chat starten</TelegramBotLink>, <GetIdBotLink>@get_id_bot</GetIdBotLink> toevoegen en de opdracht <code>/my_id</code> geven", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat-ID", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Meldingen versturen zonder geluid", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON> versturen", "components.Settings.Notifications.botUsername": "Geb<PERSON>ike<PERSON><PERSON><PERSON> bot", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} series", "components.Discover.DiscoverStudio.studioMovies": "Films van {studio}", "components.Discover.DiscoverNetwork.networkSeries": "Series van {network}", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} films", "components.Settings.scanning": "Synchroniseren…", "components.Settings.scan": "Bibliotheken synchroniseren", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr-scan", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr-scan", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex recent toegevoegde scan", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex volledige bibliotheekscan", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Volledige bibliotheekscan Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "<PERSON><PERSON> van 'onlangs toegevoegd' in Jellyfin", "components.Settings.Notifications.validationUrl": "Je moet een geldige URL opgeven", "components.Settings.Notifications.botAvatarUrl": "URL bot-avatar", "components.RequestList.RequestItem.requested": "Aangevraagd", "components.RequestList.RequestItem.modifieduserdate": "{date} door {user}", "components.RequestList.RequestItem.modified": "Gewijzigd", "components.Discover.StudioSlider.studios": "Studio's", "components.Discover.NetworkSlider.networks": "Netwerken", "components.Discover.DiscoverTvLanguage.languageSeries": "Series in het {language}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Films in het {language}", "components.UserProfile.ProfileHeader.userid": "Gebruikers-ID: {userid}", "components.UserProfile.ProfileHeader.joindate": "Lid geworden op {joindate}", "components.Settings.menuUsers": "Gebruikers", "components.Settings.SettingsUsers.userSettingsDescription": "Algemene en standaard gebruikersinstellingen configureren.", "components.Settings.SettingsUsers.userSettings": "Gebruikersinstellingen", "components.Settings.SettingsUsers.toastSettingsSuccess": "Gebruikersinstellingen opgeslagen!", "components.Settings.SettingsUsers.toastSettingsFailure": "<PERSON>r ging iets mis bij het opsla<PERSON> van de instellingen.", "components.Settings.SettingsUsers.localLogin": "Lokaal aanmelden inschakelen", "components.Settings.SettingsUsers.defaultPermissions": "Stand<PERSON><PERSON> machtigingen", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Een melding sturen wanneer een gebruiker nieuwe media aanvraagt die automatisch wordt goedgekeurd.", "components.NotificationTypeSelector.mediaAutoApproved": "Aanvraag automatisch goedgekeurd", "components.UserProfile.UserSettings.unauthorizedDescription": "Je hebt geen toestemming om de instellingen van deze gebruiker te wijzigen.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Je kan je eigen machtigingen niet wijzigen.", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuten", "components.TvDetails.episodeRuntime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.pgpPrivateKeyTip": "Versleutelde e-mailberichten ondertekenen met <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Privésleutel PGP", "components.Settings.Notifications.pgpPasswordTip": "Versleutelde e-mailberichten ondertekenen met <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Wachtwoord PGP", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.Discover.TvGenreSlider.tvgenres": "Seriegenres", "components.Discover.MovieGenreSlider.moviegenres": "Filmgenres", "components.RequestModal.alreadyrequested": "Al aangevraagd", "components.Discover.TvGenreList.seriesgenres": "Seriegenres", "components.Discover.MovieGenreList.moviegenres": "Filmgenres", "components.Settings.SettingsLogs.filterError": "Fout", "components.Settings.SettingsLogs.filterDebug": "Foutopsporing", "components.Settings.SettingsLogs.time": "Tijdstip", "components.Settings.SettingsLogs.showall": "Alle logboeken weergeven", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON>", "components.Settings.SettingsLogs.message": "Bericht", "components.Settings.SettingsLogs.logsDescription": "Je kunt deze logboeken ook rechtstreeks bekijken via <code>stdout</code>, of in <code>{appDataPath}/logs/jellyseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Logboeken", "components.Settings.SettingsLogs.level": "<PERSON>", "components.Settings.SettingsLogs.label": "Label", "components.Settings.SettingsLogs.filterWarn": "Waarschuwing", "components.Settings.SettingsLogs.filterInfo": "Info", "pages.somethingwentwrong": "Er ging iets mis", "pages.serviceunavailable": "Service niet be<PERSON>", "pages.pagenotfound": "<PERSON><PERSON><PERSON> niet gevonden", "pages.internalservererror": "Interne serverfout", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.usersettings": "Gebruikersinstellingen", "i18n.settings": "Instellingen", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Er ging iets mis bij het opslaan van het wachtwoord. Is je huidige wachtwoord correct ingevoerd?", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Meldingen", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.plex": "Plex", "components.Settings.notifications": "Meldingen", "components.Settings.SettingsUsers.users": "Gebruikers", "components.Settings.SettingsJobsCache.jobsandcache": "Taken en cache", "components.Settings.SettingsAbout.about": "Over", "components.ResetPassword.passwordreset": "Wachtwoord opnieuw instellen", "components.Settings.SettingsLogs.logDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "Aanvullende gegevens", "components.Settings.SettingsLogs.copyToClipboard": "<PERSON>ar klembord kopiëren", "components.Settings.SettingsLogs.copiedLogMessage": "Logbericht naar klembord gekopieerd.", "components.Settings.enablessl": "SSL gebruiken", "components.UserList.nouserstoimport": "<PERSON>r zijn geen Plex-gebruikers om te importeren.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "Geboren op {geboortedatum}", "components.PersonDetails.alsoknownas": "Ook bekend als: {names}", "i18n.delimitedlist": "{a}, {b}", "components.UserProfile.unlimited": "Onbeperkt", "components.UserProfile.totalrequests": "Totaal aantal aanvragen", "components.UserProfile.seriesrequest": "Serie-aanvragen", "components.UserProfile.requestsperdays": "{limit} resterend", "components.UserProfile.pastdays": "{type} (afgelopen {days} dagen)", "components.UserProfile.movierequests": "Filmaanvragen", "components.UserProfile.limit": "{remaining} van {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Aanvraaglimiet voor series", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Aanvraaglimiet voor films", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Globale limiet oversch<PERSON>ven", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globale aanvraaglimiet voor films", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globale aanvraaglimiet voor series", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {seizoen} other {seizoenen}}", "components.RequestModal.QuotaDisplay.season": "<PERSON><PERSON><PERSON>n", "components.RequestModal.QuotaDisplay.requiredquota": "Je hebt nog minstens <strong>{seasons}</strong> {seasons, plural, one {seizoensaanvraag} other {seizoensaanvragen}} nodig om deze serie aan te vragen.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON>} other {<strong>#</strong>}} {type}{remaining, plural, one {aanvraag} other {aanvragen}} over", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Je kunt een overzicht van de aanvraaglimieten van deze gebruiker bekijken op diens <ProfileLink>profielpagina</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Je kan een overzicht van je aanvraaglimieten bekijken op jouw <ProfileLink>profielpagina</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Onvoldoende seizoensaanvragen over", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {films}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Deze gebruiker mag elke <strong>{days}</strong> dagen <strong>{limit}</strong> {type} aanvragen.", "components.RequestModal.QuotaDisplay.allowedRequests": "Je mag elke <strong>{days}</strong> dagen <strong>{limit}</strong> {type} aanvragen.", "components.QuotaSelector.unlimited": "Onbeperkt", "i18n.view": "Bekijken", "i18n.tvshow": "Serie", "i18n.testing": "<PERSON>en…", "i18n.test": "Test", "i18n.status": "Status", "i18n.showingresults": "Resultaten <strong>{from}</strong> t/m <strong>{to}</strong> van <strong>{total}</strong> weergegeven", "i18n.saving": "<PERSON><PERSON><PERSON>…", "i18n.save": "Wijzigingen opslaan", "i18n.resultsperpage": "{pageSize} resultaten per pagina weergeven", "i18n.requesting": "Aanvragen…", "i18n.request4k": "Aanvragen in 4K", "i18n.previous": "Vorige", "i18n.notrequested": "<PERSON><PERSON>", "i18n.noresults": "<PERSON><PERSON> resultaten.", "i18n.next": "Volgende", "i18n.movie": "Film", "i18n.canceling": "<PERSON><PERSON><PERSON>…", "i18n.back": "Terug", "i18n.areyousure": "Weet je het zeker?", "i18n.all": "Alle", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Deze gebruiker heeft nog minstens <strong>{seasons}</strong> {seasons, plural, one {seizoensaanvraag} other {seizoensaanvragen}} nodig om deze serie aan te vragen.", "components.TvDetails.originaltitle": "Oorspronkelijke titel", "components.MovieDetails.originaltitle": "Oorspronkelijke titel", "components.LanguageSelector.originalLanguageDefault": "Alle talen", "components.LanguageSelector.languageServerDefault": "Standaard ({language})", "components.Settings.SonarrModal.testFirstTags": "Test de verbinding om labels te laden", "components.Settings.SonarrModal.tags": "Labels", "components.Settings.SonarrModal.selecttags": "Labels selecteren", "components.Settings.SonarrModal.notagoptions": "Geen labels.", "components.Settings.SonarrModal.loadingTags": "Labels laden…", "components.Settings.SonarrModal.edit4ksonarr": "4K Sonarr-server bewerken", "components.Settings.SonarrModal.default4kserver": "Standaard 4K-server", "components.Settings.SonarrModal.create4ksonarr": "Nieuwe 4K Sonarr-server toevoegen", "components.Settings.SonarrModal.animeTags": "Animelabels", "components.Settings.RadarrModal.testFirstTags": "Test de verbinding om labels te laden", "components.Settings.RadarrModal.tags": "Labels", "components.Settings.RadarrModal.selecttags": "Labels selecteren", "components.Settings.RadarrModal.notagoptions": "Geen labels.", "components.Settings.RadarrModal.edit4kradarr": "4K Radarr-server bewerken", "components.Settings.RadarrModal.default4kserver": "Standaard 4K-server", "components.Settings.RadarrModal.create4kradarr": "Nieuwe 4K Radarr-server toevoegen", "components.RequestModal.AdvancedRequester.tags": "Labels", "components.RequestModal.AdvancedRequester.selecttags": "Labels selecteren", "components.RequestModal.AdvancedRequester.notagoptions": "Geen labels.", "components.Settings.RadarrModal.loadingTags": "Labels laden…", "components.RequestList.RequestItem.mediaerror": "{mediaType} niet g<PERSON>nden", "components.RequestList.RequestItem.deleterequest": "Aanvraag verwijderen", "components.RequestCard.mediaerror": "{mediaType} niet g<PERSON>nden", "components.RequestCard.deleterequest": "Aanvraag verwijderen", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Je moet een geldige openbare PGP-sleutel opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Instellingen Telegrammeldingen opgeslagen!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "De instellingen voor Telegrammeldingen konden niet opgeslagen worden.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "E-mailberichten versleutelen met <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Openbare PGP-sleutel", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Instellingen voor e-mailmeldingen opgeslagen!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Instellingen voor e-mailmeldingen konden niet opgeslagen worden.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Instellingen voor Discord-meldingen opgeslagen!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Instellingen voor Discord-meldingen konden niet opgeslagen worden.", "components.Settings.Notifications.validationPgpPrivateKey": "Je moet een geldige PGP-privésleutel opgeven", "components.Settings.Notifications.validationPgpPassword": "Je moet een PGP-wachtwoord opgeven", "components.Settings.Notifications.botUsernameTip": "Sta gebruikers toe ook een chat met jouw bot te starten en hun eigen meldingen te configureren", "components.RequestModal.pendingapproval": "<PERSON> is in afwachting van goedkeuring.", "components.RequestList.RequestItem.cancelRequest": "Aanvraag annuleren", "components.NotificationTypeSelector.notificationTypes": "Meldingtypes", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Er is voor jouw account momenteel geen wachtwoord ingesteld. <PERSON><PERSON> hieronder een wachtwoord in om aanmelden als \"lokale gebruiker\" (met je e-mailadres) in te schakelen.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Dit gebruikersaccount heeft momenteel geen ingesteld wachtwoord. Configureer hieronder een wachtwoord zodat dit account zich kan aanmelden als een \"lokale gebruiker.\"", "components.Settings.serviceSettingsDescription": "<PERSON><PERSON> je {serverType}-server(s) hieronder in. Je kunt meerdere {serverType}-servers verbinden, maar slechts twee ervan kunnen als standaard worden gemarkeerd (<PERSON>én niet-4K en één 4K). Beheerders kunnen vóór goedkeuring de server aanpassen die voor nieuwe aanvragen gebruikt wordt.", "components.Settings.noDefaultServer": "Ten minste één {serverType}-server moet als standaard worden aangemerkt om {mediaType}aanvragen te kunnen verwerken.", "components.Settings.noDefaultNon4kServer": "Als je slechts <PERSON><PERSON> {serverType} server hebt voor zowel niet-4K als 4K-inhoud (of als je alleen 4K-inhoud downloadt), dan moet je {serverType} server <strong>NIET</strong> aangeduid worden als een 4K-server.", "components.Settings.mediaTypeSeries": "serie", "components.Settings.mediaTypeMovie": "film", "components.Settings.SettingsAbout.uptodate": "Bijgewerkt", "components.Settings.SettingsAbout.outofdate": "Vero<PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> ontwikkelversie", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> stabiel", "components.Layout.VersionStatus.outofdate": "Vero<PERSON><PERSON>", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} achter", "components.UserList.autogeneratepasswordTip": "Een door de server gegenereerd wachtwoord naar de gebruiker e-mailen", "i18n.retrying": "Opnieuw proberen…", "components.Settings.serverSecure": "<PERSON><PERSON>", "components.UserList.usercreatedfailedexisting": "Het opgegeven e-mailadres wordt al gebruikt door een andere gebruiker.", "components.RequestModal.edit": "Aanvraag a<PERSON>en", "components.RequestList.RequestItem.editrequest": "Aanvraag a<PERSON>en", "components.Settings.SonarrModal.enableSearch": "Automatisch zoeken inschakelen", "components.Settings.RadarrModal.enableSearch": "Automatisch zoeken inschakelen", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web-push", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Weergavetaal", "components.Settings.webpush": "Web-push", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Instellingen voor web-pushmeldingen opgeslagen!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Instellingen voor web-pushmeldingen zijn niet opgeslagen.", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profielnaam", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Instellingen voor web-pushmeldingen opgeslagen!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Instellingen voor web-pushmeldingen konden niet worden opgeslagen.", "components.Settings.noDefault4kServer": "Een 4K-{serverType}server moet als standaard worden gemarkeerd om gebruikers toe te laten om 4K-{mediaType} aan te vragen.", "components.Settings.is4k": "4K", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<PERSON><PERSON> vereist als je niet het <code>default</code> profiel gebruikt", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook-URL", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Je moet een geldige URL opgeven", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Instellingen voor meldingen LunaSea opgeslagen!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Instellingen voor meldingen LunaSea niet opgeslagen.", "components.Settings.SettingsUsers.newPlexLoginTip": "{mediaServerName}-gebruikers toestaan zich aan te melden zonder eerst geïmporteerd te zijn", "components.Settings.SettingsUsers.newPlexLogin": "Nieuwe {mediaServerName}-aanmelding inschakelen", "components.Settings.Notifications.toastTelegramTestSuccess": "Testmelding Telegram verzonden!", "components.Settings.Notifications.toastTelegramTestSending": "Testmelding Telegram verzenden…", "components.Settings.Notifications.toastTelegramTestFailed": "Testmelding Telegram niet verzonden.", "components.Settings.Notifications.toastEmailTestSuccess": "Testmail verzonden!", "components.Settings.Notifications.toastEmailTestSending": "<PERSON><PERSON> verzenden…", "components.Settings.Notifications.toastEmailTestFailed": "Testmail niet verzonden.", "components.Settings.Notifications.toastDiscordTestSuccess": "Testmelding Discord verzonden!", "components.Settings.Notifications.toastDiscordTestSending": "Testmelding Discord verzenden…", "components.Settings.Notifications.toastDiscordTestFailed": "Testmelding Discord niet verzonden.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Testmelding webhook verzonden!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Testmelding webhook verzenden…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Testmelding webhook niet verzonden.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Testmelding web-push verzonden!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Testmelding web-push verzenden…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Testmelding web-push niet verzonden.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Testmelding Slack verzonden!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Testmelding Slack verzenden…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Testmelding Slack niet verzonden.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Testmelding Pushover verzonden!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Testmelding Pushover verzenden…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Testmelding Pushover niet verzonden.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Testmelding Pushbullet verzonden!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Testmelding LunaSea verzenden…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Testmelding Pushbullet verzenden…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Testmelding Pushbullet niet verzonden.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Testmelding LunaSea verzonden!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Testmelding LunaSea niet verzonden.", "components.PermissionEdit.requestMoviesDescription": "Toestemming geven om niet-4K-films aan te vragen.", "components.PermissionEdit.requestTvDescription": "Toestemming geven om niet-4K-series aan te vragen.", "components.PermissionEdit.requestTv": "Series aanvragen", "components.PermissionEdit.requestMovies": "Films aanvragen", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Standaard ({language})", "components.DownloadBlock.estimatedtime": "G<PERSON><PERSON><PERSON> {time}", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Maak een <WebhookLink>inkomende webhook</WebhookLink>integratie aan", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Maak een bot</CreateBotLink> om te geb<PERSON><PERSON>n met <PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.webhookUrlTip": "Maak een <DiscordWebhookLink>webhook-integratie</DiscordWebhookLink> op je server", "components.Settings.Notifications.chatIdTip": "Start een chat met je bot, voeg <GetIdBotLink>@get_id_bot</GetIdBotLink> toe, en geef het <code>/my_id</code> commando", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Je <UsersGroupsLink>gebruikers- of groepsidentifier</UsersGroupsLink> van 30 tekens", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Een toepassing registreren</ApplicationRegistrationLink> om te gebruiken met <PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Maak een token aan vanuit je <PushbulletSettingsLink>accountinstellingen</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Je 'User' of '<PERSON><PERSON>' <LunaSeaLink>meldingswebhook-URL</LunaSeaLink>", "components.Settings.Notifications.encryptionTip": "In de meeste gevallen gebruikt impliciete TLS poort 465 en STARTTLS poort 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Altijd STARTTLS gebruiken", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Impliciete TLS gebruiken", "components.Settings.Notifications.encryptionDefault": "STARTTLS gebruiken indien beschikbaar", "components.Settings.Notifications.encryption": "Encryptiemethode", "components.Settings.webAppUrl": "URL van <WebAppLink>Web App</WebAppLink>", "components.Settings.webAppUrlTip": "Stuur gebruikers optioneel naar de web-app op uw server in plaats van de \"gehoste\" web-app", "components.UserList.localLoginDisabled": "De instelling <strong>Lokaal aanmelden inschakelen</strong> is momenteel uitgeschakeld.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Om web-pushmeldingen te ontvangen, moet Jellyseerr via HTTPS worden weergegeven.", "components.RequestList.RequestItem.requesteddate": "Aangevraagd", "components.RequestCard.failedretry": "Er ging op<PERSON>uw iets mis tijdens het aanvragen.", "components.Settings.SettingsUsers.localLoginTip": "Sta gebruikers toe zich aan te melden met hun e-mailadres en wachtwoord", "components.Settings.SettingsUsers.defaultPermissionsTip": "Initiële machtigingen toegekend aan nieuwe gebruikers", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {seizoen} other {seizoenen}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {film} other {films}}", "components.QuotaSelector.days": "{count, plural, one {dag} other {dagen}}", "components.NotificationTypeSelector.usermediaapprovedDescription": "<PERSON>en melding ontvangen wanneer je media-aanvragen goedgekeurd zijn.", "components.Settings.Notifications.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.NotificationTypeSelector.usermediarequestedDescription": "Een melding ontvangen wanneer andere gebruikers nieuwe media-aanvragen indienen waarvoor goedkeuring vereist is.", "components.NotificationTypeSelector.usermediafailedDescription": "Een melding ontvangen wanneer media-aanvragen niet kunnen worden toegevoegd aan Radarr of Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Een melding ontvangen wanneer je media-aanvragen worden geweigerd.", "components.NotificationTypeSelector.usermediaavailableDescription": "<PERSON>en melding ontvangen wanneer je media-aanvragen beschikbaar zijn.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON>en melding ontvangen wanneer andere gebruikers nieuwe media-aanvragen indienen die automatisch worden goedgekeurd.", "components.Settings.SettingsAbout.betawarning": "Dit is BETA-software. Functies kunnen kapot en/of instabiel zijn. Meld eventuele problemen op GitHub!", "components.Layout.LanguagePicker.displaylanguage": "Weergavetaal", "components.MovieDetails.showmore": "<PERSON><PERSON> tonen", "components.MovieDetails.showless": "<PERSON><PERSON> tonen", "components.TvDetails.streamingproviders": "Momenteel te streamen op", "components.MovieDetails.streamingproviders": "Momenteel te streamen op", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Taak bewerkt!", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Elk(e) {jobScheduleMinutes, plural, one {minuut} other {{jobScheduleMinutes} minuten}}", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "<PERSON>r ging iets mis bij het op<PERSON> van de ta<PERSON>.", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON> wij<PERSON>en", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Elk(e) {jobScheduleHours, plural, one {uur} other {{jobScheduleHours} uur}}", "components.Settings.SettingsAbout.runningDevelop": "Je voert de <code>develop</code>vers<PERSON> van <PERSON> uit, die alleen wordt aanbevolen als je bijdraagt aan de ontwikkeling of de allereerste versies helpt testen.", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "Weet je zeker dat je deze opmerking wilt verwijderen?", "components.IssueDetails.IssueComment.delete": "Opmerking verwijderen", "components.IssueDetails.IssueComment.edit": "Opmerking bewerken", "components.IssueDetails.IssueComment.postedby": "{relativeTime} gepost door {username}", "components.IssueDetails.IssueComment.postedbyedited": "{relativeTime} ingediend door {username} (bewerkt)", "components.IssueDetails.IssueComment.validationComment": "Je moet een bericht invoeren", "components.IssueDetails.problemepisode": "<PERSON><PERSON><PERSON><PERSON> aflevering", "components.IssueDetails.problemseason": "Getroffen seizoen", "components.ManageSlideOver.mark4kavailable": "<PERSON><PERSON> in 4K markeren", "components.ManageSlideOver.markavailable": "<PERSON><PERSON> besch<PERSON>", "components.ManageSlideOver.movie": "film", "components.IssueDetails.comments": "Opmerkingen", "components.ManageSlideOver.openarr": "Openen in {arr}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Gebruikers- of groepssleutel", "i18n.open": "Onopgelost", "i18n.resolved": "Opgelost", "components.IssueModal.CreateIssueModal.season": "Seizoen {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Probleem indienen", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Je moet een beschrijving geven", "components.IssueModal.CreateIssueModal.whatswrong": "Wat is er aan de hand?", "components.IssueModal.issueSubtitles": "Ondertiteling", "components.IssueModal.issueVideo": "Video", "components.ManageSlideOver.downloadstatus": "Downloads", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON>.", "components.IssueDetails.lastupdated": "Laatst bijgewerkt", "components.IssueDetails.IssueDescription.deleteissue": "Probleem verwijderen", "components.IssueDetails.IssueDescription.edit": "Beschrijving bewerken", "components.IssueDetails.allseasons": "<PERSON>e seizoenen", "components.IssueDetails.closeissue": "Probleem afsluiten", "components.IssueModal.CreateIssueModal.allepisodes": "Alle afleveringen", "components.IssueModal.issueAudio": "Audio", "components.IssueDetails.nocomments": "<PERSON><PERSON>.", "components.IssueModal.CreateIssueModal.reportissue": "Pro<PERSON>em melden", "components.IssueDetails.allepisodes": "Alle afleveringen", "components.IssueDetails.toasteditdescriptionsuccess": "Probleembeschrijving bewerkt!", "components.IssueDetails.toastissuedeleted": "Probleem verwijderd!", "components.IssueModal.CreateIssueModal.providedetail": "<PERSON><PERSON> een gedetailleerde uitleg van het probleem dat je bent tegengekomen.", "components.IssueDetails.toaststatusupdated": "Probleemstatus bijgewerkt!", "components.IssueDetails.closeissueandcomment": "Afsluiten met opmerking", "components.IssueModal.CreateIssueModal.problemseason": "Getroffen seizoen", "components.IssueDetails.openedby": "#{issueId} - {relativeTime} ingediend door {username}", "components.IssueDetails.IssueDescription.description": "Beschrijving", "components.NotificationTypeSelector.issuecommentDescription": "Melding sturen wanneer problemen nieuwe opmerkingen krijgen.", "components.IssueModal.CreateIssueModal.toastviewissue": "Pro<PERSON>em be<PERSON>jken", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Er ging iets mis bij het indienen van het probleem.", "components.IssueDetails.toastissuedeletefailed": "Er ging iets mis bij het verwijderen van het probleem.", "components.IssueDetails.toaststatusupdatefailed": "Er ging iets mis bij het updaten van de probleemstatus.", "components.IssueDetails.deleteissue": "Probleem verwijderen", "components.IssueDetails.episode": "Aflevering {episodeNumber}", "components.IssueDetails.issuepagetitle": "Probleem", "components.IssueDetails.issuetype": "Type", "components.IssueDetails.leavecomment": "Opmerking plaatsen", "components.IssueDetails.deleteissueconfirm": "Weet je zeker dat je dit probleem wilt verwijderen?", "components.IssueDetails.unknownissuetype": "Onbekend", "components.IssueDetails.openinarr": "Openen in {arr}", "components.IssueDetails.toasteditdescriptionfailed": "Er ging iets mis bij het bewerken van de beschrijving van het probleem.", "components.IssueList.IssueItem.issuetype": "Type", "components.IssueList.IssueItem.opened": "Ingediend", "components.IssueDetails.reopenissue": "Probleem opnieuw indienen", "components.IssueDetails.reopenissueandcomment": "Opnieuw indienen met opmerking", "components.IssueDetails.season": "Seizoen {seasonNumber}", "components.IssueList.showallissues": "Alle problemen weergeven", "components.IssueList.sortModified": "Laatst gewijzigd", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON>e seizoenen", "components.IssueModal.CreateIssueModal.episode": "Aflevering {episodeNumber}", "components.IssueList.issues": "<PERSON><PERSON>", "components.IssueList.sortAdded": "Meest recent", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.openeduserdate": "{date} door {user}", "components.IssueList.IssueItem.unknownissuetype": "Onbekend", "components.IssueList.IssueItem.viewissue": "Pro<PERSON>em be<PERSON>jken", "components.IssueList.IssueItem.problemepisode": "<PERSON><PERSON><PERSON><PERSON> aflevering", "components.IssueModal.CreateIssueModal.problemepisode": "<PERSON><PERSON><PERSON><PERSON> aflevering", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Probleemmelding voor <strong>{title}</strong> ingediend!", "components.PermissionEdit.viewissues": "<PERSON><PERSON> weergeven", "components.IssueModal.issueOther": "<PERSON>", "components.Layout.Sidebar.issues": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMedia": "G<PERSON>vens wissen", "components.ManageSlideOver.manageModalClearMediaWarning": "* Hiermee worden alle gegevens voor deze {mediaType} onomkeerbaar verwijderd, inclusief eventuele aanvragen. Als dit item in je {mediaServerName}-bibliotheek staat, worden de mediagegevens bij de volgende scan opnieuw aangemaakt.", "components.ManageSlideOver.manageModalRequests": "Aanvragen", "components.ManageSlideOver.manageModalTitle": "{mediaType} beheren", "components.ManageSlideOver.tvshow": "serie", "components.NotificationTypeSelector.userissueresolvedDescription": "Ontvang een melding wanneer problemen die jij hebt gemeld, opgel<PERSON> zijn.", "components.NotificationTypeSelector.issuecomment": "Reactie op probleem", "components.NotificationTypeSelector.issueresolvedDescription": "<PERSON><PERSON>ur meldingen wanneer problemen opgelost zijn.", "components.ManageSlideOver.openarr4k": "Openen in 4K-{arr}", "components.NotificationTypeSelector.adminissuecommentDescription": "Ontvang een melding wanneer andere gebruikers reageren op problemen.", "components.NotificationTypeSelector.userissuecommentDescription": "Ontvang een melding wanneer er nieuwe reacties komen op problemen die jij hebt gemeld.", "components.NotificationTypeSelector.userissuecreatedDescription": "Ontvang een melding wanneer andere gebruikers problemen melden.", "components.NotificationTypeSelector.issuecreated": "Pro<PERSON>em gemeld", "components.NotificationTypeSelector.issuecreatedDescription": "<PERSON>uur meldingen wanneer problemen worden gemeld.", "components.NotificationTypeSelector.issueresolved": "Probleem opgelost", "components.PermissionEdit.createissues": "<PERSON><PERSON> melden", "components.PermissionEdit.createissuesDescription": "Toestemming geven om mediaproblemen te melden.", "components.PermissionEdit.manageissues": "<PERSON><PERSON> beheren", "components.PermissionEdit.manageissuesDescription": "Toestemming geven om mediaproblemen te beheren.", "components.PermissionEdit.viewissuesDescription": "Toestemming geven om mediaproblemen te bekijken die door andere gebruikers zijn gemeld.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Maak een token aan in je <PushbulletSettingsLink>accountinstellingen</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Toegangstoken", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Instellingen voor Pushbullet-meldingen konden niet opgeslagen worden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Token toepassings-API", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Een toepassing registreren</ApplicationRegistrationLink> om te gebruiken met {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Je <UsersGroupsLink>gebruikers- of groepsidentifier</UsersGroupsLink> van 30 tekens", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Je moet een geldig toepassingstoken opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Instellingen voor Pushover-meldingen konden niet opgeslagen worden.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Instellingen voor Pushover-meldingen opgeslagen!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Je moet een toegangstoken opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Je moet een geldige gebruikers- of groepssleutel opgeven", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Instellingen voor Pushbullet-meldingen opgeslagen!", "components.IssueDetails.playonplex": "Afspelen op {mediaServerName}", "components.IssueDetails.play4konplex": "Afspelen op {mediaServerName} in 4K", "components.IssueDetails.openin4karr": "Openen in 4K {arr}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Aflevering} other {Afleveringen}}", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON><PERSON>}}", "components.ManageSlideOver.manageModalIssues": "Onopgeloste problemen", "components.IssueModal.CreateIssueModal.extras": "Extra's", "components.NotificationTypeSelector.adminissueresolvedDescription": "Ontvang een melding wanneer problemen worden opgelost door andere gebruikers.", "components.NotificationTypeSelector.issuereopened": "Probleem opnieuw ingediend", "components.NotificationTypeSelector.adminissuereopenedDescription": "Ontvang een melding wanneer problemen door andere gebruikers opnieuw worden ingediend.", "components.NotificationTypeSelector.issuereopenedDescription": "Stuur meldingen wanneer problemen opnieuw worden ingediend.", "components.NotificationTypeSelector.userissuereopenedDescription": "Ontvang een bericht wanneer problemen die jij hebt gemeld, opnieuw worden ingediend.", "components.RequestModal.requestseasons4k": "{seasonCount} {seasonCount, plural, one {seizoen} other {seizoenen}} aanvragen in 4K", "components.RequestModal.requestmovies": "{count} {count, plural, one {film} other {films}} aanvragen", "components.RequestModal.selectmovies": "Films selecteren", "components.MovieDetails.productioncountries": "Productie{countryCount, plural, one {land} other {landen}}", "components.RequestModal.requestmovies4k": "{count} {count, plural, one {film} other {films}} in 4K aanvragen", "components.TvDetails.productioncountries": "Productie{countryCount, plural, one {land} other {landen}}", "components.IssueDetails.commentplaceholder": "Opmerking toevoegen…", "components.RequestModal.requestApproved": "Aanv<PERSON>ag voor <strong>{title}</strong> goedgekeurd!", "components.RequestModal.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON> go<PERSON>", "components.Settings.RadarrModal.inCinemas": "In de bioscoop", "components.Settings.RadarrModal.released": "Uitgebracht", "components.Settings.RadarrModal.announced": "Aangekondigd", "components.Settings.Notifications.enableMentions": "Vermeldingen inschakelen", "components.Settings.Notifications.NotificationsGotify.agentenabled": "<PERSON><PERSON> ins<PERSON>", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Instellingen voor meldingen Gotify opgeslagen!", "components.Settings.Notifications.NotificationsGotify.token": "Toepassingstoken", "i18n.importing": "Importeren…", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Instellingen voor meldingen Gotify niet opgeslagen.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Testmelding Gotify niet verzonden.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Testmelding Gotify verzonden!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Testmelding Gotify verzenden…", "components.Settings.Notifications.NotificationsGotify.url": "URL server", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Je moet een toepassingstoken opgeven", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Je moet ten minste één meldingstype selecteren", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL mag niet eindigen op een schuine streep", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Je moet een geldige URL opgeven", "i18n.import": "Importeren", "components.UserList.newplexsigninenabled": "De instelling <strong><PERSON><PERSON><PERSON> Plex-aanmelding inschakelen</strong> is momenteel ingeschakeld. Plex-gebruikers met bibliotheektoegang hoeven niet te worden geïmporteerd om in te loggen.", "components.ManageSlideOver.manageModalAdvanced": "Geavanceerd", "components.ManageSlideOver.alltime": "Altijd", "components.ManageSlideOver.markallseasons4kavailable": "Alle seizoenen markeren als beschikbaar in 4K", "components.ManageSlideOver.opentautulli": "Openen in Tautulli", "components.ManageSlideOver.pastdays": "Afgelopen {days, number} dagen", "components.ManageSlideOver.playedby": "Afgespeeld door", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {keer afgespeeld} other {keer afgespeeld}}", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "<PERSON><PERSON><PERSON>-tag", "components.Settings.externalUrl": "Externe URL", "components.Settings.tautulliApiKey": "API-sleutel", "components.Settings.tautulliSettings": "Instellingen <PERSON>", "components.Settings.tautulliSettingsDescription": "Configureer <PERSON><PERSON> de instellingen van jouw Tau<PERSON>lli-server. <PERSON><PERSON><PERSON><PERSON> haalt de kijkgeschiedenis van jouw Plex-media op van Tautulli.", "components.Settings.toastTautulliSettingsFailure": "<PERSON>r ging iets mis bij het op<PERSON><PERSON>lli-instellingen.", "components.Settings.urlBase": "URL-basis", "components.Settings.validationUrlBaseTrailingSlash": "URL-basis mag niet eindigen op een schuine streep", "components.Settings.validationUrlTrailingSlash": "URL mag niet eindigen op een schuine streep", "components.UserProfile.recentlywatched": "Recent bekeken", "components.ManageSlideOver.markallseasonsavailable": "Alle seizoenen als beschikbaar markeren", "components.ManageSlideOver.manageModalMedia4k": "4K-media", "components.ManageSlideOver.manageModalMedia": "Media", "components.Settings.validationApiKey": "Je moet een API-sleutel opgeven", "components.Settings.toastTautulliSettingsSuccess": "Instellingen Tautulli opgeslagen!", "components.Settings.validationUrl": "Je moet een geldige URL opgeven", "components.Settings.validationUrlBaseLeadingSlash": "URL-basis moe<PERSON> met een schuine streep beginnen", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Gebruikers-ID Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Het <FindDiscordIdLink>meercijferige ID-nummer</FindDiscordIdLink> van je Discord-account", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Je moet een geldige gebruikers-<PERSON> Discord opgeven", "components.Settings.SettingsAbout.appDataPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.languageprofile": "Taalprofiel", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "<PERSON><PERSON><PERSON> frequentie", "components.StatusBadge.managemedia": "{mediaType} beheren", "components.StatusBadge.openinarr": "Openen in {arr}", "components.StatusBadge.playonplex": "Afspelen op {mediaServerName}", "components.UserProfile.emptywatchlist": "Media die zijn toegevoegd aan je <PlexWatchlistSupportLink>Plex-kijklijst</PlexWatchlistSupportLink> verschi<PERSON><PERSON> hier.", "components.MovieDetails.digitalrelease": "Digitale uitgave", "i18n.restartRequired": "Opnieuw opstarten vereist", "components.PermissionEdit.viewrecentDescription": "Toestemming geven om de lij<PERSON> met onlangs toegevoegde media weer te geven.", "components.PermissionEdit.viewrecent": "Onlangs toegevoegd weergeven", "components.Settings.deleteServer": "{serverType}-server verwijderen", "components.StatusChecker.appUpdated": "{applicationTitle} bijgewerkt", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB-ID", "components.StatusChecker.restartRequired": "Server opnieuw opstarten vereist", "components.StatusChecker.restartRequiredDescription": "Start de server opnieuw op om de bijgewerkte instellingen toe te passen.", "components.TitleCard.cleardata": "G<PERSON>vens wissen", "components.TitleCard.mediaerror": "{mediatype} niet gevonden", "components.TitleCard.tvdbid": "TheTVDB-ID", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.declinerequest": "Aanvraag weigeren", "components.RequestCard.editrequest": "Aanvraag a<PERSON>en", "components.RequestCard.cancelrequest": "Aanvraag annuleren", "components.RequestModal.requestcollection4ktitle": "Col<PERSON><PERSON> a<PERSON>v<PERSON>n in 4K", "components.RequestModal.requestcollectiontitle": "Collectie a<PERSON>vragen", "components.RequestModal.requestseries4ktitle": "Serie aanvragen in 4K", "components.RequestModal.requestmovie4ktitle": "Film aanvragen in 4K", "components.RequestModal.requestseriestitle": "Serie aanvragen", "components.RequestModal.requestmovietitle": "Film aanvragen", "components.TvDetails.tmdbuserscore": "Gebruikersscore TMDB", "components.TvDetails.rtaudiencescore": "Publieksscore Rotten Tomatoes", "components.TvDetails.seasonnumber": "Seizoen {seasonNumber}", "components.TvDetails.Season.somethingwentwrong": "Er ging iets mis bij het ophalen van de seizoensgegevens.", "components.TvDetails.seasonstitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverWatchlist.discoverwatchlist": "<PERSON><PERSON><PERSON>", "components.Discover.plexwatchlist": "<PERSON><PERSON><PERSON>", "components.MovieDetails.physicalrelease": "Fysieke uitgave", "components.PermissionEdit.autorequest": "Automatisch aanvragen", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex-kijklijst synchroniseren", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Series automatisch aanvragen", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatisch series op je <PlexWatchlistSupportLink>Plex-kijklijst</PlexWatchlistSupportLink> aanvragen", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatisch films op je <PlexWatchlistSupportLink>Plex-kijklijst</PlexWatchlistSupportLink> aanvragen", "components.PermissionEdit.autorequestDescription": "Toestemming geven om niet-4K media in je Plex-kijklijst automatisch aan te vragen.", "components.RequestCard.tvdbid": "TheTVDB-ID", "components.Discover.DiscoverWatchlist.watchlist": "Plex-kijklijst", "components.MovieDetails.theatricalrelease": "Bioscoopuitgave", "components.NotificationTypeSelector.mediaautorequested": "Aanvraag automatisch ingediend", "components.NotificationTypeSelector.mediaautorequestedDescription": "Ontvang een melding wanneer er automatisch nieuwe media-aanvragen worden ingediend voor items op je kijklijst.", "components.PermissionEdit.autorequestSeriesDescription": "Toestemming geven om niet-4K series in je Plex-kijklijst automatisch aan te vragen.", "components.PermissionEdit.viewwatchlists": "Plex-kijklijsten bekijken", "components.PermissionEdit.viewwatchlistsDescription": "Toestemming verlenen om de Plex-kijklijsten van andere gebruikers te bekijken.", "components.Settings.SettingsLogs.viewdetails": "Details bekijken", "components.Settings.advancedTooltip": "<PERSON><PERSON> deze instelling onjuist wordt geconfigureerd, kan dit de functionaliteit verstoren", "components.StatusChecker.reloadApp": "{applicationTitle} opnieuw laden", "components.TitleCard.tmdbid": "TMDB ID", "components.StatusChecker.appUpdatedDescription": "Klik op de onderstaande knop om de toepassing opnieuw te laden.", "components.UserProfile.plexwatchlist": "Plex-kijklijst", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Films automatisch aanvragen", "components.TvDetails.manageseries": "Serie beheren", "components.MovieDetails.managemovie": "Film beheren", "components.MovieDetails.reportissue": "Pro<PERSON>em melden", "components.PermissionEdit.autorequestMoviesDescription": "Toestemming geven om niet-4K films in je Plex-kijklijst automatisch aan te vragen.", "components.PermissionEdit.autorequestSeries": "Series automatisch aanvragen", "components.PermissionEdit.autorequestMovies": "Films automatisch aanvragen", "components.Settings.experimentalTooltip": "Het inscha<PERSON>en van deze instelling kan leiden tot onverwacht gedrag van de toe<PERSON>", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON>rr moet opnieuw worden gestart om wijzigingen in deze instelling door te voeren", "components.AirDateBadge.airedrelative": "{relativeTime} uitgezonden", "components.AirDateBadge.airsrelative": "Uitzending {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Serie-aanvragen", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# aflevering} other {# afleveringen}}", "components.TvDetails.status4k": "4K {status}", "components.MovieDetails.rtaudiencescore": "Publieksscore Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatometer Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Gebruikersscore TMDB", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON><PERSON><PERSON> go<PERSON>", "components.TvDetails.reportissue": "Pro<PERSON>em melden", "components.TvDetails.rtcriticsscore": "Tomatometer Rotten Tomatoes", "components.RequestModal.SearchByNameModal.nomatches": "We konden geen match vinden voor deze serie.", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Filmaanvragen", "components.Layout.UserDropdown.requests": "Aanvragen", "components.RequestBlock.decline": "Aanvraag weigeren", "components.Discover.emptywatchlist": "Media die zijn toegevoegd aan je <PlexWatchlistSupportLink>Plex-kijklijst</PlexWatchlistSupportLink> verschi<PERSON><PERSON> hier.", "components.RequestBlock.delete": "Aanvraag verwijderen", "components.RequestBlock.edit": "Aanvraag a<PERSON>en", "components.RequestBlock.lastmodifiedby": "Laatst gewijzigd door", "components.RequestBlock.requestdate": "Aanvraagdatum", "components.RequestBlock.requestedby": "Aangevraagd door", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON><PERSON><PERSON> go<PERSON>", "components.TvDetails.Season.noepisodes": "Afleveringenlijst niet be<PERSON>.", "components.Settings.SettingsJobsCache.imagecache": "Afbeeldingcache", "components.Settings.SettingsJobsCache.imagecachecount": "Afbeeldingen in cache", "components.Settings.SettingsJobsCache.imagecachesize": "Totale cachegrootte", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Afbeeldingcache legen", "components.Settings.SettingsJobsCache.imagecacheDescription": "<PERSON><PERSON> ingeschakeld in de instellingen, zal <PERSON><PERSON><PERSON>rr afbeeldingen proxyen en cachen van vooraf geconfigureerde externe bronnen. Gecachete afbeeldingen worden opgeslagen in je configuratiemap. Je kan de bestanden vinden in <code>{appDataPath}/cache/images</code>.", "components.DownloadBlock.formattedTitle": "{title}: se<PERSON><PERSON>n {seasonNumber} aflevering {episodeNumber}", "components.RequestCard.unknowntitle": "On<PERSON>ende titel", "components.RequestList.RequestItem.unknowntitle": "On<PERSON>ende titel", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.CreateSlider.needresults": "Je moet minstens 1 resultaat hebben.", "components.Discover.CreateSlider.providetmdbgenreid": "Geef een TMDB Genre ID op", "components.Discover.CreateSlider.providetmdbnetwork": "Geef een TMDB Network ID op", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON> resultaten.", "components.Discover.CreateSlider.providetmdbkeywordid": "Geef een TMDB Keyword ID op", "components.Discover.DiscoverSliderEdit.enable": "<PERSON>ichtbaar<PERSON><PERSON> in-/uitschakelen", "components.Discover.CreateSlider.providetmdbsearch": "<PERSON>f een zoekopdracht op", "components.Discover.CreateSlider.searchGenres": "Genres zoeken…", "components.Discover.CreateSlider.providetmdbstudio": "Geef een TMDB Studio ID op", "components.Discover.moviegenres": "Filmgenres", "components.Discover.CreateSlider.searchKeywords": "Trefwoorden zoeken…", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} serie", "components.Discover.customizediscover": "Ontdekken aanpassen", "components.Discover.DiscoverSliderEdit.remove": "Verwijderen", "components.Discover.resetfailed": "Er is iets fout gegaan bij het resetten van de instellingen van Ontdekken.", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Media die zijn toegevoegd aan je <PlexWatchlistSupportLink>Plex-kijklijst</PlexWatchlistSupportLink> verschi<PERSON><PERSON> hier.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "<PERSON><PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Onlangs toegevoegd", "components.Discover.networks": "Netwerken", "components.Discover.CreateSlider.searchStudios": "Studio's zoeken…", "components.Discover.CreateSlider.starttyping": "Begin met typen om te zoeken.", "components.Discover.CreateSlider.validationDatarequired": "Je moet een gegevenswaarde opgeven.", "components.Discover.CreateSlider.validationTitlerequired": "Je moet een titel opgeven.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} films", "components.Discover.tmdbmoviekeyword": "TMDB-filmtrefwoord", "components.Discover.tmdbnetwork": "TMDB-netwerk", "components.Discover.tmdbsearch": "TMDB-zoekopdracht", "components.Settings.SettingsMain.apikey": "API-sleutel", "components.Settings.SettingsMain.cacheImagesTip": "Externe afbeeldingen cachen (vereist veel schijfruimte)", "components.Settings.SettingsMain.generalsettingsDescription": "Algemene en standaardinstellingen van Je<PERSON>seerr configureren.", "components.Settings.SettingsMain.general": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.generalsettings": "Algemene instellingen", "components.Settings.SettingsMain.hideAvailable": "Beschikbare media verbergen", "components.Settings.SettingsMain.toastSettingsSuccess": "Instellingen opgeslagen!", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON>lider succesvol verwijderd.", "components.Discover.createnewslider": "<PERSON><PERSON><PERSON> slider maken", "components.Discover.resetwarning": "Zet alle sliders terug naar standaard. Dit zal ook alle aangepaste sliders verwijderen!", "components.Discover.stopediting": "Stop met bewerken", "components.Discover.studios": "Studio's", "components.Discover.tmdbmoviegenre": "TMDB-filmgenre", "components.Discover.tmdbstudio": "TMDB-studio", "components.Discover.tmdbtvgenre": "TMDB-genre serie", "components.Discover.tmdbtvkeyword": "TMDB-trefwoord serie", "components.Discover.updatesuccess": "Instellingen Ontdekken bijgewerkt.", "components.Discover.DiscoverMovies.sortPopularityAsc": "Populariteit oplopend", "components.Discover.DiscoverMovies.sortPopularityDesc": "Populariteit aflopend", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Verschijningsdatum oplopend", "components.Discover.DiscoverMovies.sortTitleAsc": "Titel oplopend (A-Z)", "components.Discover.DiscoverMovies.sortTitleDesc": "Titel aflopend (Z-A)", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB-beoordeling oplopend", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB-beoordeling aflopend", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON><PERSON>r verwijderen mislukt.", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# filter actief} other {# filters actief}}", "components.Discover.DiscoverTv.discovertv": "Series", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Eerste uitzenddatum oplopend", "components.Discover.DiscoverTv.sortPopularityDesc": "Populariteit aflopend", "components.Discover.DiscoverTv.sortTitleAsc": "Titel oplopend (A-Z)", "components.Discover.DiscoverTv.sortTitleDesc": "Titel aflopend (Z-A)", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# filter actief} other {# filters actief}}", "components.Discover.FilterSlideover.clearfilters": "Actieve filters wissen", "components.Discover.FilterSlideover.filters": "Filters", "components.Discover.FilterSlideover.firstAirDate": "Eerste uitzenddatum", "components.Discover.FilterSlideover.from": "<PERSON>", "components.Discover.FilterSlideover.genres": "Genres", "components.Discover.FilterSlideover.keywords": "Trefwoorden", "components.Discover.FilterSlideover.originalLanguage": "Oorspronkelijke taal", "components.Discover.FilterSlideover.ratingText": "Beoordelingen tussen {minValue} en {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Verschijningsdatum", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minuten looptijd", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB-gebruikersscore", "components.Discover.FilterSlideover.to": "<PERSON><PERSON>", "components.Discover.resetsuccess": "Instellingen Ontdekken succesvol teruggezet.", "components.Discover.resettodefault": "Terugzetten naar standaard", "components.Discover.tvgenres": "Seriegenre", "components.Discover.updatefailed": "Er is iets fout gegaan bij het bijwerken van de instellingen van Ontdekken.", "components.Layout.Sidebar.browsemovies": "Films", "components.Selector.nooptions": "<PERSON><PERSON> resultaten.", "components.Selector.searchStudios": "Studio's zoeken…", "components.Selector.starttyping": "Begin met typen om te zoeken.", "components.Settings.SettingsMain.applicationTitle": "Toepassingstitel", "components.Settings.SettingsMain.locale": "Weergavetaal", "components.Settings.SettingsMain.originallanguage": "Taal voor Ontdekken", "components.Settings.SettingsMain.originallanguageTip": "Inhoud filteren op oorspronkelijke taal", "components.Settings.SettingsMain.partialRequestsEnabled": "Gedeeltelijke serie-aanvragen toestaan", "components.Settings.SettingsMain.toastApiKeyFailure": "Er ging iets mis bij het genereren van een nieuwe API-sleutel.", "components.Settings.SettingsMain.toastApiKeySuccess": "Nieuwe API-sleutel gegenereerd!", "components.Settings.SettingsMain.validationApplicationTitle": "Je moet een toepassingstitel opgeven", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL mag niet eindigen op een schuine streep", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# filter actief} other {# filters actief}}", "components.Discover.DiscoverMovies.discovermovies": "Films", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB-beoordeling aflopend", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Verschijningsdatum aflopend", "components.Discover.DiscoverTv.sortPopularityAsc": "Populariteit oplopend", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB-beoordeling oplopend", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Eerste uitzenddatum aflopend", "components.Settings.SettingsMain.applicationurl": "Toepassings-URL", "components.Layout.Sidebar.browsetv": "Series", "components.Selector.searchGenres": "Genres selecteren…", "components.Selector.searchKeywords": "Trefwoorden zoeken…", "components.Settings.SettingsMain.cacheImages": "Afbeeldingscaching inschakelen", "components.Settings.SettingsMain.validationApplicationUrl": "Je moet een geldige URL opgeven", "components.Settings.SettingsMain.toastSettingsFailure": "<PERSON>r ging iets mis bij het opsla<PERSON> van de instellingen.", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "Aangepaste slider maken", "components.Discover.CreateSlider.addfail": "Nieuwe slider maken mislukt.", "components.Discover.CreateSlider.addsuccess": "Nieuwe slider aangemaakt en instellingen Ontdekken opgeslagen.", "components.Discover.CreateSlider.editSlider": "Slide<PERSON> bewerken", "components.Discover.CreateSlider.editfail": "Slider bewerken mislukt.", "components.Discover.CreateSlider.editsuccess": "Slider bewerkt en instellingen Ontdekken opgeslagen.", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON> slider", "components.Discover.FilterSlideover.streamingservices": "Streamingdiensten", "components.Selector.showless": "<PERSON><PERSON> tonen", "components.Selector.showmore": "<PERSON><PERSON> tonen", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Elke {jobScheduleSeconds, plural, one {seconde} other {{jobScheduleSeconds} seconden}}", "components.Settings.SettingsJobsCache.availability-sync": "Synchronisatie van mediabeschikbaarheid", "components.Discover.tmdbmoviestreamingservices": "Streamingdiensten voor films TMDB", "components.Discover.tmdbtvstreamingservices": "Streamingdiensten voor series TMDB", "components.Login.validationhostrequired": "{mediaServerName}-URL vereist", "components.Layout.UserWarnings.emailInvalid": "E-mailadres is ongeldig.", "components.Login.description": "<PERSON>angez<PERSON> dit de eerste keer is dat je je aanmeldt bij {applicationName}, dien je een geldig e-mailadres op te geven.", "components.Login.saving": "Toevoegen…", "components.ManageSlideOver.removearr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van {arr}", "components.Settings.RadarrModal.tagRequests": "Aanvragen taggen", "components.MovieDetails.openradarr4k": "Film openen in 4K-Radarr", "components.Settings.RadarrModal.tagRequestsInfo": "Automatisch een extra label toevoegen met de gebruikers-id en weergavenaam van de aanvrager", "components.Settings.SonarrModal.animeSeriesType": "Serietype anime", "components.Settings.SonarrModal.tagRequestsInfo": "Automatisch een extra label toevoegen met de gebruikers-id en weergavenaam van de aanvrager", "components.Settings.jellyfinsettings": "{mediaServerName}-instellingen", "components.Settings.jellyfinlibrariesDescription": "De {mediaServerName}-bibliotheken die op titels worden gescand. Klik op onderstaande knop als er geen bibliotheken in de lijst staan.", "components.Settings.manualscanDescriptionJellyfin": "Normaliter wordt dit eenmaal per 24 uur uitgevoerd. <PERSON><PERSON><PERSON><PERSON> zal de lij<PERSON> met onlangs toegevoegde media op je {mediaServerName}-server vaker controleren. Als dit de eerste keer is dat je Je<PERSON><PERSON><PERSON> instelt, wordt aanbevolen eenmalig een handmatige, volledige bibliotheekscan uit te voeren!", "components.Settings.save": "Wijzigingen opslaan", "components.Settings.syncJellyfin": "Bibliotheken synchoniseren", "components.TvDetails.play": "Afspelen op {mediaServerName}", "components.Discover.FilterSlideover.tmdbuservotecount": "Aantal gebruikersstemmen TMDB", "components.Login.save": "Toevoegen", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Hi<PERSON>mee wordt deze {mediaType} onomkeerbaar verwijderd van {arr}, inclusief alle bestanden.", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Apparaatstandaard", "components.Settings.Notifications.userEmailRequired": "Gebruikerse-mail vereisen", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON><PERSON> ondersteunen", "components.Settings.SonarrModal.seriesType": "Serietype", "components.Settings.jellyfinSettings": "{mediaServerName}-instellingen", "components.Setup.configuremediaserver": "Mediaserver instellen", "components.TvDetails.play4k": "Afspelen op {mediaServerName} in 4K", "components.UserList.mediaServerUser": "{mediaServerName}-gebruiker", "components.UserList.noJellyfinuserstoimport": "<PERSON>r zijn geen {mediaServerName}-gebruikers om te importeren.", "components.UserProfile.UserSettings.UserGeneralSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Apparaatstandaard", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Meldingsgel<PERSON>", "components.Login.signinwithjellyfin": "{mediaServerName} account gebruiken", "components.Discover.FilterSlideover.voteCount": "Aantal stemmen tussen {minValue} en {maxValue}", "components.Layout.UserWarnings.emailRequired": "<PERSON>en e-mail<PERSON><PERSON> is vereist.", "components.Layout.UserWarnings.passwordRequired": "<PERSON><PERSON> wachtwo<PERSON> is vereist.", "components.Login.credentialerror": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of wachtwoord is onjuist.", "components.Login.emailtooltip": "Het adres hoeft niet gelieerd te zijn aan je {mediaServerName}-instantie.", "components.Login.initialsignin": "Verbinden", "components.Login.initialsigningin": "<PERSON><PERSON><PERSON><PERSON>…", "components.Login.title": "E-mail toevoegen", "components.Login.username": "Gebruikersnaam", "components.Login.validationEmailRequired": "Je moet een e-mailadres opgeven", "components.Login.validationEmailFormat": "Ongeldig e-mailadres", "components.Login.validationemailformat": "Geldig e-mailadres vereist", "components.Login.validationhostformat": "Geldige URL vereist", "components.Login.validationusernamerequired": "Gebruikersnaam vereist", "components.ManageSlideOver.removearr4k": "Verwijderen van 4K-{arr}", "components.MovieDetails.downloadstatus": "Downloadstatus", "components.MovieDetails.imdbuserscore": "Gebruikersbeoordeling IMDB", "components.MovieDetails.openradarr": "Film openen in Radarr", "components.MovieDetails.play": "Afspelen op {mediaServerName}", "components.MovieDetails.play4k": "Afspelen op {mediaServerName} in 4K", "components.Settings.Notifications.NotificationsPushover.sound": "Meldingsgel<PERSON>", "components.Settings.SonarrModal.tagRequests": "Aanvragen taggen", "components.Settings.jellyfinSettingsFailure": "Er is iets misgegaan bij het op<PERSON><PERSON> van de {mediaServerName}-instellingen.", "components.Settings.jellyfinSettingsSuccess": "{mediaServerName}-instellingen opgeslagen!", "components.Settings.jellyfinlibraries": "{mediaServerName}-bibliotheken", "components.Settings.manualscanJellyfin": "Handmatige bibliotheekscan", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.saving": "<PERSON><PERSON><PERSON>…", "components.Settings.syncing": "Synchroniseren", "components.Settings.timeout": "Time-out", "components.Setup.signin": "Aanmelden", "components.Setup.signinWithJellyfin": "<PERSON><PERSON> de Jelly<PERSON> g<PERSON> in", "components.TitleCard.addToWatchList": "Toevoegen aan kijklijst", "components.TitleCard.watchlistError": "Er is iets misgegaan. Probeer het opnieuw.", "components.UserList.importfromJellyfin": "{mediaServerName}-gebruikers importeren", "components.UserList.importfromJellyfinerror": "Er is iets misgegaan bij het importeren van {mediaServerName}-gebruikers.", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "{mediaServerName}-gebruiker", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Wijzigingen opslaan", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "<PERSON><PERSON><PERSON>…", "i18n.collection": "Col<PERSON><PERSON>", "components.UserProfile.localWatchlist": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> {username}", "components.Setup.signinWithPlex": "Vul de Plex gegevens in", "components.Settings.jellyfinSettingsDescription": "Configureer optioneel de interne en externe eindpunten voor je {mediaServerName}-server. Meestal verschilt de externe URL van de interne URL. Als je wilt doorverwijzen naar een andere pagina voor wachtwoordherstel, kun je een aangepaste URL voor wachtwoordherstel instellen voor het aanmelden met {mediaServerName}. Je kunt ook de API-sleutel voor Jellyfin wijzigen, die eerder automatisch is gegenereerd.", "components.Settings.jellyfinsettingsDescription": "Configureer de instellingen voor uw {mediaServerName} server. {mediaServerName} scanned uw {mediaServerName} bibliotheken om te zien welke content beschikbaar is.", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> is ver<PERSON><PERSON><PERSON><PERSON> van de kijk<PERSON>j<PERSON>!", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> is toege<PERSON><PERSON>d aan de kijklijst!", "components.TitleCard.watchlistCancel": "kijk<PERSON><PERSON><PERSON> voor <strong>{title}</strong> is gean<PERSON><PERSON><PERSON>.", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName}-{userCount, plural, one {gebruiker} other {gebruikers}} geïmporteerd!", "components.UserList.newJellyfinsigninenabled": "De instelling <strong>Nieuwe {mediaServerName}-aan<PERSON><PERSON> gebruiken</strong> is momenteel ingeschakeld. {mediaServerName}-gebruike<PERSON> met toegang tot de bibliotheek hoeven niet geïmporteerd te worden om zich te kunnen aanmelden.", "components.Login.back": "Ga terug", "components.Login.invalidurlerror": "Kan geen verbinding maken met de {mediaServerName} server.", "components.TvDetails.addtowatchlist": "Toevoegen aan kijklijst", "components.Login.validationUrlBaseTrailingSlash": "URL basis mag niet eindigen met een schuine streep", "components.Selector.returningSeries": "Terugkerende serie", "components.MovieDetails.addtowatchlist": "Toevoegen aan kijklijst", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> is ver<PERSON><PERSON><PERSON><PERSON> van je kijklijst!", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> is toegevo<PERSON>d aan je kijklijst!", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> is ver<PERSON><PERSON><PERSON><PERSON> van de kijk<PERSON>j<PERSON>!", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Geldig e-mailadres vereist", "components.Login.adminerror": "Je moet een beheerdersaccount geb<PERSON>iken om je aan te melden.", "components.Selector.inProduction": "In productie", "components.Discover.FilterSlideover.status": "Status", "components.Login.hostname": "{mediaServerName} URL", "components.Login.port": "Poort", "components.Login.servertype": "Servertype", "components.Login.validationHostnameRequired": "Je moet een geldige hostnaam of IP-adres opgeven", "components.Login.validationPortRequired": "Je moet een geldig poortnummer opgeven", "components.Login.validationUrlBaseLeadingSlash": "URL-basis moet beginnen met een schuine streep", "components.Login.validationUrlTrailingSlash": "URL mag niet eindigen met een schuine streep", "components.Login.validationservertyperequired": "<PERSON><PERSON> een servertype", "components.MovieDetails.removefromwatchlist": "Verwijderen van kijklijst", "components.MovieDetails.watchlistError": "Er is iets misgegaan. Probeer het opnieuw.", "components.RequestList.RequestItem.profileName": "<PERSON><PERSON>", "components.Selector.canceled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Selector.ended": "Afgelopen", "components.Selector.pilot": "Pilot", "components.Selector.planned": "<PERSON><PERSON><PERSON>", "components.Selector.searchStatus": "Selecteer status...", "components.Settings.invalidurlerror": "<PERSON><PERSON> niet verbinden met de {mediaServerName} server.", "components.Settings.jellyfinForgotPasswordUrl": "Wachtwoord vergeten URL", "components.Settings.jellyfinSyncFailedGenericError": "Er is iets misgegaan bij het synchroniseren van bibliotheken", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Geen bibliotheken gevonden", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Aangepaste authenticatie met automatische bibliotheekgroepering wordt niet ondersteund", "components.Setup.back": "Ga terug", "components.Setup.configemby": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configjellyfin": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configplex": "Configureer Plex", "components.Setup.servertype": "Kies servertype", "components.Setup.signinWithEmby": "<PERSON>ul de Emby gegevens in", "components.Setup.subtitle": "<PERSON><PERSON> met het kie<PERSON> van je mediaserver", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.TvDetails.removefromwatchlist": "Verwijderen uit kijklijst", "components.TvDetails.watchlistError": "Er is iets misgegaan. Probeer het opnieuw.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> is toege<PERSON><PERSON>d aan de kijklijst!", "components.UserList.username": "Gebruikersnaam", "components.UserList.validationUsername": "Je moet een gebruikersnaam opgeven", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "E-mailadres vereist", "components.Login.enablessl": "Gebruik SSL", "components.Login.urlBase": "URL basis", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Streamingdiensten tonen op regionale beschikbaarheid", "component.BlacklistBlock.blacklistdate": "<PERSON><PERSON> g<PERSON>", "components.Settings.scanbackground": "Het scannen gebeurt op de achtergrond. In de tussentijd kun je verde<PERSON><PERSON> met het instelpro<PERSON>.", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> staat niet op de blokkeerlijst.", "components.Layout.Sidebar.blacklist": "Blokkeerlijst", "components.Settings.SettingsMain.streamingRegionTip": "Streamingdiensten tonen op regionale beschikbaarheid", "components.PermissionEdit.manageblacklistDescription": "Toestemming geven om geblokkeerde media te beheren.", "components.RequestList.RequestItem.removearr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van {arr}", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Regio voor Ontdekken", "components.Settings.apiKey": "API-sleutel", "i18n.blacklistSuccess": "<strong>{title}</strong> is succesvol g<PERSON>.", "i18n.addToBlacklist": "Toevoegen aan blo<PERSON>keerlijst", "component.BlacklistBlock.blacklistedby": "Geblokkeerd door", "component.BlacklistModal.blacklisting": "Blokkeren", "components.Blacklist.blacklistSettingsDescription": "Media op de blokkeerlijst beheren.", "components.Blacklist.blacklistdate": "datum", "components.Blacklist.blacklistedby": "{date} door {user}", "components.Blacklist.blacklistsettings": "Instellingen blokkeerlijst", "components.Blacklist.mediaName": "<PERSON><PERSON>", "components.Blacklist.mediaTmdbId": "tmdb-id", "components.Blacklist.mediaType": "Type", "components.PermissionEdit.blacklistedItems": "Media blokkeren.", "components.PermissionEdit.blacklistedItemsDescription": "Toestemming geven om media te blokkeren.", "components.PermissionEdit.manageblacklist": "Blokkeerlijst beheren", "components.PermissionEdit.viewblacklistedItems": "Geblokkeerde media inzien.", "components.PermissionEdit.viewblacklistedItemsDescription": "Toestemming geven om geblokkeerde media in te zien.", "components.RequestList.sortDirection": "Sorteerrichting wisselen", "components.Settings.Notifications.validationWebhookRoleId": "Je moet een geldige Discord Role ID opgeven", "components.Settings.Notifications.webhookRoleId": "Role-id melding", "components.Settings.Notifications.webhookRoleIdTip": "De role-id die in het webhook-bericht vermeld moet worden. Laat leeg om vermeldingen uit te zetten", "components.Settings.SettingsJobsCache.usersavatars": "Gebruikersavatars", "components.Settings.SettingsMain.discoverRegion": "Regio voor Ontdekken", "components.Settings.SettingsMain.discoverRegionTip": "Inhoud filteren op regionale beschikbaarheid", "components.Settings.SettingsMain.streamingRegion": "Regio voor streamen", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Inhoud filteren op regionale beschikbaarheid", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Regio voor streamen", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Deze e-mail is bezet!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "<PERSON>en andere gebruiker heeft deze gebruikersnaam al. Je moet een e-mail instellen", "i18n.blacklist": "Blokkeren", "i18n.blacklistDuplicateError": "<strong>{title}</strong> staat al op de blokkeerlijst.", "i18n.blacklistError": "Er is iets misgegaan. Probeer het opnieuw.", "i18n.blacklisted": "Geblokkeerd", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> is succesvol ver<PERSON><PERSON><PERSON><PERSON> van de <PERSON>.", "i18n.removefromBlacklist": "Verwijderen van blokkeerlijst", "i18n.specials": "Specials", "components.Settings.SettingsJobsCache.plex-refresh-token": "Plex-verversingstoken", "components.Settings.tip": "Tip", "components.Settings.SettingsNetwork.networkDisclaimer": "Gebruik de netwerkparameters van je container/systeem in plaats van deze instellingen. <PERSON><PERSON> de {docs} voor meer informatie.", "components.DiscoverTvUpcoming.upcomingtv": "Aankomende series", "components.Settings.OverrideRuleModal.create": "Regel aanmaken", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Als je groepschat topics heeft, kun je hier een thread-/topic-ID opgeven", "components.Settings.OverrideRuleModal.keywords": "Sleutelwoorden", "components.Settings.SettingsMain.enableSpecialEpisodes": "Aanvragen voor speciale afleveringen toestaan", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "<PERSON><PERSON><PERSON> deze instelling NIET in tenzij je begrijpt wat je doet!", "components.Settings.SettingsNetwork.network": "Netwerk", "components.Login.loginwithapp": "Meld je aan met {appName}", "components.Settings.Notifications.messageThreadId": "Thread-/topic-ID", "components.Settings.OverrideRuleModal.settingsDescription": "Specificeert welke instellingen worden aangepast als aan bovenstaande voorwaarden wordt voldaan.", "components.Settings.addrule": "Nieuwe uitzonderingsregel", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Het is niet gelukt om met je gegevens met {mediaServerName} te verbinden", "components.Settings.Notifications.messageThreadIdTip": "Als je groepschat topics heeft, kun je hier een thread-/topic-ID opgeven", "components.Settings.OverrideRuleModal.conditionsDescription": "Specificeert voorwaarden voor het toepassen van parameterveranderingen. Ieder veld moet gevalideerd worden om de regel toe te passen (EN-operatie). Een veld is geldig als een van de eigenschappen overeenkomen (OF-operatie).", "components.Selector.searchUsers": "Gebruikers selecteren…", "components.Settings.overrideRulesDescription": "Uitzonderingsregels laten je eigenschappen selecteren die worden vervangen als een aanvraag met de regel overeen<PERSON>mt.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON> met je g<PERSON><PERSON><PERSON> mis<PERSON>t", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "<PERSON><PERSON><PERSON> je {mediaServerName}-g<PERSON><PERSON>s in om je account aan {applicationName} te koppelen.", "components.Settings.Notifications.validationMessageThreadId": "De thread-/topic-ID moet een positief geheel getal zijn", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "G<PERSON><PERSON><PERSON> ',' als scheidingsteken en '*.' als joker voor subdomeinen", "components.Settings.SettingsUsers.mediaServerLogin": "<PERSON><PERSON><PERSON><PERSON> met {mediaServerName} inschakelen", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Gekoppelde accounts", "components.Settings.OverrideRuleModal.selectQualityProfile": "Kwaliteitsprofiel selecteren", "components.Settings.OverrideRuleTile.users": "Gebruikers", "components.Setup.librarieserror": "Validatie mislukt. Schakel de bibliotheken opnieuw in en uit om verder te gaan.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "Je hebt geen toestemming om de gekoppelde accounts van deze gebruiker aan te passen.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "Thread-/topic-ID", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "Dit account is al gekoppeld aan een {applicationName}-gebruiker", "components.Login.noadminerror": "Geen administratorgebruiker gevonden op de server.", "components.Login.orsigninwith": "Of meld je aan met", "components.Settings.OverrideRuleTile.tags": "Labels", "components.Settings.OverrideRuleModal.conditions": "Voorwaarden", "components.Settings.OverrideRuleModal.createrule": "Nieuwe uitzonderingsregel", "components.Settings.OverrideRuleModal.editrule": "Uitzonderingsregel bewerken", "components.Settings.OverrideRuleModal.genres": "Genres", "components.Settings.OverrideRuleModal.languages": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.notagoptions": "Geen labels.", "components.Settings.OverrideRuleModal.qualityprofile": "Kwaliteitsprofiel", "components.Settings.OverrideRuleModal.rootfolder": "Hoofdmap", "components.Settings.OverrideRuleModal.ruleCreated": "Uitzonderingsregel aangemaakt!", "components.Settings.OverrideRuleModal.ruleUpdated": "Uitzonderingsregel bijgewerkt!", "components.Settings.OverrideRuleModal.selectRootFolder": "Hoofdmap selecteren", "components.Settings.OverrideRuleModal.selectService": "Dienst select<PERSON>n", "components.Settings.OverrideRuleModal.selecttags": "Labels selecteren", "components.Settings.OverrideRuleModal.service": "<PERSON><PERSON>", "components.Settings.OverrideRuleModal.serviceDescription": "Deze regel op de geselecteerde dienst toepassen.", "components.Settings.OverrideRuleModal.settings": "Instellingen", "components.Settings.OverrideRuleModal.tags": "Labels", "components.Settings.OverrideRuleModal.users": "Gebruikers", "components.Settings.OverrideRuleTile.conditions": "Voorwaarden", "components.Settings.OverrideRuleTile.genre": "Genre", "components.Settings.OverrideRuleTile.keywords": "Sleutelwoorden", "components.Settings.OverrideRuleTile.language": "Taal", "components.Settings.OverrideRuleTile.qualityprofile": "Kwaliteitsprofiel", "components.Settings.OverrideRuleTile.rootfolder": "Hoofdmap", "components.Settings.OverrideRuleTile.settings": "Instellingen", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Geavanceerde netwerkinstellingen", "components.Settings.SettingsNetwork.csrfProtection": "CSRF-bescherming inschakelen", "components.Settings.SettingsNetwork.docs": "documentatie", "components.Settings.SettingsNetwork.csrfProtectionTip": "Stel externe API-toegang in op alleen-lezen (HTTPS vereist)", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "Dwing Je<PERSON>rr om eerst IPv4-addressen te herleiden, in plaats van IPv6", "components.Settings.SettingsNetwork.forceIpv4First": "Eerst IPv4 herleiden forceren", "components.Settings.SettingsNetwork.networksettings": "Netwerkinstellingen", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S)-proxy", "components.Settings.SettingsNetwork.toastSettingsFailure": "Er is iets misgegaan bij het opslaan van de instellingen.", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Instellingen opgeslagen!", "components.Settings.SettingsNetwork.trustProxy": "Proxy-ondersteuning inschakelen", "components.Settings.SettingsNetwork.validationProxyPort": "Je dient een geldige poort op te geven", "components.Settings.SettingsUsers.loginMethods": "Aanmeldmethodes", "components.Settings.SettingsUsers.loginMethodsTip": "Confi<PERSON>reer a<PERSON>meldmethodes voor gebruikers.", "components.Settings.SettingsUsers.mediaServerLoginTip": "Sta gebruikers toe zich aan te melden met hun {mediaServerName}-account", "components.Settings.SettingsNetwork.networksettingsDescription": "Configu<PERSON> de netwerkinstellingen van je Jellyseerr-instantie.", "components.Settings.SettingsNetwork.proxyBypassFilter": "<PERSON><PERSON><PERSON> genegeerd door proxy", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Proxy omzeilen voor lokale adressen", "components.Settings.SettingsNetwork.proxyHostname": "Proxy-hostnaam", "components.Settings.SettingsNetwork.proxyPassword": "Proxy-wachtwoord", "components.Settings.SettingsNetwork.proxyPort": "Proxy-poort", "components.Settings.SettingsNetwork.proxySsl": "SSL gebruiken voor proxy", "components.Settings.SettingsNetwork.proxyUser": "Proxy-gebruikersnaam", "components.Settings.SettingsNetwork.trustProxyTip": "<PERSON><PERSON><PERSON> in staat om IP-adressen van clënten achter een proxy juist te registreren", "components.Settings.SettingsUsers.atLeastOneAuth": "Er dient ten minste één authenticatiemethode te worden geselecteerd.", "components.Settings.menuNetwork": "Netwerk", "components.Settings.overrideRules": "Uitzonderingsregels", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Er is een onbekende fout opgetreden", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Wachtwoord", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "Je dient een wachtwoord op te geven", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Toevoegen…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "{mediaServerName}-account koppelen", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Gebruikersnaam", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "Je dient een gebruike<PERSON> op te geven", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Gekoppeld account verwijderen mislukt.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Er is een onbekende fout opgetreden", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Deze externe accounts zijn gekoppeld aan je {applicationName}-account.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "<PERSON>r zijn geen externe accounts aan je account gekoppeld.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Dit account is al gekoppeld aan een Plex-gebruiker", "components.UserProfile.UserSettings.menuLinkedAccounts": "Gekoppelde accounts", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "Het thread-/topic-ID dient een positief geheel getal te zijn"}