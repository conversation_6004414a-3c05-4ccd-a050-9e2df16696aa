---
title: Adding Users
description: Add users to your Jellyseerr instance.
sidebar_position: 2
---

# Adding Users

There are currently two methods to add users to Jellyseerr: importing Mediaserver users and creating "local users." All new users are created with the [default permissions](/using-jellyseerr/settings/users#default-permissions) defined in **Settings &rarr; Users**.

### Importing Mediaserver Users

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs groupId="media-server-type" queryString>
  <TabItem value="jellyfin" label="Jellyfin">
    Clicking the **Import Jellyfin Users** button on the **User List** page will fetch the list of users with access to the Jellyfin server and add them to Jellyseerr automatically.

    Importing Jellyfin users is not required, however. Any user with access to the Jellyfin server can log in to Jellyseerr even if they have not been imported, and will be assigned the configured [default permissions](/using-jellyseerr/settings/users#default-permissions) upon their first login.

:::tip
To disable new Jellyfin sign-ins, navigate to **Settings &rarr; Users** and uncheck the [**Enable New Jellyfin Sign-In**](/using-jellyseerr/settings/users#enable-new-jellyfinembyplex-sign-in) box.
:::

  </TabItem>
  <TabItem value="emby" label="Emby">
    Clicking the **Import Emby Users** button on the **User List** page will fetch the list of users with access to the Emby server and add them to Jellyseerr automatically.

    Importing Emby users is not required, however. Any user with access to the Emby server can log in to Jellyseerr even if they have not been imported, and will be assigned the configured [default permissions](/using-jellyseerr/settings/users#default-permissions) upon their first login.

:::tip
To disable new Emby sign-ins, navigate to **Settings &rarr; Users** and uncheck the [**Enable New Emby Sign-In**](/using-jellyseerr/settings/users#enable-new-jellyfinembyplex-sign-in) box.
:::

  </TabItem>
  
  <TabItem value="plex" label="Plex">
    Clicking the **Import Plex Users** button on the **User List** page will fetch the list of users with access to the Plex server from [plex.tv](https://www.plex.tv/), and add them to Jellyseerr automatically.

    Importing Plex users is not required, however. Any user with access to the Plex server can log in to Jellyseerr even if they have not been imported, and will be assigned the configured [default permissions](/using-jellyseerr/settings/users#default-permissions) upon their first login.

:::tip
To disable new Plex sign-ins, navigate to **Settings &rarr; Users** and uncheck the [**Enable New Plex Sign-In**](/using-jellyseerr/settings/users#enable-new-jellyfinembyplex-sign-in) box.
:::

  </TabItem>
</Tabs>

### Creating Local Users

If you would like to grant Jellyseerr access to a user who doesn't have their own Plex account and/or access to the Plex server, you can manually add them by clicking the **Create Local User** button.

#### Email Address

Enter a valid email address at which the user can receive messages pertaining to their account and other notifications. The email address currently cannot be modified after the account is created.

#### Automatically Generate Password

If an [application URL](/using-jellyseerr/settings/general#application-url) is set and [email notifications](/using-jellyseerr/notifications/email) have been configured and enabled, Jellyseerr can automatically generate a password for the new user.

#### Password

If you would prefer to manually configure a password, enter a password here that is a minimum of 8 characters.
