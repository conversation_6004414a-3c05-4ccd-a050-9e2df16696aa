{"components.AirDateBadge.airedrelative": "{relativeTime} 방영됨", "components.AppDataWarning.dockerVolumeMissingDescription": "<code>{appDataPath}</code> 볼륨이 정상적으로 마운트되지 않았습니다. 컨테이너가 중지되거나 다시 시작되면 모든 데이터가 삭제됩니다.", "components.CollectionDetails.overview": "정보", "components.CollectionDetails.requestcollection": "컬렉션 요청", "components.AirDateBadge.airsrelative": "{relativeTime} 방영 예정", "components.CollectionDetails.numberofmovies": "{count} 영화", "components.CollectionDetails.requestcollection4k": "4K로 컬렉션 요청", "components.Discover.CreateSlider.addSlider": "슬라이더 추가", "components.Discover.CreateSlider.addcustomslider": "사용자 지정 슬라이더 생성", "components.Discover.CreateSlider.addfail": "새 슬라이더를 생성하는 데 실패했습니다.", "components.Discover.CreateSlider.editSlider": "슬라이더 수정", "components.Discover.CreateSlider.editfail": "슬라이더 수정에 실패했습니다.", "components.Discover.CreateSlider.addsuccess": "새 슬라이더를 만들고 검색 사용자 지정 설정을 저장했습니다.", "components.Discover.CreateSlider.editsuccess": "슬라이더를 수정하고 검색 사용자 지정 설정을 저장했습니다.", "components.Discover.CreateSlider.needresults": "1개 이상의 검색 결과가 필요합니다.", "components.Discover.CreateSlider.nooptions": "결과가 없습니다.", "components.Discover.CreateSlider.searchGenres": "장르 검색…", "components.Discover.CreateSlider.searchKeywords": "검색어…", "components.Discover.CreateSlider.searchStudios": "스튜디오 검색…", "components.Discover.CreateSlider.slidernameplaceholder": "슬라이더 이름", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} 영화", "components.Discover.upcomingmovies": "개봉 예정작", "components.Layout.SearchInput.searchPlaceholder": "영화 및 시리즈 검색", "components.PermissionEdit.request4kMovies": "4K 영화 요청", "components.PermissionEdit.request4kMoviesDescription": "4K 영화를 요청할 수 있는 권한을 부여합니다.", "components.Discover.CreateSlider.providetmdbkeywordid": "TMDB 키워드 ID 입력", "components.Discover.CreateSlider.providetmdbgenreid": "TMDB 장르 ID 입력", "components.Discover.CreateSlider.providetmdbsearch": "검색어 입력", "components.Discover.CreateSlider.providetmdbstudio": "TMDB 스튜디오 ID 입력", "components.Discover.CreateSlider.starttyping": "검색어를 입력하세요.", "components.Discover.CreateSlider.validationDatarequired": "값을 입력해야 합니다.", "components.Discover.CreateSlider.validationTitlerequired": "이름을 입력해야 합니다.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} 영화", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} 영화", "components.Discover.DiscoverMovies.discovermovies": "영화", "components.Discover.DiscoverMovies.sortPopularityAsc": "인기 오름차순", "components.Discover.DiscoverMovies.sortPopularityDesc": "인기 내림차순", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "개봉일 오래된순", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "개봉일 최신순", "components.Discover.DiscoverMovies.sortTitleAsc": "제목 (A-Z) 오름차순", "components.Discover.DiscoverNetwork.networkSeries": "{network} 시리즈", "components.Discover.DiscoverMovies.sortTitleDesc": "제목 (Z-A) 내림차순", "components.Discover.DiscoverSliderEdit.deletefail": "슬라이더를 삭제하지 못했습니다.", "components.Discover.DiscoverSliderEdit.deletesuccess": "슬라이더를 삭제했습니다.", "components.Discover.DiscoverSliderEdit.enable": "표시 여부", "components.Discover.DiscoverSliderEdit.remove": "삭제", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB 점수 오름차순", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB 점수 내림차순", "components.Discover.DiscoverStudio.studioMovies": "{studio} 영화", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "첫 방영일 오래된순", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "첫 방영일 최신순", "components.Discover.DiscoverTv.sortPopularityAsc": "인기 오름차순", "components.Discover.DiscoverTv.sortPopularityDesc": "인기 내림차순", "components.Discover.DiscoverTv.sortTitleAsc": "제목 (A-Z) 오름차순", "components.Discover.DiscoverTv.sortTitleDesc": "제목 (Z-A) 내림차순", "components.Discover.DiscoverWatchlist.discoverwatchlist": "당신의 Plex 관심 목록", "components.Discover.DiscoverWatchlist.watchlist": "Plex 시청 목록", "components.Discover.FilterSlideover.clearfilters": "선택한 필터 해제", "components.Discover.FilterSlideover.filters": "필터", "components.Discover.FilterSlideover.firstAirDate": "첫 방영일", "components.Discover.FilterSlideover.from": "부터", "components.Discover.FilterSlideover.genres": "장르", "components.Discover.FilterSlideover.keywords": "키워드", "components.Discover.FilterSlideover.originalLanguage": "원작 언어", "components.Discover.FilterSlideover.ratingText": "{minValue}~{maxValue} 사이의 점수", "components.Discover.FilterSlideover.releaseDate": "공개일", "components.Discover.FilterSlideover.runtime": "상영 시간", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} 분 상영 시간", "components.Discover.FilterSlideover.streamingservices": "스트리밍 서비스", "components.Discover.FilterSlideover.studio": "스튜디오", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB 회원 점수", "components.Discover.FilterSlideover.to": "까지", "components.Discover.MovieGenreList.moviegenres": "영화 장르", "components.Discover.NetworkSlider.networks": "방송사", "components.Discover.networks": "방송사", "components.Discover.popularmovies": "인기 영화", "components.Discover.tmdbnetwork": "TMDB 방송사", "components.Discover.upcoming": "개봉 예정작", "components.Layout.Sidebar.browsemovies": "영화", "components.PermissionEdit.autoapprove4kMoviesDescription": "4K 영화 요청의 자동 승인 권한을 부여합니다.", "components.PermissionEdit.autoapproveMovies": "영화 자동 수락", "components.PermissionEdit.autoapproveMoviesDescription": "비-4K 영화 요청의 자동 승인 권한을 부여합니다.", "components.PermissionEdit.autorequestMovies": "영화 자동 요청", "components.PermissionEdit.autorequestMoviesDescription": "Plex Watchlist를 통해 비-4K 영화를 자동으로 요청할 수 있는 권한을 부여합니다.", "components.PermissionEdit.requestMoviesDescription": "비-4K 영화를 요청할 수 있는 권한을 부여합니다.", "components.PermissionEdit.requestMovies": "영화 요청", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{영화} 당 {quotaDays} {일}</quotaUnits>", "components.RequestModal.selectmovies": "영화 선택", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "영화 자동 요청", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "<PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>의 영화를 자동으로 요청합니다", "i18n.movies": "영화", "components.Discover.CreateSlider.providetmdbnetwork": "TMDB 방송사 ID 입력", "components.PermissionEdit.autoapprove4kMovies": "4K 영화 자동 수락", "components.Discover.DiscoverTv.discovertv": "시리즈", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB 점수 오름차순", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB 점수 내림차순", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} 시리즈", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} 시리즈", "components.IssueDetails.allepisodes": "모든 에피소드", "components.IssueDetails.play4konplex": "Plex에서 4K로 재생", "components.IssueDetails.playonplex": "Plex에서 재생", "components.IssueModal.CreateIssueModal.season": "시즌 {seasonNumber}", "components.Layout.Sidebar.browsetv": "시리즈", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "시리즈 요청", "components.Login.password": "비밀번호", "components.Layout.UserDropdown.settings": "설정", "components.Discover.createnewslider": "새 슬라이더 생성", "components.Layout.UserDropdown.requests": "요청", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} 시리즈", "components.Discover.MovieGenreSlider.moviegenres": "영화 장르", "components.Discover.PlexWatchlistSlider.plexwatchlist": "plex 관심 목록", "components.Discover.RecentlyAddedSlider.recentlyAdded": "최근에 추가됨", "components.Discover.TvGenreList.seriesgenres": "시리즈 장르", "components.Discover.TvGenreSlider.tvgenres": "시리즈 장르", "components.Discover.emptywatchlist": "<PlexWatchlistSupportLink>Plex 관심 목록</PlexWatchlistSupportLink>에 추가된 미디어가 여기에 표시됩니다.", "components.Discover.moviegenres": "영화 장르", "components.Discover.plexwatchlist": "Plex 관심 목록", "components.Discover.populartv": "인기 시리즈", "components.Discover.recentlyAdded": "최근에 추가됨", "components.Discover.recentrequests": "최근 요청", "components.Discover.tmdbtvgenre": "TDMB 시리즈 장르", "components.Discover.tmdbtvkeyword": "TDMB 시리즈 키워드", "components.Discover.tvgenres": "시리즈 장르", "components.DownloadBlock.formattedTitle": "{title}: 시즌 {seasonNumber} 에피소드 {episodeNumber}", "components.IssueDetails.IssueComment.validationComment": "메시지를 입력해야 합니다", "components.IssueDetails.episode": "에피소드 {episodeNumber}", "components.IssueDetails.season": "시즌 {seasonNumber}", "components.IssueModal.CreateIssueModal.episode": "에피소드 {episodeNumber}", "components.IssueModal.CreateIssueModal.whatswrong": "무슨 문제인가요?", "components.IssueModal.issueSubtitles": "자막", "components.Layout.Sidebar.requests": "요청", "components.Layout.Sidebar.settings": "설정", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "영화 요청", "components.Login.email": "이메일 주소", "components.Login.forgotpassword": "비밀번호를 잊으셨나요?", "components.Login.signinwithoverseerr": "{applicationTitle} 계정을 사용하세요", "components.Login.signinwithplex": "Plex 계정을 사용하세요", "components.ManageSlideOver.manageModalAdvanced": "고급", "components.Login.validationemailrequired": "유효한 이메일 주소를 입력해야 합니다", "components.Login.validationpasswordrequired": "비밀번호를 입력해야 합니다", "components.ManageSlideOver.downloadstatus": "다운로드", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB 사용자 투표 수", "components.Discover.FilterSlideover.voteCount": "{minValue}와 {maxValue} 사이의 투표 수", "components.Discover.tmdbmoviegenre": "TMDB 영화 장르", "components.Discover.tmdbmoviekeyword": "TMDB 영화 키워드", "components.Discover.upcomingtv": "다음 시리즈", "components.Discover.updatefailed": "디스커버 사용자 지정 설정을 업데이트하는 데 문제가 발생했습니다.", "components.DownloadBlock.estimatedtime": "예상 {time}", "components.IssueDetails.IssueComment.areyousuredelete": "이 댓글을 삭제하시겠습니까?", "components.IssueDetails.IssueDescription.description": "설명", "components.IssueDetails.IssueDescription.edit": "설명 수정", "components.IssueDetails.deleteissueconfirm": "이 이슈를 삭제하시겠습니까?", "components.IssueDetails.issuepagetitle": "이슈", "components.IssueDetails.unknownissuetype": "알 수 없음", "components.IssueList.IssueItem.issuestatus": "상태", "components.IssueList.IssueItem.issuetype": "유형", "components.IssueList.IssueItem.opened": "열림", "components.IssueList.IssueItem.openeduserdate": "{user}의 {date}", "components.IssueList.IssueItem.unknownissuetype": "알 수 없음", "components.IssueList.IssueItem.viewissue": "이슈 보기", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {에피소드} other {에피소드}}", "components.IssueList.sortModified": "마지막 수정", "components.IssueModal.CreateIssueModal.allepisodes": "모든 에피소드", "components.IssueModal.CreateIssueModal.problemepisode": "영향을 받는 에피소드", "components.IssueModal.CreateIssueModal.problemseason": "영향을 받는 시즌", "components.IssueModal.CreateIssueModal.providedetail": "발생한 이슈에 대한 자세한 설명을 작성해주세요.", "components.IssueModal.CreateIssueModal.reportissue": "이슈 보고", "components.IssueModal.CreateIssueModal.toastviewissue": "이슈 보기", "components.Layout.Sidebar.dashboard": "디스커버", "components.Layout.UserDropdown.signout": "로그아웃", "components.Login.signin": "로그인", "components.ManageSlideOver.manageModalClearMedia": "데이터 지우기", "components.ManageSlideOver.markallseasonsavailable": "모든 시즌을 사용 가능하게 표시", "components.Layout.VersionStatus.outofdate": "오래됨", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {커밋} other {커밋}} 이후", "components.ManageSlideOver.markavailable": "사용 가능하게 표시", "components.ManageSlideOver.playedby": "플레이한 사용자", "components.MediaSlider.ShowMoreCard.seemore": "자세히 보기", "components.MovieDetails.MovieCast.fullcast": "전체 출연진", "components.MovieDetails.MovieCrew.fullcrew": "전체 제작진", "components.MovieDetails.markavailable": "사용 가능한 것으로 표시", "components.MovieDetails.physicalrelease": "실물 개봉일", "components.MovieDetails.showless": "간단히 보기", "components.MovieDetails.tmdbuserscore": "TMDB 사용자 점수", "components.MovieDetails.viewfullcrew": "전체 제작진 보기", "components.MovieDetails.watchtrailer": "예고편 보기", "components.NotificationTypeSelector.adminissuecommentDescription": "다른 사용자가 이슈에 댓글을 남길 때 알림을 받습니다.", "components.NotificationTypeSelector.adminissuereopenedDescription": "다른 사용자가 이슈를 다시 열 때 알림을 받습니다.", "components.NotificationTypeSelector.adminissueresolvedDescription": "다른 사용자가 이슈를 해결했을 때 알림을 받습니다.", "components.NotificationTypeSelector.issuecomment": "이슈 댓글", "components.NotificationTypeSelector.issuereopenedDescription": "이슈가 다시 열리면 알림을 보냅니다.", "components.NotificationTypeSelector.mediaautorequestedDescription": "Plex 시청 목록에 있는 항목에 대해 새 미디어 요청이 자동으로 제출될 때 알림을 받습니다.", "components.NotificationTypeSelector.mediaavailableDescription": "미디어 요청을 사용할 수 있게 되면 알림을 보냅니다.", "components.NotificationTypeSelector.mediafailed": "요청 처리 실패", "components.NotificationTypeSelector.mediarequestedDescription": "사용자가 승인이 필요한 새 미디어 요청을 제출할 때 알림을 보냅니다.", "components.NotificationTypeSelector.notificationTypes": "알림 유형", "components.PermissionEdit.admin": "관리자", "components.PermissionEdit.autoapprove4k": "4K 자동 승인", "components.PermissionEdit.autoapprove4kDescription": "모든 4K 미디어 요청에 대해 자동 승인을 부여합니다.", "components.PermissionEdit.autoapprove4kSeriesDescription": "4K 시리즈 요청에 대한 자동 승인을 부여합니다.", "components.PermissionEdit.autoapproveDescription": "4K가 아닌 모든 미디어 요청에 대해 자동 승인을 부여합니다.", "components.PermissionEdit.autorequestSeries": "자동 요청 시리즈", "components.PermissionEdit.autorequestSeriesDescription": "Plex 시청 목록를 통해 비 4K 시리즈에 대한 요청을 자동으로 제출할 수 있는 권한을 부여합니다.", "components.PermissionEdit.createissuesDescription": "미디어 이슈를 보고할 권한을 부여합니다.", "components.PermissionEdit.manageissues": "이슈 관리", "components.PermissionEdit.request4kTv": "4K 시리즈 요청", "components.PermissionEdit.requestTv": "시리즈 요청", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.RequestBlock.delete": "삭제 요청", "components.RequestBlock.requestedby": "요청자", "components.RequestButton.viewrequest4k": "4K 요청 보기", "components.RequestCard.approverequest": "요청 승인", "components.QuotaSelector.movies": "{count, plural, one {영화} other {영화}}", "components.RequestCard.cancelrequest": "요청 취소", "components.RequestCard.declinerequest": "요청 거부", "components.RequestCard.mediaerror": "{mediaType} 찾을 수 없음", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestCard.seasons": "{seasonCount, plural, one {시즌} other {시즌}}", "components.RequestList.RequestItem.mediaerror": "{mediaType} 찾을 수 없음", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.unknowntitle": "알 수 없는 제목", "components.RequestList.requests": "요청", "components.RequestList.showallrequests": "모든 요청 표시", "components.RequestModal.AdvancedRequester.rootfolder": "루트 폴더", "components.RequestModal.QuotaDisplay.season": "시즌", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {시즌} other {시즌}}", "components.RequestModal.alreadyrequested": "이미 요청됨", "components.RequestModal.approve": "요청 승인", "components.RequestModal.pendingapproval": "당신의 요청이 승인 대기 중입니다.", "components.RequestModal.requestcollection4ktitle": "4K로 컬렉션 요청", "components.RequestModal.requestcollectiontitle": "컬렉션 요청", "components.RequestModal.requestfrom": "{username}님의 요청이 승인 대기 중입니다.", "components.RequestModal.requestmovie4ktitle": "4K 영화 요청", "components.RequestModal.requestmovies4k": "4K에서 {count} {count, plural, one {영화} other {영화}} 요청", "components.RequestModal.requestmovietitle": "영화 요청", "components.RequestModal.requestseasons": "요청 {seasonCount} {seasonCount, plural, one {Season} other {Seasons}}", "components.ResetPassword.passwordreset": "비밀번호 재설정", "components.ResetPassword.validationpasswordmatch": "비밀번호가 일치해야 합니다", "components.Selector.searchGenres": "장르 선택…", "components.Selector.searchKeywords": "키워드 검색…", "components.ResetPassword.validationemailrequired": "유효한 이메일 주소를 입력해야 합니다", "components.Settings.Notifications.NotificationsGotify.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "유효한 URL을 입력해야 합니다", "components.Settings.Notifications.NotificationsSlack.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "유효한 애플리케이션 토큰을 입력해야 합니다", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Slack 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Slack 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "슬랙 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "웹훅 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "웹훅 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.botAvatarUrl": "봇 아바타 URL", "components.Settings.Notifications.toastEmailTestFailed": "이메일 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.toastEmailTestSending": "이메일 테스트 알림을 보내는 중…", "components.Settings.Notifications.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.webhookUrl": "웹훅 URL", "components.Settings.RadarrModal.add": "서버 추가", "components.Settings.RadarrModal.announced": "공개됨", "components.Settings.RadarrModal.apiKey": "API 키", "components.Settings.RadarrModal.baseUrl": "기본 URL", "components.Settings.Notifications.validationUrl": "유효한 URL을 입력해야 합니다", "components.Settings.RadarrModal.create4kradarr": "새 4K Radarr 서버 추가", "components.Settings.RadarrModal.edit4kradarr": "4K Radarr 서버 수정", "components.Settings.RadarrModal.enableSearch": "자동 검색 활성화", "components.Settings.RadarrModal.externalUrl": "외부접속 URL", "components.Settings.RadarrModal.hostname": "호스트 네임 또는 IP 주소", "components.Settings.RadarrModal.inCinemas": "영화관에서", "components.Settings.RadarrModal.loadingTags": "태그 불러오는 중…", "components.Settings.RadarrModal.notagoptions": "태그가 없습니다.", "components.Settings.RadarrModal.port": "포트", "components.Settings.RadarrModal.released": "출시됨", "components.Settings.RadarrModal.selectRootFolder": "루트 폴더 선택", "components.Settings.RadarrModal.ssl": "SSL 사용", "components.Settings.RadarrModal.syncEnabled": "스캔 활성화", "components.Settings.RadarrModal.loadingprofiles": "품질 프로필 불러오는 중…", "components.Settings.RadarrModal.testFirstTags": "태그를 불러오기 위한 연결 테스트", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL 기반에는 선행 슬래시가 있어야 합니다", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL 기반은 슬래시로 끝나지 않아야 합니다", "components.Settings.SettingsAbout.Releases.viewchangelog": "변경 로그 보기", "components.Settings.SettingsAbout.Releases.viewongithub": "GitHub에서 보기", "components.Settings.SettingsAbout.betawarning": "현재 베타 소프트웨어입니다. 기능이 손상되거나 불안정할 수 있습니다. 문제가 있으면 GitHub에서 보고하세요!", "components.Settings.SettingsJobsCache.cachehits": "조회 수", "components.Settings.SettingsJobsCache.jobsandcache": "작업 및 캐시", "components.Settings.SettingsJobsCache.jobstarted": "{jobname}이(가) 시작되었습니다.", "components.Settings.SettingsJobsCache.unknownJob": "알 수 없는 작업", "components.Settings.SettingsLogs.resumeLogs": "재개", "components.Settings.SettingsMain.toastApiKeyFailure": "새 API 키를 생성하는 동안 문제가 발생했습니다.", "components.Settings.SettingsUsers.defaultPermissions": "기본 권한", "components.Settings.SonarrModal.port": "포트", "components.Settings.SonarrModal.animequalityprofile": "애니메이션 품질 프로필", "components.Settings.menuServices": "서비스", "components.Settings.toastPlexConnecting": "Plex에 연결 시도 중…", "components.TvDetails.overview": "정보", "components.TvDetails.overviewunavailable": "정보를 볼 수 없습니다.", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes 시청자 점수", "components.UserProfile.ProfileHeader.profile": "프로필 보기", "components.UserProfile.ProfileHeader.userid": "사용자 ID: {userid}", "components.UserProfile.ProfileHeader.settings": "설정 수정", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "관리자", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "표시 언어", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "디스코드 사용자 ID", "i18n.partiallyavailable": "일부 사용 가능", "pages.pagenotfound": "페이지를 찾을 수 없음", "components.Settings.RadarrModal.tagRequestsInfo": "요청자의 사용자 ID 및 표시 이름으로 추가 태그를 자동으로 추가합니다", "components.Settings.RadarrModal.tagRequests": "태그 요청", "components.Settings.RadarrModal.tags": "태그", "components.Settings.RadarrModal.testFirstRootFolders": "루트 폴더를 불러오기 위한 연결 테스트", "components.Settings.RadarrModal.testFirstQualityProfiles": "품질 프로필을 불러오기 위한 연결 테스트", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr에 연결하지 못했습니다.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr 연결이 성공적으로 설정되었습니다!", "components.Settings.RadarrModal.validationApiKeyRequired": "API 키를 입력해야 합니다", "components.Settings.RadarrModal.validationApplicationUrl": "유효한 URL을 입력해야 합니다", "components.Settings.RadarrModal.validationProfileRequired": "품질 프로필을 선택해야 합니다", "components.Settings.SettingsAbout.Releases.currentversion": "현재", "components.Settings.SettingsAbout.Releases.latestversion": "최신", "components.Settings.SettingsAbout.Releases.releasedataMissing": "현재 출시 데이터를 사용할 수 없습니다.", "components.Settings.SettingsAbout.appDataPath": "데이터 디렉토리", "components.Settings.SettingsAbout.documentation": "문서", "components.Settings.SettingsAbout.gettingsupport": "지원 받기", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON><PERSON> 정보", "components.Settings.SettingsAbout.preferredmethod": "선호", "components.Settings.SettingsAbout.runningDevelop": "당신은 <code>개발</code>에 기여하거나 최신 테스트를 지원하는 사람들에게만 권장되는 Overserr 분기를 실행하고 있습니다.", "components.Settings.SettingsAbout.timezone": "시간대", "components.Settings.SettingsJobsCache.availability-sync": "사용가능한 미디어 동기화", "components.Settings.SettingsJobsCache.cache": "캐시", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr는 외부 API 엔드포인트에 대한 요청을 캐시하여 성능을 최적화하고 불필요한 API 호출을 방지합니다.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} 캐시가 플러시되었습니다.", "components.Settings.SettingsJobsCache.cachekeys": "전체 키", "components.Settings.SettingsJobsCache.cacheksize": "키 크기", "components.Settings.SettingsJobsCache.canceljob": "작업 취소", "components.Settings.SettingsJobsCache.command": "명령", "components.Settings.SettingsJobsCache.download-sync": "다운로드 동기화", "components.Settings.SettingsJobsCache.editJobSchedule": "작업 수정", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "현재 주파수", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "새 주파수", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "매 {jobScheduleSeconds, plural, one {초} other {{jobScheduleSeconds} 초}}", "components.Settings.SettingsJobsCache.imagecache": "이미지 캐시", "components.Settings.SettingsJobsCache.imagecacheDescription": "설정에서 활성화하면 Jellyseerr는 미리 구성된 외부 소스에서 이미지를 프록시하고 캐시합니다. 캐시된 이미지는 구성 폴더에 저장됩니다. <code>{appDataPath}/cache/images</code> 에서 파일을 찾을 수 있습니다.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "작업을 저장하는 동안 문제가 발생했습니다.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "작업이 성공적으로 수정되었습니다!", "components.Settings.SettingsJobsCache.jobs": "작업", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr는 특정 유지 관리 작업을 정기적으로 예약된 작업으로 수행하지만 아래에서 수동으로 트리거할 수도 있습니다. 작업을 수동으로 실행해도 일정이 변경되지는 않습니다.", "components.Settings.SettingsJobsCache.jobtype": "유형", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex 전체 라이브러리 스캔", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex 최근 추가 스캔", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex 시청 목록 동기화", "components.Settings.SettingsJobsCache.process": "프로세스", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr 스캔", "components.Settings.SettingsJobsCache.runnow": "지금 실행", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr 스캔", "components.Settings.SettingsLogs.copiedLogMessage": "로그 메시지를 클립보드에 복사했습니다.", "components.Settings.SettingsLogs.copyToClipboard": "클립보드에 복사", "components.Settings.SettingsLogs.filterDebug": "디버그", "components.Settings.SettingsLogs.filterError": "오류", "components.Settings.SettingsLogs.filterWarn": "경고", "components.Settings.SettingsLogs.label": "레이블", "components.Settings.SettingsLogs.level": "심각도", "components.Settings.SettingsLogs.logDetails": "로그 정보", "components.Settings.SettingsLogs.logs": "로그", "components.Settings.SettingsLogs.logsDescription": "로그는 <code>stdout</code> 또는 <code>{appDataPath}/logs/overseerr.log</code> 통해 직접 볼 수도 있습니다.", "components.Settings.SettingsLogs.showall": "전체 로그 보기", "components.Settings.SettingsMain.general": "일반", "components.Settings.SettingsMain.validationApplicationUrl": "유효한 URL을 입력해야 합니다", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.SettingsUsers.defaultPermissionsTip": "새 사용자에게 할당된 초기 권한", "components.Settings.SettingsUsers.localLogin": "로컬 로그인 활성화", "components.Settings.SettingsUsers.newPlexLogin": "새 Plex 로그인 활성화", "components.Settings.SettingsUsers.newPlexLoginTip": "Plex 사용자를 미리 불러오지 않았어도 로그인하도록 허용", "components.Settings.SettingsUsers.toastSettingsFailure": "설정을 저장하는 동안 문제가 발생했습니다.", "components.Settings.SettingsUsers.tvRequestLimitLabel": "전역 시리즈 요청 제한", "components.Settings.SettingsUsers.userSettings": "사용자 설정", "components.Settings.SettingsUsers.userSettingsDescription": "전역 및 기본 사용자 설정을 구성합니다.", "components.Settings.SonarrModal.animerootfolder": "애니메이션 루트 폴더", "components.Settings.SonarrModal.createsonarr": "새 Sonarr 서버 추가", "components.Settings.SonarrModal.default4kserver": "기본 4K 서버", "components.Settings.SonarrModal.enableSearch": "자동 검색 활성화", "components.Settings.SonarrModal.externalUrl": "외부접속 URL", "components.Settings.SonarrModal.languageprofile": "언어 프로필", "components.Settings.SonarrModal.loadingTags": "태그 불러오는 중…", "components.Settings.SonarrModal.loadingprofiles": "품질 프로필 불러오는 중…", "components.Settings.SonarrModal.loadingrootfolders": "루트 폴더 불러오는 중…", "components.Settings.SonarrModal.qualityprofile": "품질 프로필", "components.Settings.SonarrModal.rootfolder": "루트 폴더", "components.Settings.SonarrModal.seasonfolders": "시즌 폴더", "components.Settings.SonarrModal.selectLanguageProfile": "언어 프로필 선택", "components.Settings.SonarrModal.selectRootFolder": "루트 폴더 선택", "components.Settings.SonarrModal.selectQualityProfile": "품질 프로필 선택", "components.Settings.SonarrModal.server4k": "4K 서버", "components.Settings.SonarrModal.servername": "서버 이름", "components.Settings.SonarrModal.tagRequests": "태그 요청", "components.Settings.SonarrModal.ssl": "SSL 사용", "components.Settings.SonarrModal.tagRequestsInfo": "요청자의 사용자 ID 및 표시 이름이 포함된 추가 태그를 자동으로 추가합니다", "components.Settings.SonarrModal.tags": "태그", "components.Settings.SonarrModal.testFirstRootFolders": "루트 폴더를 불러오기 위한 연결 테스트", "components.Settings.SonarrModal.testFirstLanguageProfiles": "언어 프로필을 불러오기 위한 연결 테스트", "components.Settings.SonarrModal.testFirstQualityProfiles": "품질 프로필을 불러오기 위한 연결 테스트", "components.Settings.SonarrModal.validationApiKeyRequired": "API 키를 입력해야 합니다", "components.Settings.SonarrModal.validationApplicationUrl": "유효한 URL을 입력해야 합니다", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "기본 URL은 슬래시로 끝나지 않아야 합니다", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.SonarrModal.validationHostnameRequired": "유효한 호스트 네임 또는 IP 주소를 입력해야 합니다", "components.Settings.addsonarr": "Sonarr 서버 추가", "components.Settings.SonarrModal.validationRootFolderRequired": "루트 폴더를 선택해야 합니다", "components.Settings.advancedTooltip": "이 설정을 잘못 구성하면 기능이 제대로 작동하지 않을 수 있습니다", "components.Settings.cancelscan": "스캔 취소", "components.Settings.copied": "클립보드에 API 키를 복사했습니다.", "components.Settings.currentlibrary": "현재 라이브러리: {name}", "components.Settings.default4k": "기본 4K", "components.Settings.deleteServer": "{serverType} 서버 삭제", "components.Settings.externalUrl": "외부접속 URL", "components.Settings.hostname": "호스트 네임 또는 IP 주소", "components.Settings.is4k": "4K", "components.Settings.manualscan": "수동으로 라이브러리 스캔", "components.Settings.mediaTypeMovie": "영화", "components.Settings.manualscanDescription": "일반적으로 이는 24시간에 한 번 실행됩니다. Jellyseerr는 Plex 서버의 최근 추가 항목을 더 적극적으로 확인합니다. 만약 Plex를 처음 구성하는 경우, 한번은 전체 수동 라이브러리 스캔을 권장합니다!", "components.Settings.mediaTypeSeries": "시리즈", "components.Settings.menuAbout": "정보", "components.Settings.menuGeneralSettings": "일반", "components.Settings.menuJobs": "작업 및 캐시", "components.Settings.menuLogs": "로그", "components.Settings.menuNotifications": "알림", "components.Settings.menuUsers": "사용자", "components.Settings.noDefaultNon4kServer": "비-4K 및 4K 콘텐츠를 전부 처리하는 유일한 {serverType} 서버가 있는 경우(또는 4K 콘텐츠만 다운로드하는 경우), {serverType} 서버는 4K 서버로 지정되어서는 <strong>안됩니다</strong>.", "components.Settings.noDefaultServer": "{mediaType} 요청을 처리하기 위해서는 적어도 하나 이상의 {serverType} 서버를 기본 설정해야 합니다.", "components.Settings.notifications": "알림", "components.Settings.plexlibrariesDescription": "Jellyseerr에서 타이틀을 스캔하는 라이브러리입니다. Plex 연결 설정을 설정하고 저장한 후, 라이브러리가 표시되지 않는 경우 아래 버튼을 클릭하세요.", "components.Settings.port": "포트", "components.Settings.radarrsettings": "Radarr 설정", "components.Settings.restartrequiredTooltip": "이 변경된 설정이 적용되려면 Jellyseerr를 재시작해야 합니다", "components.Settings.serverRemote": "원격", "components.Settings.serverSecure": "보안", "components.Settings.serverpreset": "서버", "components.Settings.serverpresetLoad": "사용 가능한 서버를 불러오려면 버튼을 눌러 주세요", "components.Settings.serverpresetManualMessage": "수동 구성", "components.Settings.serverpresetRefreshing": "서버 검색 중…", "components.Settings.tautulliApiKey": "API 키", "components.Settings.toastPlexConnectingFailure": "Plex에 연결하지 못했습니다.", "components.Settings.toastPlexConnectingSuccess": "Plex 연결이 성공적으로 설정되었습니다!", "components.Settings.toastPlexRefreshFailure": "Plex 서버 목록을 검색하지 못했습니다.", "components.Settings.toastPlexRefreshSuccess": "Plex 서버 목록을 성공적으로 검색했습니다!", "components.Settings.toastTautulliSettingsFailure": "Tautulli 설정을 저장하는 동안 문제가 발생했습니다.", "components.Settings.validationHostnameRequired": "유효한 호스트 네임 또는 IP 주소를 입력해야 합니다", "components.Settings.validationUrlBaseTrailingSlash": "기본 URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.validationUrlTrailingSlash": "기본 URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.webAppUrl": "<WebAppLink>웹 앱</WebAppLink> URL", "components.Settings.webhook": "웹훅", "components.Settings.webpush": "웹 푸시", "components.Setup.configureservices": "서비스 구성", "components.Setup.continue": "계속", "components.Setup.finish": "설정 완료", "components.Setup.finishing": "마무리하는 중…", "components.Setup.signinMessage": "Plex 계정으로 로그인하세요", "components.StatusBadge.playonplex": "Plex에서 재생", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.StatusChecker.appUpdated": "{applicationTitle} 수정됨", "components.StatusChecker.appUpdatedDescription": "애플리케이션을 다시 로드하려면 아래 버튼을 클릭해주세요.", "components.StatusChecker.reloadApp": "{applicationTitle} 새로고침", "components.TitleCard.cleardata": "데이터 비우기", "components.TitleCard.mediaerror": "{mediaType} 찾을 수 없음", "components.TitleCard.tmdbid": "TMDB ID", "components.TvDetails.Season.noepisodes": "에피소드 목록을 사용할 수 없습니다.", "components.TvDetails.TvCast.fullseriescast": "전체 시리즈 출연진", "components.TvDetails.anime": "애니메이션", "components.TvDetails.TvCrew.fullseriescrew": "전체 시리즈 제작진", "components.TvDetails.episodeRuntime": "에피소드 런타임", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# 에피소드} other {# 에피소드}}", "components.TvDetails.episodeRuntimeMinutes": "{runtime} 분", "components.TvDetails.manageseries": "시리즈 관리", "components.TvDetails.originallanguage": "원작 언어", "components.TvDetails.originaltitle": "원작명", "components.TvDetails.nextAirDate": "다음 방영일", "components.TvDetails.network": "{networkCount, plural, one {방송사} other {방송사}}", "components.TvDetails.recommendations": "추천", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes 토마토미터", "components.TvDetails.seasonnumber": "시즌 {seasonNumber}", "components.TvDetails.seasons": "{seasonCount, plural, one {# 시즌} other {# 시즌}}", "components.TvDetails.showtype": "시리즈 유형", "components.TvDetails.similar": "유사한 시리즈", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.streamingproviders": "현재 스트리밍 중", "components.TvDetails.tmdbuserscore": "TMDB 사용자 점수", "components.TvDetails.seasonstitle": "시즌", "components.TvDetails.viewfullcrew": "전체 제작진 보기", "components.TvDetails.watchtrailer": "예고편 보기", "components.UserList.accounttype": "유형", "components.UserList.autogeneratepassword": "비밀번호 자동 생성", "components.UserList.bulkedit": "일괄 수정", "components.UserList.create": "만들기", "components.UserList.created": "가입됨", "components.UserList.createlocaluser": "로컬 사용자 생성", "components.UserList.edituser": "사용자 권한 수정", "components.UserList.email": "이메일 주소", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {user} other {users}}를 성공적으로 불러왔습니다!", "components.UserList.importfromplex": "Plex 사용자 불러오기", "components.UserList.importfromplexerror": "Plex 사용자를 불러오는 동안 문제가 발생했습니다.", "components.UserList.localLoginDisabled": "<strong>로컬 로그인 활성화</strong> 설정이 현재 비활성화되어 있습니다.", "components.UserList.localuser": "로컬 사용자", "components.UserList.nouserstoimport": "불러올 Plex 사용자가 없습니다.", "components.UserList.sortDisplayName": "표시 이름", "components.UserList.sortCreated": "가입 일자", "components.UserList.sortRequests": "요청 수", "components.UserList.totalrequests": "요청", "components.UserList.usercreatedfailed": "사용자를 생성하는 동안 문제가 발생했습니다.", "components.UserList.usercreatedfailedexisting": "입력된 이메일 주소는 이미 다른 사용자가 사용 중입니다.", "components.UserList.usercreatedsuccess": "사용자가 성공적으로 생성되었습니다!", "components.UserList.userdeleteerror": "사용자를 삭제하는 동안 문제가 발생했습니다.", "components.UserList.userfail": "사용자 권한을 저장하는 동안 문제가 발생했습니다.", "components.UserList.userlist": "사용자 목록", "components.UserProfile.ProfileHeader.joindate": "{joindate}에 가입함", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "계정 유형", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "자동으로 영화 요청 <PlexWatchlistSupportLink>Plex 시청 목록</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "원작 언어로 콘텐츠 필터링", "components.UserProfile.UserSettings.UserGeneralSettings.region": "디스커버 지역", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "자동으로 시리즈 요청 <PlexWatchlistSupportLink>Plex 시청 목록</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.role": "역할", "components.UserProfile.UserSettings.UserGeneralSettings.user": "사용자", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "이메일 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "알림", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "알림 설정", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP 공개 키", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "다음을 사용하여 이메일 메시지 암호화 <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "사용자의 토큰 생성 <PushbulletSettingsLink>계정 설정</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Pushbullet 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "사용자 또는 그룹 키", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "조용히 전송", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "소리 없이 알림 보내기", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "유효한 사용자 ID를 입력해야 합니다", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "암호 확인", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "현재 암호", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "암호를 저장하는 동안 문제가 발생했습니다.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "비밀번호를 저장하는 동안 문제가 발생했습니다. 현재 비밀번호를 올바르게 입력하셨습니까?", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "암호가 일치해야 합니다", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "비밀번호가 너무 짧습니다. 최소 8자리 이상이어야 합니다", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "설정을 저장하는 동안 문제가 발생했습니다.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "권한이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.menuGeneralSettings": "일반", "components.UserProfile.UserSettings.menuNotifications": "알림", "components.UserProfile.UserSettings.menuPermissions": "권한", "components.UserProfile.pastdays": "{type} (지난 {days} 일)", "components.UserProfile.recentlywatched": "최근에 본 영화", "components.UserProfile.seriesrequest": "시리즈 요청", "components.UserProfile.totalrequests": "총 요청 수", "i18n.all": "전체", "i18n.approve": "승인", "i18n.approved": "승인됨", "i18n.available": "사용 가능", "i18n.back": "뒤로", "i18n.cancel": "취소", "i18n.canceling": "취소하는 중…", "i18n.close": "닫기", "i18n.deleting": "삭제 중…", "i18n.import": "불러오기", "i18n.importing": "불러오는 중…", "i18n.loading": "불러오는 중…", "i18n.movie": "영화", "i18n.pending": "보류 중", "i18n.previous": "이전", "i18n.requested": "요청됨", "i18n.requesting": "요청 중…", "i18n.resolved": "해결됨", "i18n.restartRequired": "다시 시작 필요", "i18n.resultsperpage": "페이지당 {pageSize}개의 결과 표시", "i18n.retry": "다시 시도", "i18n.retrying": "다시 시도 중…", "i18n.save": "변경 사항 저장", "i18n.saving": "저장 중…", "i18n.settings": "설정", "i18n.showingresults": "<strong>{from}</strong>부터 <strong>{to}</strong>까지의 결과 중 <strong>{total}</strong>개를 표시합니다", "i18n.status": "상태", "i18n.test": "테스트", "i18n.testing": "테스트 중…", "i18n.tvshow": "시리즈", "i18n.tvshows": "시리즈", "i18n.usersettings": "사용자 설정", "i18n.view": "보기", "pages.internalservererror": "내부 서버 오류", "pages.oops": "죄송합니다", "components.Discover.tmdbmoviestreamingservices": "TMDB 영화 스트리밍 서비스", "components.IssueDetails.problemepisode": "영향을 받는 에피소드", "components.IssueDetails.reopenissue": "이슈 다시 열기", "components.IssueDetails.openinarr": "{arr}에서 열기", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {시즌} other {시즌}}", "components.IssueList.sortAdded": "가장 최근", "components.IssueList.issues": "이슈", "components.IssueList.showallissues": "모든 이슈 표시", "components.IssueModal.CreateIssueModal.toastFailedCreate": "이슈를 제출하는 동안에 문제가 발생했습니다.", "components.Layout.LanguagePicker.displaylanguage": "표시 언어", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> 개발", "components.ManageSlideOver.tvshow": "시리즈", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {재생} other {재생}}", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes 시청자 점수", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes 토마토미터", "components.MovieDetails.runtime": "{minutes} 분", "components.PermissionEdit.manageissuesDescription": "미디어 이슈를 관리할 권한을 부여합니다.", "components.PersonDetails.crewmember": "제작진", "components.PermissionEdit.viewissues": "이슈 보기", "components.RequestBlock.decline": "요청 거부", "components.RequestModal.pendingrequest": "보류 중인 요청", "components.ResetPassword.emailresetlink": "이메일 복구 링크", "components.ResetPassword.confirmpassword": "비밀번호 확인", "components.ResetPassword.gobacklogin": "로그인 페이지로 돌아가기", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsPushover.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.discordsettingssaved": "Discord 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "웹훅 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.toastEmailTestSuccess": "이메일 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.validationPgpPrivateKey": "유효한 PGP 개인 키를 입력해야 합니다", "components.Settings.Notifications.validationSmtpPortRequired": "유효한 포트 번호를 입력해야 합니다", "components.Settings.RadarrModal.loadingrootfolders": "루트 폴더 불러오는 중…", "components.Settings.RadarrModal.selectMinimumAvailability": "최소 요구 사항 선택", "components.Settings.RadarrModal.validationRootFolderRequired": "루트 폴더를 선택해야 합니다", "components.Settings.RadarrModal.selecttags": "태그 선택", "components.Settings.RadarrModal.server4k": "4K 서버", "components.Settings.SettingsAbout.totalmedia": "전체 미디어", "components.Settings.SettingsAbout.totalrequests": "총 요청 수", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname}이(가) 취소되었습니다.", "components.Settings.SettingsJobsCache.jobname": "작업 이름", "components.Settings.SonarrModal.baseUrl": "기본 URL", "components.Settings.SonarrModal.defaultserver": "기본 서버", "components.Settings.SonarrModal.edit4ksonarr": "4K Sonarr 서버 수정", "components.Settings.activeProfile": "활성화된 프로필", "components.Settings.SonarrModal.validationPortRequired": "유효한 포트 번호를 입력해야 합니다", "components.TvDetails.productioncountries": "제작 {countryCount, plural, one {국가} other {국가}}", "components.UserList.owner": "소유자", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "표시 이름", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "이메일 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "텔레그램 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "유효한 Chat ID를 입력해야 합니다", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "웹 푸시", "components.UserProfile.unlimited": "무제한", "pages.returnHome": "홈으로 돌아가기", "components.Settings.tautulliSettings": "Tautulli 설정", "components.TvDetails.cast": "출연진", "components.UserList.user": "사용자", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "일반 설정", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat ID", "i18n.decline": "거절", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# 선택한 필터} other {# 선택한 필터}}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# 선택한 필터} other {# 선택한 필터}}", "components.Discover.PlexWatchlistSlider.emptywatchlist": "<PlexWatchlistSupportLink>Plex 시청 목록</PlexWatchlistSupportLink>에 추가한 미디어는 여기에 표시됩니다.", "components.Discover.StudioSlider.studios": "스튜디오", "components.Discover.discover": "디스커버", "components.Discover.resetsuccess": "디스커버 사용자 지정 설정을 재설정했습니다.", "components.Discover.resettodefault": "기본값으로 재설정", "components.Discover.studios": "스튜디오", "components.Discover.resetfailed": "디스커버 사용자 지정 설정을 재설정하는 중에 문제가 발생했습니다.", "components.Discover.trending": "인기 있는", "components.Discover.updatesuccess": "디스커버 사용자 지정 설정이 업데이트되었습니다.", "components.IssueDetails.IssueComment.delete": "댓글 삭제", "components.IssueDetails.IssueComment.postedby": "{username}님이 {relativeTime}에 게시함", "components.IssueDetails.IssueComment.postedbyedited": "{username}님이 {relativeTime}에 게시함 (수정됨)", "components.IssueDetails.allseasons": "모든 시즌", "components.IssueDetails.closeissue": "이슈 종료", "components.IssueDetails.closeissueandcomment": "댓글 첨부로 닫기", "components.IssueDetails.comments": "댓글", "components.IssueDetails.deleteissue": "이슈 삭제", "components.IssueDetails.commentplaceholder": "댓글 추가…", "components.IssueDetails.leavecomment": "댓글", "components.IssueDetails.issuetype": "유형", "components.IssueDetails.nocomments": "댓글이 없습니다.", "components.IssueDetails.openedby": "#{issueId}가 {username}에 의해 {relativeTime}에 열림", "components.IssueDetails.lastupdated": "마지막 업데이트", "components.IssueDetails.openin4karr": "4K {arr}에서 열기", "components.IssueDetails.toasteditdescriptionsuccess": "이슈 내용이 성공적으로 수정되었습니다!", "components.IssueDetails.toastissuedeleted": "이슈가 성공적으로 삭제되었습니다!", "components.IssueDetails.toastissuedeletefailed": "이슈를 삭제하는 동안 문제가 발생했습니다.", "components.IssueDetails.toaststatusupdated": "이슈 상태가 성공적으로 수정되었습니다!", "components.IssueModal.CreateIssueModal.extras": "추가 콘텐츠", "components.IssueModal.CreateIssueModal.allseasons": "모든 시즌", "components.IssueModal.issueAudio": "오디오", "components.IssueModal.issueOther": "기타", "components.IssueModal.issueVideo": "영상", "components.LanguageSelector.languageServerDefault": "기본값 ({language})", "components.LanguageSelector.originalLanguageDefault": "모든 언어", "components.Layout.Sidebar.issues": "이슈", "components.Layout.UserDropdown.myprofile": "프로필", "components.Login.loginerror": "로그인을 시도하는 중에 문제가 발생했습니다.", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON> 안정", "components.Login.signingin": "로그인 중…", "components.ManageSlideOver.manageModalIssues": "진행 중인 이슈", "components.ManageSlideOver.manageModalMedia": "미디어", "components.ManageSlideOver.manageModalNoRequests": "요청 없음.", "components.ManageSlideOver.mark4kavailable": "4K에서 사용 가능한 것으로 표시", "components.MovieDetails.budget": "제작비", "components.MovieDetails.reportissue": "이슈 보고", "components.MovieDetails.releasedate": "{releaseCount, plural, one {개봉일} other {개봉일}}", "components.MovieDetails.theatricalrelease": "극장 개봉일", "components.MovieDetails.studio": "{studioCount, plural, one {스튜디오} other {스튜디오}}", "components.NotificationTypeSelector.issuecreatedDescription": "이슈가 보고되면 알림을 보냅니다.", "components.NotificationTypeSelector.mediaAutoApproved": "자동 승인 요청", "components.NotificationTypeSelector.mediadeclined": "요청 거부됨", "components.NotificationTypeSelector.mediadeclinedDescription": "미디어 요청이 거부되면 알림을 보냅니다.", "components.NotificationTypeSelector.usermediadeclinedDescription": "미디어 요청이 거부되면 알림을 받습니다.", "components.PermissionEdit.advancedrequestDescription": "고급 미디어 요청 옵션을 수정할 권한을 부여합니다.", "components.PermissionEdit.autoapprove": "자동 승인", "components.PermissionEdit.autoapprove4kSeries": "4K 영화 자동 승인", "components.PermissionEdit.autoapproveSeries": "시리즈 자동 승인", "components.PermissionEdit.autoapproveSeriesDescription": "비 4K 시리즈 요청에 대해 자동 승인을 부여합니다.", "components.PermissionEdit.createissues": "이슈 보고", "components.PermissionEdit.autorequest": "자동 요청", "components.PermissionEdit.autorequestDescription": "Plex 시청 목록를 통해 4K가 아닌 미디어에 대한 요청을 자동으로 제출할 수 있는 권한을 부여합니다.", "components.PermissionEdit.managerequests": "요청 관리", "components.PermissionEdit.managerequestsDescription": "미디어 요청을 관리할 수 있는 권한을 부여합니다. 이 권한이 있는 사용자의 모든 요청은 자동으로 승인됩니다.", "components.PermissionEdit.request": "요청", "components.PermissionEdit.request4k": "4K 요청", "components.PermissionEdit.request4kTvDescription": "4K 시리즈에 대한 요청을 제출할 수 있는 권한을 부여합니다.", "components.PermissionEdit.requestDescription": "4K가 아닌 미디어에 대한 요청을 제출할 수 있는 권한을 부여합니다.", "components.PermissionEdit.users": "사용자 관리", "components.PermissionEdit.viewrequests": "요청 보기", "components.PermissionEdit.viewrequestsDescription": "다른 사용자가 제출한 미디어 요청을 볼 수 있는 권한을 부여합니다.", "components.PersonDetails.ascharacter": "{character} 역", "components.QuotaSelector.days": "{count, plural, one {일} other {일}}", "components.RegionSelector.regionServerDefault": "기본 ({region})", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{시즌} 당 {quotaDays} {일}</quotaUnits>", "components.RequestBlock.lastmodifiedby": "최종 수정자", "components.RequestBlock.requestoverrides": "요청 재정의", "components.RequestButton.approve4krequests": "승인 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.approverequest": "요청 승인", "components.RequestButton.approverequest4k": "4K 요청 승인", "components.RequestBlock.seasons": "{seasonCount, plural, one {시즌} other {시즌}}", "components.RequestButton.declinerequests": "거부 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.declinerequest": "요청 거부", "components.RequestButton.requestmore": "추가 요청", "components.RequestButton.requestmore4k": "4K에서 추가 요청", "components.RequestButton.viewrequest": "요청 보기", "components.RequestCard.deleterequest": "요청 삭제", "components.RequestCard.editrequest": "요청 수정", "components.RequestCard.failedretry": "요청을 다시 시도하는 동안 문제가 발생했습니다.", "components.RequestList.RequestItem.deleterequest": "요청 삭제", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {시즌} other {시즌}}", "components.RequestList.RequestItem.modified": "수정됨", "components.RequestModal.AdvancedRequester.destinationserver": "대상 서버", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "남은 시즌 요청이 충분하지 않음", "components.RequestModal.QuotaDisplay.quotaLinkUser": "<ProfileLink>프로필 페이지</ProfileLink>에서 이 사용자의 요청 제한에 관한 요약을 확인할 수 있습니다.", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {영화} other {영화}}", "components.RequestModal.errorediting": "요청을 수정하는 동안 문제가 발생했습니다.", "components.RequestModal.cancel": "요청 취소", "components.RequestModal.numberofepisodes": "에피소드 수", "components.RequestModal.edit": "요청 수정", "components.RequestModal.requestadmin": "이 요청은 자동으로 승인됩니다.", "components.RequestModal.requestCancel": "<strong>{title}</strong> 에 대한 요청이 취소되었습니다.", "components.RequestModal.requestseriestitle": "시리즈 요청", "components.RequestModal.season": "시즌", "components.RequestModal.seasonnumber": "시즌 {number}", "components.RequestModal.selectseason": "시즌 선택", "components.Search.search": "검색", "components.Search.searchresults": "검색 결과", "components.Selector.nooptions": "검색된 결과가 없습니다.", "components.Selector.searchStudios": "스튜디오 검색…", "components.Selector.starttyping": "검색하시려면 입력해주세요.", "components.Settings.Notifications.NotificationsGotify.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "프로필을 사용하지 않는 경우 <code>default</code> 프로필이 필요", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsLunaSea.profileName": "프로필 이름", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "사용자 또는 장치 기반 <LunaSeaLink>notification webhook URL</LunaSeaLink>", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "채널 태그", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Pushbullet 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet 테스트 알림 보내기…", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "Jellyseerr와 함께 사용할 <ApplicationRegistrationLink>애플리케이션 등록</ApplicationRegistrationLink>", "components.Settings.Notifications.NotificationsPushover.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.NotificationsPushover.accessToken": "애플리케이션 API 토큰", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "유효한 URL을 입력해야 합니다", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Web Push 알림을 받으려면 Overserr가 HTTPS를 통해 입력되어야 합니다.", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON 페이로드", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "기본값으로 재설정", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "웹훅 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "웹훅 URL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "유효한 JSON 페이로드를 입력해야 합니다", "components.Settings.Notifications.allowselfsigned": "자체 서명된 인증서 허용", "components.Settings.Notifications.authUser": "SMTP 사용자 이름", "components.Settings.Notifications.botApiTip": "Jellyseerr와 함께 사용할 <CreateBotLink>봇 생성</CreateBotLink>이 필요합니다", "components.Settings.Notifications.botUsername": "봇 사용자 이름", "components.Settings.Notifications.chatIdTip": "봇과 채팅을 시작하고 <GetIdBotLink>@get_id_bot</GetIdBotLink>, <code>/my_id</code> 명령을 실행하세요", "components.Settings.Notifications.discordsettingsfailed": "Discord 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.chatId": "Chat ID", "components.Settings.Notifications.botUsernameTip": "사용자가 봇과 채팅을 시작하고 자신의 알림을 구성하도록 허용", "components.Settings.Notifications.emailsettingsfailed": "이메일 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.enableMentions": "멘션 활성화", "components.Settings.Notifications.encryption": "암호화 방식", "components.Settings.Notifications.emailsettingssaved": "이메일 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.encryptionDefault": "가능한 경우 STARTTLS 사용", "components.Settings.Notifications.encryptionImplicitTls": "암시적 TLS 사용", "components.Settings.Notifications.encryptionNone": "없음", "components.Settings.Notifications.encryptionOpportunisticTls": "항상 STARTTLS 사용", "components.Settings.Notifications.pgpPassword": "PGP 비밀번호", "components.Settings.Notifications.pgpPrivateKey": "PGP 개인 키", "components.Settings.Notifications.pgpPrivateKeyTip": "다음을 사용하여 암호화된 이메일 메시지 서명 <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "조용히 전송", "components.Settings.Notifications.pgpPasswordTip": "다음을 사용하여 암호화된 이메일 메시지 서명 <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilentlyTip": "소리 없이 알림 보내기", "components.Settings.Notifications.senderName": "발신자 이름", "components.Settings.Notifications.smtpHost": "SMTP 호스트", "components.Settings.Notifications.smtpPort": "SMTP 포트", "components.Settings.Notifications.telegramsettingsfailed": "텔레그램 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.telegramsettingssaved": "텔레그램 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.validationPgpPassword": "PGP 비밀번호를 입력해야 합니다", "components.Settings.Notifications.validationBotAPIRequired": "봇 인증 토큰을 입력해야 합니다", "components.Settings.Notifications.validationChatIdRequired": "유효한 Chat ID를 입력해야 합니다", "components.Settings.Notifications.toastTelegramTestSuccess": "텔레그램 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.validationEmail": "유효한 이메일 주소를 입력해야 합니다", "components.Settings.Notifications.webhookUrlTip": "당신의 서버에서 <DiscordWebhookLink>웹훅 통합</DiscordWebhookLink> 만들기", "components.Settings.RadarrModal.minimumAvailability": "최소 요구 사항", "components.Settings.RadarrModal.servername": "서버 이름", "components.Settings.RadarrModal.validationNameRequired": "서버 이름을 입력해야 합니다", "components.Settings.RadarrModal.validationPortRequired": "유효한 포트 번호를 입력해야 합니다", "components.Settings.SettingsAbout.about": "정보", "components.Settings.SettingsAbout.Releases.releases": "출시", "components.Settings.SettingsAbout.helppaycoffee": "커피 한잔 사주기", "components.Settings.SettingsAbout.outofdate": "오래됨", "components.Settings.SettingsLogs.message": "메시지", "components.Settings.SettingsLogs.pauseLogs": "일시중지", "components.Settings.SettingsMain.originallanguageTip": "원작 언어로 콘텐츠 필터링", "components.Settings.SettingsMain.partialRequestsEnabled": "부분 시리즈 요청 허용", "components.Settings.SettingsUsers.localLoginTip": "사용자가 Plex OAuth 대신 이메일 주소와 암호를 사용하여 로그인하도록 허용", "components.Settings.SettingsUsers.movieRequestLimitLabel": "전역 영화 요청 제한", "components.Settings.SettingsUsers.toastSettingsSuccess": "사용자 설정이 성공적으로 저장되었습니다!", "components.Settings.SonarrModal.apiKey": "API 키", "components.Settings.SonarrModal.editsonarr": "Sonarr 서버 수정", "components.Settings.SonarrModal.notagoptions": "태그가 없습니다.", "components.Settings.SonarrModal.selecttags": "태그 선택", "components.Settings.SonarrModal.toastSonarrTestFailure": "Sonarr에 연결하지 못했습니다.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr 연결이 성공적으로 설정되었습니다!", "components.Settings.address": "주소", "components.Settings.enablessl": "SSL 사용", "components.Settings.menuPlexSettings": "Plex", "components.Settings.plexlibraries": "Plex 라이브러리", "components.Settings.plex": "Plex", "components.Settings.scanning": "동기화 중…", "components.Settings.services": "서비스", "components.Settings.validationUrl": "유효한 URL을 입력해야 합니다", "components.StatusBadge.openinarr": "{arr}에서 열기", "components.TvDetails.Season.somethingwentwrong": "시즌 데이터를 검색하는 동안 문제가 발생했습니다.", "components.UserList.creating": "생성 중…", "components.UserList.deleteuser": "사용자 삭제", "components.UserList.passwordinfodescription": "애플리케이션 URL을 구성하고 이메일 알림을 활성화하여 자동으로 비밀번호를 생성할 수 있도록 설정해주세요.", "components.UserList.role": "역할", "components.UserList.plexuser": "Plex 사용자", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "기본값 ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "소유자", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "시리즈 요청 제한", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "사용자 ID", "components.UserProfile.UserSettings.UserNotificationSettings.email": "이메일", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "30자의 <UsersGroupsLink>사용자 또는 그룹 식별자</UsersGroupsLink>를 입력해주세요", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Pushover 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "비밀번호가 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserPermissions.permissions": "권한", "components.UserProfile.emptywatchlist": "<PlexWatchlistSupportLink>Plex 시청 목록</PlexWatchlistSupportLink>에 추가한 미디어는 여기에 표시됩니다.", "components.UserProfile.movierequests": "영화 요청", "components.UserProfile.UserSettings.unauthorizedDescription": "이 사용자의 설정을 수정할 수 있는 권한이 없습니다.", "i18n.advanced": "고급", "i18n.edit": "수정", "i18n.next": "다음", "i18n.noresults": "결과가 없습니다.", "i18n.request4k": "4K로 요청", "i18n.processing": "처리 중", "i18n.request": "요청", "i18n.unavailable": "사용할 수 없음", "pages.somethingwentwrong": "문제가 발생했습니다", "components.Discover.customizediscover": "디스커버 사용자 지정", "components.Discover.resetwarning": "모든 슬라이더를 기본값으로 재설정합니다. 이렇게 하면 모든 사용자 지정 슬라이더도 삭제됩니다!", "components.Discover.stopediting": "수정 중단", "components.Discover.tmdbsearch": "TMDB 검색", "components.Discover.tmdbstudio": "TMDB 스튜디오", "components.Discover.tmdbtvstreamingservices": "TMDB TV 스트리밍 서비스", "components.IssueList.IssueItem.problemepisode": "영향을 받는 에피소드", "components.IssueDetails.IssueComment.edit": "댓글 수정", "components.IssueDetails.IssueDescription.deleteissue": "이슈 삭제", "components.Layout.Sidebar.users": "사용자", "components.ManageSlideOver.alltime": "전체 시간", "components.IssueDetails.problemseason": "영향을 받는 시즌", "components.IssueDetails.reopenissueandcomment": "댓글과 함께 다시 이슈 열기", "components.IssueDetails.toasteditdescriptionfailed": "이슈 내용을 수정하는 동안 문제가 발생했습니다.", "components.IssueDetails.toaststatusupdatefailed": "이슈 상태를 수정하는 동안 문제가 발생했습니다.", "components.IssueModal.CreateIssueModal.submitissue": "이슈 제출", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<strong>{title}</strong> 에 대한 이슈 보고서가 성공적으로 제출되었습니다!", "components.IssueModal.CreateIssueModal.validationMessageRequired": "반드시 내용을 입력해야 합니다", "components.Login.signinheader": "계속하려면 로그인하세요", "components.ManageSlideOver.movie": "영화", "components.MovieDetails.originaltitle": "원작명", "components.ManageSlideOver.openarr4k": "4K {arr}에서 열기", "components.MovieDetails.originallanguage": "원작 언어", "components.ManageSlideOver.manageModalClearMediaWarning": "* 이렇게 하면 모든 요청을 포함하여 이 {mediaType}에 대한 모든 데이터가 되돌릴 수 없게 삭제됩니다. 이 항목이 Plex 라이브러리에 있으면 다음 스캔 중에 미디어 정보가 다시 생성됩니다.", "components.ManageSlideOver.manageModalMedia4k": "4K 미디어", "components.ManageSlideOver.openarr": "{arr}에서 열기", "components.ManageSlideOver.manageModalRequests": "요청", "components.ManageSlideOver.manageModalTitle": "{mediaType} 관리", "components.ManageSlideOver.markallseasons4kavailable": "모든 시즌을 4K에서 사용 가능하게 표시", "components.ManageSlideOver.opentautulli": "Tautulli에서 열기", "components.ManageSlideOver.pastdays": "지난 {days, number}일", "components.MovieDetails.cast": "출연진", "components.MovieDetails.digitalrelease": "디지털 출시", "components.MovieDetails.managemovie": "영화 관리", "components.MovieDetails.mark4kavailable": "4K에서 사용 가능하게 표시", "components.MovieDetails.overview": "정보", "components.MovieDetails.overviewunavailable": "정보를 볼 수 없습니다.", "components.MovieDetails.revenue": "수익", "components.MovieDetails.productioncountries": "제작 {countryCount, plural, one {국가} other {국가}}", "components.MovieDetails.recommendations": "추천", "components.MovieDetails.showmore": "자세히 보기", "components.MovieDetails.streamingproviders": "현재 스트리밍 중", "components.NotificationTypeSelector.issuecreated": "보고된 이슈", "components.MovieDetails.similar": "비슷한 콘텐츠", "components.NotificationTypeSelector.issuecommentDescription": "이슈에 새 댓글이 수신되면 알림을 보냅니다.", "components.NotificationTypeSelector.issuereopened": "재개된 이슈", "components.NotificationTypeSelector.issueresolvedDescription": "이슈가 해결되면 알림을 보냅니다.", "components.NotificationTypeSelector.mediaapproved": "요청 승인됨", "components.NotificationTypeSelector.mediaautorequested": "자동으로 제출된 요청", "components.NotificationTypeSelector.userissueresolvedDescription": "보고한 이슈가 해결되면 알림을 받습니다.", "components.PermissionEdit.usersDescription": "사용자를 관리할 수 있는 권한을 부여합니다. 이 권한이 있는 사용자는 관리자 권한으로 사용자를 수정하거나 부여할 수 없습니다.", "components.QuotaSelector.unlimited": "무제한", "components.NotificationTypeSelector.issueresolved": "해결된 이슈", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "사용자가 자동으로 승인되는 새 미디어 요청을 제출할 때 알림을 보냅니다.", "components.NotificationTypeSelector.mediaapprovedDescription": "미디어 요청이 수동으로 승인되면 알림을 보냅니다.", "components.NotificationTypeSelector.mediaavailable": "요청 가능", "components.NotificationTypeSelector.mediafailedDescription": "미디어 요청이 Radarr 또는 Sonarr에 추가되지 않으면 알림을 보냅니다.", "components.NotificationTypeSelector.usermediaavailableDescription": "미디어 요청을 사용할 수 있게 되면 알림을 받습니다.", "components.NotificationTypeSelector.usermediarequestedDescription": "다른 사용자가 승인이 필요한 새 미디어 요청을 제출할 때 알림을 받습니다.", "components.NotificationTypeSelector.userissuecreatedDescription": "다른 사용자가 이슈를 보고하면 알림을 받습니다.", "components.PermissionEdit.request4kDescription": "4K 미디어에 대한 요청을 제출할 수 있는 권한을 부여합니다.", "components.NotificationTypeSelector.mediarequested": "보류 중인 승인 요청", "components.NotificationTypeSelector.userissuecommentDescription": "신고한 이슈에 새로운 댓글이 도착하면 알림을 받습니다.", "components.NotificationTypeSelector.userissuereopenedDescription": "보고한 이슈가 다시 열릴 때 알림을 받습니다.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "다른 사용자가 자동으로 승인되는 새 미디어 요청을 제출할 때 알림을 받습니다.", "components.NotificationTypeSelector.usermediaapprovedDescription": "미디어 요청이 승인되면 알림을 받습니다.", "components.NotificationTypeSelector.usermediafailedDescription": "미디어 요청이 Radarr 또는 Sonarr에 추가되지 않을 때 알림을 받습니다.", "components.PermissionEdit.adminDescription": "완전한 관리자 액세스. 다른 모든 권한 검사를 무시합니다.", "components.PermissionEdit.advancedrequest": "고급 요청", "components.PermissionEdit.viewrecent": "최근 추가된 항목 보기", "components.PermissionEdit.requestTvDescription": "비 4K 시리즈에 대한 요청을 제출할 수 있는 권한을 부여합니다.", "components.PermissionEdit.viewwatchlists": "Plex 시청 목록 보기", "components.PermissionEdit.viewwatchlistsDescription": "다른 사용자의 Plex 시청 목록을 볼 수 있는 권한을 부여합니다.", "components.PersonDetails.appearsin": "출연", "components.PermissionEdit.viewissuesDescription": "다른 사용자가 보고한 미디어 이슈를 볼 수 있는 권한을 부여합니다.", "components.PermissionEdit.viewrecentDescription": "최근 추가된 미디어 목록을 볼 수 있는 권한을 부여합니다.", "components.PersonDetails.alsoknownas": "다른 이름: {names}", "components.PersonDetails.birthdate": "{birthdate} 출생", "components.RequestBlock.profilechanged": "품질 프로필", "components.RequestBlock.rootfolder": "루트 폴더", "components.RequestList.RequestItem.cancelRequest": "요청 취소", "components.RequestList.RequestItem.requested": "요청됨", "components.RegionSelector.regionDefault": "모든 지역", "components.RequestBlock.approve": "요청 승인", "components.RequestBlock.languageprofile": "언어 프로필", "components.RequestButton.decline4krequests": "거부 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.declinerequest4k": "4K 요청 거부", "components.RequestBlock.edit": "요청 수정", "components.RequestBlock.server": "대상 서버", "components.RequestButton.approverequests": "승인 {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestCard.unknowntitle": "알 수 없는 제목", "components.RequestList.RequestItem.failedretry": "요청을 다시 시도하는 동안 문제가 발생했습니다.", "components.RequestList.RequestItem.requesteddate": "요청됨", "components.RequestList.sortAdded": "가장 최근", "components.RequestList.sortModified": "마지막 수정", "components.RequestList.RequestItem.editrequest": "요청 수정", "components.RequestList.RequestItem.modifieduserdate": "{user}의 {date}", "components.RequestModal.QuotaDisplay.movie": "영화", "components.ResetPassword.resetpasswordsuccessmessage": "비밀번호가 성공적으로 재설정되었습니다!", "components.Settings.Notifications.NotificationsWebhook.authheader": "인증 헤더", "components.RequestModal.AdvancedRequester.advancedoptions": "고급", "components.RequestModal.AdvancedRequester.animenote": "* 이 시리즈는 애니메이션입니다.", "components.RequestModal.AdvancedRequester.notagoptions": "태그가 없습니다.", "components.RequestModal.AdvancedRequester.selecttags": "태그 선택", "components.RequestModal.AdvancedRequester.tags": "태그", "components.RequestModal.SearchByNameModal.notvdbiddescription": "이 시리즈를 자동으로 매칭할 수 없습니다. 아래에서 올바른 일치 항목을 선택하십시오.", "components.RequestModal.AdvancedRequester.default": "{name} (기본값)", "components.RequestModal.AdvancedRequester.qualityprofile": "품질 프로필", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "언어 프로필", "components.RequestModal.AdvancedRequester.requestas": "다음으로 요청", "components.RequestModal.QuotaDisplay.allowedRequests": "<strong>{days}</strong> 일 마다 <strong>{limit}</strong> {type} 을(를) 요청할 수 있습니다 .", "components.RequestModal.QuotaDisplay.quotaLink": "<ProfileLink>프로필 페이지</ProfileLink>에서 요청 제한에 관한 요약을 확인할 수 있습니다.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "이 사용자는 <strong>{days}</strong> 일 마다 <strong>{limit}</strong> {type} 을(를) 요청할 수 있습니다.", "components.RequestModal.QuotaDisplay.requiredquota": "이 시리즈에 대한 요청을 제출하려면 최소한 <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}}가 남아 있어야 합니다.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "이 사용자가 이 시리즈에 대한 요청을 제출하려면 최소한 <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}}가 남아 있어야 합니다.", "components.RequestModal.SearchByNameModal.nomatches": "이 시리즈와 일치하는 항목을 찾을 수 없습니다.", "components.RequestModal.autoapproval": "자동 승인", "components.RequestModal.pending4krequest": "보류 중인 4K 요청", "components.RequestModal.requestSuccess": "<strong>{title}</strong> 요청 성공했습니다!", "components.RequestModal.requestApproved": "<strong>{title}</strong> 요청이 승인되었습니다!", "components.RequestModal.requestseries4ktitle": "4K 시리즈 요청", "components.RequestModal.requestcancelled": "<strong>{title}</strong> 에 대한 요청이 취소되었습니다.", "components.ResetPassword.email": "이메일 주소", "components.RequestModal.requestedited": "<strong>{title}</strong> 요청이 성공적으로 수정되었습니다!", "components.RequestModal.requesterror": "요청을 제출하는 동안 문제가 발생했습니다.", "components.RequestModal.requestseasons4k": "4K에서 {seasonCount} {seasonCount, plural, one {Season} other {Seasons}} 요청", "components.RequestModal.requestmovies": "{count} {count, plural, one {영화} other {영화}} 요청", "components.ResetPassword.password": "비밀번호", "components.ResetPassword.resetpassword": "비밀번호 재설정", "components.ResetPassword.validationpasswordminchars": "비밀번호가 너무 짧습니다; 최소 8자리 이상이어야 합니다", "components.ResetPassword.validationpasswordrequired": "암호를 입력해야 합니다", "components.Selector.showless": "간단히 보기", "components.Selector.showmore": "더 보기", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsGotify.token": "애플리케이션 토큰", "components.Settings.Notifications.NotificationsGotify.url": "서버 URL", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "웹훅 URL", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "액세스 토큰", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "사용자의 토큰 생성 <PushbulletSettingsLink>Account Settings</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "액세스 토큰을 입력해야 합니다", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsPushover.userToken": "사용자 또는 그룹 키", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "30자의 <UsersGroupsLink>사용자 또는 그룹 식별자</UsersGroupsLink>를 입력해주세요", "components.Settings.Notifications.NotificationsSlack.validationTypes": "적어도 하나 이상의 알림 유형을 선택해야 합니다", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "웹훅 URL", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Web Push 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "<WebhookLink>Incoming 웹훅</WebhookLink> 통합을 생성하세요", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web Push 테스트 알림을 보내는 중…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web Push 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "에이전트 활성화", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Web Push 알림 설정을 저장하지 못했습니다.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web Push 알림 설정이 성공적으로 저장되었습니다!", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON 페이로드가 성공적으로 재설정되었습니다!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "템플릿 변수 도움말", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "웹훅 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.authPass": "SMTP 비밀번호", "components.Settings.Notifications.agentenabled": "에이전트 활성화", "components.Settings.Notifications.emailsender": "발신자 주소", "components.Settings.Notifications.botAPI": "봇 인증 토큰", "components.Settings.SettingsMain.toastApiKeySuccess": "새 API 키가 성공적으로 생성되었습니다!", "components.Settings.Notifications.toastDiscordTestFailed": "Discord 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.toastDiscordTestSending": "Discord 테스트 알림을 보내는 중…", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord 테스트 알림이 전송되었습니다!", "components.Settings.Notifications.toastTelegramTestFailed": "텔레그램 테스트 알림을 보내지 못했습니다.", "components.Settings.Notifications.toastTelegramTestSending": "텔레그램 테스트 알림을 보내는 중…", "components.Settings.RadarrModal.createradarr": "새 Radarr 서버 추가", "components.Settings.RadarrModal.default4kserver": "기본 4K 서버", "components.Settings.RadarrModal.editradarr": "Radarr 서버 수정", "components.Settings.RadarrModal.defaultserver": "기본 서버", "components.Settings.RadarrModal.selectQualityProfile": "품질 프로필 선택", "components.Settings.RadarrModal.rootfolder": "루트 폴더", "components.Settings.RadarrModal.qualityprofile": "품질 프로필", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "최소 요구 사항을 선택해야 합니다", "components.ResetPassword.requestresetlinksuccessmessage": "만약 입력된 이메일 주소가 유효한 사용자와 연결되어 있다면, 비밀번호 재설정 링크가 해당 이메일 주소로 전송될 것입니다.", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "애플리케이션 토큰을 입력해야 합니다", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "유효한 URL을 입력해야 합니다", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "유효한 사용자 또는 그룹 키를 입력해야 합니다", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "유효한 URL을 입력해야 합니다", "components.Settings.Notifications.validationSmtpHostRequired": "유효한 호스트 네임 또는 IP 주소를 입력해야 합니다", "components.Settings.RadarrModal.validationHostnameRequired": "유효한 호스트 네임 또는 IP 주소를 입력해야 합니다", "components.Settings.Notifications.encryptionTip": "대부분의 경우 암시적 TLS는 465 포트를 사용하고 STARTTLS는 587 포트를 사용합니다", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} 변경 로그", "components.Settings.SettingsAbout.supportoverseerr": "Overseerr 지원", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON><PERSON><PERSON> 지원", "components.Settings.SettingsAbout.uptodate": "최신", "components.Settings.SettingsAbout.githubdiscussions": "GitHub 토론", "components.Settings.SettingsAbout.version": "버전", "components.Settings.SettingsJobsCache.cachevsize": "값 크기", "components.Settings.SettingsJobsCache.cachemisses": "누락", "components.Settings.SettingsJobsCache.cachename": "캐시 이름", "components.Settings.SettingsJobsCache.download-sync-reset": "다운로드 동기화 재설정", "components.Settings.SettingsJobsCache.image-cache-cleanup": "이미지 캐시 비우기", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "매 {jobScheduleMinutes, plural, one {분} other {{jobScheduleMinutes} 분}}", "components.Settings.SettingsJobsCache.flushcache": "캐시 플러시", "components.Settings.SettingsJobsCache.imagecachecount": "저장된 이미지", "components.Settings.SettingsJobsCache.imagecachesize": "총 캐시 크기", "components.Settings.SettingsLogs.filterInfo": "정보", "components.Settings.SettingsJobsCache.nextexecution": "다음 실행", "components.Settings.SettingsLogs.extraData": "추가 데이터", "components.Settings.SettingsLogs.time": "타임스탬프", "components.Settings.SettingsMain.cacheImages": "이미지 캐싱 활성화", "components.Settings.SettingsMain.generalsettingsDescription": "Jellyseerr에 대한 전역 및 기본 설정을 구성합니다.", "components.Settings.SettingsLogs.viewdetails": "세부 정보 보기", "components.Settings.SettingsMain.applicationurl": "애플리케이션 URL", "components.Settings.SettingsMain.apikey": "API 키", "components.Settings.SettingsMain.cacheImagesTip": "외부 소스 이미지 캐시 (상당한 디스크 공간 필요)", "components.Settings.SettingsMain.applicationTitle": "애플리케이션 이름", "components.Settings.SettingsMain.hideAvailable": "사용 가능한 미디어 숨기기", "components.Settings.SettingsMain.locale": "표시 언어", "components.Settings.SettingsMain.generalsettings": "일반 설정", "components.Settings.SettingsMain.originallanguage": "디스커버 언어", "components.Settings.SettingsMain.toastSettingsFailure": "설정을 저장하는 동안 문제가 발생했습니다.", "components.Settings.SettingsMain.toastSettingsSuccess": "사용자 설정이 성공적으로 저장되었습니다!", "components.Settings.SettingsMain.validationApplicationTitle": "애플리케이션 이름을 입력해야 합니다", "components.Settings.SonarrModal.syncEnabled": "스캔 활성화", "components.Settings.SettingsUsers.users": "사용자", "components.Settings.SonarrModal.add": "서버 추가", "components.Settings.SonarrModal.animelanguageprofile": "애니메이션 언어 프로필", "components.Settings.SonarrModal.create4ksonarr": "새 4K Sonarr 서버 추가", "components.Settings.SonarrModal.loadinglanguageprofiles": "언어 프로필 불러오는 중…", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "기본 URL 앞에는 슬래시가 있어야 합니다", "components.Settings.SonarrModal.validationNameRequired": "서버 이름을 입력해야 합니다", "components.Settings.serviceSettingsDescription": "아래에서 {serverType} 서버(들)를 구성하세요. 여러 개의 {serverType} 서버를 연결할 수 있지만, 기본 설정으로는 두 개만 지정할 수 있습니다 (비-4K 한 대와 4K 한 대). 관리자는 승인 이전에 새로운 요청을 처리하는 데 사용할 서버를 재지정할 수 있습니다.", "components.StatusBadge.managemedia": "{mediaType} 관리", "components.Settings.SonarrModal.animeTags": "애니메이션 태그", "components.Settings.SonarrModal.hostname": "호스트 네임 또는 IP 주소", "components.Settings.SonarrModal.testFirstTags": "태그 불러오기를 위한 연결 테스트", "components.Settings.SonarrModal.validationLanguageProfileRequired": "언어 프로필을 선택해야 합니다", "components.Settings.SonarrModal.validationProfileRequired": "품질 프로필을 선택해야 합니다", "components.Settings.addradarr": "Radarr 서버 추가", "components.Settings.default": "기본값", "components.Settings.email": "이메일", "components.Settings.experimentalTooltip": "이 설정을 활성화하면 예기치 않은 애플리케이션 동작이 발생할 수 있습니다", "components.Settings.notrunning": "실행 중이 아님", "components.Settings.deleteserverconfirm": "정말 서버를 삭제하시겠습니까?", "components.Settings.librariesRemaining": "남은 라이브러리: {count}", "components.Settings.noDefault4kServer": "4K {serverType} 서버는 사용자가 4K {mediaType} 요청을 제출할 수 있도록 기본 설정되어야 합니다.", "components.Settings.scan": "라이브러리 동기화", "components.Settings.plexsettingsDescription": "Plex 서버의 설정을 구성하세요. Jellyseerr는 Plex 라이브러리를 스캔하여 콘텐츠의 사용 가능성을 판단합니다.", "components.Settings.notificationsettings": "알림 설정", "components.Settings.plexsettings": "Plex 설정", "components.Settings.notificationAgentSettingsDescription": "알림 에이전트를 구성하고 활성화합니다.", "components.Settings.serverLocal": "로컬", "components.Settings.sonarrsettings": "Sonarr 설정", "components.Settings.ssl": "SSL", "components.Settings.startscan": "스캔 시작", "components.Settings.urlBase": "기본 URL", "components.Settings.toastPlexRefresh": "Plex에서 서버 목록 검색 중…", "components.Settings.toastTautulliSettingsSuccess": "Tautulli 설정이 성공적으로 저장되었습니다!", "components.Settings.validationApiKey": "API 키를 입력해야 합니다", "components.Settings.validationPortRequired": "유효한 포트 번호를 입력해야 합니다", "components.Settings.validationUrlBaseLeadingSlash": "기본 URL은 슬래시로 끝나서는 안 됩니다", "components.Settings.webAppUrlTip": "사용자에게 \"호스팅된\" 웹 앱 대신 서버의 웹 앱으로 이동하도록 선택적으로 설정할 수 있습니다", "components.Setup.setup": "설정", "components.Setup.welcome": "Jellyseerr에 오신 것을 환영합니다", "components.StatusBadge.status4k": "4K {status}", "components.StatusBadge.status": "{status}", "components.TvDetails.firstAirDate": "첫 방영일", "components.StatusChecker.restartRequired": "서버 재시작 필요", "components.StatusChecker.restartRequiredDescription": "수정된 설정을 적용하려면 서버를 다시 시작하십시오.", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TvDetails.reportissue": "이슈 보고", "components.UserList.admin": "관리자", "components.UserList.autogeneratepasswordTip": "서버에서 생성한 비밀번호를 사용자에게 이메일로 보내기", "components.UserList.deleteconfirm": "이 사용자를 삭제하시겠습니까? 모든 요청 데이터가 영구적으로 삭제됩니다.", "components.UserList.newplexsigninenabled": "<strong>새 Plex 로그인</strong> 설정이 현재 활성화되어 있습니다. 라이브러리 액세스 권한이 있는 Plex 사용자는 로그인하기 위해 미리 불러올 필요가 없습니다.", "components.UserList.userssaved": "사용자 권한이 성공적으로 저장되었습니다!", "components.UserList.validationEmail": "유효한 이메일 주소를 입력해야 합니다", "components.UserList.userdeleted": "사용자가 성공적으로 삭제되었습니다!", "components.UserList.users": "사용자", "components.UserList.password": "비밀번호", "components.UserList.validationpasswordminchars": "비밀번호가 너무 짧습니다. 최소 8자리 이상이어야 합니다", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Discord 사용자 계정과 연결된 <FindDiscordIdLink>다중 자릿수 ID 번호</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "유효한 애플리케이션 토큰을 입력해야 합니다", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex 사용자", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "액세스 토큰", "components.UserProfile.UserSettings.UserGeneralSettings.general": "일반", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "영화 요청 제한", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "전역 제한 재정의", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "디스커버 언어", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "로컬 사용자", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "설정을 저장하는 동안 문제가 발생했습니다.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "사용자 계정과 연결된 <FindDiscordIdLink>다중 자릿수 ID 번호</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "영화 자동 요청", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "유효한 Discord 사용자 ID를 입력해야 합니다", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "애플리케이션 API 토큰", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>애플리케이션 등록</ApplicationRegistrationLink>을 통해 {applicationTitle}과(와) 함께 사용할 수 있도록 등록해주세요", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>채팅 시작</TelegramBotLink>, <GetIdBotLink>@get_id_bot</GetIdBotLink>, <code>/my_id</code> 명령을 실행하세요", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "유효한 사용자 또는 그룹 키를 입력해야 합니다", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "텔레그램 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "유효한 PGP 공개 키를 입력해야 합니다", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "액세스 토큰을 입력해야 합니다", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "이 사용자 계정에는 현재 암호가 설정되어 있지 않습니다. 이 계정이 \"로컬 사용자\"로 로그인할 수 있도록 하려면 아래 암호를 구성하세요", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "웹 푸시 알림 설정을 저장하지 못했습니다.", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "새 암호", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "새 비밀번호를 확인해야 합니다", "components.UserProfile.UserSettings.menuChangePass": "비밀번호", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "웹 푸시 알림 설정이 성공적으로 저장되었습니다!", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "당신의 계정에는 현재 비밀번호가 설정되어 있지 않습니다. 이메일 주소를 사용하여 \"로컬 사용자\" 로 로그인하려면 아래 비밀번호를 구성하세요.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "이 사용자의 암호를 수정할 수 있는 권한이 없습니다.", "components.UserProfile.UserSettings.UserPasswordChange.password": "비밀번호", "i18n.open": "열기", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "현재 암호를 입력해야 합니다", "components.UserProfile.limit": "{limit} 중 {remaining}", "components.UserProfile.requestsperdays": "{limit} 남음", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "새 비밀번호를 입력해야 합니다", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "자신의 권한을 수정할 수 없습니다.", "components.UserProfile.plexwatchlist": "Plex 시청 목록", "components.UserProfile.recentrequests": "최근 요청", "i18n.areyousure": "확실합니까?", "i18n.declined": "거부됨", "i18n.collection": "컬렉션", "i18n.delete": "삭제", "i18n.delimitedlist": "{a}, {b}", "i18n.experimental": "실험적", "i18n.failed": "실패", "i18n.notrequested": "요청되지 않음", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.serviceunavailable": "서비스를 사용할 수 없음", "components.Settings.settingUpPlexDescription": "Plex를 설정하려면, 세부 정보를 수동으로 입력하거나 <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>에서 검색된 서버를 선택할 수 있습니다. 사용 가능한 서버 목록을 불러오려면 드롭다운 오른쪽에 있는 버튼을 누르세요.", "components.Settings.tautulliSettingsDescription": "선택적으로 Tautulli 서버의 설정을 구성하세요. Jellyseerr는 Tautulli로부터 Plex 미디어의 시청 기록 데이터를 불러옵니다.", "components.RequestBlock.requestdate": "요청 일자", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# 선택한 필터} other {# 선택한 필터}}", "components.QuotaSelector.seasons": "{count, plural, one {시즌} other {시즌}}", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {없음} other {<strong>#</strong>개의}} {type} {remaining, plural, one {요청} other {요청}} 남음", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "매 {jobScheduleHours, plural, one {시간} other {{jobScheduleHours} 시간}}", "components.MovieDetails.imdbuserscore": "IMDB 사용자 점수"}