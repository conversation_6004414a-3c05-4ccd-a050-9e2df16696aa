name: Merge Conflict Labeler

on:
  push:
    branches:
      - develop
  pull_request_target:
    branches:
      - develop
    types: [synchronize]

jobs:
  label:
    name: Labeling
    runs-on: ubuntu-latest
    if: ${{ github.repository == 'Fallenbagel/jellyseerr' }}
    permissions:
      contents: read
      pull-requests: write
    steps:
      - name: Apply label
        uses: eps1lon/actions-label-merge-conflict@v3
        with:
          dirtyLabel: 'merge conflict'
          commentOnDirty: 'This pull request has merge conflicts. Please resolve the conflicts so the PR can be successfully reviewed and merged.'
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
