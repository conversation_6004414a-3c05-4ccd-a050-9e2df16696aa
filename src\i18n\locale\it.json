{"components.Discover.recentrequests": "<PERSON><PERSON>", "components.Discover.recentlyAdded": "Aggiunti di recente", "components.Discover.populartv": "Serie popolari", "components.Discover.popularmovies": "Film popolari", "components.Settings.Notifications.emailsettingssaved": "Impostazioni delle notifiche via posta elettronica salvate con successo!", "components.Settings.Notifications.emailsettingsfailed": "Impossibile salvare le impostazioni delle notifiche via posta elettronica.", "components.Settings.Notifications.emailsender": "Indirizzo del mittente", "components.Settings.address": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.ssl": "Usa SSL", "components.Settings.SonarrModal.port": "Porta", "components.Settings.SonarrModal.hostname": "Hostname o indirizzo IP", "components.Settings.SettingsAbout.version": "Versione", "components.Settings.RadarrModal.ssl": "Usa SSL", "components.Settings.RadarrModal.port": "Porta", "components.Settings.RadarrModal.hostname": "Hostname o indirizzo IP", "components.Settings.Notifications.agentenabled": "Abilita Agente", "components.Search.searchresults": "Risultati di ricerca", "components.RequestModal.selectseason": "Seleziona stagioni", "components.RequestModal.seasonnumber": "Stagione {number}", "components.RequestModal.season": "Stagione", "components.RequestModal.requestadmin": "Questa richiesta sarà approvata automaticamente.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> richiesto con successo!", "components.RequestModal.requestCancel": "<PERSON><PERSON> per <strong>{title}</strong> eliminata.", "components.RequestModal.numberofepisodes": "Nº di episodi", "components.RequestModal.cancel": "Cancella la richiesta", "components.RequestList.requests": "<PERSON><PERSON>", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.PersonDetails.ascharacter": "come {character}", "components.PersonDetails.appearsin": "Comparse", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studio}}", "components.MovieDetails.similar": "<PERSON><PERSON> simi<PERSON>", "components.MovieDetails.runtime": "{minutes} minuti", "components.MovieDetails.revenue": "Incassi", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data di rilascio} other {Date di rilascio}}", "components.MovieDetails.recommendations": "Consigliati", "components.MovieDetails.overviewunavailable": "Trama non disponibile.", "components.MovieDetails.overview": "Trama", "components.MovieDetails.originallanguage": "Lingua originale", "components.MovieDetails.cast": "Cast", "components.MovieDetails.budget": "Budget", "components.MovieDetails.MovieCast.fullcast": "Cast completo", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Impostazioni", "components.Layout.Sidebar.requests": "<PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Cerca film e serie", "components.Discover.upcomingmovies": "Film in uscita", "components.Discover.upcoming": "Film in uscita", "components.Discover.trending": "Di tendenza", "components.UserList.deleteconfirm": "Sei sicuro di voler rimuovere questo utente? Tutti le richieste verranno rimosse permanentemente.", "components.UserList.created": "Unito", "components.UserList.admin": "Amministratore", "components.UserList.role": "<PERSON><PERSON><PERSON>", "components.UserList.plexuser": "Utente Plex", "components.UserList.deleteuser": "Elimina l'utente", "components.UserList.userlist": "<PERSON><PERSON><PERSON> u<PERSON>", "components.UserList.userdeleteerror": "Qualcosa è andato storto nel rimuovere l'utente.", "components.UserList.userdeleted": "Utente rimosso con successo!", "components.UserList.user": "Utente", "components.UserList.totalrequests": "<PERSON><PERSON>", "i18n.declined": "Rifiutato", "i18n.decline": "<PERSON><PERSON><PERSON><PERSON>", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.available": "Disponibile", "i18n.approved": "A<PERSON>rovato", "i18n.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.unavailable": "Non disponibile", "i18n.tvshows": "Serie", "i18n.processing": "Elaborazione", "i18n.pending": "In sospeso", "i18n.partiallyavailable": "Parzialmente disponibile", "i18n.movies": "Film", "i18n.deleting": "Eliminazione…", "i18n.delete": "Elimina", "pages.oops": "Ops", "components.Settings.RadarrModal.rootfolder": "Cartella principale", "components.Settings.RadarrModal.selectRootFolder": "Seleziona una cartella principale", "components.Settings.RadarrModal.selectQualityProfile": "Seleziona un profilo qualità", "components.Settings.RadarrModal.selectMinimumAvailability": "Seleziona la disponibilità minima", "components.Settings.RadarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.RadarrModal.minimumAvailability": "Disponibilità minima", "components.Settings.RadarrModal.editradarr": "Modifica server Radarr", "components.Settings.RadarrModal.defaultserver": "Server predefinito", "components.Settings.RadarrModal.apiKey": "Chiave API", "components.Settings.RadarrModal.add": "Aggiungi un server", "components.Settings.Notifications.discordsettingsfailed": "Impossibile salvare le impostazioni di Discord.", "components.RequestModal.requestfrom": "La richiesta di {username} è in attesa di approvazione.", "components.RequestModal.pendingrequest": "<PERSON><PERSON> in sospeso", "components.Layout.Sidebar.dashboard": "Esplora", "components.TvDetails.cast": "Cast", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCast.fullseriescast": "Cast completo della serie", "components.Setup.welcome": "Benvenuti in Jellyseerr", "components.Settings.validationPortRequired": "È necessario fornire un numero di porta valido", "components.Settings.validationHostnameRequired": "È necessario fornire un valido hostname o indirizzo IP", "components.Settings.startscan": "Avvia la scansione", "components.Settings.ssl": "SSL", "components.Settings.radarrsettings": "Impostazioni Radarr", "components.Settings.plexsettings": "Impostazioni Plex", "components.Settings.notrunning": "Non in esecuzione", "components.Settings.notificationsettings": "Impostazioni delle notifiche", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notifiche", "components.Settings.menuLogs": "<PERSON><PERSON>", "components.Settings.menuGeneralSettings": "Generali", "components.Settings.menuAbout": "Info", "components.Settings.manualscan": "Scansione manuale della libreria", "components.Settings.librariesRemaining": "Biblioteche rimanenti: {count}", "components.Settings.hostname": "Hostname o indirizzo IP", "components.Settings.deleteserverconfirm": "Sei sicuro/a di voler eliminare questo server?", "components.Settings.default4k": "4K predefinito", "components.Settings.default": "Predefinito", "components.Settings.currentlibrary": "Libreria corrente: {name}", "components.Settings.copied": "Chiave API copiata negli appunti.", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON>'anal<PERSON>", "components.Settings.addsonarr": "Aggiungi un server Sonarr", "components.Settings.addradarr": "Aggiungi un server Radarr", "components.Settings.activeProfile": "Profilo attivo", "components.Settings.SonarrModal.validationRootFolderRequired": "È necessario selezionare una cartella principale", "components.Settings.SonarrModal.validationProfileRequired": "È necessario selezionare un profilo di qualità", "components.Settings.SonarrModal.validationPortRequired": "È necessario fornire un numero di porta valido", "components.Settings.SonarrModal.validationNameRequired": "È necessario dare un nome di server", "components.Settings.SonarrModal.validationHostnameRequired": "È necessario fornire un hostname o un indirizzo IP valido", "components.Settings.SonarrModal.validationApiKeyRequired": "È necessario fornire una chiave API", "components.Settings.SonarrModal.testFirstRootFolders": "Testa la connessione per caricare le cartelle", "components.Settings.SonarrModal.testFirstQualityProfiles": "Testa la connessione per caricare profili di qualità", "components.Settings.SonarrModal.servername": "Nome server", "components.Settings.SonarrModal.server4k": "Server 4K", "components.Settings.SonarrModal.selectRootFolder": "Seleziona una cartella principale", "components.Settings.SonarrModal.selectQualityProfile": "Seleziona un profilo qualità", "components.Settings.SonarrModal.seasonfolders": "Cartelle stagione", "components.Settings.SonarrModal.rootfolder": "Cartella principale", "components.Settings.SonarrModal.qualityprofile": "<PERSON><PERSON>", "components.Settings.SonarrModal.loadingrootfolders": "Caricamento delle cartelle…", "components.Settings.SonarrModal.loadingprofiles": "Caricamento profili qualità…", "components.Settings.SonarrModal.editsonarr": "Modifica server <PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.defaultserver": "Server predefinito", "components.Settings.SonarrModal.createsonarr": "Aggiungi un nuovo server Sonarr", "components.Settings.SonarrModal.baseUrl": "URL di base", "components.Settings.SonarrModal.apiKey": "Chiave API", "components.Settings.SonarrModal.animerootfolder": "Cartella principale anime", "components.Settings.SonarrModal.animequalityprofile": "Profilo qualità anime", "components.Settings.SonarrModal.add": "Aggiungi un server", "components.Settings.SettingsAbout.totalrequests": "Totale richieste", "components.Settings.SettingsAbout.totalmedia": "Media totali", "components.Settings.SettingsAbout.overseerrinformation": "Informazioni su Jellyseerr", "components.Settings.SettingsAbout.githubdiscussions": "Discussioni su GitHub", "components.Settings.SettingsAbout.gettingsupport": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationRootFolderRequired": "È necessario selezionare una cartella principale", "components.Settings.RadarrModal.validationProfileRequired": "È necessario selezionare un profilo di qualità", "components.Settings.RadarrModal.validationPortRequired": "È necessario fornire un numero di porta valido", "components.Settings.RadarrModal.validationNameRequired": "È necessario fornire un nome al server", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "È necessario selezionare una disponibilità minima", "components.Settings.RadarrModal.validationHostnameRequired": "È necessario fornire un hostname o un indirizzo IP valido", "components.Settings.RadarrModal.validationApiKeyRequired": "È necessario fornire una chiave API", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Connessione a Radarr stabilita con successo!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Impossibile connettersi a Radarr.", "components.Settings.RadarrModal.testFirstRootFolders": "Testa la connessione per caricare le cartelle", "components.Settings.RadarrModal.testFirstQualityProfiles": "Testa la connessione per caricare i profili qualità", "components.Settings.RadarrModal.servername": "Nome server", "components.Settings.RadarrModal.server4k": "Server 4K", "components.Settings.RadarrModal.loadingrootfolders": "Caricamento delle cartelle…", "components.Settings.RadarrModal.loadingprofiles": "Caricamento profili di qualità…", "components.Settings.RadarrModal.createradarr": "Aggiungi un nuovo server Radarr", "components.Settings.RadarrModal.baseUrl": "URL di base", "components.Settings.Notifications.webhookUrl": "URL webhook", "components.Settings.Notifications.validationSmtpPortRequired": "È necessario fornire un numero di porta valido", "components.Settings.Notifications.validationSmtpHostRequired": "È necessario fornire un hostname o un indirizzo IP valido", "components.Settings.Notifications.smtpPort": "Porta SMTP", "components.Settings.Notifications.smtpHost": "Host SMTP", "components.Settings.Notifications.discordsettingssaved": "Impostazioni di Discord salvate con successo!", "components.Settings.Notifications.authUser": "Nome utente SMTP", "components.Settings.Notifications.authPass": "Password SMTP", "components.RequestModal.requestseasons": "<PERSON><PERSON> {seasonCount} {seasonCount, plural, one {St<PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.TvDetails.similar": "Serie simili", "components.TvDetails.showtype": "Tipo di serie", "components.TvDetails.recommendations": "Consigliati", "components.TvDetails.overviewunavailable": "Trama non disponibile.", "components.TvDetails.overview": "Trama", "components.TvDetails.originallanguage": "Lingua originale", "components.TvDetails.network": "{networkCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON>}}", "components.Setup.finishing": "Finalizzazione…", "components.Settings.menuJobs": "Processi & Cache", "components.Setup.signinMessage": "Comincia accedendo con il tuo account Plex", "components.Settings.sonarrsettings": "Impostazioni Sonarr", "components.Settings.plexsettingsDescription": "Configura le impostazioni per il tuo server Plex. Jellyseerr scansiona le tue librerie Plex per determinare la disponibilità dei contenuti.", "components.Settings.plexlibrariesDescription": "Le librerie che Jellyseerr scansiona per i titoli. Configura e salva le impostazioni di connessione Plex, quindi fai clic sul pulsante qui sotto se non sono elencate librerie.", "components.Settings.plexlibraries": "Librerie Plex", "components.Settings.manualscanDescription": "Normalmente, questo verrà eseguito solo una volta ogni 24 ore. Je<PERSON><PERSON>rr controllerà il server Plex per i nuovi aggiunti in modo più aggressivo. Se è la prima volta che configuri Plex, si consiglia di eseguire una scansione manuale completa della libreria!", "components.Settings.port": "Porta", "components.Setup.configureservices": "Configura i servizi", "components.Setup.finish": "Termina la configurazione", "components.Setup.continue": "Continua", "pages.returnHome": "Ritorna alla pagina iniziale", "i18n.close": "<PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "<PERSON>ri un caffè", "components.Settings.SettingsAbout.Releases.viewongithub": "Visualizza su GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Visualizza il registro modifiche", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} <PERSON><PERSON> ca<PERSON>i", "components.Settings.SettingsAbout.Releases.releases": "Versioni", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Dati di versione non sono momentaneamente disponibili.", "components.Settings.SettingsAbout.Releases.latestversion": "Versione più recente", "components.Settings.SettingsAbout.Releases.currentversion": "Versione attuale", "components.UserList.importfromplexerror": "Qualcosa è andato storto nell'importare gli utenti Plex.", "components.UserList.importfromplex": "Importa utenti Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {utente} other {utenti}} Plex {userCount, plural, one {importato} other {importati}} correttamente!", "components.TvDetails.viewfullcrew": "Vedi troupe completa", "components.TvDetails.TvCrew.fullseriescrew": "Troupe completa serie", "components.PersonDetails.crewmember": "Troupe", "components.MovieDetails.MovieCrew.fullcrew": "Troupe completa", "components.MovieDetails.viewfullcrew": "Vedi troupe completa", "components.Settings.Notifications.allowselfsigned": "Consenti i certificati autofirmati", "components.TvDetails.firstAirDate": "Prima data di messa in onda", "components.CollectionDetails.requestcollection": "<PERSON><PERSON> r<PERSON>", "components.CollectionDetails.overview": "Trama", "components.CollectionDetails.numberofmovies": "{count} Film", "components.TvDetails.watchtrailer": "Guarda il trailer", "components.MovieDetails.watchtrailer": "Guarda il trailer", "i18n.requested": "<PERSON><PERSON>", "components.RequestList.RequestItem.failedretry": "Qualcosa è andato storto nel riprovare la richiesta.", "i18n.retry": "<PERSON><PERSON><PERSON><PERSON>", "i18n.failed": "Fallito", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL webhook", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Impostazioni di Slack salvate con successo!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Impossibile salvare le impostazioni di Slack.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Abilita Agente", "components.Settings.Notifications.validationChatIdRequired": "È necessario fornire un ID della discussione valido", "components.Settings.Notifications.validationBotAPIRequired": "Devi fornire un token di autorizzazione del bot", "components.Settings.Notifications.telegramsettingssaved": "Impostazioni di Telegram salvate con successo!", "components.Settings.Notifications.telegramsettingsfailed": "Impossibile salvare le impostazioni di Telegram.", "components.Settings.Notifications.senderName": "Nome del mittente", "components.Settings.Notifications.chatId": "ID chat", "components.Settings.Notifications.botAPI": "Token di autorizzazione bot", "components.Settings.SettingsAbout.documentation": "Documentazione", "components.NotificationTypeSelector.mediarequestedDescription": "Invia notifiche quando gli utenti presentano nuove richieste di media che richiedono approvazione.", "components.NotificationTypeSelector.mediarequested": "Richiesta in attesa di approvazione", "components.NotificationTypeSelector.mediafailedDescription": "Invia notifiche quando le richieste non vengono aggiunte a Radarr o Sonarr.", "components.NotificationTypeSelector.mediafailed": "Elaborazione della richiesta non riuscita", "components.NotificationTypeSelector.mediaavailableDescription": "Invia notifiche quando i media richiesti diventano disponibili.", "components.NotificationTypeSelector.mediaapprovedDescription": "Invia notifiche quando le richieste sono approvate manualmente.", "components.NotificationTypeSelector.mediaapproved": "Richie<PERSON> approvata", "components.NotificationTypeSelector.mediaavailable": "Richiesta disponibile", "i18n.request": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "È necessario fornire una chiave utente o di gruppo valida", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "È necessario fornire un token di applicazione valido", "components.Settings.Notifications.NotificationsPushover.userToken": "Chiave utente o di gruppo", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Impostazioni di Pushover salvate con successo!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Impossibile salvare le impostazioni di Pushover.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Abilita Agente", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token API applicazione", "components.RequestList.sortModified": "Ultima modifica", "components.RequestList.sortAdded": "<PERSON><PERSON> recente", "components.RequestList.showallrequests": "<PERSON>ra tutte le richieste", "components.RequestButton.declinerequest4k": "Rifiuta la richiesta 4K", "components.RequestButton.declinerequest": "Rifiuta la richiesta", "components.RequestButton.decline4krequests": "Rifiuta {requestCount, plural, one {Richiesta 4K} other {{requestCount} Richieste 4K}}", "components.RequestButton.approverequests": "Approva {requestCount, plural, one {<PERSON><PERSON>} other {{requestCount} Richie<PERSON>}}", "components.RequestButton.approverequest4k": "Approva richiesta 4K", "components.RequestButton.approverequest": "Approva la richiesta", "components.RequestButton.approve4krequests": "Approva {requestCount, plural, one {Richie<PERSON> 4K} other {{requestCount} Richieste 4K}}", "components.UserList.creating": "Creazione…", "components.UserList.createlocaluser": "Crea un utente locale", "components.UserList.create": "<PERSON><PERSON>", "components.UserList.autogeneratepassword": "Genera automaticamente la password", "components.UserList.password": "Password", "components.UserList.localuser": "Utente locale", "components.UserList.usercreatedsuccess": "Utente creato correttamente!", "components.UserList.usercreatedfailed": "Qualcosa è andato storto nel creare l'utente.", "components.UserList.validationpasswordminchars": "La password è troppo corta; deve avere un minimo di 8 caratteri", "components.UserList.passwordinfodescription": "Configura un URL dell'applicazione e abilita le notifiche via e-mail per consentire la generazione automatica della password.", "components.UserList.email": "Indirizzo e-mail", "components.RequestButton.viewrequest4k": "Visualizza la richiesta 4K", "components.RequestButton.viewrequest": "Visualizza la richiesta", "components.RequestButton.requestmore4k": "<PERSON><PERSON> di più in 4K", "components.RequestButton.requestmore": "<PERSON><PERSON> di più", "components.Login.validationpasswordrequired": "È necessario fornire una password", "components.Login.validationemailrequired": "È necessario fornire un indirizzo e-mail valido", "components.Login.signinwithoverseerr": "Usa il tuo account {applicationTitle}", "components.Login.password": "Password", "components.Login.loginerror": "Qualcosa è andato storto durante il tentativo di accesso.", "components.Login.email": "Indirizzo e-mail", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Guida per variabili di modello", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Abilita Agente", "components.Settings.Notifications.NotificationsWebhook.authheader": "Intestazione di autorizzazione", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON>", "components.RequestBlock.server": "Server di destinazione", "components.RequestBlock.rootfolder": "Cartella principale", "components.RequestBlock.profilechanged": "<PERSON><PERSON>", "components.NotificationTypeSelector.mediadeclinedDescription": "Invia notifiche quando i media richiesti vengono rifiutati.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON> rifiutata", "i18n.experimental": "Sperimentale", "i18n.edit": "Modifica", "components.StatusBadge.status4k": "4K {status}", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Impostazioni di Webhook salvate con successo!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Impossibile salvare le impostazioni di Webhook.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL del webhook", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "È necessario fornire un payload JSON valido", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Payload JSON reimpostato correttamente!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Ripristino delle impostazioni predefinite", "components.Settings.Notifications.NotificationsWebhook.customJson": "Payload JSON", "components.RequestModal.requestedited": "<PERSON><PERSON> di <strong>{title}</strong> modificata correttamente!", "components.RequestModal.requestcancelled": "<PERSON><PERSON> per <strong>{title}</strong> eliminata.", "components.RequestModal.pending4krequest": "Richiesta 4K in sospeso", "components.RequestModal.errorediting": "Qualcosa è andato storto durante la modifica della richiesta.", "components.RequestModal.autoapproval": "Approvazione automatica", "components.RequestModal.AdvancedRequester.rootfolder": "Cartella principale", "components.RequestModal.AdvancedRequester.qualityprofile": "Profilo di qualità", "components.RequestModal.AdvancedRequester.destinationserver": "Server di destinazione", "components.RequestModal.AdvancedRequester.default": "{name} (Standard)", "components.RequestModal.AdvancedRequester.animenote": "* Questa serie è un anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "Avanzate", "components.RequestButton.declinerequests": "Rifiuta {requestCount, plural, one {<PERSON><PERSON>} other {{requestCount} Richie<PERSON>}}", "components.RequestBlock.requestoverrides": "Aggiramenti della richiesta", "components.UserList.bulkedit": "Modifica collettiva", "components.UserList.userssaved": "Permessi salvati con successo!", "components.PermissionEdit.users": "Gestisci gli utenti", "components.PermissionEdit.request4kTv": "Richiedi serie in 4K", "components.PermissionEdit.request4kMovies": "Rechiedi film in 4K", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapproveSeries": "Approva Automaticamente le Serie", "components.PermissionEdit.autoapproveMovies": "Approva Automaticamente i Film", "components.PermissionEdit.autoapprove": "Approvazione automatica", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON> a<PERSON>", "components.PermissionEdit.adminDescription": "Accesso amministratore completo. Aggira tutti gli altri permessi.", "components.PermissionEdit.admin": "Amministratore", "components.Login.signinwithplex": "Usa il tuo account Plex", "components.Login.signinheader": "Accedi per continuare", "components.Login.signingin": "Accesso in corso…", "components.Login.signin": "Accedi", "components.MovieDetails.markavailable": "Segna come disponibile", "components.MovieDetails.mark4kavailable": "Segna come disponibile in 4K", "components.Login.forgotpassword": "Password dimenticata?", "components.Discover.discover": "Esplora", "components.AppDataWarning.dockerVolumeMissingDescription": "Il volume <code>{appDataPath}</code> non è configurato correttamente. Tutte le modifiche apportate saranno perse quando il container verrà interrotto o riavviato.", "components.Settings.serverpresetLoad": "Premi il pulsante per caricare i server disponibili", "components.Settings.serverpreset": "Server", "components.Settings.serverRemote": "remoto", "components.Settings.serverLocal": "locale", "components.Settings.notificationAgentSettingsDescription": "Configura e abilita gli agenti di notifica.", "components.Settings.SonarrModal.validationLanguageProfileRequired": "È necessario selezionare un profilo lingua", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "L'URL di base non deve terminare con una barra obliqua", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "L'URL di base deve avere una barra obliqua", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "L'URL non deve terminare con una barra obliqua", "components.Settings.SonarrModal.validationApplicationUrl": "È necessario fornire un URL valido", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Connessione a Sonarr stabilita con successo!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Impossibile connettersi a Sonarr.", "components.Settings.SonarrModal.animelanguageprofile": "Profilo lingua anime", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Verifica la connessione per caricare i profili della lingua", "components.Settings.SonarrModal.syncEnabled": "Abilita Sc<PERSON>ione", "components.Settings.SonarrModal.selectLanguageProfile": "Seleziona il profilo della lingua", "components.Settings.SonarrModal.loadinglanguageprofiles": "Caricamento dei profili di lingua…", "components.Settings.SonarrModal.languageprofile": "Profilo lingua", "components.Settings.SonarrModal.externalUrl": "URL esterno", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON> ora", "components.Settings.SettingsJobsCache.process": "Processo", "components.Settings.SettingsJobsCache.nextexecution": "Prossima esecuzione", "components.Settings.SettingsJobsCache.jobtype": "Tipo", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} avviato.", "components.Settings.SettingsJobsCache.jobsDescription": "Je<PERSON><PERSON>rr esegue alcune attività di manutenzione come lavori programmati regolarmente, ma possono anche essere attivati manualmente qui sotto. Eseguire manualmente un lavoro non altererà la sua programmazione.", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobname": "Nome processo", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} annullato.", "components.Settings.SettingsJobsCache.flushcache": "Svuota cache", "components.Settings.SettingsJobsCache.command": "Comand<PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON>o", "components.Settings.SettingsJobsCache.cachevsize": "Dimensione valore", "components.Settings.SettingsJobsCache.cachename": "Nome cache", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheksize": "Dimensione chiave", "components.Settings.SettingsJobsCache.cachekeys": "<PERSON><PERSON> totali", "components.Settings.SettingsJobsCache.cachehits": "Chiamate", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} cache svuotata.", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr memorizza nella cache le richieste agli endpoint API esterni per ottimizzare le prestazioni e evitare chiamate API non necessarie.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "L'URL di base non deve terminare con una barra obliqua", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "L'URL di base deve avere una barra obliqua", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "L'URL non deve terminare con una barra obliqua", "components.Settings.RadarrModal.validationApplicationUrl": "È necessario fornire un URL valido", "components.Settings.RadarrModal.syncEnabled": "Abilita Sc<PERSON>ione", "components.Settings.RadarrModal.externalUrl": "URL esterno", "components.Settings.Notifications.validationEmail": "È necessario fornire un indirizzo e-mail valido", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "È necessario fornire un URL valido", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "È necessario fornire un URL valido", "components.Search.search": "Cerca", "components.ResetPassword.validationemailrequired": "È necessario fornire un indirizzo e-mail valido", "components.ResetPassword.validationpasswordrequired": "È necessario fornire una password", "components.ResetPassword.validationpasswordminchars": "La password è troppo corta; dovrebbe avere un minimo di 8 caratteri", "components.ResetPassword.validationpasswordmatch": "Le password devono coincidere", "components.ResetPassword.resetpasswordsuccessmessage": "La password è stata reimpostata con successo!", "components.ResetPassword.resetpassword": "Reimposta la password", "components.ResetPassword.requestresetlinksuccessmessage": "Un collegamento per reimpostare la password sarà inviato all'indirizzo e-mail fornito se è associato a un utente valido.", "components.ResetPassword.password": "Password", "components.ResetPassword.gobacklogin": "Ritorna alla pagina di accesso", "components.ResetPassword.emailresetlink": "Invia un link di recupero via e-mail", "components.ResetPassword.email": "Indirizzo e-mail", "components.ResetPassword.confirmpassword": "Conferma la password", "components.RequestModal.requesterror": "Qualcosa è andato storto durante l'invio della richiesta.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Non siamo riusciti a trovare una corrispondenza per questa serie. Seleziona la corrispondenza corretta dall'elenco seguente.", "components.RequestModal.AdvancedRequester.requestas": "<PERSON><PERSON> come", "components.RequestModal.AdvancedRequester.languageprofile": "Profilo lingua", "components.PermissionEdit.viewrequestsDescription": "Concede il permesso di visualizzare le richieste di altri utenti.", "components.PermissionEdit.viewrequests": "Visualizza le richieste", "components.PermissionEdit.usersDescription": "Concede il permesso di gestire gli utenti. Gli utenti con questo permesso non possono modificare gli utenti con privilegio di Amministratore, o concederlo.", "components.PermissionEdit.requestDescription": "Concedere l'autorizzazione per richiedere media non 4K.", "components.PermissionEdit.request4kTvDescription": "Concede l'autorizzazione per richiedere serie in 4K.", "components.PermissionEdit.request4kMoviesDescription": "Concede l'autorizzazione per richiedere film in 4K.", "components.PermissionEdit.request4kDescription": "Concede l'autorizzazione per richiedere media in 4K.", "components.PermissionEdit.request4k": "Richiesta 4K", "components.PermissionEdit.request": "<PERSON><PERSON>", "components.PermissionEdit.managerequestsDescription": "Concede il permesso di gestire le richieste. Tutte le richieste fatte da un utente con questo permesso sono automaticamente approvate.", "components.PermissionEdit.autoapproveSeriesDescription": "Concede l'approvazione automatica per le richieste di serie non in 4K.", "components.PermissionEdit.autoapproveMoviesDescription": "Concede l'approvazione automatica per le richieste di film non in 4K.", "components.PermissionEdit.autoapproveDescription": "Concede l'approvazione automatica per tutte le richieste non in 4K.", "components.PermissionEdit.advancedrequestDescription": "Concede il permesso di modificare opzioni di richiesta avanzate.", "i18n.advanced": "<PERSON><PERSON><PERSON>", "components.UserList.validationEmail": "<PERSON><PERSON> rich<PERSON>a", "components.UserList.users": "<PERSON><PERSON><PERSON>", "components.Setup.setup": "Impostazione", "components.Settings.toastPlexRefreshSuccess": "Elenco dei server Plex recuperato con successo!", "components.Settings.toastPlexRefreshFailure": "Impossibile recuperare l'elenco dei server Plex.", "components.Settings.toastPlexRefresh": "Recupero dell'elenco dei server da Plex…", "components.Settings.toastPlexConnectingSuccess": "Connessione a Plex stabilita con successo!", "components.Settings.toastPlexConnectingFailure": "Impossibile connettersi a Plex.", "components.Settings.toastPlexConnecting": "Tentativo di connessione a Plex…", "components.Settings.serverpresetRefreshing": "Recupero di server…", "components.Settings.serverpresetManualMessage": "Configurazione manuale", "components.TvDetails.nextAirDate": "Prossima data di messa in onda", "components.Settings.settingUpPlexDescription": "Per impostare Plex, potete inserire i dati manualmente o selezionare un server recuperato da <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Premi il pulsante a destra del menu a tendina per recuperare la lista di server disponibili.", "components.Settings.Notifications.sendSilentlyTip": "Invia notifiche senza suono", "components.Settings.Notifications.sendSilently": "Invia silenziosamente", "components.UserList.sortCreated": "Data di unione", "components.UserList.sortRequests": "Conteggio richieste", "components.UserList.sortDisplayName": "<PERSON>me da mostrare", "components.PermissionEdit.autoapprove4k": "Auto-approva 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Concede l'approvazione automatica per le richieste di film in 4K.", "components.PermissionEdit.autoapprove4kMovies": "Auto-approva i film in 4K", "components.PermissionEdit.autoapprove4kSeriesDescription": "Concede l'approvazione automatica per le richieste di serie in 4K.", "components.PermissionEdit.autoapprove4kSeries": "Auto-approva le serie in 4K", "components.PermissionEdit.autoapprove4kDescription": "Concede l'approvazione automatica per tutte le richieste 4K.", "components.Layout.UserDropdown.myprofile": "<PERSON>ilo", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Impostazioni Notifiche", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Il <FindDiscordIdLink>numero ID a più cifre</FindDiscordIdLink> associato all'account utente", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID utente", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Impostazioni salvate con successo!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Qualcosa è andato storto nel salvare le impostazioni.", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Utente Plex", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Utente locale", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Impostazioni generali", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "<PERSON>me da mostrare", "components.UserProfile.ProfileHeader.settings": "Modifica le impostazioni", "components.UserProfile.ProfileHeader.profile": "Visualizza profilo", "components.UserList.userfail": "Qualcosa è andato storto durante il salvataggio dei permessi.", "components.UserList.edituser": "Modifica le autorizzazioni dell'utente", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "È necessario fornire un token di accesso", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Impostazioni di Pushover salvate correttamente!", "components.Layout.UserDropdown.settings": "Impostazioni", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Impossibile salvare le impostazioni di Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Abilita Agente", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Token di accesso", "components.UserProfile.recentrequests": "<PERSON><PERSON>", "components.UserProfile.UserSettings.menuPermissions": "Autorizzazioni", "components.UserProfile.UserSettings.menuNotifications": "Notifiche", "components.UserProfile.UserSettings.menuGeneralSettings": "Generali", "components.UserProfile.UserSettings.menuChangePass": "Password", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Permessi salvati correttamente!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Qualcosa è andato storto nel salvare le impostazioni.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Autorizzazioni", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "La password è troppo corta; deve avere un minimo di 8 caratteri", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "È necessario fornire una nuova password", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "È necessario fornire la password attuale", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Le password devono coincidere", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "È necessario confermare la nuova password", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Password salvata correttamente!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Qualcosa è andato storto durante il salvataggio della password.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Password", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nuova password", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Password attuale", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Conferma la password", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "È necessario fornire un ID utente valido", "components.CollectionDetails.requestcollection4k": "<PERSON><PERSON> in 4K", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Regione da scoprire", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Lingua da scoprire", "components.Settings.webhook": "Webhook", "components.Settings.email": "E-mail", "components.RegionSelector.regionDefault": "Tutte le regioni", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtra i contenuti per disponibilità regionale", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtra i contenuti per lingua originale", "components.Discover.upcomingtv": "Serie in uscita", "components.RegionSelector.regionServerDefault": "Predefinito ({region})", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Non hai il permesso di modificare la password di questo utente.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Utente", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Proprietario", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Amministratore", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Tipo di account", "components.UserList.owner": "Proprietario", "components.UserList.accounttype": "Tipo", "components.Discover.DiscoverNetwork.networkSeries": "Serie di {network}", "components.Discover.DiscoverMovieGenre.genreMovies": "Film di {genre}", "components.Settings.SettingsJobsCache.download-sync-reset": "Reimposta sincronizzazione di scaricamento", "i18n.loading": "Caricamento…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "È necessario fornire un ID chat valido", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Inizia una chat</TelegramBotLink>, aggiungi <GetIdBotLink>@get_id_bot</GetIdBotLink>, ed esegui il comando <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID chat", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Invia notifiche senza suono", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Invia silenziosamente", "components.TvDetails.seasons": "{seasonCount, plural, one {# St<PERSON><PERSON>} other {# <PERSON><PERSON><PERSON>}}", "components.Settings.SettingsJobsCache.unknownJob": "Task sconosciuto", "components.Settings.SettingsJobsCache.download-sync": "Scarica sincronizzazione", "components.Settings.Notifications.botUsername": "Username del Bot", "components.Discover.DiscoverTvGenre.genreSeries": "Serie di {genre}", "components.Discover.DiscoverStudio.studioMovies": "Film di {studio}", "components.Settings.scanning": "Sincronizzazione…", "components.Settings.scan": "Sincronizza Librerie", "components.Settings.SettingsJobsCache.sonarr-scan": "Scansione Sonarr", "components.Settings.SettingsJobsCache.radarr-scan": "Scansione Radarr", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Scansione aggiunti di recente su Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Scansione completa della libreria di Plex", "components.Settings.Notifications.validationUrl": "È necessario fornire un URL valido", "components.Settings.Notifications.botAvatarUrl": "URL avatar bot", "components.RequestList.RequestItem.requested": "<PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} da {user}", "components.RequestList.RequestItem.modified": "Modificato", "components.Discover.StudioSlider.studios": "<PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "<PERSON><PERSON>", "components.Discover.DiscoverTvLanguage.languageSeries": "Serie in {language}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Film in {language}", "components.UserProfile.ProfileHeader.userid": "ID utente: {userid}", "components.UserProfile.ProfileHeader.joindate": "Is<PERSON>rit<PERSON>/a dal {joindate}", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.userSettingsDescription": "Configura le impostazioni utente globali e predefinite.", "components.Settings.SettingsUsers.userSettings": "Impostazioni Utente", "components.Settings.SettingsUsers.toastSettingsSuccess": "Impostazioni utente salvate correttamente!", "components.Settings.SettingsUsers.toastSettingsFailure": "Qualcosa è andato storto durante il salvataggio delle impostazioni.", "components.Settings.SettingsUsers.localLogin": "Abilita Accesso Locale", "components.Settings.SettingsUsers.defaultPermissions": "Autorizzazioni Predefinite", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Invia notifiche quando gli utenti presentano nuove richieste che vengono approvate automaticamente.", "components.NotificationTypeSelector.mediaAutoApproved": "Richiesta approvata automaticamente", "components.Settings.Notifications.pgpPrivateKey": "Chiave privata PGP", "components.Settings.Notifications.pgpPasswordTip": "Firma i messaggi di posta elettronica crittografati utilizzando <OpenPgpLink> OpenPGP </OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Password PGP", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.Discover.TvGenreSlider.tvgenres": "Generi Serie", "components.Discover.MovieGenreSlider.moviegenres": "Generi film", "components.UserProfile.UserSettings.unauthorizedDescription": "Non hai l'autorizzazione per modificare le impostazioni di questo utente.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Non è possibile modificare le proprie autorizzazioni.", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuti", "components.TvDetails.episodeRuntime": "Durata di un episodio", "components.RequestModal.alreadyrequested": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "Generi serie", "components.Discover.MovieGenreList.moviegenres": "Generi film", "components.Settings.Notifications.pgpPrivateKeyTip": "Firma i messaggi di posta elettronica crittografati utilizzando <OpenPgpLink> OpenPGP </OpenPgpLink>", "pages.somethingwentwrong": "Qualcosa è andato storto", "pages.serviceunavailable": "Servizio non disponibile", "pages.pagenotfound": "Pagina non trovata", "pages.internalservererror": "Errore interno del server", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.usersettings": "Impostazioni utente", "i18n.settings": "Impostazioni", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Qualcosa è andato storto durante il salvataggio della password. La password attuale è stata inserita correttamente?", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notifiche", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Generali", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.plex": "Plex", "components.Settings.notifications": "Notifiche", "components.Settings.SettingsLogs.logs": "<PERSON><PERSON>", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.time": "Data e ora", "components.Settings.SettingsLogs.showall": "Mostra tutti i registri", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "Pausa", "components.Settings.SettingsLogs.message": "Messaggio", "components.Settings.SettingsLogs.logsDescription": "Puoi anche visualizzare questi registri direttamente tramite <code>stdout</code>, o in <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.level": "Gravità", "components.Settings.SettingsLogs.label": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterWarn": "Attenzione", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterError": "Errore", "components.Settings.SettingsLogs.filterDebug": "Debug", "components.Settings.SettingsJobsCache.jobsandcache": "Processi e cache", "components.Settings.SettingsAbout.about": "Info", "components.ResetPassword.passwordreset": "Reimposta la password", "components.Settings.enablessl": "Usa SSL", "components.Settings.SettingsLogs.logDetails": "Dettagli registro", "components.Settings.SettingsLogs.extraData": "<PERSON><PERSON> aggiu<PERSON>", "components.Settings.SettingsLogs.copyToClipboard": "Copia negli appunti", "components.Settings.SettingsLogs.copiedLogMessage": "Messaggio di registro copiato negli appunti.", "components.UserList.nouserstoimport": "Non ci sono utenti PLex da importare.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "Data di nascita {birthdate}", "components.PersonDetails.alsoknownas": "<PERSON>ri nomi: {names}", "i18n.delimitedlist": "{a}, {b}", "components.MovieDetails.originaltitle": "<PERSON><PERSON>", "i18n.view": "Vista", "i18n.tvshow": "Serie", "i18n.testing": "Test in corso…", "i18n.test": "Test", "i18n.status": "Stato", "i18n.showingresults": "Mostrando <strong>{from}</strong> a <strong>{to}</strong> di <strong>{total}</strong> risultati", "i18n.resultsperpage": "Mostra {pageSize} risultati per pagina", "i18n.saving": "Salvataggio…", "i18n.save": "<PERSON><PERSON>", "i18n.requesting": "<PERSON><PERSON> in corso…", "i18n.request4k": "<PERSON><PERSON> in 4K", "i18n.previous": "Precedente", "i18n.notrequested": "Non Richiesto", "i18n.noresults": "<PERSON><PERSON><PERSON> r<PERSON>.", "i18n.next": "<PERSON><PERSON>", "i18n.movie": "Film", "i18n.canceling": "Annullamento…", "i18n.back": "Indietro", "i18n.areyousure": "Sei sicuro?", "i18n.all": "<PERSON><PERSON>", "components.UserProfile.unlimited": "Illimitato", "components.UserProfile.totalrequests": "<PERSON><PERSON>", "components.UserProfile.seriesrequest": "Richieste Serie", "components.UserProfile.requestsperdays": "{limit, plural, one {rimanente} other {rimanenti}}", "components.UserProfile.pastdays": "{type} (ultimi {days} giorni)", "components.UserProfile.movierequests": "Richieste Film", "components.UserProfile.limit": "{remaining} di {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limite <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limite <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Ignora Limite Globale", "components.TvDetails.originaltitle": "<PERSON><PERSON>", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Limite Globale Richiesta Serie", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Limite Globale Richiesta Film", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {stagione} other {stagioni}}", "components.RequestModal.QuotaDisplay.season": "stagione", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Questo utente deve avere almeno <strong>{seasons}</strong> {seasons, plural, one {richiesta rimanente} other {richieste rimanenti}} per poter richiedere questa serie.", "components.RequestModal.QuotaDisplay.requiredquota": "Devi avere almeno <strong>{seasons}</strong> {seasons, plural, one {richiesta rimanente} other {richieste rimanenti}} per poter richiedere questa serie.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {richiesta rimanente} other {richieste rimanenti}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Puoi vedere un riassunto sui limiti di questo utente nella sua <ProfileLink>pagina profilo</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Puoi vedere un riepilogo dei tuoi limiti nella <ProfileLink>pagina del tuo profilo</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Non rimangono abbastanza stagioni da richiedere", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {film}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Questo utente è autorizzato a richiedere <strong>{limit}</strong> {type} ogni <strong>{days}</strong> giorni.", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON><PERSON><PERSON> rich<PERSON>ere <strong>{limit}</strong> {type} ogni <strong>{days}</strong> giorni.", "components.QuotaSelector.unlimited": "Illimitato", "components.LanguageSelector.originalLanguageDefault": "<PERSON>tte le lingue", "components.LanguageSelector.languageServerDefault": "Predefinito ({language})", "components.Settings.SonarrModal.testFirstTags": "Verifica la connessione per caricare i tag", "components.Settings.SonarrModal.tags": "Tag", "components.Settings.SonarrModal.selecttags": "Seleziona i tag", "components.Settings.SonarrModal.notagoptions": "Nessun tag.", "components.Settings.SonarrModal.loadingTags": "Caricamento tag…", "components.Settings.SonarrModal.edit4ksonarr": "Modifica server Sonarr 4K", "components.Settings.SonarrModal.default4kserver": "Server 4K predefinito", "components.Settings.SonarrModal.create4ksonarr": "Aggiungi un nuovo server Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Tag Anime", "components.Settings.RadarrModal.testFirstTags": "Verifica la connessione per caricare i tag", "components.Settings.RadarrModal.tags": "Tag", "components.Settings.RadarrModal.selecttags": "Seleziona i tag", "components.Settings.RadarrModal.notagoptions": "Nessun tag.", "components.Settings.RadarrModal.loadingTags": "Caricamento tag…", "components.Settings.RadarrModal.edit4kradarr": "Modifica server Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Server 4K predefinito", "components.Settings.RadarrModal.create4kradarr": "Aggiungi un nuovo server Radarr 4K", "components.RequestModal.AdvancedRequester.tags": "Tag", "components.RequestModal.AdvancedRequester.selecttags": "Seleziona i tag", "components.RequestModal.AdvancedRequester.notagoptions": "Nessun tag.", "components.Layout.VersionStatus.outofdate": "Non aggiornato", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {versione} other {versioni}} indietro", "components.Settings.serviceSettingsDescription": "Configura i tuoi server {serverType} qui sotto. Puoi collegare più server {serverType}, ma solo due possono essere contrassegnati come predefiniti (uno non-4K e uno 4K). Gli amministratori possono selezionare il server usato per elaborare le nuove richieste prima dell'approvazione.", "components.Settings.noDefaultServer": "Almeno un server {serverType} deve essere contrassegnato come predefinito affinché le richieste {mediaType} possano essere processate.", "components.Settings.noDefaultNon4kServer": "Se hai solo un singolo server {serverType} per contenuti non-4K e 4K (o se scarichi solo contenuti 4K), il tuo server {serverType} <strong>NON</strong> dovre<PERSON> essere designato come server 4K.", "components.Settings.mediaTypeSeries": "serie", "components.Settings.mediaTypeMovie": "film", "components.Settings.SettingsAbout.uptodate": "Aggiornato", "components.Settings.SettingsAbout.outofdate": "Non aggiornato", "components.Settings.Notifications.validationPgpPrivateKey": "È necessario fornire una chiave privata PGP valida", "components.Settings.Notifications.validationPgpPassword": "È necessario fornire una password PGP", "components.Settings.Notifications.botUsernameTip": "Consenti inoltre agli utenti di avviare una chat con il tuo bot e configurare le proprie notifiche personali", "components.RequestModal.pendingapproval": "La richiesta è in attesa di approvazione.", "components.RequestList.RequestItem.mediaerror": "{mediaType} non trovato", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON>", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.mediaerror": "{mediaType} non trovato", "components.RequestCard.deleterequest": "<PERSON><PERSON>", "components.NotificationTypeSelector.notificationTypes": "Tipi di Notifica", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr Stabile", "i18n.retrying": "Nuovo tentativo in corso …", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Il tuo account attualmente non ha una password impostata. Configura una password qui sotto per abilitare l'accesso come \"utente locale\" usando il tuo indirizzo email.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Questo account utente attualmente non ha una password impostata. Configura una password qui sotto per permettere a questo account di accedere come \"utente locale.\"", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "È necessario fornire una chiave pubblica PGP valida", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Impostazioni di Telegram salvate con successo!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Impossibile salvare le impostazioni di Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Crittografa i messaggi di posta elettronica usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Chiave pubblica PGP", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Impostazioni Email salvate con successo!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Impossibile salvare le impostazioni Email.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Impostazioni di Discord salvate con successo!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Impossibile salvare le impostazioni di Discord.", "components.UserList.usercreatedfailedexisting": "L'indirizzo e-mail fornito è già in uso da un altro utente.", "components.UserList.autogeneratepasswordTip": "Invia una password generata automaticamente all'utente via e-mail", "components.Settings.serverSecure": "sicuro", "components.RequestModal.edit": "Modifica Richiesta", "components.RequestList.RequestItem.editrequest": "Modifica Richiesta", "components.Settings.SonarrModal.enableSearch": "Abilita Ricerca Automatica", "components.Settings.RadarrModal.enableSearch": "Abilita Ricerca Automatica", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Notifiche Web", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Lingua di visualizzazione", "components.Settings.webpush": "Notifica Web", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Impostazioni di notifica web salvate con successo!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Impossibile salvare le impostazioni di notifica web.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Abilita Agente", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Impostazioni di notifica Web push salvate correttamente!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Impossibile salvare le impostazioni di notifica via Web push.", "components.Settings.noDefault4kServer": "Un server 4K {serverType} deve essere contrassegnato come predefinito per permettere agli utenti di inviare richieste 4K {mediaType}.", "components.Settings.is4k": "4K", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "È necessario fornire un URL valido", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Impostazioni LunaSea salvate con successo!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Impossibile salvare le impostazioni di notifica LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Richie<PERSON> solo se non si usa il profilo <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Abilita Agente", "components.PermissionEdit.requestTvDescription": "Concedere l'autorizzazione per richiedere serie non 4K.", "components.PermissionEdit.requestTv": "Richiedi Serie", "components.PermissionEdit.requestMoviesDescription": "Concedere il permesso di richiedere film non in 4K.", "components.PermissionEdit.requestMovies": "Richiedi Film", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Invio notifica di prova webhook…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Invio notifica di prova webhook non riuscito.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Notifica di prova web push inviata!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Invio notifica di prova web push…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Impossibile inviare la notifica di prova Web push.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Notifica di prova con Slack inviata!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "<PERSON>vio della notifica di prova con <PERSON>ck…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "<PERSON>vio della notifica di prova S<PERSON>ck non riuscito.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Notifica di prova Pushover inviata!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Invio notifica di prova Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Impossibile inviare la notifica di prova Pushover.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Notifica di prova Pushbullet inviata!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Invio notifica di prova Pushbullet …", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Invio della notifica di prova Pushbullet non riuscito.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Notifica di test LunaSea inviata!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Invio notifica di prova con LunaSea …", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Impossibile inviare notifica con LunaSea.", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON> {time}", "components.Settings.Notifications.encryptionImplicitTls": "Usa TLS Implicito", "components.Settings.Notifications.encryptionTip": "Sol<PERSON>mente, TLS Implicito usa la porta 465 e STARTTLS usa la porta 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Usa sempre STARTTLS", "components.Settings.Notifications.encryptionNone": "Nessuna", "components.Settings.Notifications.encryptionDefault": "Usa STARTTLS se disponibile", "components.Settings.Notifications.encryption": "Metodo di crittografia", "components.Settings.Notifications.chatIdTip": "Inizia una chat con il tuo bot, aggiungi <GetIdBotLink>@get_id_bot</GetIdBotLink>, ed emetti il comando <code>/my_id</code>", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Crea un bot</CreateBotLink> per l'uso con Je<PERSON>seerr", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Notifica Webhook di prova inviata!", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Crea un'integrazione <WebhookLink>Incoming Webhook</WebhookLink>", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Il tuo identificatore <UsersGroupsLink>utente o gruppo</UsersGroupsLink> di 30 caratteri", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicazioneRegistrazioneLink>Registra un'applicazione</ApplicazioneRegistrazioneLink> per l'uso con Je<PERSON>seerr", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Crea un token dalle tue <PushbulletSettingsLink>Impostazioni account</PushbulletSettingsLink>", "components.Settings.Notifications.toastDiscordTestFailed": "Invio della notifica di prova Discord non riuscito.", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON> un <DiscordWebhookLink>integrazione webhook</DiscordWebhookLink> nel server", "components.Settings.Notifications.toastTelegramTestSuccess": "Notifica di prova con Telegram inviata!", "components.Settings.Notifications.toastTelegramTestSending": "Invio notifica di prova con Telegram…", "components.Settings.Notifications.toastTelegramTestFailed": "Impossibile inviare notifica di prova con Telegram.", "components.Settings.Notifications.toastEmailTestSuccess": "Notifica email di prova inviata!", "components.Settings.Notifications.toastEmailTestSending": "Invio notifica email di prova…", "components.Settings.Notifications.toastEmailTestFailed": "Impossibile inviare la notifica email di prova.", "components.Settings.Notifications.toastDiscordTestSending": "<PERSON>vio della notifica Discord di prova…", "components.Settings.Notifications.toastDiscordTestSuccess": "Notifica Discord di prova inviata!", "components.UserList.localLoginDisabled": "L'impostazione <strong>Abilita Accesso Locale</strong> è attualmente disabilitata.", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Predefinito ({language})", "components.Settings.webAppUrlTip": "Indirizza opzionalmente gli utenti alla web app sul tuo server invece che alla web app \"ospitata\"", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "La tua <LunaSeaLink>notifica webhook URL</LunaSeaLink> basata su utente o dispositivo", "components.Settings.webAppUrl": "URL <WebAppLink>Web App</WebAppLink>", "components.Settings.SettingsUsers.newPlexLoginTip": "Permetti agli utenti di Plex di accedere senza essere prima importati", "components.Settings.SettingsUsers.newPlexLogin": "Abilita nuovo accesso con Plex", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Per ricevere notifiche push web, <PERSON><PERSON><PERSON><PERSON> deve essere servito tramite HTTPS.", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON>", "components.RequestCard.failedretry": "Qualcosa è andato storto nel riprovare la richiesta.", "components.Settings.SettingsUsers.localLoginTip": "Consentire agli utenti di accedere utilizzando l'indirizzo di posta elettronica e la password, anziché Plex OAuth", "components.Settings.SettingsUsers.defaultPermissionsTip": "Permessi assegnati ai nuovi utenti", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {stagione} other {stagioni}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {film}}", "components.QuotaSelector.days": "{count, plural, one {giorno} other {giorni}}", "components.Settings.Notifications.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsSlack.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsPushover.validationTypes": "È necessario selezionare almeno un tipo di notifica", "components.Settings.SettingsAbout.betawarning": "Questo software è in BETA. Alcuni componenti potrebbero non funzionare correttamente. Aiutaci segnalando i problemi su GitHub!", "components.NotificationTypeSelector.usermediarequestedDescription": "Ricevi una notifica quando altri utenti presentano nuove richieste che richiedono l'approvazione.", "components.NotificationTypeSelector.usermediafailedDescription": "Ricevi una notifica quando le richieste non vengono aggiunte a Radarr o Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Ricevi una notifica quando le tue richieste vengono rifiutate.", "components.NotificationTypeSelector.usermediaavailableDescription": "Ricevi una notifica quando le tue richieste diventano disponibili.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Ricevi una notifica quando le tue richieste vengono approvate.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Ricevi una notifica quando altri utenti inviano nuove richieste che vengono approvate automaticamente.", "components.Layout.LanguagePicker.displaylanguage": "Lingua Interfaccia", "components.MovieDetails.showmore": "Mostra di più", "components.MovieDetails.showless": "<PERSON>ra meno", "components.TvDetails.streamingproviders": "Ora in streaming su", "components.MovieDetails.streamingproviders": "Ora in streaming su", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.edit": "Modifica commento", "components.IssueDetails.IssueComment.postedby": "Inviato {relativeTime} da{username}", "components.IssueDetails.IssueComment.validationComment": "Devi inserire un messaggio", "components.IssueDetails.IssueDescription.deleteissue": "Elimina segnalazione", "components.IssueDetails.comments": "Commenti", "components.IssueDetails.toasteditdescriptionfailed": "Qualcosa è andato storto durante la modifica della descrizione.", "components.IssueDetails.closeissue": "<PERSON>udi <PERSON>", "components.IssueDetails.closeissueandcomment": "<PERSON><PERSON> con un commento", "components.IssueDetails.deleteissue": "Elimina segnalazione", "components.IssueDetails.deleteissueconfirm": "Sei sicuro di voler eliminare questa segnalazione?", "components.IssueDetails.issuepagetitle": "Segnalazione", "components.IssueDetails.issuetype": "Tipo", "components.IssueDetails.problemepisode": "Episodio coinvolto", "components.IssueDetails.problemseason": "Stagione coinvolta", "components.IssueDetails.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.toastissuedeleted": "Segnalazione eliminata con successo!", "components.IssueDetails.toastissuedeletefailed": "Qualcosa è andato storto durante l'eliminazione della segnalazione.", "components.IssueDetails.toaststatusupdated": "Stato segnalazione aggiornato correttamente!", "components.IssueDetails.toaststatusupdatefailed": "Qualcosa è andato storto durante l'aggiornamento dello stato della segnalazione.", "components.IssueList.IssueItem.problemepisode": "Episodio coinvolto", "components.IssueModal.CreateIssueModal.problemepisode": "Episodio coinvolto", "components.IssueModal.CreateIssueModal.problemseason": "Stagione coinvolta", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Qualcosa è andato storto durante l'invio della segnalazione.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Segnalazione per <strong>{title}</strong> inviata correttamente!", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Devi fornire una descrizione", "components.IssueModal.CreateIssueModal.whatswrong": "Qual è il problema?", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "Altro", "components.IssueModal.issueSubtitles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.issueVideo": "Video", "components.ManageSlideOver.markavailable": "Segna come disponibile", "components.ManageSlideOver.tvshow": "serie tv", "components.IssueDetails.IssueDescription.description": "Descrizione", "components.IssueList.IssueItem.viewissue": "Visualizza segnalazione", "components.IssueDetails.IssueComment.areyousuredelete": "Sei sicuro di voler eliminare questo commento?", "components.IssueDetails.IssueComment.delete": "Elimina commento", "components.IssueDetails.IssueComment.postedbyedited": "Inviato {relativeTime} da {username} (Modificato)", "components.IssueDetails.leavecomment": "Commento", "components.IssueDetails.IssueDescription.edit": "Modifica descrizione", "components.IssueDetails.allepisodes": "<PERSON><PERSON> gli episodi", "components.IssueDetails.allseasons": "<PERSON><PERSON> le stagioni", "components.IssueDetails.episode": "Episodio {episodeNumber}", "components.IssueDetails.lastupdated": "Ultimo aggiornamento", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON> commento.", "components.IssueList.IssueItem.issuestatus": "Stato", "components.IssueDetails.season": "Stagione {seasonNumber}", "components.IssueList.IssueItem.issuetype": "Tipo", "components.IssueList.IssueItem.unknownissuetype": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueList.sortModified": "Ultima modifica", "components.IssueModal.CreateIssueModal.episode": "Episodio {episodeNumber}", "components.IssueModal.CreateIssueModal.allepisodes": "<PERSON><PERSON> gli episodi", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON> le stagioni", "components.IssueList.issues": "Segnalazioni", "components.IssueList.sortAdded": "<PERSON><PERSON> recente", "components.IssueDetails.toasteditdescriptionsuccess": "Descrizione della segnalazione modificata con successo!", "components.IssueModal.CreateIssueModal.providedetail": "Fornisci una descrizione dettagliata del problema riscontrato.", "components.IssueList.showallissues": "Mostra tutte le segnalazioni", "components.IssueDetails.reopenissue": "Riapri segnalazione", "components.IssueDetails.reopenissueandcomment": "Riapri con un commento", "components.IssueModal.CreateIssueModal.reportissue": "Se<PERSON>la un problema", "components.IssueModal.CreateIssueModal.season": "Stagione {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Invia segnalazione", "components.IssueModal.CreateIssueModal.toastviewissue": "Visualizza segnalazione", "components.Layout.Sidebar.issues": "Segnalazioni", "components.ManageSlideOver.manageModalClearMedia": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "components.ManageSlideOver.downloadstatus": "Downloads", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON><PERSON> richiesta.", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON>", "components.ManageSlideOver.movie": "film", "components.ManageSlideOver.manageModalTitle": "Gestisci {mediaType}", "components.ManageSlideOver.mark4kavailable": "Segna come disponibile in 4K", "components.IssueDetails.openinarr": "Apri in {arr}", "components.IssueDetails.playonplex": "Guarda su Plex", "components.ManageSlideOver.openarr": "Apri in {arr}", "components.ManageSlideOver.openarr4k": "Apri in 4K {arr}", "components.IssueDetails.play4konplex": "Guarda in 4K su Plex", "components.IssueDetails.openin4karr": "Apri in 4k {arr}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {<PERSON><PERSON>od<PERSON>} other {<PERSON>pisodi}}", "components.PermissionEdit.createissuesDescription": "Concedi il permesso di segnalare problemi con i contenuti.", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.ManageSlideOver.manageModalIssues": "Segnalazioni aperte", "components.IssueModal.CreateIssueModal.extras": "Extra", "components.PermissionEdit.manageissues": "Gestisci segnalazioni", "components.NotificationTypeSelector.adminissuecommentDescription": "Ricevi una notifica quando altri utenti commentano nella segnalazione.", "components.NotificationTypeSelector.issuecomment": "Commenti segnalazione", "components.NotificationTypeSelector.issuecommentDescription": "Invia notifiche quando le segnalazioni ricevono nuovi commenti.", "components.IssueDetails.openedby": "#{issueId} aperto {relativeTime} da {username}", "components.IssueList.IssueItem.openeduserdate": "{date} da {user}", "components.PermissionEdit.viewissues": "Guarda segnalazione", "components.PermissionEdit.manageissuesDescription": "Concedi permesso per gestire le segnalazioni del contenuto.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Qualcosa è andato storto nel salvataggio del job.", "components.NotificationTypeSelector.userissuecommentDescription": "Ricevi una notifica quando una segnalazione riceve nuovi commenti.", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifica Job", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Ogni {jobScheduleHours, plural, one {ora} other {{jobScheduleHours} ore}}", "components.IssueList.IssueItem.opened": "Aperto", "components.PermissionEdit.createissues": "<PERSON><PERSON><PERSON> problemi", "components.NotificationTypeSelector.userissuecreatedDescription": "Ottieni una notifica quando altri utenti segnalano dei problemi.", "components.NotificationTypeSelector.userissueresolvedDescription": "Ricevi una notifica quando le tue segnalazioni vengono risolte.", "components.PermissionEdit.viewissuesDescription": "Concedi l'autorizzazione per visualizzare le segnalazioni sui media effettuati da altri utenti.", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nuova frequenza", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Ogni {jobScheduleMinutes, plural, one {minuto} other {{jobScheduleMinutes} minuti}}", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Job modificato correttamente!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Impossibile salvare le impostazioni Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Impostazioni Pushover salvate con successo!", "components.ManageSlideOver.manageModalClearMediaWarning": "* Questo rimuoverà irreversibilmente tutti i dati per questo {mediaType}, incluse eventuali richieste. Se questo elemento esiste nella tua libreria {mediaServerName}, le informazioni multimediali verranno ricreate durante la scansione successiva.", "components.NotificationTypeSelector.issuecreated": "Problema Segnalato", "components.NotificationTypeSelector.issuecreatedDescription": "Invia una notifica quando un problema viene segnalato.", "components.NotificationTypeSelector.issueresolved": "<PERSON><PERSON> risolto", "components.NotificationTypeSelector.issueresolvedDescription": "Invia una notifica quando un problema viene risolto.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Access Token", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "API Token applicazione", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Chiave utente o di un gruppo", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Crea un token dalle tue <PushbulletSettingsLink>Impostazioni Account</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Impostazioni di Pushbullet salvate con successo!", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Impossibile salvare le impostazioni di Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registra un applicazione</ApplicationRegistrationLink> per usarla con {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "La tua stringa di 30 caratteri <UsersGroupsLink>per identificare utente o un gruppo</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "<PERSON> fornire un access token", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Devi fornire un application token valido", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Devi fornire una chiave utente o di gruppo valida", "i18n.resolved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.open": "Aperto", "components.Settings.SettingsAbout.runningDevelop": "Stai eseguendo il ramo <code>develop</code> di <PERSON><PERSON><PERSON><PERSON>, che è consigliato solo per coloro che contribuiscono allo sviluppo o assistono con i test più avanza<PERSON>.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Ricevi una notifica quanto le segnalazioni vengono riaperte da altri utenti.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Ricevi una notifica quanto le segnalazioni vengono risolte da altri utenti.", "components.NotificationTypeSelector.issuereopened": "Segnalazione Riaperta", "components.NotificationTypeSelector.issuereopenedDescription": "Invia una notifica quando le segnalazioni vengono riaperte.", "components.NotificationTypeSelector.userissuereopenedDescription": "Ricevi una notifica quanto le tue segnalazioni vengono riaperte.", "components.RequestModal.requestseasons4k": "Richiedi {seasonCount} {seasonCount, plural, one {St<PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}} in 4K", "components.RequestModal.selectmovies": "Film selezionati", "components.MovieDetails.productioncountries": "{countryCount, plural, one {<PERSON>ese} other {<PERSON>esi}} di produzione", "components.TvDetails.productioncountries": "{countryCount, plural, one {<PERSON>ese} other {<PERSON>esi}} di produzione", "components.RequestModal.requestmovies": "<PERSON><PERSON> {count} {count, plural, one {Film} other {Films}}", "components.RequestModal.requestmovies4k": "<PERSON><PERSON> {count} {count, plural, one {Film} other {Films}} in 4K", "components.IssueDetails.commentplaceholder": "Aggiungi un commento…", "components.RequestModal.requestApproved": "<PERSON><PERSON> di <strong>{title}</strong> approvata!", "components.RequestModal.approve": "<PERSON><PERSON><PERSON><PERSON> rich<PERSON>a", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.inCinemas": "Al cinema", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Abilita agente", "components.Settings.Notifications.NotificationsGotify.url": "Server URL", "i18n.import": "Importa", "components.ManageSlideOver.alltime": "Sempre", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "Media 4K", "components.ManageSlideOver.markallseasons4kavailable": "Segna tutte le stagioni disponibili in 4K", "components.ManageSlideOver.markallseasonsavailable": "Segna tutte le stagioni disponibili", "components.ManageSlideOver.opentautulli": "Apri in Tautulli", "components.ManageSlideOver.pastdays": "<PERSON><PERSON><PERSON><PERSON> {days, number} <PERSON><PERSON><PERSON>", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {riproduzione} other {riproduzioni}}", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Impossibile salvare le impostazioni di notifica di Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Impossibile inviare la notifica di prova Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Invio notifica di prova Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Notifica di prova Gotify inviata!", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Devi fornire un application token", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Devi selezionare almeno un tipo di notifica", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Devi fornire un URL valido", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "L'URL non deve terminare con una barra obliqua", "components.Settings.urlBase": "URL di base", "components.Settings.externalUrl": "URL esterno", "components.Settings.tautulliApiKey": "Chiave API", "components.Settings.tautulliSettings": "Impostazioni di Tautulli", "components.Settings.toastTautulliSettingsSuccess": "Impostazioni di Tautulli salvate con successo!", "components.Settings.validationApiKey": "Devi fornire una chiave API", "components.Settings.validationUrlBaseLeadingSlash": "L’URL di base deve contenere una barra obliqua", "components.Settings.validationUrlTrailingSlash": "L'URL non deve terminare con una barra obliqua", "components.UserList.newplexsigninenabled": "L'impostazione <strong>Abilita nuovo accesso Plex</strong> è attualmente abilitata. Gli utenti Plex con accesso alla libreria non devono essere importati per accedere.", "i18n.importing": "Importazione…", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Impostazioni di notifica Gotify salvate correttamente!", "components.Settings.Notifications.NotificationsGotify.token": "Token applicazione", "components.Settings.toastTautulliSettingsFailure": "Qualcosa è andato storto durante il salvataggio delle impostazioni di Tautulli.", "components.Settings.Notifications.enableMentions": "Abilita menzioni", "components.UserProfile.recentlywatched": "Visto di recente", "components.Settings.validationUrl": "Devi fornire un URL valido", "components.Settings.tautulliSettingsDescription": "Configura facoltativamente le impostazioni per il tuo server <PERSON>. Je<PERSON>seerr recupera i dati della cronologia di visione dei tuoi media Plex da Tautulli.", "components.ManageSlideOver.playedby": "<PERSON><PERSON><PERSON><PERSON><PERSON> da", "components.Settings.validationUrlBaseTrailingSlash": "L'URL di base non deve terminare con una barra obliqua", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Tag del canale", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "È necessario fornire un ID Utente Discord valido", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID Utente Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Il <FindDiscordIdLink>numero ID a più cifre</FindDiscordIdLink> associato al tuo account utente Discord", "components.Settings.SettingsAbout.appDataPath": "Cartella applicazione", "components.RequestBlock.languageprofile": "Lingua <PERSON>ilo", "components.MovieDetails.digitalrelease": "Uscita versione digitale", "components.Discover.DiscoverWatchlist.discoverwatchlist": "La tua lista da guardare Plex", "components.Discover.plexwatchlist": "La tua lista da guardare Plex", "components.PermissionEdit.autorequest": "Richiesta automatica", "components.Discover.DiscoverWatchlist.watchlist": "Lista da guardare Plex", "components.MovieDetails.managemovie": "Gestisci Film", "components.MovieDetails.physicalrelease": "Uscita versione fisica", "components.MovieDetails.theatricalrelease": "Uscita nelle sale", "components.MovieDetails.reportissue": "Se<PERSON>la un problema", "components.Discover.CreateSlider.addSlider": "<PERSON>gg<PERSON>ngi Slider", "components.Discover.CreateSlider.searchKeywords": "Cerca parole chiave…", "components.Discover.CreateSlider.searchGenres": "Cerca generi…", "components.Discover.CreateSlider.searchStudios": "Cerca studi…", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON><PERSON> r<PERSON>.", "components.AirDateBadge.airedrelative": "Trasmesso {relativeTime}", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON>", "components.Discover.CreateSlider.starttyping": "Inizia a scrivere per cercare.", "components.Discover.CreateSlider.validationDatarequired": "Devi inserire un valore di dati.", "components.Discover.CreateSlider.validationTitlerequired": "Devi inserire un titolo.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Film", "components.Discover.DiscoverSliderEdit.deletefail": "Impossibile eliminare slider.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Slider eliminato correttamente.", "components.Discover.createnewslider": "Crea nuovo Slider", "components.AirDateBadge.airsrelative": "In onda {relativeTime}", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# filtro attivo} other {# filtri attivi}}", "components.Discover.DiscoverMovies.discovermovies": "Film", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popolarità Crescente", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data di Uscita Crescente", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popolarità Decrescente", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data di Uscita Decrescente", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON> (A-Z) Crescente", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON> (Z-A) Decrescente", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Voto TMDB Crescente", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Voto TMDB Decrescente", "components.Discover.DiscoverTv.discovertv": "Serie", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Data messa in onda Crescente", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Data messa in onda Decrescente", "components.Discover.DiscoverTv.sortPopularityAsc": "Popolarità Crescente", "components.Discover.DiscoverTv.sortPopularityDesc": "Popolarità Decrescente", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON>", "components.Discover.CreateSlider.addfail": "Impossibile creare un nuovo slider.", "components.Discover.CreateSlider.editSlider": "Modifica Slider", "components.Discover.CreateSlider.editfail": "Impossibile modificare lo slider.", "components.Discover.CreateSlider.editsuccess": "Modifica slider e salva le impostazioni di ricerca personalizzate.", "components.Discover.CreateSlider.needresults": "Devi avere almeno 1 risultato.", "components.Discover.CreateSlider.providetmdbkeywordid": "Fornire un TMDB Keyword ID", "components.Discover.CreateSlider.providetmdbgenreid": "Fornire un TMDB Genre ID", "components.Discover.CreateSlider.providetmdbnetwork": "Fornire un TMDB Network ID", "components.Discover.CreateSlider.providetmdbsearch": "Fornire una query di ricerca", "components.Discover.CreateSlider.providetmdbstudio": "Fornire un TMDB Studio ID", "components.Discover.DiscoverSliderEdit.enable": "Attiva/disattiva visibilità", "components.Discover.DiscoverSliderEdit.remove": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Aggiunti di recente", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {#filtro attivo} other {# filtri attivi}}", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON> (A-Z) Crescente", "components.Discover.FilterSlideover.voteCount": "Numero di voti tra {minValue} e {maxValue}", "components.Discover.tmdbmoviekeyword": "TMDB Film Parola chiave", "components.Discover.tmdbsearch": "TMDB Cerca", "components.Discover.tvgenres": "Serie Generi", "components.Discover.tmdbmoviestreamingservices": "TMDB Film Servizi di streaming", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Richieste Film", "components.Layout.Sidebar.browsetv": "Serie", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Richieste Serie", "components.Discover.PlexWatchlistSlider.emptywatchlist": "I media aggiunti alla tua <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> appariranno qui.", "components.Discover.tmdbmoviegenre": "TMDB Film Genere", "components.Discover.tmdbtvgenre": "TMDB Serie Genere", "components.Discover.tmdbtvkeyword": "TMDB Serie Parola chiave", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB Punteggio Decrescente", "components.Discover.FilterSlideover.clearfilters": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.firstAirDate": "Data messa in onda", "components.Discover.FilterSlideover.from": "Da", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "<PERSON><PERSON><PERSON> chiave", "components.Discover.FilterSlideover.originalLanguage": "Lingua Originale", "components.Discover.FilterSlideover.ratingText": "<PERSON><PERSON><PERSON><PERSON> tra {minValue} e {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Data di rilascio", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON>", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} minuti durata", "components.Discover.FilterSlideover.to": "A", "components.Discover.customizediscover": "Personalizza Esplora", "components.Layout.UserDropdown.requests": "<PERSON><PERSON>", "components.DownloadBlock.formattedTitle": "{title}: Stagi<PERSON> {seasonNumber} Episodio {episodeNumber}", "components.Discover.PlexWatchlistSlider.plexwatchlist": "La tua Plex Watchlist", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON> (Z-A) Decrescente", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB Punteggio Crescente", "components.Discover.FilterSlideover.streamingservices": "Servizi di streaming", "components.Discover.emptywatchlist": "I media aggiunti alla tua <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> appariranno qui.", "components.Layout.Sidebar.browsemovies": "Film", "components.Selector.showmore": "Mostra di più", "components.Selector.starttyping": "Inizia a digitare per cercare.", "components.Settings.SettingsMain.apikey": "Chiave API", "components.Settings.SettingsMain.applicationTitle": "Titolo applicazione", "components.Settings.SettingsMain.generalsettings": "Impostazioni generali", "components.Settings.SettingsMain.hideAvailable": "Nascondi media disponibili", "components.Settings.SettingsMain.locale": "Lingua interfaccia", "components.Settings.SettingsMain.originallanguage": "Lingua ricerca", "components.Settings.SettingsMain.originallanguageTip": "Filtra contenuti per lingua originale", "components.Settings.SettingsMain.toastApiKeySuccess": "Nuova chiave API generata con successo!", "components.Settings.SettingsMain.toastSettingsFailure": "Qualcosa è andato storto durante il salvataggio delle impostazioni.", "components.StatusBadge.managemedia": "Gestisci {mediaType}", "components.StatusBadge.playonplex": "Riproduci su Plex", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.StatusChecker.appUpdated": "{applicationTitle} aggiornato", "components.TvDetails.Season.somethingwentwrong": "Qualcosa è andato storto durante il recupero dei dati della stagione.", "components.TvDetails.reportissue": "Se<PERSON>la un problema", "components.TvDetails.seasonnumber": "Stagione {seasonNumber}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Richiedi automaticamente i film presenti nella tua <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "i18n.collection": "Collezione", "components.TitleCard.tmdbid": "TMDB ID", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sincronizza Plex Watchlist", "components.Settings.SettingsMain.toastApiKeyFailure": "Qualcosa è andato storto durante la generazione di una nuova chiave API.", "components.TvDetails.manageseries": "Gestisci Serie", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON>", "components.Selector.searchKeywords": "Ricerca parole chiave…", "components.Selector.searchGenres": "Seleziona generi…", "components.Selector.showless": "<PERSON>ra meno", "components.Settings.SettingsMain.applicationurl": "URL applicazione", "components.Settings.SettingsMain.validationApplicationTitle": "Devi fornire un titolo dell'applicazione", "components.Settings.SettingsMain.validationApplicationUrl": "Devi fornire un URL valido", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> deve essere riavviato per rendere effettive le modifiche", "components.TitleCard.tvdbid": "TheTVDB ID", "components.UserProfile.emptywatchlist": "I media aggiunti alla tua <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> appariranno qui.", "components.TvDetails.status4k": "4K {status}", "i18n.restartRequired": "Riavvio necessario", "components.Selector.nooptions": "<PERSON><PERSON><PERSON> r<PERSON>.", "components.StatusChecker.appUpdatedDescription": "<PERSON><PERSON><PERSON> il bottone sottostante per ricaricare l'applicazione.", "components.StatusChecker.reloadApp": "Ricarica {applicationTitle}", "components.StatusChecker.restartRequired": "Riavvio del server necessario", "components.TitleCard.mediaerror": "{mediaType} non trovato", "components.Settings.SettingsMain.toastSettingsSuccess": "Impostazioni salvate con successo!", "components.UserProfile.plexwatchlist": "Plex Watchlist", "components.TvDetails.seasonstitle": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Richiedi automaticamente le serie presenti nella tua <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.Discover.moviegenres": "Film per genere", "components.RequestModal.requestseries4ktitle": "Richiedi serie in 4K", "components.RequestModal.requestmovie4ktitle": "Richiedi film in 4K", "components.Discover.FilterSlideover.tmdbuserscore": "Voto utenti TMDB", "components.StatusBadge.openinarr": "Apri su {arr}", "components.Settings.SonarrModal.animeSeriesType": "Tipo serie anime", "components.Settings.SonarrModal.seriesType": "Tipo serie TV", "components.RequestModal.requestmovietitle": "Richiedi film", "components.Discover.FilterSlideover.tmdbuservotecount": "Numero voti utenti TMDB", "components.RequestModal.requestseriestitle": "Richiedi serie", "components.Login.adminerror": "Devi utilizzare un account amministratore per accedere.", "components.Login.description": "Poiché è il tuo primo accesso a {applicationName}, è necessario aggiungere un'email valida.", "components.Login.emailtooltip": "L'indirizzo non deve essere associato alla tua istanza di {mediaServerName}.", "components.Login.enablessl": "Usa SSL", "components.Login.hostname": "URL di {mediaServerName}", "components.Login.initialsignin": "<PERSON><PERSON><PERSON>", "components.Login.invalidurlerror": "Impossibile connettersi al server {mediaServerName}.", "components.Login.port": "Porta", "components.Login.save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.saving": "Aggiungendo…", "components.Login.signinwithjellyfin": "Usa il tuo account {mediaServerName}", "components.Login.title": "Aggiungi Email", "components.Login.urlBase": "Base URL", "components.Login.username": "Nome utente", "components.Login.validationEmailRequired": "<PERSON> fornire un'email", "components.Login.validationHostnameRequired": "Devi fornire un hostname o un indirizzo IP valido", "components.Login.validationPortRequired": "Devi fornire un numero di porta valido", "components.Login.validationUrlBaseTrailingSlash": "L'URL di base non deve terminare con una barra finale", "components.Login.validationUrlTrailingSlash": "L'URL non deve terminare con una barra finale", "components.Login.validationemailformat": "È richiesta un'email valida", "components.Login.validationhostformat": "È richiesta un'URL valida", "components.Login.validationhostrequired": "È richiesta l'URL di {mediaServerName}", "components.ManageSlideOver.removearr": "<PERSON><PERSON><PERSON><PERSON> da {arr}", "components.ManageSlideOver.removearr4k": "<PERSON><PERSON><PERSON><PERSON> da 4K {arr}", "components.MovieDetails.addtowatchlist": "Aggiungi alla lista di visione", "components.MovieDetails.downloadstatus": "Stato del download", "components.MovieDetails.imdbuserscore": "Punteggio utente IMDb", "components.MovieDetails.openradarr": "<PERSON>i il film in Radarr", "components.MovieDetails.removefromwatchlist": "<PERSON><PERSON><PERSON><PERSON> dalla lista di visione", "components.MovieDetails.rtaudiencescore": "Punteggio del pubblico di Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatometro di Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Punteggio Utente TMDB", "components.MovieDetails.watchlistError": "Qualcosa è andato storto, riprova.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> aggiunto alla lista di visione con successo!", "components.PermissionEdit.autorequestMovies": "Richiesta automatica dei film", "components.PermissionEdit.autorequestSeries": "Richiesta automatica di serie", "components.PermissionEdit.autorequestSeriesDescription": "Concedi il permesso per inviare automaticamente richieste per serie non 4K tramite Plex Watchlist.", "components.PermissionEdit.viewrecentDescription": "Concedi il permesso per visualizzare l'elenco dei media aggiunti di recente.", "components.PermissionEdit.viewwatchlists": "Visualizza le liste di controllo di {mediaServerName}", "components.RequestBlock.approve": "Approva la richiesta", "components.RequestBlock.decline": "Rifiuta la richiesta", "components.RequestBlock.delete": "Elimina la richiesta", "components.RequestBlock.edit": "Modifica la richiesta", "components.RequestBlock.lastmodifiedby": "Ultima modifica da", "components.RequestBlock.requestedby": "<PERSON><PERSON> da", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON>a", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON><PERSON> rich<PERSON>a", "components.RequestCard.editrequest": "Modifica richiesta", "components.RequestCard.tmdbid": "ID TMDB", "components.RequestCard.tvdbid": "ID TheTVDB", "components.RequestCard.unknowntitle": "<PERSON><PERSON>", "components.RequestList.RequestItem.profileName": "<PERSON>ilo", "components.RequestList.RequestItem.tvdbid": "ID TheTVDB", "components.RequestList.RequestItem.unknowntitle": "<PERSON><PERSON>", "components.RequestModal.requestcollection4ktitle": "<PERSON><PERSON>zione in 4K", "components.RequestModal.requestcollectiontitle": "Richiedi Col<PERSON>zione", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Predefinito dispositivo", "components.Settings.SettingsAbout.supportjellyseerr": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.availability-sync": "Sincronizzazione della disponibilità dei media", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Frequenza Attuale", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Ogni {jobScheduleSeconds, plural, one {secondo} other {{jobScheduleSeconds} secondi}}", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Pulizia della cache delle immagini", "components.Settings.SettingsJobsCache.imagecache": "Cache delle immagini", "components.Settings.SettingsJobsCache.imagecachecount": "Immagini memorizzate nella cache", "components.Settings.SettingsJobsCache.imagecachesize": "Dimensione totale della cache", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Scansione completa della libreria Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Scansione degli elementi aggiunti di recente di Jellyfin", "components.Settings.SettingsMain.cacheImages": "Abilita la cache delle immagini", "components.Settings.SettingsMain.general": "General", "components.Login.credentialerror": "Il nome utente o la password sono errati.", "components.Login.initialsigningin": "Connessione in corso…", "components.MovieDetails.openradarr4k": "<PERSON>i il film in 4K Radarr", "components.MovieDetails.play": "Riproduci su {mediaServerName}", "components.NotificationTypeSelector.mediaautorequested": "Richiesta inviata automaticamente", "components.Settings.Notifications.userEmailRequired": "Richiedi email utente", "components.Discover.resetfailed": "Si è verificato un errore nel ripristino delle impostazioni di personalizzazione della scoperta.", "components.Login.validationEmailFormat": "Email non valida", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* <PERSON>o rimuoverà in modo irreversibile questo {mediaType} da {arr}, inclusi tutti i file.", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> rim<PERSON>o dalla lista di visione con successo!", "components.PermissionEdit.viewrecent": "Visualizza Aggiunti Recenti", "components.RequestBlock.requestdate": "Data della richiesta", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON><PERSON> rich<PERSON>a", "components.Selector.searchStudios": "Cerca studi…", "components.Settings.RadarrModal.tagRequestsInfo": "Aggiungi automaticamente un'etichetta aggiuntiva con l'ID utente e il nome visualizzato del richiedente", "components.Login.validationUrlBaseLeadingSlash": "L'URL di base deve avere una barra iniziale", "components.MovieDetails.play4k": "Riproduci in 4K su {mediaServerName}", "components.NotificationTypeSelector.mediaautorequestedDescription": "Ricevi notifiche quando nuove richieste di media vengono inviate automaticamente per gli elementi nella tua lista di visione.", "components.PermissionEdit.autorequestDescription": "Concedi il permesso per inviare automaticamente richieste per media non-4K tramite la Plex Watchlist.", "components.Discover.updatefailed": "Qualcosa è andato storto durante l'aggiornamento delle impostazioni di personalizzazione della scoperta.", "components.Login.validationusernamerequired": "È richiesto un nome utente", "components.PermissionEdit.autorequestMoviesDescription": "Concedi il permesso per inviare automaticamente richieste di film non 4K tramite Plex Watchlist.", "components.PermissionEdit.viewwatchlistsDescription": "Concedi il permesso per visualizzare le liste di controllo di {mediaServerName} di altri utenti.", "components.RequestList.RequestItem.tmdbid": "ID TMDB", "components.Settings.Notifications.NotificationsPushover.sound": "Suono di notifica", "components.Settings.SettingsMain.cacheImagesTip": "<PERSON><PERSON> le immagini provenienti da fonti esterne (richiede una quantità significativa di spazio su disco)", "components.Settings.SettingsMain.generalsettingsDescription": "Configure global and default settings for Jellyseerr.", "components.RequestModal.SearchByNameModal.nomatches": "Non siamo riusciti a trovare una corrispondenza per questa serie.", "components.Settings.RadarrModal.tagRequests": "Tagga richieste", "components.Settings.SettingsJobsCache.imagecacheDescription": "Quando abilitato nelle impostazi<PERSON>, <PERSON><PERSON><PERSON><PERSON> far<PERSON> da proxy e memorizzerà nella cache le immagini provenienti da fonti esterne preconfigurate. Le immagini memorizzate nella cache vengono salvate nella tua cartella di configurazione. Puoi trovare i file in `<code>{appDataPath}/cache/images</code>`.", "components.Discover.CreateSlider.addsuccess": "Creato un nuovo cursore e salvato le impostazioni di personalizzazione della scoperta.", "components.Discover.DiscoverTvKeyword.keywordSeries": "Serie {keywordTitle}", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtro Attivo} other {# Filtri Attivi}}", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.networks": "<PERSON><PERSON>", "components.Discover.resetsuccess": "Impostazioni di personalizzazione della scoperta ripristinate con successo.", "components.Discover.resettodefault": "Ripristina predefiniti", "components.Discover.resetwarning": "Ripristina tutti i cursori ai predefiniti. Questo eliminerà anche eventuali cursori personalizzati!", "components.Discover.stopediting": "Interrompi modifica", "components.Discover.studios": "<PERSON><PERSON>", "components.Discover.tmdbnetwork": "Rete TMDB", "components.Discover.tmdbstudio": "Studio TMDB", "components.Discover.tmdbtvstreamingservices": "Servizi di Streaming TV TMDB", "components.Discover.updatesuccess": "Impostazioni di personalizzazione della scoperta aggiornate.", "components.Layout.UserWarnings.emailInvalid": "L'indirizzo email non è valido.", "components.Layout.UserWarnings.emailRequired": "È necessario un indirizzo email.", "components.Layout.UserWarnings.passwordRequired": "È richiesta una password.", "components.Settings.SettingsMain.partialRequestsEnabled": "Consenti richieste parziali di serie"}