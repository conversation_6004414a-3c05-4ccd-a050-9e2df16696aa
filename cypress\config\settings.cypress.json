{"clientId": "6919275e-142a-48d8-be6b-93594cbd4626", "vapidPrivate": "tmnslaO8ZWN6bNbSEv_rolPeBTlNxOwCCAHrM9oZz3M", "vapidPublic": "BK_EpP8NDm9waor2zn6_S28o3ZYv4kCkJOfYpO3pt3W6jnPmxrgTLANUBNbbyaNatPnSQ12De9CeqSYQrqWzHTs", "main": {"apiKey": "testkey", "applicationTitle": "<PERSON><PERSON><PERSON><PERSON>", "applicationUrl": "", "csrfProtection": false, "cacheImages": false, "defaultPermissions": 32, "defaultQuotas": {"movie": {}, "tv": {}}, "hideAvailable": false, "localLogin": true, "newPlexLogin": true, "discoverRegion": "", "streamingRegion": "", "originalLanguage": "", "blacklistedTags": "", "blacklistedTagsLimit": 50, "trustProxy": false, "mediaServerType": 1, "partialRequestsEnabled": true, "enableSpecialEpisodes": false, "locale": "en"}, "plex": {"name": "Seerr", "ip": "***********", "port": 32400, "useSsl": false, "libraries": [{"id": "1", "name": "Movies", "enabled": true, "type": "movie"}], "machineId": "test"}, "jellyfin": {"name": "", "ip": "", "port": 8096, "useSsl": false, "urlBase": "", "externalHostname": "", "jellyfinForgotPasswordUrl": "", "libraries": [], "serverId": ""}, "tautulli": {}, "radarr": [], "sonarr": [], "public": {"initialized": true}, "notifications": {"agents": {"email": {"enabled": false, "options": {"emailFrom": "", "smtpHost": "", "smtpPort": 587, "secure": false, "ignoreTls": false, "requireTls": false, "allowSelfSigned": false, "senderName": "<PERSON><PERSON><PERSON><PERSON>"}}, "discord": {"enabled": false, "types": 0, "options": {"webhookUrl": "", "webhookRoleId": "", "enableMentions": true}}, "slack": {"enabled": false, "types": 0, "options": {"webhookUrl": ""}}, "telegram": {"enabled": false, "types": 0, "options": {"botAPI": "", "chatId": "", "messageThreadId": "", "sendSilently": false}}, "pushbullet": {"enabled": false, "types": 0, "options": {"accessToken": ""}}, "pushover": {"enabled": false, "types": 0, "options": {"accessToken": "", "userToken": ""}}, "webhook": {"enabled": false, "types": 0, "options": {"webhookUrl": "", "jsonPayload": "IntcbiAgICBcIm5vdGlmaWNhdGlvbl90eXBlXCI6IFwie3tub3RpZmljYXRpb25fdHlwZX19XCIsXG4gICAgXCJldmVudFwiOiBcInt7ZXZlbnR9fVwiLFxuICAgIFwic3ViamVjdFwiOiBcInt7c3ViamVjdH19XCIsXG4gICAgXCJtZXNzYWdlXCI6IFwie3ttZXNzYWdlfX1cIixcbiAgICBcImltYWdlXCI6IFwie3tpbWFnZX19XCIsXG4gICAgXCJ7e21lZGlhfX1cIjoge1xuICAgICAgICBcIm1lZGlhX3R5cGVcIjogXCJ7e21lZGlhX3R5cGV9fVwiLFxuICAgICAgICBcInRtZGJJZFwiOiBcInt7bWVkaWFfdG1kYmlkfX1cIixcbiAgICAgICAgXCJ0dmRiSWRcIjogXCJ7e21lZGlhX3R2ZGJpZH19XCIsXG4gICAgICAgIFwic3RhdHVzXCI6IFwie3ttZWRpYV9zdGF0dXN9fVwiLFxuICAgICAgICBcInN0YXR1czRrXCI6IFwie3ttZWRpYV9zdGF0dXM0a319XCJcbiAgICB9LFxuICAgIFwie3tyZXF1ZXN0fX1cIjoge1xuICAgICAgICBcInJlcXVlc3RfaWRcIjogXCJ7e3JlcXVlc3RfaWR9fVwiLFxuICAgICAgICBcInJlcXVlc3RlZEJ5X2VtYWlsXCI6IFwie3tyZXF1ZXN0ZWRCeV9lbWFpbH19XCIsXG4gICAgICAgIFwicmVxdWVzdGVkQnlfdXNlcm5hbWVcIjogXCJ7e3JlcXVlc3RlZEJ5X3VzZXJuYW1lfX1cIixcbiAgICAgICAgXCJyZXF1ZXN0ZWRCeV9hdmF0YXJcIjogXCJ7e3JlcXVlc3RlZEJ5X2F2YXRhcn19XCJcbiAgICB9LFxuICAgIFwie3tpc3N1ZX19XCI6IHtcbiAgICAgICAgXCJpc3N1ZV9pZFwiOiBcInt7aXNzdWVfaWR9fVwiLFxuICAgICAgICBcImlzc3VlX3R5cGVcIjogXCJ7e2lzc3VlX3R5cGV9fVwiLFxuICAgICAgICBcImlzc3VlX3N0YXR1c1wiOiBcInt7aXNzdWVfc3RhdHVzfX1cIixcbiAgICAgICAgXCJyZXBvcnRlZEJ5X2VtYWlsXCI6IFwie3tyZXBvcnRlZEJ5X2VtYWlsfX1cIixcbiAgICAgICAgXCJyZXBvcnRlZEJ5X3VzZXJuYW1lXCI6IFwie3tyZXBvcnRlZEJ5X3VzZXJuYW1lfX1cIixcbiAgICAgICAgXCJyZXBvcnRlZEJ5X2F2YXRhclwiOiBcInt7cmVwb3J0ZWRCeV9hdmF0YXJ9fVwiXG4gICAgfSxcbiAgICBcInt7Y29tbWVudH19XCI6IHtcbiAgICAgICAgXCJjb21tZW50X21lc3NhZ2VcIjogXCJ7e2NvbW1lbnRfbWVzc2FnZX19XCIsXG4gICAgICAgIFwiY29tbWVudGVkQnlfZW1haWxcIjogXCJ7e2NvbW1lbnRlZEJ5X2VtYWlsfX1cIixcbiAgICAgICAgXCJjb21tZW50ZWRCeV91c2VybmFtZVwiOiBcInt7Y29tbWVudGVkQnlfdXNlcm5hbWV9fVwiLFxuICAgICAgICBcImNvbW1lbnRlZEJ5X2F2YXRhclwiOiBcInt7Y29tbWVudGVkQnlfYXZhdGFyfX1cIlxuICAgIH0sXG4gICAgXCJ7e2V4dHJhfX1cIjogW11cbn0i"}}, "webpush": {"enabled": false, "options": {}}, "gotify": {"enabled": false, "types": 0, "options": {"url": "", "token": "", "priority": 0}}, "ntfy": {"enabled": false, "types": 0, "options": {"url": "", "topic": ""}}}}, "jobs": {"plex-recently-added-scan": {"schedule": "0 */5 * * * *"}, "plex-full-scan": {"schedule": "0 0 3 * * *"}, "radarr-scan": {"schedule": "0 0 4 * * *"}, "sonarr-scan": {"schedule": "0 30 4 * * *"}, "plex-watchlist-sync": {"schedule": "0 */10 * * * *"}, "availability-sync": {"schedule": "0 0 5 * * *"}, "download-sync": {"schedule": "0 * * * * *"}, "download-sync-reset": {"schedule": "0 0 1 * * *"}, "jellyfin-recently-added-scan": {"schedule": "0 */5 * * * *"}, "jellyfin-full-scan": {"schedule": "0 0 3 * * *"}, "image-cache-cleanup": {"schedule": "0 0 5 * * *"}}}