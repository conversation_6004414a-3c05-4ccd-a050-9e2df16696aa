---
title: AUR (Arch User Repository)
description: Install Jellyseerr using the Arch User Repository
sidebar_position: 4
---

# AUR (Arch User Repository)

:::note Disclaimer
This AUR package is not maintained by us but by a third party. Please refer to the maintainer for any issues.
:::

:::info
This method is not recommended for most users. It is intended for advanced users who are using Arch Linux or an Arch-based distribution.
:::

## Installation

To install Jellyseerr from the AUR, you can use an AUR helper like `yay` or `paru`:

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs groupId="aur-methods" queryString>
  <TabItem value="yay" label="yay">
    ```bash
    yay -S jellyseerr
    ```
  </TabItem>
  <TabItem value="paru" label="paru">
    ```bash
    paru -S jellyseerr
    ```
  </TabItem>
</Tabs>

:::info
After installing Jellyseerr, configure it by visiting the web UI at `http://[address]:5055` and completing the setup steps.
:::

:::tip
You can find the environment file at `/etc/conf.d/jellyseerr` and the service file at `/etc/systemd/system/jellyseerr.service`.
:::
