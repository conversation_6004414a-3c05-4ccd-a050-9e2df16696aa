{"components.Discover.CreateSlider.editsuccess": "Urejen drsnik in shranjene nastavitve prilagajanja odkrivanja.", "components.CollectionDetails.numberofmovies": "{count} film/ov", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON><PERSON>", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Premiera ↓", "components.AppDataWarning.dockerVolumeMissingDescription": "Pripenjanje nosilca <code>{appDataPath}</code> ni bilo pravilno konfigurirano. Vsi podatki bodo izbrisani, ko se vsebnik zaustavi ali znova zažene.", "components.Discover.DiscoverMovies.sortPopularityDesc": "Priljubljenost ↑", "components.AirDateBadge.airsrelative": "Predvajanje {relativeTime}", "components.CollectionDetails.overview": "Pregled", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Active Filter} drugo {# Active Filters}}", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Ocena TMDB ↓", "components.AirDateBadge.airedrelative": "Predvajano {relativeTime}", "components.Discover.CreateSlider.searchStudios": "Iskanje studiev …", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON><PERSON> ↑", "components.Discover.CreateSlider.providetmdbnetwork": "Navedite ID omrežja TMDB", "components.Discover.CreateSlider.addfail": "Novega drsnika ni bilo mogoče ustvariti.", "components.CollectionDetails.requestcollection": "Zahtevaj zbirko", "components.Discover.DiscoverMovieGenre.genreMovies": "Filmi: {genre}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filmi: {language}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.DiscoverMovies.sortPopularityAsc": "Priljubljenost ↓", "components.Discover.CreateSlider.needresults": "Imeti morate vsaj 1 rezultat.", "components.Discover.CreateSlider.addcustomslider": "Ustvari drsnik po meri", "components.Discover.DiscoverTv.sortPopularityAsc": "Priljubljenost ↓", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON><PERSON> d<PERSON>", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON> (a-ž) ↓", "components.Discover.CreateSlider.validationDatarequired": "Navesti morate vrednost podatkov.", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Premiera ↑", "components.Discover.DiscoverTv.discovertv": "Serije", "components.Discover.DiscoverSliderEdit.deletefail": "Drsnika ni bilo mogoče izbrisati.", "components.Discover.CreateSlider.providetmdbstudio": "Navedite ID studia v TMDB", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON> (a-ž) ↑", "components.Discover.DiscoverStudio.studioMovies": "{studio} filmi", "components.Discover.DiscoverTv.sortPopularityDesc": "Priljubljenost ↑", "components.Discover.CreateSlider.searchGenres": "<PERSON><PERSON><PERSON><PERSON> …", "components.Discover.CreateSlider.editfail": "Drsnika ni bilo mogoče urediti.", "components.Discover.CreateSlider.starttyping": "Tipkajte za iskanje.", "components.Discover.DiscoverSliderEdit.enable": "Preklopi vidnost", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.CollectionDetails.requestcollection4k": "Zahtevaj zbirko 4K", "components.Discover.CreateSlider.providetmdbsearch": "Vnesite iskalno poizvedbo", "components.Discover.DiscoverNetwork.networkSeries": "{network} serije", "components.Discover.CreateSlider.providetmdbkeywordid": "Navedite ID ključne besede TMDB", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Filmi: {keywordTitle}", "components.Discover.CreateSlider.validationTitlerequired": "<PERSON><PERSON><PERSON> morate naslov.", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Datum izdaje ↓", "components.Discover.CreateSlider.nooptions": "<PERSON>.", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Ocena TMDB ↑", "components.Discover.CreateSlider.searchKeywords": "Iskanje po kl<PERSON><PERSON><PERSON><PERSON> be<PERSON>ah …", "components.Discover.CreateSlider.addsuccess": "Ustvarjen nov drsnik in shranjene nastavitve prilagajanja odkrivanja.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Drsnik je bil uspešno izbrisan.", "components.Discover.DiscoverMovies.discovermovies": "Filmi", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON> (a-ž) ↓", "components.Discover.CreateSlider.providetmdbgenreid": "Navedite ID žanra TMDB", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON> (a-ž) ↑", "components.Discover.DiscoverSliderEdit.remove": "Odstrani", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Ocena TMDB ↓", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Series"}