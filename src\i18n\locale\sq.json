{"components.Layout.Sidebar.requests": "Kërkesat", "components.Layout.Sidebar.settings": "Cilësimet", "components.Login.signin": "Hyr", "components.IssueModal.CreateIssueModal.submitissue": "Paraqit Problemin", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<PERSON>orti i problemit për <strong>{title}</strong> u paraqit me sukses!", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Kjo do të heqë në mënyrë të pakthyeshme të gjitha të dhënat për këtë {mediaType}, duke përfshirë çdo kërkesë. Nëse ky artikull ekziston në bibliotekën tuaj {mediaServerName}, informacioni i medias do të rikrijohet gjatë skanimit të ardhshëm.", "components.AppDataWarning.dockerVolumeMissingDescription": "Monitimi i volumit <code>{appDataPath}</code> nuk u konfigurua siç duhet. Gjithë informacioni do të fshihet kur kontenieri do të mbyllet ose të ristartohet.", "components.Discover.StudioSlider.studios": "Studiot", "components.Layout.UserDropdown.settings": "Cilësimet", "components.Login.signingin": "Po identifikohet…", "components.ManageSlideOver.opentautulli": "Hape në <PERSON>lli", "components.ManageSlideOver.manageModalAdvanced": "<PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Pastro të dhënat", "components.ManageSlideOver.playedby": "Luajtur nga", "components.MovieDetails.MovieCrew.fullcrew": "Ekuipazhi i plotë", "components.MovieDetails.markavailable": "Shënoni si të disponueshme", "components.MovieDetails.revenue": "Të a<PERSON>hurat", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.showless": "<PERSON><PERSON><PERSON><PERSON> më pak", "components.MovieDetails.mark4kavailable": "Shënoni si të disponueshëm në 4K", "components.MovieDetails.runtime": "{minutes} minuta", "components.IssueList.IssueItem.problemepisode": "Episodi i ndikuar", "components.IssueList.sortAdded": "Më të fundit", "components.IssueModal.CreateIssueModal.episode": "E<PERSON>odi {episodeNumber}", "components.IssueList.sortModified": "Modifikuar së Fundmi", "components.IssueModal.CreateIssueModal.allepisodes": "<PERSON>ë gjithë Episodet", "components.IssueModal.CreateIssueModal.allseasons": "Të gjithë Sezonet", "components.IssueModal.CreateIssueModal.extras": "Ekstrat", "components.Login.loginerror": "Diçka shkoi keq duke u përpjekur të hyja.", "components.Login.signinheader": "Identif<PERSON><PERSON> për të vazhduar", "components.MovieDetails.budget": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.openarr4k": "Hape në 4K {arr}", "components.Layout.UserDropdown.myprofile": "Profili", "components.IssueList.showallissues": "Shfaq të gjithë Problemet", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.edit": "Modifiko <PERSON>", "components.CollectionDetails.numberofmovies": "{count} Filma", "components.CollectionDetails.overview": "Faqja e Përgjit<PERSON>hme", "components.CollectionDetails.requestcollection": "Kërk<PERSON> Koleks<PERSON>in", "components.CollectionDetails.requestcollection4k": "Kërko Koleksionin në 4K", "components.Discover.DiscoverMovieGenre.genreMovies": "Filma {genre}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filma {language}", "components.Discover.DiscoverNetwork.networkSeries": "Seriale {network}", "components.Discover.DiscoverStudio.studioMovies": "Filma {studio}", "components.Discover.DiscoverTvGenre.genreSeries": "Seriale {genre}", "components.Discover.DiscoverTvLanguage.languageSeries": "Seriale {language}", "components.Discover.MovieGenreList.moviegenres": "<PERSON><PERSON>re Filmi", "components.Discover.MovieGenreSlider.moviegenres": "<PERSON><PERSON>re Filmi", "components.Discover.NetworkSlider.networks": "Rrjetet", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON><PERSON>", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON><PERSON>", "components.Discover.discover": "Zbulo", "components.Discover.popularmovies": "Filma Popullorë", "components.Discover.populartv": "<PERSON><PERSON><PERSON>", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "components.Discover.recentrequests": "Kërkesat e Fundit", "components.Discover.trending": "Në Trend", "components.Discover.upcoming": "Filmat që vijnë së shpejti", "components.Discover.upcomingmovies": "Filmat që vijnë së shpejti", "components.Discover.upcomingtv": "Serialet që vijnë së shpejti", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON> {time}", "components.IssueDetails.IssueComment.areyousuredelete": "A jeni të sigurt që doni të fshini këtë koment?", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON>", "components.IssueDetails.IssueComment.postedby": "Postuar {relativeTime} nga {username}", "components.IssueDetails.IssueComment.postedbyedited": "Postuar {relativeTime} nga {username} (Modifikuar)", "components.IssueDetails.IssueComment.validationComment": "Ju duhet të vendosni një mesazh", "components.IssueDetails.IssueDescription.deleteissue": "Fshi Problemin", "components.IssueDetails.IssueDescription.description": "Përshkrim", "components.IssueDetails.allepisodes": "<PERSON>ë gjithë Episodet", "components.IssueDetails.allseasons": "Të gjithë Sezonet", "components.IssueDetails.closeissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.closeissueandcomment": "<PERSON><PERSON><PERSON> <PERSON>", "components.IssueDetails.commentplaceholder": "Shto një koment…", "components.IssueDetails.comments": "Komentet", "components.IssueDetails.deleteissue": "Fshi Problemin", "components.IssueDetails.deleteissueconfirm": "A jeni të sigurt që doni të fshini këtë problem?", "components.IssueDetails.episode": "E<PERSON>odi {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problem", "components.IssueDetails.issuetype": "T<PERSON><PERSON>", "components.IssueDetails.lastupdated": "Përditësuar së Fundmi", "components.IssueDetails.leavecomment": "Ko<PERSON>", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON><PERSON> koment.", "components.IssueDetails.openedby": "#{issueId} hapur {relativeTime} nga {username}", "components.IssueDetails.openin4karr": "Hape në 4K {arr}", "components.IssueDetails.openinarr": "Hape në {arr}", "components.IssueDetails.play4konplex": "Luaje në 4K në {mediaServerName}", "components.IssueDetails.playonplex": "Luaj<PERSON> në {mediaServerName}", "components.IssueDetails.problemepisode": "Episodi i ndikuar", "components.IssueDetails.problemseason": "Sezoni i ndikuar", "components.IssueDetails.reopenissue": "Rihap Problemin", "components.IssueDetails.reopenissueandcomment": "Rihap me Koment", "components.IssueDetails.season": "<PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Diçka shkoi keq duke mod<PERSON><PERSON><PERSON><PERSON> përshk<PERSON>in e problemit.", "components.IssueDetails.toasteditdescriptionsuccess": "Përshkrimi i Problemit u modifikua me sukses!", "components.IssueDetails.toastissuedeleted": "Problemi u fshi me sukses!", "components.IssueDetails.toastissuedeletefailed": "Diçka shkoi keq duke f<PERSON><PERSON><PERSON> problemin.", "components.IssueDetails.toaststatusupdated": "Statusi i problemit u përditësua me sukses!", "components.IssueDetails.toaststatusupdatefailed": "Diçka shkoi keq duke p<PERSON>rditësuar statusin e problemit.", "components.IssueDetails.unknownissuetype": "<PERSON>", "components.IssueList.IssueItem.issuestatus": "Statusi", "components.IssueList.IssueItem.issuetype": "T<PERSON><PERSON>", "components.IssueList.IssueItem.opened": "Hapur", "components.IssueList.IssueItem.openeduserdate": "{date} nga {user}", "components.IssueList.IssueItem.unknownissuetype": "<PERSON>", "components.IssueList.IssueItem.viewissue": "<PERSON><PERSON><PERSON><PERSON> problemin", "components.IssueList.issues": "Problemet", "components.IssueModal.CreateIssueModal.problemepisode": "Episodi i ndikuar", "components.IssueModal.CreateIssueModal.problemseason": "Sezoni i ndikuar", "components.IssueModal.CreateIssueModal.providedetail": "Ju lutemi të jepni një shpjegim të hollësishëm për problemin që keni hasur.", "components.IssueModal.CreateIssueModal.reportissue": "<PERSON><PERSON><PERSON> një problem", "components.IssueModal.CreateIssueModal.season": "<PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Diçka shkoi keq duke paraqitur problemin.", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Ju duhet të jepni një përshkrim", "components.IssueModal.CreateIssueModal.whatswrong": "Çfarë nuk shkon?", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "T<PERSON> tjera", "components.IssueModal.issueSubtitles": "Titra", "components.IssueModal.issueVideo": "Video", "components.LanguageSelector.languageServerDefault": "<PERSON>g<PERSON> ({language})", "components.LanguageSelector.originalLanguageDefault": "<PERSON>ë g<PERSON>ha <PERSON>", "components.Layout.LanguagePicker.displaylanguage": "Sh<PERSON>q <PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Kërko Filma & Seriale", "components.Layout.Sidebar.dashboard": "Zbulo", "components.Layout.Sidebar.issues": "Problemet", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.outofdate": "E skaduar", "components.Login.email": "Adresa e emailit", "components.Login.forgotpassword": "Harrove <PERSON>?", "components.Login.password": "Fjalëkalimi", "components.Login.signinwithoverseerr": "Përdor llogarinë tënde {applicationTitle}", "components.Login.signinwithplex": "Përdor llogarinë tënde Plex", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {version} other {versione}} mbrapa", "components.Login.validationemailrequired": "Ju duhet të jepni një adresë të vlefshme e-mail", "components.Login.validationpasswordrequired": "Duhet të jepni një fjalëkalim", "components.ManageSlideOver.alltime": "<PERSON>ë gjitha koh<PERSON>", "components.ManageSlideOver.downloadstatus": "Shkarkimet", "components.ManageSlideOver.manageModalIssues": "Probleme të hapura", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "Media 4K", "components.ManageSlideOver.manageModalNoRequests": "Asnjë kërkesë.", "components.ManageSlideOver.manageModalRequests": "Kërkesat", "components.ManageSlideOver.manageModalTitle": "Menaxho {mediaType}", "components.ManageSlideOver.mark4kavailable": "Shënoni si të disponueshëm në 4K", "components.ManageSlideOver.markallseasons4kavailable": "Shënoni të gjitha Sezonet si të disponueshme në 4K", "components.ManageSlideOver.markallseasonsavailable": "Shënoni të gjitha Sezonet si të disponueshme", "components.ManageSlideOver.markavailable": "Shënoni si të disponueshme", "components.ManageSlideOver.movie": "film", "components.ManageSlideOver.openarr": "Hape në {arr}", "components.ManageSlideOver.pastdays": "<PERSON><PERSON><PERSON> {days, number} Ditë", "components.ManageSlideOver.tvshow": "seri", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON> më shumë", "components.MovieDetails.MovieCast.fullcast": "<PERSON><PERSON><PERSON> i <PERSON>", "components.MovieDetails.cast": "<PERSON><PERSON><PERSON>", "components.MovieDetails.originallanguage": "<PERSON><PERSON><PERSON>", "components.MovieDetails.overview": "Vështrim i përgjithshëm", "components.MovieDetails.overviewunavailable": "Vështrimi i përgjithshëm i paarritshëm.", "components.MovieDetails.productioncountries": "Prodhimi {countryCount, plural, one {Shtet} other {Shtete}}", "components.MovieDetails.recommendations": "Rekomandime", "components.MovieDetails.showmore": "<PERSON><PERSON><PERSON><PERSON> më shumë", "components.MovieDetails.similar": "Tituj të ng<PERSON>hëm", "components.MovieDetails.streamingproviders": "Po Transmetohet Në", "components.MovieDetails.viewfullcrew": "<PERSON><PERSON> e<PERSON> e plotë", "components.Settings.menuJobs": "Punët & Cache", "components.Settings.menuNotifications": "Njoftime", "components.Settings.menuServices": "Shërbime", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.noDefault4kServer": "Një server 4K {serverType} duhet të shënohet si i prezgjedhur në mënyrë që t'u mundësojë përdoruesve të paraqesin kërkesat e {mediaType} 4K .", "components.Settings.noDefaultNon4kServer": "Nëse keni vetëm një server të vetëm {serverType} për të dy përmbajtjet jo-4K dhe 4K (ose nëse shkarkon vetëm përmbajtjen 4K), serveri juaj {serverType} <strong>NUK</strong>duhet të caktohet si një server 4K.", "components.Settings.noDefaultServer": "Të paktën një server {serverType} duhet të shënohet si parazgjedhje në mënyrë që kërkesat për {mediaType} të përpunohen.", "components.Settings.notifications": "Njoftimet", "components.Settings.notificationsettings": "Cilësimet e njoftimit", "components.Settings.notrunning": "Nuk Po <PERSON>", "components.Settings.plexlibraries": "Libraritë e Plex", "components.Settings.scan": "Sinkronizoni Libraritë", "components.Settings.scanning": "Po sinkronizohet…", "components.Settings.serverSecure": "i sigurt", "components.Settings.serverpreset": "<PERSON><PERSON>", "components.Settings.serverpresetLoad": "Shtyp butonin për të ngarkuar serverët e disponueshëm", "components.Settings.toastPlexConnecting": "Duke u përpjekur për t'u lidhur me Plex…", "components.Settings.toastPlexConnectingFailure": "D<PERSON>sh<PERSON>i lidhja me Plex.", "components.Settings.toastPlexConnectingSuccess": "Lidhja Plex u krijua me sukses!", "i18n.testing": "<PERSON> testuar…", "i18n.view": "<PERSON><PERSON>", "components.MovieDetails.watchtrailer": "<PERSON><PERSON>", "components.NotificationTypeSelector.adminissuecommentDescription": "Lajmërohu kur përdoruesit e tjerë komentojnë mbi problemet.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Njoftohuni kur problemet rihapen nga përdorues të tjerë.", "components.NotificationTypeSelector.issuecomment": "Komenti i Problemit", "components.NotificationTypeSelector.issuecommentDescription": "Dërgoni njoftime kur problemet marrin komente të reja.", "components.NotificationTypeSelector.issuecreated": "Problemi i Raportuar", "components.NotificationTypeSelector.issuecreatedDescription": "Dërgoni njoftime kur raportohen probleme.", "components.NotificationTypeSelector.issuereopened": "Problemi u Rihap", "components.NotificationTypeSelector.issuereopenedDescription": "Dërgo njoftime kur problemet rihapen.", "components.NotificationTypeSelector.issueresolved": "Problemi u Zgjidh", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Dërgo njoftime kur përdoruesit paraqesin kërkesa të reja për media të cilat miratohen automatikisht.", "components.NotificationTypeSelector.mediaapproved": "Kërkesa u Miratua", "components.NotificationTypeSelector.mediaapprovedDescription": "Dërgo njoftime kur kërkesat për media miratohen manualisht.", "components.NotificationTypeSelector.mediaavailable": "Kërkesa e Disponueshme", "components.NotificationTypeSelector.mediaavailableDescription": "Dërgo njoftime kur të bëhen të disponueshme kërkesat për media.", "components.NotificationTypeSelector.mediadeclined": "Kërkesa u refuzua", "components.NotificationTypeSelector.mediadeclinedDescription": "Dërgoni njoftime kur kërkesat për media refuzohen.", "components.NotificationTypeSelector.mediafailed": "Përpunimi i kërkesës dështoi", "components.NotificationTypeSelector.mediafailedDescription": "Dërgo njoftime kur kërkesat për media nuk shtohen te Radarr ose Sonarr.", "components.NotificationTypeSelector.mediarequested": "Kërkesë në pritje të miratimit", "components.NotificationTypeSelector.mediarequestedDescription": "Dërgo njoftime kur përdoruesit paraqesin kërkesa të reja për media të cilat kërkojnë miratim.", "components.NotificationTypeSelector.notificationTypes": "Llojet e njoftimeve", "components.NotificationTypeSelector.userissuecommentDescription": "Njoftohu kur problemet që ke raportuar marrin komente të reja.", "components.NotificationTypeSelector.userissuecreatedDescription": "Lajmërohu kur përdoruesit e tjerë raportojnë probleme.", "components.NotificationTypeSelector.userissuereopenedDescription": "Njoftohu kur problemet që keni raportuar rihapen.", "components.NotificationTypeSelector.userissueresolvedDescription": "Njoftohu kur problemet që keni raportuar zgjidhen.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Njoftohuni kur përdoruesit e tjerë paraqesin kërkesa të reja për media të cilat miratohen automatikisht.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Njoftohuni kur kërkesat tuaja për media miratohen.", "components.NotificationTypeSelector.usermediaavailableDescription": "Njoftohuni kur kërkesat tuaja për media bëhen të disponueshme.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Njoftohuni kur kërkesat tuaja për media refuzohen.", "components.NotificationTypeSelector.usermediarequestedDescription": "Njoftohuni kur përdoruesit e tjerë paraqesin kërkesa të reja për media që kërkojnë miratim.", "components.PermissionEdit.admin": "Administrator", "components.PermissionEdit.adminDescription": "Aks<PERSON> i plotë i administratorit. Anashkalon të gjitha kontrollet e tjera të lejeve.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Jep aprovimin automatik për kërkesat e serive 4K.", "components.PermissionEdit.autoapproveDescription": "<PERSON><PERSON> miratim automatik për të gjitha kërkesat për media jo-4K.", "components.PermissionEdit.autoapproveMovies": "Aprovo automatikisht filmat", "components.PermissionEdit.autoapproveMoviesDescription": "Jep aprovimin automatik për kërkesat e filmave jo-4K.", "components.PermissionEdit.autoapproveSeries": "Aprovo automatikisht Serialet", "components.PermissionEdit.autoapproveSeriesDescription": "Jep aprovimin automatik për kërkesat e serialeve jo-4K.", "components.PermissionEdit.createissues": "Raporto Problemet", "components.PermissionEdit.createissuesDescription": "<PERSON><PERSON><PERSON> leje për të raportuar problemet e medias.", "components.PermissionEdit.manageissues": "Menaxho Problemet", "components.PermissionEdit.manageissuesDescription": "<PERSON><PERSON><PERSON> leje për të menaxhuar problemet e medias.", "components.PermissionEdit.managerequests": "Men<PERSON><PERSON>", "components.PermissionEdit.managerequestsDescription": "Jepni leje për të menaxhuar kërkesat e medias. Të gjitha kërkesat e bëra nga një përdorues me këtë leje do të miratohen automatikisht.", "components.PermissionEdit.request": "Kërkesë", "components.PermissionEdit.request4k": "Kërkesë 4K", "components.PermissionEdit.request4kDescription": "Je<PERSON> leje për të paraqitur kërkesat për media 4K.", "components.PermissionEdit.request4kMovies": "Kërko filma 4K", "components.PermissionEdit.request4kMoviesDescription": "<PERSON><PERSON> leje për të dërguar kërkesa për filma 4K.", "components.PermissionEdit.request4kTv": "Kërko Seriale 4K", "components.PermissionEdit.request4kTvDescription": "<PERSON><PERSON> leje për të dërguar kërkesa për seriale 4K.", "components.PermissionEdit.requestDescription": "<PERSON><PERSON> leje për të paraqitur kërkesat për media jo-4K.", "components.PermissionEdit.requestTv": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.requestTvDescription": "<PERSON><PERSON> leje për të paraqitur kërkesa për seri jo-4K.", "components.PermissionEdit.users": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.usersDescription": "Jep leje për të menaxhuar përdoruesit. Përdoruesit me këtë leje nuk mund të modifikojnë ose të japin privilegjin e administratorit.", "components.PermissionEdit.viewissues": "Shiko <PERSON>et", "components.PermissionEdit.viewissuesDescription": "<PERSON><PERSON> leje për të parë problemet e medias të raportuara nga përdoruesit e tjerë.", "components.PermissionEdit.viewrequests": "Shikoni Kërkesat", "components.PermissionEdit.viewrequestsDescription": "Je<PERSON>ni leje për të parë kërkesat për media të paraqitura nga përdorues të tjerë.", "components.PersonDetails.alsoknownas": "<PERSON><PERSON><PERSON> edhe si: {names}", "components.PersonDetails.appearsin": "Paraqitjet", "components.PersonDetails.ascharacter": "si {character}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RegionSelector.regionServerDefault": "Parazgjedhur ({region})", "components.RequestBlock.requestoverrides": "Anashkalime të Kërkesës", "components.RequestBlock.rootfolder": "Direktoria", "components.RequestBlock.server": "Serveri i destinacionit", "components.RequestButton.decline4krequests": "Refuzo {requestCount, plural, one {Kërkesën 4K} other {{requestCount} Kërkesat 4K}}", "components.RequestButton.declinerequest": "Refuzo kërkesën", "components.RequestButton.declinerequests": "Refuzo {requestCount, plural, one {Kërkesën} other {{requestCount} Kërkesat}}", "components.RequestButton.viewrequest4k": "Shiko Kërkesën 4K", "components.RequestCard.failedretry": "Diçka shkoi keq duke e riprovuar kërkesën.", "components.RequestList.RequestItem.modified": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} nga {user}", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.requests": "Kërkesat", "components.RequestList.showallrequests": "Shfaq të gjitha kërk<PERSON>at", "components.RequestList.sortAdded": "Më të fundit", "components.RequestList.sortModified": "Modifikuar së Fundmi", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON>", "components.RequestModal.AdvancedRequester.animenote": "* Kjo seri ë<PERSON>të një anime.", "components.RequestModal.AdvancedRequester.destinationserver": "Serveri i destinacionit", "components.RequestModal.AdvancedRequester.languageprofile": "Profili i gjuhës", "components.RequestModal.AdvancedRequester.requestas": "Kërko Si", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON> lejoheni të kërkoni <strong>{limit}</strong> {type} çdo <strong>{days}</strong> ditë.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Nuk kanë mbetur kërkesa të mjaftueshme për se<PERSON>in", "components.RequestModal.QuotaDisplay.quotaLink": "Ju mund të shikoni një përmbledhje të kufizimeve tuaja të kërkesës në <ProfileLink>faqen tuaj të profilit</ProfileLink>.", "components.RequestModal.QuotaDisplay.season": "sezon", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Nuk mund të përputheshim automatikisht me kërkesën tënde. Ju lutem zgjidhni ndeshjen e saktë nga lista më poshtë.", "components.RequestModal.alreadyrequested": "E <PERSON>rkuar <PERSON>", "components.RequestModal.approve": "<PERSON><PERSON>", "components.RequestModal.autoapproval": "<PERSON><PERSON>im automatik", "components.RequestModal.edit": "Ndrysho k<PERSON>rkesën", "components.RequestModal.errorediting": "Diçka shkoi keq duke mod<PERSON><PERSON><PERSON><PERSON> kërkesën.", "components.RequestModal.numberofepisodes": "# i Episodeve", "components.RequestModal.pending4krequest": "", "components.RequestModal.pendingrequest": "Kërkesë në pritje", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON> pë<PERSON> <strong>{title}</strong> u miratua!", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON> pë<PERSON> <strong>{title}</strong> u anullua.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> u kërkua me sukses!", "components.RequestModal.requestadmin": "Kjo kërkesë do të miratohet automatikisht.", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON><PERSON> për <strong>{title}</strong> u modifikua me sukses!", "components.RequestModal.requesterror": "Diçka shkoi keq duke para<PERSON><PERSON> kërkesën.", "components.RequestModal.requestfrom": "Kërkesa e {username} është në pritje të miratimit.", "components.RequestModal.requestmovies": "<PERSON><PERSON><PERSON><PERSON> {count} {count, plural, one {Film} other {Filma}}", "components.RequestModal.requestmovies4k": "<PERSON><PERSON><PERSON><PERSON> {count} {count, plural, one {Film} other {Filma}} në 4K", "components.RequestModal.season": "<PERSON><PERSON><PERSON>", "components.RequestModal.seasonnumber": "<PERSON><PERSON><PERSON> {numri}", "components.RequestModal.selectmovies": "Zgjidh Filmat", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON>(et)", "components.ResetPassword.email": "Adresa e emailit", "components.ResetPassword.resetpassword": "Zëvendëso fjal<PERSON><PERSON><PERSON><PERSON> tuaj", "components.ResetPassword.resetpasswordsuccessmessage": "Fjalëkalimi u zëvendësua me sukses!", "components.ResetPassword.validationemailrequired": "Ju duhet të jepni një adresë të vlefshme e-mail", "components.ResetPassword.validationpasswordmatch": "Fjalëkalimet duhet të përputhen", "components.ResetPassword.validationpasswordminchars": "Fjalëkalimi është shumë i shkurtër; duhet të jetë një minimum prej 8 karakteresh", "components.ResetPassword.validationpasswordrequired": "Duhet të japësh një fjalëkalim", "components.Search.search": "K<PERSON><PERSON><PERSON>", "components.Search.searchresults": "Rezultatet e Kërkimit", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aktivizo agjentin", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Cilësimet e njoftimit të Gotify nuk u ruajtën.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Cilësimet e njoftimit Gotify u ruajtën me sukses!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Njoftimi i testit Gotify nuk u dërgua.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Po dërgohet njoftimi i testit Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "U dërgua njoftimi i testit Gotify!", "components.Settings.Notifications.NotificationsGotify.token": "Token e Aplikimit", "components.Settings.Notifications.NotificationsGotify.url": "URL e serverit", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Duhet të japësh një token aplikacioni", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Duhet të zgjedhësh të paktën një tip njoftimi", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Em<PERSON> i profilit", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Kërkohet vetëm nëse nuk përdoret profili <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Cilësimet e njoftimit të LunaSea nuk u ruajtën.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Rregullimet e njoftimeve LunaSea u ruajtën me sukses!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Njoftimi i testit të LunaSea nuk u dërgua.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "<PERSON> d<PERSON><PERSON><PERSON><PERSON> njoft<PERSON> e testit LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "<PERSON><PERSON><PERSON> një token nga <PushbulletSettingsLink>Cilësimet e llogarisë</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aktivizo agjentin", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Etiketa e kanalit", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Duhet të japësh një Token hyrjeje", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL e webhook", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "<PERSON><PERSON><PERSON> nj<PERSON> integrim <WebhookLink>me Webhook</WebhookLink>", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aktivizo agjentin", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "<PERSON><PERSON><PERSON> të marrë njoftimet e shtytjes në internet, <PERSON><PERSON><PERSON>rr duhet të shërbehet mbi HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Njoftimi i testit në ueb dështoi të dërgohet.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Po dërgohet njoftimi test në ueb…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "U dërgua njoftimi test në ueb!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Cilësimet e njoftimit në ueb nuk u ruajtën.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Cilësimet e njoftimit në ueb u ruajtën me sukses!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aktivizo agjentin", "components.Settings.Notifications.NotificationsWebhook.authheader": "Header i autorizimit", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Rivendos në Parazgjedhje", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Njoftimi i testit Webhook dështoi të dërgonte.", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Ju duhet të siguroni një ngarkesë të vlefshme JSON", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Duhet të zgjedhësh të paktën një tip njoftimi", "components.Settings.Notifications.sendSilently": "Dërgo në heshtje", "components.Settings.Notifications.telegramsettingsfailed": "Cilësimet e njoftimit të telegramit nuk u ruajtën.", "components.Settings.Notifications.toastDiscordTestFailed": "Njoftimi i testit të Discord nuk u dërgua.", "components.Settings.Notifications.toastDiscordTestSending": "Po dërgon njoftimin e testit Discord…", "components.Settings.Notifications.toastEmailTestSuccess": "Njoftimi i testit me email u dërgua!", "components.Settings.Notifications.toastTelegramTestFailed": "Njoftimi i testit telegram dështoi të dërgonte.", "components.Settings.Notifications.toastTelegramTestSending": "Po dërgohet njoftimi i testit të Telegramit…", "components.Settings.Notifications.toastTelegramTestSuccess": "Njoftimi për testin e telegramit u dërgua!", "components.Settings.Notifications.validationBotAPIRequired": "Ju duhet të jepni një TOKEN autorizimi të botit", "components.Settings.Notifications.validationChatIdRequired": "Ju duhet të siguroni një ID të vlefshme chat", "components.Settings.Notifications.validationEmail": "Ju duhet të jepni një adresë të vlefshme emaili", "components.Settings.Notifications.validationPgpPassword": "Duhet të jepni një fjalëkalim PGP", "components.Settings.Notifications.validationPgpPrivateKey": "Duhet të japësh një çelës privat PGP të vlefshëm", "components.Settings.Notifications.validationSmtpPortRequired": "Du<PERSON>t të jepni një numër të vlefshëm porte", "components.Settings.Notifications.validationTypes": "Duhet të zgjidhni të paktën një lloj njoftimi", "components.Settings.RadarrModal.testFirstTags": "<PERSON><PERSON> për të ngarkuar etiketat", "components.Settings.RadarrModal.toastRadarrTestFailure": "<PERSON><PERSON><PERSON> me <PERSON>.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Lidhja Radarr u vendos me sukses!", "components.Settings.RadarrModal.validationApiKeyRequired": "Duhet të japësh një çelës API", "components.Settings.RadarrModal.validationApplicationUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Baza e URL nuk duhet të përfundojë me një slesh", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Duhet të zgjedhësh minimumin e disponueshmërisë", "components.Settings.RadarrModal.validationPortRequired": "Du<PERSON>t të jepni një numër të vlefshëm porte", "components.Settings.RadarrModal.validationProfileRequired": "Duhet të zgjedhësh një profil cilësie", "components.Settings.RadarrModal.validationRootFolderRequired": "Duhet të zgjedhësh një dosje rrënjë", "components.Settings.SettingsAbout.Releases.currentversion": "Aktuale", "components.Settings.SettingsAbout.Releases.latestversion": "E fundit", "components.Settings.SettingsAbout.Releases.viewchangelog": "Shfaq Changelog", "components.Settings.SettingsAbout.Releases.viewongithub": "Shikoni në GitHub", "components.Settings.SettingsAbout.about": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.betawarning": "Ky është programi BETA. Veçoritë mund të jenë të thyera dhe/ose të paqëndrueshme. Ju lutem raportoni ndonjë çështje në GitHub!", "components.Settings.menuAbout": "<PERSON><PERSON><PERSON>", "components.Settings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.requestMoviesDescription": "<PERSON><PERSON> leje për të dërguar kërkesa për filma jo-4K.", "components.RequestButton.declinerequest4k": "Refuzo kërkesën 4K", "components.NotificationTypeSelector.usermediafailedDescription": "Njoftohuni kur kërkesat për media nuk shtohen te Radarr ose Sonarr.", "components.PermissionEdit.advancedrequest": "Kërkesa të Avancuara", "components.PermissionEdit.advancedrequestDescription": "<PERSON><PERSON> leje për të modifikuar opsionet e avancuara të kërkesës për media.", "components.PermissionEdit.autoapprove": "Aprovo automatikisht", "components.RequestModal.QuotaDisplay.requiredquotaUser": "<PERSON>y përdorues duhet të ketë të paktën <strong>{seasons}</strong> {seasons, plural, one {kërkesë sezoni} other {kërk<PERSON><PERSON> sezonesh}} të mbetur në mënyrë që të paraqesë një kërkesë për këtë seri.", "components.RegionSelector.regionDefault": "<PERSON><PERSON> g<PERSON>", "components.RequestButton.approverequests": "Mirato {requestCount, plural, one {Kërkesën} other {{requestCount} Kërkesat}}", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON><PERSON> ka etiketa.", "components.ResetPassword.gobacklogin": "<PERSON><PERSON><PERSON> te faqja e hyrjes", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Ju mund të shikoni një përmbledhje të kufijve të kërkesës së këtij përdoruesi në <ProfileLink>faqen e tyre të profilit</ProfileLink>.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Njoftohuni kur problemet zgjidhen nga përdoruesit e tjerë.", "components.NotificationTypeSelector.issueresolvedDescription": "Dërgoni njoftime kur problemet zgjidhen.", "components.NotificationTypeSelector.mediaAutoApproved": "Kërkesa u Miratua Automatikisht", "components.PermissionEdit.autoapprove4k": "Aprovo automatikisht 4K", "components.PermissionEdit.autoapprove4kMovies": "Aprovo automatikisht Filmat 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Jep aprovimin automatik për kërkesat e filmave 4K.", "components.PermissionEdit.requestMovies": "Kërko Filma", "components.QuotaSelector.unlimited": "<PERSON>", "components.RequestButton.requestmore": "Kërko më shumë", "components.RequestButton.requestmore4k": "Kërko më shumë në 4K", "components.RequestModal.AdvancedRequester.qualityprofile": "Profili i cilësisë", "components.RequestModal.AdvancedRequester.tags": "Etiketat", "components.RequestCard.mediaerror": "Titulli shoqërue<PERSON> për këtë kërkesë nuk është më në dispozicion.", "components.RequestList.RequestItem.editrequest": "Ndrysho k<PERSON>rkesën", "components.RequestModal.AdvancedRequester.rootfolder": "Direktoria", "components.RequestButton.approverequest": "<PERSON><PERSON>", "components.PermissionEdit.autoapprove4kDescription": "Je<PERSON> aprovimin automatik për të gjitha kërkesat e mediave 4K.", "components.PermissionEdit.autoapprove4kSeries": "Aprovo automatikisht Serialet 4K", "components.RequestBlock.profilechanged": "Profili i cilësisë", "components.RequestButton.approverequest4k": "Mirato kërkesën 4K", "components.RequestCard.deleterequest": "Fshije <PERSON>rkesën", "components.RequestList.RequestItem.mediaerror": "Titulli shoqërue<PERSON> për këtë kërkesë nuk është më në dispozicion.", "components.RequestButton.viewrequest": "<PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "Fshije <PERSON>rkesën", "components.RequestList.RequestItem.failedretry": "Diçka shkoi keq duke e riprovuar kërkesën.", "components.RequestModal.AdvancedRequester.selecttags": "Zgjidh etiketat", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> pë<PERSON> <strong>{title}</strong> u anullua.", "components.ResetPassword.password": "Fjalëkalimi", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aktivizo agjentin", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Duhet të zgjedhësh të paktën një tip njoftimi", "components.Settings.RadarrModal.released": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON><PERSON>s lejohet të kërkojë <strong>{limit}</strong> {type} çdo <strong>{days}</strong> ditë.", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.pendingapproval": "Kërkesa juaj është në pritje të miratimit.", "components.RequestModal.QuotaDisplay.requiredquota": "<PERSON><PERSON>t të keni të paktën <strong>{seasons}</strong> {seasons, plural, one {kërkesë sezoni} other {kërk<PERSON>a sezonesh}} të mbetur në mënyrë që të dërgoni një kërkesë për këtë seri.", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL nuk duhet të përfundojë me një slesh", "i18n.unavailable": "I paarrits<PERSON>m", "i18n.tvshow": "Seri", "components.ResetPassword.requestresetlinksuccessmessage": "Një link për rivendosjen e fjalëkalimit do të dërgohet në adresën email të dhënë nëse është e lidhur me një përdorues të vlefshëm.", "i18n.usersettings": "Cilësimet e përdoruesit", "components.ResetPassword.confirmpassword": "Konfirmo <PERSON>", "components.ResetPassword.emailresetlink": "Linku e rikuperimit të postës elektronike", "i18n.tvshows": "Seria", "components.ResetPassword.passwordreset": "Rivendos f<PERSON>", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Cilësimet e njoftimit Pushbullet u ruajtën me sukses!", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Duhet të jepni një URL të vlefshme", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "U dërgua njoftimi i testit LunaSea!", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL nuk duhet të përfundojë me një slesh", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Cilësimet e njoftimit Pushbullet nuk u ruajtën.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Po dërgohet njoftimi i testit Pushbullet…", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Ngarkesa JSON u rivendos me sukses!", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.RadarrModal.selectQualityProfile": "Zgjidh profilin e cilësisë", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Njoftimi i testit Pushbullet dështoi të dërgohet.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Njoftimi i testit pushbullet dërguar!", "components.Settings.SettingsAbout.outofdate": "E skaduar", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.Settings.Notifications.validationSmtpHostRequired": "Duhet të jepni një emër hosti ose adresë IP të vlefshme", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Po dërgohet njoftimi i testit të webhook…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "U dërgua njoftimi i testit webhook!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Rregullimet e njoftimit të Webhook dështuan të ruhen.", "components.Settings.Notifications.authPass": "Fjalëkalimi SMTP", "components.Settings.Notifications.botAPI": "Token e Autorizimit të Bot", "components.Settings.RadarrModal.rootfolder": "Direktoria", "components.Settings.RadarrModal.testFirstQualityProfiles": "<PERSON>o <PERSON>jen për të ngarkuar profilet e cilësisë", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL e webhook", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Cilësimet e njoftimit Webhook u ruajtën me sukses!", "components.Settings.Notifications.agentenabled": "Aktivizo agjentin", "components.Settings.Notifications.allowselfsigned": "Lejo çertifika<PERSON> e vetë-nënshkruara", "components.Settings.Notifications.authUser": "Emri i përdoruesit SMTP", "components.Settings.RadarrModal.selectMinimumAvailability": "Zgjidh disponueshmërinë minimale", "components.Settings.RadarrModal.selectRootFolder": "Zgjidhni dosjen rrënjë", "components.Settings.RadarrModal.selecttags": "Zgjidh etiketat", "components.Settings.RadarrModal.server4k": "Serveri 4K", "components.Settings.RadarrModal.servername": "Emri i serverit", "components.Settings.RadarrModal.ssl": "Përdor SSL", "components.Settings.RadarrModal.syncEnabled": "Aktivo skanimin", "components.Settings.RadarrModal.tags": "Etiketat", "components.Settings.RadarrModal.testFirstRootFolders": "<PERSON>o <PERSON>jen për të ngarkuar dosjet rrënjë", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Baza e URL-së duhet të ketë slesh në fillim", "components.Settings.RadarrModal.validationHostnameRequired": "<PERSON> duhet të siguroni një emër të vlefshëm host ose ad<PERSON> IP", "components.Settings.RadarrModal.validationNameRequired": "Du<PERSON><PERSON> të japësh një emër serveri", "components.Settings.SettingsAbout.Releases.releases": "L<PERSON><PERSON><PERSON>", "components.Settings.Notifications.pgpPrivateKey": "Çelësi privat PGP", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Të dhënat e lëshimit nuk janë të disponueshme.", "components.Settings.SettingsAbout.documentation": "Dokumentacioni", "components.Settings.SettingsAbout.gettingsupport": "Marrja e mbështetjes", "components.Settings.SettingsAbout.githubdiscussions": "Diskutimet e GitHub", "components.Settings.Notifications.sendSilentlyTip": "Dërgo njoftime pa zë", "components.Settings.Notifications.senderName": "Emri i dërguesit", "components.Settings.Notifications.smtpPort": "Porta SMTP", "components.Settings.Notifications.telegramsettingssaved": "Cilësimet e njoftimit të telegramit u ruajtën me sukses!", "components.Settings.Notifications.toastDiscordTestSuccess": "Njoftimi i testit të Discord u dërgua!", "components.Settings.Notifications.toastEmailTestFailed": "Njoftimi i testit me email nuk u dërgua.", "components.Settings.Notifications.toastEmailTestSending": "Po dërgo<PERSON>t njoftimi i testit me email…", "components.Settings.startscan": "<PERSON><PERSON>", "components.Settings.tautulliApiKey": "Çelësi API", "components.Settings.tautulliSettings": "Cilësimet e Tautulli", "components.Settings.toastPlexRefresh": "<PERSON> ma<PERSON><PERSON> e serverave nga Plex…", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL e Webhook", "components.Settings.notificationAgentSettingsDescription": "Konfiguro dhe aktivizo agjentët e njoftimit.", "components.Settings.plexlibrariesDescription": "Libraritë e Jellyseerr skanojnë për tituj. Vendos dhe ruaj cilësimet e lidhjes Plex, pastaj kliko në butonin më poshtë nëse nuk janë të listuara libraritë.", "components.Settings.plexsettingsDescription": "Konfiguro cilësimet për serverin tuaj Plex. Jellyseerr skanon libraritë tuaja Plex për të përcaktuar disponueshmërinë e përmbajtjes.", "components.Settings.port": "Porta", "components.Settings.radarrsettings": "Cilësimet e Radarr", "components.Settings.serverRemote": "në distancë", "components.Settings.sonarrsettings": "Cilësimet e Sonarr", "components.Settings.serverLocal": "lokale", "components.Settings.serverpresetRefreshing": "Duke ma<PERSON><PERSON> serverat…", "components.Settings.tautulliSettingsDescription": "Konfiguro në mënyrë opsionale rregullimet për serverin tënd Tautulli. Jellyseerr merr të dhënat e historisë për mediat tuaja Plex nga Tautulli.", "components.Settings.plexsettings": "Cilësimet e Plex", "components.Settings.serverpresetManualMessage": "Konfigurimi manual", "components.Settings.services": "Shërbime", "components.Settings.toastPlexRefreshFailure": "<PERSON><PERSON><PERSON> gjatë marrjes së listës së serverave Plex.", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Episod} other {Episode}}", "components.RequestModal.requestseasons4k": "Kërko {seasonCount} {seasonCount, plural, one {Sezon} other {Sezone}} në 4K", "components.RequestModal.requestseasons": "Kërko {seasonCount} {seasonCount, plural, one {Sezon} other {Sezone}}", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<LunaSeaLink>URL e Njoftimit Webhook</LunaSeaLink> të bazuar në përdorues ose paisje", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Duhet të zgjidhni të paktën një lloj njoftimi", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token API i aplikacionit", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Regjistroni një aplikacion</ApplicationRegistrationLink> për ta përdorur me <PERSON><PERSON>rr", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Ativizo Agjentin", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Cilësimet e njoftimit të Pushover nuk u ruajtën.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Cilësimet e njoftimit Pushover u ruajtën me sukses!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Njoftimi i testit <PERSON><PERSON><PERSON> dështoi të dërgonte.", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "<UsersGroupsLink>Identifikuesi juaj i grupit ose i përdoruesit</UsersGroupsLink> me 30 karaktere", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Duhet të japësh një token të vlefshëm aplikimi", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Duhet të zgjedhësh të paktën një tip njoftimi", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Duhet të jepni një çelës të vlefshëm përdoruesi ose grupi", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Ativizo Agjentin", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Cilësimet e njoftimit të Slack nuk u ruajtën.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Cilësimet e njoftimeve të Slack u ruajtën me sukses!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Njoftimi i testit të Slack dështoi të dërgohet.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Po dërgohet njoftimi i testit S<PERSON>ck…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "U dërgua njoftimi për testin e Slack!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Duhet të zgjidhni të paktën një lloj njoftimi", "components.Settings.Notifications.NotificationsWebhook.customJson": "Ngarkesa JSON", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Ndihmë për variablin e shabllonit", "components.Settings.Notifications.botApiTip": "<CreateBotLink><PERSON><PERSON><PERSON> një bot</CreateBotLink> për ta përdorur me Je<PERSON><PERSON>rr", "components.Settings.Notifications.botAvatarUrl": "URL-ja e Avatarit të Bot", "components.Settings.Notifications.botUsername": "Emri i përdoruesit të botit", "components.Settings.Notifications.botUsernameTip": "Lejoni përdoruesit të fillojnë gjithashtu një bisedë me robotin tuaj dhe të konfigurojnë njoftimet e tyre", "components.Settings.Notifications.chatId": "ID e bisedës", "components.Settings.Notifications.discordsettingsfailed": "Cilësimet e njoftimit të Diskordit nuk u ruajtën.", "components.Settings.Notifications.discordsettingssaved": "Cilësimet e njoftimit të Diskordit u ruajtën me sukses!", "components.Settings.Notifications.emailsender": "Adresa e dërguesit", "components.Settings.Notifications.emailsettingsfailed": "Cilësimet e njoftimit me email nuk u ruajtën.", "components.Settings.Notifications.enableMentions": "Aktivizo Përmendjet", "components.Settings.Notifications.encryption": "Metoda e kriptimit", "components.Settings.Notifications.encryptionDefault": "Përdorni STARTTLS nëse disponohet", "components.Settings.Notifications.encryptionImplicitTls": "Përdorni TLS të nënkuptuar", "components.Settings.Notifications.encryptionNone": "Asnjë", "components.Settings.Notifications.encryptionTip": "<PERSON>ë shumicën e rasteve, TLS Implicit përdor portën 465 dhe STARTTLS përdor portën 587", "components.Settings.Notifications.pgpPassword": "Fjalëkalimi PGP", "components.Settings.Notifications.pgpPasswordTip": "Nënshkruani mesa<PERSON>het e emailit të koduar duke p<PERSON><PERSON><PERSON><PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKeyTip": "Nënshkruani mesa<PERSON>het e emailit të koduar duke p<PERSON><PERSON><PERSON><PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.validationUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.Notifications.webhookUrl": "URL e Webhook", "components.Settings.RadarrModal.add": "Shto Server", "components.Settings.RadarrModal.announced": "I shpallur", "components.Settings.RadarrModal.apiKey": "Çelësi API", "components.Settings.RadarrModal.baseUrl": "Baza URL", "components.Settings.RadarrModal.create4kradarr": "Shto server të ri 4K Radarr", "components.Settings.RadarrModal.createradarr": "Shto server të ri Radarr", "components.Settings.RadarrModal.default4kserver": "Serveri i parazgjedhur 4K", "components.Settings.RadarrModal.defaultserver": "Serveri i parazgjedhur", "components.Settings.RadarrModal.edit4kradarr": "Ndrysho serverin 4K Radarr", "components.Settings.RadarrModal.editradarr": "Ndrysho serverin Radarr", "components.Settings.RadarrModal.enableSearch": "Aktivizo Kërkimin Automatik", "components.Settings.RadarrModal.externalUrl": "URL e jashtme", "components.Settings.RadarrModal.hostname": "Em<PERSON> i host ose adresa IP", "components.Settings.RadarrModal.inCinemas": "<PERSON><PERSON>", "components.Settings.RadarrModal.loadingTags": "Po ngarkon etiketat…", "components.Settings.RadarrModal.loadingprofiles": "<PERSON><PERSON> profilet e cilë<PERSON>ë…", "components.Settings.RadarrModal.loadingrootfolders": "<PERSON> ng<PERSON><PERSON><PERSON> dosjet rrënj<PERSON>…", "components.Settings.RadarrModal.minimumAvailability": "Disponueshmëria <PERSON>", "components.Settings.SettingsAbout.totalmedia": "Media totale", "components.Settings.SettingsAbout.totalrequests": "Kërkesat totale", "components.Settings.SettingsAbout.uptodate": "E përditësuar", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "Memorja cache {cachename} u fshi.", "components.Settings.SettingsJobsCache.cachehits": "Hitet", "components.Settings.SettingsJobsCache.cachekeys": "Çelësat totalë", "components.Settings.SettingsJobsCache.cacheksize": "Madhësia e çelësit", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "Emri i cache", "components.Settings.SettingsJobsCache.cachevsize": "Madhësia e vlerës", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON> punën", "components.Settings.SettingsJobsCache.command": "Komandë", "components.Settings.SettingsJobsCache.download-sync-reset": "Rivendos Sinkronizimin e Shkarkimit", "components.Settings.SettingsJobsCache.editJobSchedule": "Modifiko punën", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Frekuenca", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Çdo {jobScheduleHours, plural, one {orë} other {{jobScheduleHours} orë}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Çdo {jobScheduleMinutes, plural, one {minutë} other {{jobScheduleMinutes} minuta}}", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Diçka shkoi keq duke ruajtur punën.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Puna u modifikua me sukses!", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} u anulua.", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON> i punës", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobsandcache": "Punët & Cache", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} filloi.", "components.Settings.SettingsJobsCache.jobtype": "T<PERSON><PERSON>", "components.Settings.SettingsJobsCache.nextexecution": "Ekzekutimi tjetër", "components.Settings.SettingsJobsCache.plex-full-scan": "Skanimi i plotë i Librarisë Plex", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Skanimi i të shtuarave kohët e fundit nga Plex", "components.Settings.SettingsJobsCache.process": "Procesi", "components.Settings.SettingsJobsCache.radarr-scan": "Skanimi i Radarr", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "Skanimi i Sonarr", "components.Settings.SettingsJobsCache.unknownJob": "Punë e panjohur", "components.Settings.SettingsLogs.copiedLogMessage": "Mesazhi i regjistrit u kopjua.", "components.Settings.SettingsLogs.copyToClipboard": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.extraData": "Të dhëna shtesë", "components.Settings.SettingsLogs.filterDebug": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterError": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterInfo": "Informacion", "components.Settings.SettingsLogs.filterWarn": "Paralajmërim", "components.Settings.SettingsLogs.label": "Etiketa", "components.Settings.SettingsLogs.level": "Ashpërsia", "components.Settings.SettingsLogs.logDetails": "Detajet e regjistrit", "components.Settings.SettingsLogs.logs": "Regjistrat", "components.Settings.SettingsLogs.logsDescription": "<PERSON> gjithashtu mund t'i shikoni këto regjistra direkt nëpërmjet <code>stdout</code>, ose në <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.pauseLogs": "Ndalo", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.showall": "<PERSON><PERSON><PERSON>q të gjitha regji<PERSON>t", "components.Settings.Notifications.encryptionOpportunisticTls": "Përdorni gjithmonë STARTTLS", "components.Settings.Notifications.NotificationsPushover.userToken": "Çelësi i përdoruesit ose i grupit", "components.Settings.Notifications.chatIdTip": "Fillo një bisedë me robotin tënd, shto <GetIdBotLink>@get_id_bot</GetIdBotLink>, dhe vendos komandën <code>/my_id</code>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "<PERSON> d<PERSON><PERSON><PERSON><PERSON> njo<PERSON> e testit Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Njoftimi i testit Pushover është dërguar!", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON><PERSON> një integrim <DiscordWebhookLink>webhook</DiscordWebhookLink> në serverin tuaj", "components.Settings.Notifications.emailsettingssaved": "Cilësimet e njoftimit me email u ruajtën me sukses!", "components.Settings.SettingsAbout.version": "Versioni", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr ruan ne memorje cache kërkesat për api të jashtme për të optimizuar performancën dhe për të shmangur bërjen e thirrjeve API të panevojshme.", "components.Settings.SettingsJobsCache.download-sync": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobsDescription": "Mbikëqyrësi kryen disa detyra mirëmbajtjeje si punë të programuara rregullisht, por ato mund të aktivizohen edhe manualisht më poshtë. Bërja manuale e një pune nuk do të ndryshojë programin e saj.", "components.Settings.SettingsLogs.message": "Mesazh", "components.Settings.menuLogs": "Regjistrat", "components.Settings.menuPlexSettings": "Plex", "components.Settings.plex": "Plex", "components.Settings.settingUpPlexDescription": "Për të vendosur Plex, ju ose mund të vendosni detajet manualisht ose të zgjidhni një server të marrë nga <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Shtyp butonin në të djathtë të listës për të marrë listën e serverëve në dispozicion.", "components.Settings.ssl": "SSL", "components.Settings.toastPlexRefreshSuccess": "Lista e serverëve Plex u mor me sukses!", "components.Settings.toastTautulliSettingsFailure": "Diçka shkoi keq duke ruajtur cilësimet e Tautullit.", "components.Settings.toastTautulliSettingsSuccess": "Cilësimet e Tautulli u ruajtën me sukses!", "components.Settings.urlBase": "Baza URL", "components.Settings.validationApiKey": "Duhet të japësh një çelës API", "components.Settings.validationPortRequired": "Du<PERSON>t të jepni një numër të vlefshëm porte", "components.Settings.validationUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.validationUrlBaseLeadingSlash": "Baza e URL-së duhet të ketë slesh përpara", "components.Settings.validationUrlBaseTrailingSlash": "Baza e URL nuk duhet të përfundojë me një slesh", "components.Settings.validationUrlTrailingSlash": "URL nuk duhet të përfundojë me një slesh", "components.Settings.webAppUrl": "URL e <WebAppLink>Aplikacionit Web</WebAppLink>", "components.Settings.webhook": "Webhook", "components.Settings.webpush": "Web Push", "components.Setup.configureservices": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.continue": "Vazhdo", "components.Setup.finish": "Përfundo konfigurimin", "components.Setup.finishing": "<PERSON> p<PERSON><PERSON><PERSON><PERSON>…", "components.Setup.setup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Setup.signinMessage": "Filloni duke u identifikuar me llogarinë tuaj Plex", "components.Setup.welcome": "Mirë se vini në Jellyseerr", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "{status} 4K", "components.TvDetails.TvCast.fullseriescast": "Kast i plotë i serisë", "components.TvDetails.TvCrew.fullseriescrew": "Ekuipazhi i plotë i serisë", "components.TvDetails.anime": "Anime", "components.TvDetails.cast": "<PERSON><PERSON><PERSON>", "components.TvDetails.episodeRuntime": "Gjatësia e Episodit", "components.TvDetails.network": "{networkCount, plural, one {Rrjet} other {Rrjete}}", "components.TvDetails.nextAirDate": "Data e ardhshme e transmetimit", "components.TvDetails.originallanguage": "<PERSON><PERSON><PERSON>", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.overview": "Vështrim i përgjithshëm", "components.TvDetails.overviewunavailable": "Vështrimi i përgjithshëm i paarritshëm.", "components.TvDetails.productioncountries": "{countryCount, plural, one {Shtet} other {Shtete}} <PERSON><PERSON><PERSON>", "components.TvDetails.recommendations": "Rekomandime", "components.TvDetails.seasons": "{seasonCount, plural, one {# Sezon} other {# Sezone}}", "components.TvDetails.showtype": "Lloji i serisë", "components.TvDetails.similar": "Seri të ngjashme", "components.TvDetails.streamingproviders": "Po Transmetohet Në", "components.TvDetails.viewfullcrew": "<PERSON><PERSON> e<PERSON> e plotë", "components.TvDetails.watchtrailer": "<PERSON><PERSON>", "components.UserList.accounttype": "T<PERSON><PERSON>", "components.UserList.admin": "Administrator", "components.UserList.autogeneratepassword": "Gjenero automatikisht fjalëkalimin", "components.UserList.autogeneratepasswordTip": "Dërgo një fjalëkalim të gjeneruar nga serveri tek përdoruesi", "components.UserList.bulkedit": "Redaktimi në masë", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.created": "<PERSON><PERSON> bash<PERSON>ar", "components.UserList.createlocaluser": "<PERSON><PERSON><PERSON> lokal", "components.UserList.creating": "<PERSON> k<PERSON>…", "components.UserList.deleteconfirm": "Je i sigurt që do ta fshish këtë përdorues? Të gjitha të dhënat e kërkesave të tyre do të hiqen përgjithmonë.", "components.UserList.deleteuser": "<PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON>", "components.UserList.edituser": "Ndrysho të drejtat e përdoruesit", "components.UserList.localLoginDisabled": "Cilësimi <strong>Aktivizo identifikimin lokal</strong> është aktualisht i çaktivizuar.", "components.UserList.localuser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.newplexsigninenabled": "Cilësimi <strong>Aktivizo hyrjen e re në Plex</strong> është aktualisht i aktivizuar. Përdoruesit e Plex me akses në librari nuk kanë nevojë të importohen për t'u identifikuar.", "components.UserList.nouserstoimport": "Nuk ka përdorues Plex për të importuar.", "components.UserList.owner": "<PERSON><PERSON><PERSON>", "components.UserList.password": "Fjalëkalimi", "components.UserList.passwordinfodescription": "Konfiguro një URL aplikacioni dhe aktivizo njoftimet me email për të lejuar gjenerimin automatik të fjalëkalimit.", "components.UserList.plexuser": "Përdoruesi Plex", "components.UserList.sortRequests": "Numri i kërkesave", "components.UserList.role": "Roli", "components.UserList.sortCreated": "Data e anëtarësimit", "components.UserList.sortDisplayName": "<PERSON><PERSON> <PERSON>h<PERSON>", "components.UserList.totalrequests": "Kërkesat", "components.UserList.user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.usercreatedfailed": "Diçka shkoi keq duke <PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.UserList.usercreatedfailedexisting": "Adresa e email-it e dhënë është tashmë në përdorim nga një përdorues tjetër.", "components.UserList.usercreatedsuccess": "Përdoruesi u krijua me sukses!", "components.UserList.userdeleted": "Përdoruesi u fshi me sukses!", "components.UserList.userdeleteerror": "Diçka shkoi keq duke <PERSON><PERSON><PERSON><PERSON>.", "components.UserList.userfail": "Diçka shkoi keq duke ruajtur lejet e përdoruesit.", "components.UserList.userlist": "Lista e përdoruesve", "components.UserList.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.userssaved": "Të drejtat e përdoruesit të ruajtura me sukses!", "components.UserList.validationEmail": "Ju duhet të jepni një adresë të vlefshme e-mail", "components.UserList.validationpasswordminchars": "Fjalëkalimi është shumë i shkurtër; duhet të jetë një minimum prej 8 karakteresh", "components.UserProfile.ProfileHeader.joindate": "U bashkua në {joindate}", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON><PERSON><PERSON> profilin", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.ProfileHeader.userid": "ID e përdoruesit: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Lloji i llogarisë", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Sh<PERSON>q <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "<PERSON><PERSON> <PERSON>h<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID e përdoruesit të Diskordit", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>Numri i identifikimit me shumë shifra</FindDiscordIdLink> i lidhur me llogarinë tuaj të përdoruesit Discord", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Tejkalo limitin global", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Cilësimet e përgjithshme", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "<PERSON>g<PERSON> ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Kufiri i Kërkesës për Film", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Gjuha e Zbulimit", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtro përmbajtjen sipas gjuhës or<PERSON>ale", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtro përmbajtjen sipas disponueshmërisë rajonale", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Roli", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Përdoruesi Plex", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Rajoni i Zbulimit", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Diçka shkoi keq duke rua<PERSON><PERSON> cilësimet.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Cilësimet u ruajtën me sukses!", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Ju duhet të siguroni një ID të vlefshme të përdoruesit Discord", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID e përdoruesit", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limiti i kërkesës së serisë", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Cilësimet e njoftimit me email nuk u ruajtën.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Cilësimet e njoftimit me email u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Njoftimet", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Cilësimet e njoftimit", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Çelësi publik PGP", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Kripto mesazhet e emailit duke përdorur <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Cilësimet e njoftimit Pushbullet nuk u ruajtën.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Cilësimet e njoftimit Pushbullet u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Token API i aplikacionit", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Çelësi i përdoruesit ose i grupit", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "<UsersGroupsLink>Identifikuesi juaj i grupit ose i përdoruesit</UsersGroupsLink> me 30 karaktere", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Cilësimet e njoftimit të Pushover nuk u ruajtën.", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Dërgo në heshtje", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Dërgo njoftime pa zë", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID e bisedës", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink> Fillo një chat</TelegramBotLink>, shto <GetIdBotLink>@get_id_bot</GetIdBotLink>, dhe lëshoni komandën <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Cilësimet e njoftimit të Telegramit nuk u ruajtën.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Cilësimet e njoftimit Pushover u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Cilësimet e njoftimit të Telegramit u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Ju duhet të jepni një ID të vlefshme përdoruesi", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Duhet të japësh një token të vlefshëm aplikimi", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Duhet të jepni një çelës të vlefshëm përdoruesi ose grupi", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Duhet të jepni një ID të vlefshme bisede", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Cilësimet e njoftimit në ueb nuk u ruajtën.", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Konfirmo <PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Fjalëkalimi aktual", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Fjalëkalimi i ri", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Kjo llogari përdoruesi aktualisht nuk ka një fjalëkalim të caktuar. Konfiguro një fjalëkalim më poshtë për të mundësuar që kjo llogari të identifikohet si \"përdorues lokal.\"", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Llogaria juaj aktualisht nuk ka një fjalëkalim të caktuar. Konfiguro një fjalëkalim më poshtë për të mundësuar identifikimin si \"përdorues lokal\" duke p<PERSON><PERSON>rur adresën tënde të emailit.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Fjalëkalimi", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Diçka shkoi keq duke r<PERSON><PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Fjalëkalimi u ruajt me sukses!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Ju duhet të konfirmoni fjalëkalimin e ri", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Fjalëkalimet duhet të përputhen", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Duhet të japësh një fjalëkalim të ri", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Fjalëkalimi është shumë i shkurtër; duhet të jetë së paku 8 karaktere", "components.UserProfile.UserSettings.UserPermissions.permissions": "Le<PERSON>", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Nuk mund të ndryshosh të drejtat e tua.", "components.UserProfile.UserSettings.menuChangePass": "Fjalëkalimi", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuNotifications": "Njoftime", "components.UserProfile.UserSettings.menuPermissions": "Le<PERSON>", "components.UserProfile.UserSettings.unauthorizedDescription": "Ju nuk keni leje për të modifikuar cilësimet e këtij përdoruesi.", "components.UserProfile.limit": "{remaining} prej {limit}", "components.UserProfile.pastdays": "{type} (kaluar {days} ditë)", "components.UserProfile.recentlywatched": "Shi<PERSON>ar së fundmi", "components.UserProfile.totalrequests": "Kërkesat totale", "components.UserProfile.unlimited": "<PERSON>", "i18n.advanced": "<PERSON>", "i18n.approved": "<PERSON><PERSON><PERSON><PERSON>", "i18n.areyousure": "A je i sigurt?", "i18n.canceling": "<PERSON> an<PERSON>…", "i18n.close": "M<PERSON>lle", "i18n.decline": "Refuzo", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "Fshije", "i18n.deleting": "<PERSON> e fshir<PERSON>…", "i18n.delimitedlist": "{a}, {b}", "i18n.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.import": "Importo", "i18n.importing": "Duke importuar…", "i18n.movie": "Film", "i18n.open": "<PERSON>p", "i18n.partiallyavailable": "Pjesërisht në dispozicion", "i18n.pending": "<PERSON><PERSON> pritje", "i18n.previous": "Para", "i18n.processing": "Përpunim", "i18n.request": "Kërkesë", "i18n.request4k": "Kërkesë në 4K", "i18n.save": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "i18n.saving": "<PERSON> rua<PERSON>…", "i18n.settings": "Cilësimet", "i18n.requested": "<PERSON>", "i18n.requesting": "<PERSON>…", "i18n.resolved": "U Zgjidh", "i18n.resultsperpage": "Shfaq {pageSize} rezultate për faqe", "i18n.retry": "<PERSON><PERSON>", "i18n.retrying": "Duke provuar s<PERSON><PERSON>…", "i18n.showingresults": "<PERSON> sh<PERSON><PERSON>ur <strong>{from}</strong> tek <strong>{to}</strong> e <strong>{total}</strong> rezultateve", "i18n.status": "Statusi", "i18n.test": "Test", "pages.internalservererror": "Gabim i brendshëm i serverit", "pages.oops": "Ups", "pages.pagenotfound": "<PERSON><PERSON><PERSON> nuk u gjet", "pages.returnHome": "<PERSON><PERSON><PERSON> në shtëpi", "pages.serviceunavailable": "Shërbimi i paarritshëm", "pages.somethingwentwrong": "Diçka shkoi keq", "pages.errormessagewithcode": "{statusCode} - {error}", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Sezon} other {Sezone}}", "components.Layout.VersionStatus.streamdevelop": "Je<PERSON>seerr në Zhvillim", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {luajtje} other {luajtje}}", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data e Lëshimit} other {Datat e Lëshimit}}", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studio}}", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {ditë} other {ditë}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {filma}}", "components.QuotaSelector.seasons": "{count, plural, one {sezon} other {sezone}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} për {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} për {quotaDays} {days}</quotaUnits>", "components.RequestBlock.seasons": "{seasonCount, plural, one {Sezon} other {Sezone}}", "components.RequestButton.approve4krequests": "Aprovo {requestCount, plural, one {Kërkesën 4K} other {{requestCount} Kërkesat 4K}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON>zonet}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {<PERSON>zonet}}", "components.RequestModal.AdvancedRequester.default": "{name} (e parazg<PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {filma}}", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON><PERSON><PERSON>} other {<strong>#</strong>}} {type} {remaining, plural, one {kërkesë} other {kërkesa}} ngelur", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezon} other {sezone}}", "components.Settings.RadarrModal.notagoptions": "<PERSON><PERSON> ka etiketa.", "components.Settings.RadarrModal.port": "Porta", "components.Settings.RadarrModal.qualityprofile": "Profili i cilësisë", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Ndryshimet", "components.Settings.SettingsAbout.helppaycoffee": "<PERSON><PERSON>hm<PERSON> për të paguar kafenë", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Preferuar", "components.Settings.SettingsAbout.runningDevelop": "Ju jeni duke p<PERSON><PERSON><PERSON><PERSON> de<PERSON> <code>develop</code> të <PERSON>, e cila është e rekomanduar vetëm për ata që kontribuojnë në zhvillimin ose të ndihmojë në testimin.", "components.Settings.SettingsAbout.timezone": "Zona Kohore", "components.Settings.SettingsLogs.time": "<PERSON><PERSON> kohore", "components.Settings.SettingsUsers.defaultPermissions": "Lejet e parazgjedhura", "components.Settings.SettingsUsers.localLogin": "Aktivizo identifikimin lokal", "components.Settings.SettingsUsers.localLoginTip": "<PERSON><PERSON> p<PERSON>t të identifikohen duke p<PERSON><PERSON><PERSON><PERSON> ad<PERSON> e tyre të emailit dhe fjalëkalimin, në vend të Plex OAuth", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Limiti Global i kërkesës për Filma", "components.Settings.SettingsUsers.newPlexLogin": "Aktivizo hyrjen në {mediaServerName} të ri", "components.Settings.SettingsUsers.newPlexLoginTip": "<PERSON><PERSON> p<PERSON>rdorue<PERSON> e {mediaServerName} të identifikohen pa u importuar më parë", "components.Settings.SettingsUsers.toastSettingsFailure": "Diçka shkoi keq duke rua<PERSON><PERSON> cilësimet.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Cilësimet e përdoruesit u ruajtën me sukses!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Limiti Global i kërkesës së Serialeve", "components.Settings.SettingsUsers.userSettings": "Cilësimet e përdoruesit", "components.Settings.SettingsUsers.userSettingsDescription": "Konfiguro cilësimet globale dhe të paracaktuara të përdoruesit.", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "Shto Server", "components.Settings.SonarrModal.animeTags": "Etiketat anime", "components.Settings.SonarrModal.animelanguageprofile": "Profili i gjuhës anime", "components.Settings.SonarrModal.animequalityprofile": "Profili i Cilësisë së Anime", "components.Settings.SonarrModal.apiKey": "Çelësi API", "components.Settings.SonarrModal.baseUrl": "Baza URL", "components.Settings.SonarrModal.createsonarr": "Shto Serverin e Ri Sonarr", "components.Settings.SonarrModal.edit4ksonarr": "Ndrysho serverin 4K Sonarr", "components.Settings.SonarrModal.editsonarr": "Ndrysho serverin Sonarr", "components.Settings.SonarrModal.enableSearch": "Aktivizo Kërkimin Automatik", "components.Settings.SonarrModal.externalUrl": "URL e jashtme", "components.Settings.SonarrModal.hostname": "Emri i hostit ose adresa IP", "components.Settings.SonarrModal.languageprofile": "Profili i gjuhës", "components.Settings.SonarrModal.loadingTags": "<PERSON> ng<PERSON><PERSON>r etiketat…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Ngarkimi i profileve gjuhësore…", "components.Settings.SonarrModal.loadingprofiles": "<PERSON><PERSON> profilet e cilë<PERSON>ë…", "components.Settings.SonarrModal.loadingrootfolders": "<PERSON> ng<PERSON><PERSON><PERSON> dosjet rrënj<PERSON>…", "components.Settings.SonarrModal.notagoptions": "<PERSON><PERSON> ka etiketa.", "components.Settings.SonarrModal.port": "Porta", "components.Settings.SonarrModal.qualityprofile": "Profili i cilësisë", "components.Settings.SonarrModal.rootfolder": "Direktoria", "components.Settings.SonarrModal.seasonfolders": "Dosjet e sezonit", "components.Settings.SonarrModal.selectLanguageProfile": "Zgjidh profilin e gjuhës", "components.Settings.SonarrModal.selectQualityProfile": "Zgjidh profilin e cilësisë", "components.Settings.SonarrModal.selectRootFolder": "Zgjidhni dosjen rrënjë", "components.Settings.SonarrModal.selecttags": "Zgjidh etiketat", "components.Settings.SonarrModal.server4k": "Serveri 4K", "components.Settings.SonarrModal.servername": "Emri i serverit", "components.Settings.SonarrModal.tags": "Etiketat", "components.Settings.SonarrModal.testFirstQualityProfiles": "<PERSON>o <PERSON>jen për të ngarkuar profilet e cilësisë", "components.Settings.SonarrModal.testFirstRootFolders": "<PERSON>o <PERSON>jen për të ngarkuar dosjet rrënjë", "components.Settings.SonarrModal.toastSonarrTestFailure": "Dështoi lidhja me Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "<PERSON><PERSON><PERSON> Sonarr u krijua me sukses!", "components.Settings.SonarrModal.validationApiKeyRequired": "Duhet të japësh një çelës API", "components.Settings.SonarrModal.validationApplicationUrl": "Duhet të jepni një URL të vlefshme", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL nuk duhet të përfundojë me një slesh", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "URL bazë duhet të ketë një slesh perpara", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "URL bazë nuk duhet të përfundojë me një slesh", "components.Settings.SonarrModal.validationHostnameRequired": "<PERSON> duhet të siguroni një emër të vlefshëm host ose adrese IP", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Duhet të zgjedhësh një profil gjuhe", "components.Settings.SonarrModal.validationNameRequired": "Du<PERSON><PERSON> të japësh një emër serveri", "components.Settings.SonarrModal.validationPortRequired": "Du<PERSON>t të jepni një numër të vlefshëm porte", "components.Settings.SonarrModal.validationProfileRequired": "Duhet të zgjedhësh një profil cilësie", "components.Settings.activeProfile": "Profili aktiv", "components.Settings.addradarr": "Shto Serverin Radarr", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON>in <PERSON>", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON>", "components.Settings.copied": "Çelësi API u kopjua.", "components.Settings.currentlibrary": "Libraria aktuale: {name}", "components.Settings.default": "E paracaktuar", "components.Settings.default4k": "E Paracaktuar 4K", "components.Settings.deleteserverconfirm": "Jeni i sigurt që dëshironi ta fshini këtë server?", "components.Settings.email": "Email", "components.Settings.enablessl": "Përdorni SSL", "components.Settings.externalUrl": "URL e jashtme", "components.Settings.hostname": "Emri i hostit ose adresa IP", "components.Settings.is4k": "4K", "components.Settings.librariesRemaining": "Libraritë e mbetura: {count}", "components.Settings.manualscan": "Skanimi manual i Librarisë", "components.Settings.manualscanDescription": "Normalisht, kjo do të bëhet vetëm një herë në 24 orë. Jellyseerr do të kontrollojë serverin tuaj Plex për media të shtuar kohët e fundit në mënyrë më agresive. Nëse kjo është hera e parë që konfiguroni Plex, kë<PERSON>llohet një skanim manual një herë i plotë i librarisë!", "components.Settings.mediaTypeMovie": "film", "components.Settings.mediaTypeSeries": "seri", "components.Settings.SonarrModal.default4kserver": "Serveri i parazgjedhur 4K", "components.Settings.SonarrModal.animerootfolder": "Dosja e rrënjës së animes", "components.Settings.SettingsUsers.defaultPermissionsTip": "Lejet fillestare të caktuara për përdoruesit e rinj", "components.Settings.SonarrModal.defaultserver": "Serveri i parazgjedhur", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Lidhja e provës për të ngarkuar profilet e gjuhës", "components.Settings.SonarrModal.create4ksonarr": "Shto Serverin e Ri Sonarr 4K", "components.Settings.SonarrModal.ssl": "Përdor SSL", "components.Settings.SonarrModal.syncEnabled": "Aktivo skanimin", "components.Settings.SonarrModal.testFirstTags": "<PERSON><PERSON> për të ngarkuar etiketat", "components.Settings.SonarrModal.validationRootFolderRequired": "Duhet të zgjedhësh një dosje rrënjë", "components.Settings.serviceSettingsDescription": "Konfiguro serverin tënd {serverType} më poshtë. Ju mund të lidhni shumë servera {serverType}, por vetëm dy prej tyre mund të shënohen si të prezgjedhur (një jo-4K dhe një 4K). Administratorët janë në gjendje të kapërcejnë serverin e përdorur për të përpunuar kërkesa të reja përpara miratimit.", "components.Settings.validationHostnameRequired": "<PERSON> duhet të siguroni një emër të vlefshëm host ose adrese IP", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Ju nuk keni leje për të modifikuar fjalëkalimin e këtij përdoruesi.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Diçka shkoi keq duke ruaj<PERSON> fjalëkalimin. A u fut fjalëkalimi në mënyrë korrekte?", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Diçka shkoi keq duke rua<PERSON><PERSON> cilësimet.", "components.Settings.webAppUrlTip": "Në mënyrë opsionale drejto përdoruesit në aplikacionin web në serverin tënd në vend të atij web", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minuta", "components.UserList.importfrommediaserver": "Importoni p<PERSON> {mediaServerName}", "components.UserList.importfromplex": "Importoni përdoruesit Plex", "components.UserList.importfromplexerror": "Diçka shkoi keq duke import<PERSON><PERSON> përdoruesit Plex.", "components.TvDetails.firstAirDate": "Data e parë e transmetimit", "components.UserList.email": "Adresa email", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {përdorues} other {përdorues}} nga Plex u importuan me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Cilësimet e njoftimit në ueb u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>Numri i identifikimit me shumë shifra</FindDiscordIdLink> i lidhur me llogarinë tuaj të përdoruesit", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Duhet të jepni një çelës publik të vlefshëm PGP", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Duhet të japësh një Token hyrjeje", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Lejet u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Cilësimet e njoftimit të Diskordit nuk u ruajtën.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "<PERSON><PERSON><PERSON> një token nga <PushbulletSettingsLink>Cilësimet e llogarisë</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Regjistro një aplikacion</ApplicationRegistrationLink> për pë<PERSON><PERSON> me {applicationTitle}", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Ju duhet të jepni fjalëkalimin tuaj aktual", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Cilësimet e njoftimit të Diskordit u ruajtën me sukses!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.movies": "Filma", "components.UserProfile.recentrequests": "Kërkesat e Fundit", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON>", "components.UserProfile.movierequests": "Kërkesa për filma", "components.UserProfile.requestsperdays": "{limit} t<PERSON> m<PERSON>", "i18n.all": "<PERSON><PERSON> gjitha", "i18n.approve": "Mirato", "i18n.back": "Mbrapa.", "i18n.loading": "<PERSON> ng<PERSON>ohet…", "components.UserProfile.seriesrequest": "Kërkesat e serisë", "i18n.available": "Në dispozicion", "i18n.cancel": "<PERSON><PERSON>", "i18n.edit": "<PERSON><PERSON><PERSON><PERSON>", "i18n.next": "Tjetra", "i18n.experimental": "Eksperimentale", "i18n.noresults": "S'ka rezultate.", "i18n.notrequested": "Nuk është Kerkuar", "components.Settings.SettingsAbout.appDataPath": "Direktoria e të dhënave", "components.MovieDetails.digitalrelease": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.physicalrelease": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.theatricalrelease": "Publikimi Teatror", "components.PermissionEdit.viewrecent": "Shiko <PERSON> së fundi", "components.PermissionEdit.viewrecentDescription": "<PERSON><PERSON> leje për të parë listën e mediave të shtuara kohët e fundit.", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Lista juaj e shikimit në Plex", "components.PermissionEdit.autorequestMoviesDescription": "Je<PERSON>ni lejen për të paraqitur automatikisht kërkesat për mediat jo-4K nëpërmjet Plex Watchlist.", "components.PermissionEdit.autorequestSeries": "Kërkesa Automatike Serialesh", "components.PermissionEdit.autorequestSeriesDescription": "<PERSON><PERSON> leje për të paraqitur automatikisht kërkesat për seri jo-4K nëpërmjet Plex Watchlist.", "components.Discover.DiscoverWatchlist.watchlist": "Lista e Shikimit Plex", "components.Discover.plexwatchlist": "Lista juaj e shikimit në Plex", "components.MovieDetails.reportissue": "<PERSON><PERSON><PERSON> një problem", "components.NotificationTypeSelector.mediaautorequested": "Kërkesa u dorëzua automatikisht", "components.PermissionEdit.autorequest": "Auto-Kërkesë", "components.PermissionEdit.autorequestDescription": "Je<PERSON>ni lejen për të paraqitur automatikisht kërkesat për mediat jo-4K nëpërmjet Plex Watchlist.", "components.PermissionEdit.autorequestMovies": "Kërkesë automatike për filma", "components.PermissionEdit.viewwatchlists": "Shiko listat e shikimit të Plex", "components.PermissionEdit.viewwatchlistsDescription": "<PERSON><PERSON> leje për të parë listat e Plex Watchlist të përdoruesve të tjerë.", "components.MovieDetails.managemovie": "Menaxho filmin", "components.AirDateBadge.airedrelative": "Transmetuar {relativeTime}", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Kërkesat e Filmave", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Kërkesat e Serialeve", "components.Layout.UserDropdown.requests": "Kërkesat", "components.MovieDetails.rtaudiencescore": "Rezultati i audiencës së Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatometeri i Rotten Tomatoes", "components.NotificationTypeSelector.mediaautorequestedDescription": "Njoftohuni kur kërkesat e reja të medias dorëzohen automatikisht për artikujt në Listën tuaj të Plex Watchlist.", "components.RequestBlock.languageprofile": "Profili i gjuhës", "components.AirDateBadge.airsrelative": "Duke u Transmetuar {relativeTime}", "components.Discover.emptywatchlist": "Këtu do të shfaqet media e shtuar në listën tuaj <PlexWatchlistSupportLink>Plex</PlexWatchlistSupportLink>.", "components.RequestBlock.decline": "Refuzo kërkesën", "components.RequestBlock.delete": "Fshije <PERSON>rkesën", "components.RequestBlock.edit": "Ndrysho k<PERSON>rkesën", "components.MovieDetails.tmdbuserscore": "Nota e përdoruesve TMDB", "components.RequestBlock.approve": "<PERSON><PERSON>", "components.RequestBlock.lastmodifiedby": "Ndryshuar së fundi nga", "components.RequestBlock.requestdate": "Data e Kërkesës", "components.RequestBlock.requestedby": "K<PERSON>rkuar nga", "components.RequestCard.approverequest": "<PERSON><PERSON>"}