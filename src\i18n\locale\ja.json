{"components.Discover.popularmovies": "人気の映画", "components.Discover.populartv": "人気のテレビ番組", "components.Discover.recentlyAdded": "最近追加された動画", "components.Discover.recentrequests": "最近のリクエスト", "components.Discover.trending": "トレンド", "components.Discover.upcoming": "今後リリースされる映画", "components.Discover.upcomingmovies": "今後リリースされる映画", "components.Layout.SearchInput.searchPlaceholder": "作品名で検索", "components.Layout.Sidebar.dashboard": "ホーム", "components.Layout.Sidebar.requests": "リクエスト", "components.Layout.Sidebar.settings": "設定", "components.Layout.Sidebar.users": "ユーザー", "components.Layout.UserDropdown.signout": "ログアウト", "components.MovieDetails.budget": "予算", "components.MovieDetails.cast": "出演者", "components.MovieDetails.originallanguage": "オリジナルの言語", "components.MovieDetails.overview": "ストーリー", "components.MovieDetails.overviewunavailable": "ストーリー情報がありません。", "components.MovieDetails.recommendations": "オススメの作品", "components.MovieDetails.releasedate": "公開日", "components.MovieDetails.revenue": "収益", "components.MovieDetails.runtime": "{minutes} 分", "components.MovieDetails.similar": "関連作品", "components.PersonDetails.appearsin": "出演作品", "components.PersonDetails.ascharacter": "{character} 役", "components.RequestBlock.seasons": "シーズン", "components.RequestCard.seasons": "シーズン", "components.RequestList.RequestItem.seasons": "シーズン", "components.RequestList.requests": "リクエスト", "components.RequestModal.cancel": "キャンセルリクエスト", "components.RequestModal.numberofepisodes": "エピソード数", "components.RequestModal.pendingrequest": "", "components.RequestModal.requestCancel": "<strong>{title}</strong> のリクエストは取り消されました。", "components.RequestModal.requestSuccess": "<strong>{title}</strong> のリクエストは完了しました。", "components.RequestModal.requestadmin": "このリクエストは今すぐ承認されます。", "components.RequestModal.requestfrom": "{username} はすでにリクエストを上げています。", "components.RequestModal.requestseasons": "{seasonCount} シーズンをリクエスト", "components.RequestModal.season": "シーズン", "components.RequestModal.seasonnumber": "シーズン {number}", "components.RequestModal.selectseason": "シーズンを選ぶ", "components.Search.searchresults": "検索結果", "components.Settings.Notifications.agentenabled": "エージェントを有効にする", "components.Settings.Notifications.authPass": "SMTP 認証パスワード", "components.Settings.Notifications.authUser": "SMTP 認証ユーザー", "components.Settings.Notifications.emailsender": "配信元メールアドレス", "components.Settings.Notifications.smtpHost": "SMTP ホスト", "components.Settings.Notifications.smtpPort": "SMTP ポート", "components.Settings.Notifications.validationSmtpHostRequired": "有効なホスト名・IP アドレスを入力してください", "components.Settings.Notifications.validationSmtpPortRequired": "有効なポートを入力してください", "components.Settings.Notifications.webhookUrl": "ウェブフック URL", "components.Settings.RadarrModal.add": "サーバーを追加", "components.Settings.RadarrModal.apiKey": "API キー", "components.Settings.RadarrModal.baseUrl": "URL のベース", "components.Settings.RadarrModal.createradarr": "Radarr サーバーを追加", "components.Settings.RadarrModal.defaultserver": "デフォルトサーバー", "components.Settings.RadarrModal.editradarr": "Radarr サーバーを編集", "components.Settings.RadarrModal.hostname": "ホスト名・IP アドレス", "components.Settings.RadarrModal.minimumAvailability": "最低リリース状況", "components.Settings.RadarrModal.port": "ポート", "components.Settings.RadarrModal.qualityprofile": "画質プロファイル", "components.Settings.RadarrModal.rootfolder": "ルートフォルダー", "components.Settings.RadarrModal.selectMinimumAvailability": "最低リリース状況を選ぶ", "components.Settings.RadarrModal.selectQualityProfile": "画質プロファイルを選ぶ", "components.Settings.RadarrModal.selectRootFolder": "ルートフォルダーを選ぶ", "components.Settings.RadarrModal.server4k": "4K サーバー", "components.Settings.RadarrModal.servername": "サーバー名", "components.Settings.RadarrModal.ssl": "SSL を有効にする", "components.Settings.RadarrModal.toastRadarrTestFailure": "Radarr サーバーの接続は失敗しました。", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr サーバーの接続は成功しました!", "components.Settings.RadarrModal.validationApiKeyRequired": "API キーを入力してください", "components.Settings.RadarrModal.validationHostnameRequired": "有効なホスト名・IP アドレスを入力してください", "components.Settings.RadarrModal.validationPortRequired": "有効なポートを入力してください", "components.Settings.RadarrModal.validationProfileRequired": "プロファイルを選択してください", "components.Settings.RadarrModal.validationRootFolderRequired": "ルートフォルダーを選択してください", "components.Settings.SonarrModal.add": "サーバーを追加", "components.Settings.SonarrModal.apiKey": "API キー", "components.Settings.SonarrModal.baseUrl": "URL のベース", "components.Settings.SonarrModal.createsonarr": "Sonarr サーバーを追加", "components.Settings.SonarrModal.defaultserver": "デフォルトサーバー", "components.Settings.SonarrModal.editsonarr": "Sonarr サーバーを編集", "components.Settings.SonarrModal.hostname": "ホスト名・IP アドレス", "components.Settings.SonarrModal.port": "ポート", "components.Settings.SonarrModal.qualityprofile": "画質プロファイル", "components.Settings.SonarrModal.rootfolder": "ルートフォルダー", "components.Settings.SonarrModal.seasonfolders": "シーズンフォルダー", "components.Settings.SonarrModal.selectQualityProfile": "画質プロファイルを選ぶ", "components.Settings.SonarrModal.selectRootFolder": "ルートフォルダーを選ぶ", "components.Settings.SonarrModal.server4k": "4K サーバー", "components.Settings.SonarrModal.servername": "サーバー名", "components.Settings.SonarrModal.ssl": "SSL を有効にする", "components.Settings.SonarrModal.validationApiKeyRequired": "API キーの入力が必要です", "components.Settings.SonarrModal.validationHostnameRequired": "有効なホスト名・IP アドレスを入力してください", "components.Settings.SonarrModal.validationPortRequired": "有効なポートを入力してください", "components.Settings.SonarrModal.validationProfileRequired": "プロファイルを選択してください", "components.Settings.SonarrModal.validationRootFolderRequired": "ルートフォルダーを選択してください", "components.Settings.activeProfile": "アクティブプロファイル", "components.Settings.addradarr": "Radarr サーバーを追加", "components.Settings.address": "アドレス", "components.Settings.addsonarr": "Sonarr サーバーを追加", "components.Settings.cancelscan": "スキャンをキャンセル", "components.Settings.copied": "API キーをクリップボードにコピーされた。", "components.Settings.currentlibrary": "現在のライブラリー：{name}", "components.Settings.default": "デフォルト", "components.Settings.default4k": "デフォルト 4K", "components.Settings.deleteserverconfirm": "このサーバーを削除しますか?", "components.Settings.hostname": "ホスト名・IP アドレス", "components.Settings.librariesRemaining": "残りのライブラリー：{count}", "components.Settings.manualscan": "手動ライブラリースキャン", "components.Settings.manualscanDescription": "通常は 24 時間に一度しか実行されません。Jellyseerr は、Plex サーバーの最近追加されたフォルダをより頻繁にチェックします。初めて Plex を設定する場合は、一度手動でライブラリーをスキャンすることをお勧めします。", "components.Settings.menuAbout": "Jellyseerr について", "components.Settings.menuGeneralSettings": "一般", "components.Settings.menuJobs": "ジョブ", "components.Settings.menuLogs": "ログ", "components.Settings.menuNotifications": "通知", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "連携サービス", "components.Settings.notificationsettings": "通知設定", "components.Settings.notrunning": "実行されていない", "components.Settings.plexlibraries": "Plex ライブラリー", "components.Settings.plexlibrariesDescription": "Jellyseerr がスキャンしてタイトルを探すライブラリ。リストにない場合、Plex の接続設定を保存し、下のボタンをクリックしてください。", "components.Settings.plexsettings": "Plex の設定", "components.Settings.plexsettingsDescription": "Plex サーバーの設定。Jellyseerr は、Plex サーバーを使用して、間隔をおいてライブラリをスキャンし、利用可能なコンテンツを確認します。", "components.Settings.port": "ポート", "components.Settings.radarrsettings": "Radarr 設定", "components.Settings.sonarrsettings": "Sonarr 設定", "components.Settings.ssl": "SSL", "components.Settings.startscan": "スキャンを開始", "components.Setup.configureservices": "連携サービスの設定", "components.Setup.continue": "続行", "components.Setup.finish": "セットアップを完了", "components.Setup.finishing": "保存中…", "components.Setup.signinMessage": "Plex アカウントでログインして始める", "components.Setup.welcome": "<PERSON><PERSON><PERSON><PERSON> へようこそ", "components.TvDetails.cast": "出演者", "components.TvDetails.originallanguage": "オリジナルの言語", "components.TvDetails.overview": "ストーリー", "components.TvDetails.overviewunavailable": "ストーリー情報がありません。", "components.TvDetails.recommendations": "オススメの作品", "components.TvDetails.similar": "類似シリーズ", "components.UserList.admin": "管理者", "components.UserList.created": "作成日", "components.UserList.plexuser": "Plexユーザー", "components.UserList.role": "役割", "components.UserList.totalrequests": "リクエスト数", "components.UserList.user": "ユーザー", "components.UserList.userlist": "ユーザーリスト", "i18n.approve": "承認", "i18n.approved": "承認済み", "i18n.available": "視聴可能", "i18n.cancel": "キャンセル", "i18n.decline": "拒否する", "i18n.declined": "拒否済み", "i18n.delete": "削除", "i18n.movies": "映画", "i18n.partiallyavailable": "一部視聴可能", "i18n.pending": "リクエスト中", "i18n.processing": "処理中", "i18n.tvshows": "シリーズ", "i18n.unavailable": "視聴不可", "pages.oops": "ああ", "pages.returnHome": "ホームへ戻る", "components.TvDetails.TvCast.fullseriescast": "すべての出演者", "components.Settings.validationPortRequired": "有効なポートを入力してください", "components.Settings.validationHostnameRequired": "有効なホスト名・IP アドレスを入力してください", "components.Settings.SonarrModal.validationNameRequired": "サーバー名を入力してください", "components.Settings.SettingsAbout.version": "バージョン", "components.Settings.SettingsAbout.totalrequests": "総リクエスト数", "components.Settings.SettingsAbout.totalmedia": "総メディア数", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON><PERSON> 情報", "components.Settings.SettingsAbout.githubdiscussions": "GitHub ディスカッション", "components.Settings.SettingsAbout.gettingsupport": "サポート", "components.Settings.RadarrModal.validationNameRequired": "サーバー名を入力してください", "components.Settings.Notifications.emailsettingssaved": "メール通知設定が保存されました。", "components.Settings.Notifications.emailsettingsfailed": "メール通知設定の保存に失敗しました。", "components.Settings.Notifications.discordsettingsfailed": "Discord の通知設定の保存に失敗しました。", "components.Settings.Notifications.discordsettingssaved": "Discord の通知設定が保存されました。", "components.MovieDetails.MovieCast.fullcast": "すべての出演者", "i18n.deleting": "削除中…", "components.UserList.userdeleteerror": "ユーザーの削除する時に問題が発生しました。", "components.UserList.userdeleted": "ユーザーが削除されました。", "components.UserList.deleteuser": "ユーザーの削除", "components.UserList.deleteconfirm": "このユーザーを削除しますか？このユーザーの既存のリクエストデータはすべて削除されます。", "components.TvDetails.showtype": "番組タイプ", "components.TvDetails.network": "テレビ局", "components.TvDetails.anime": "アニメ", "components.Settings.SonarrModal.testFirstRootFolders": "ルートフォルダーをロードするには先に接続をテストしてください", "components.Settings.SonarrModal.testFirstQualityProfiles": "画質プロファイルをロードするには先に接続をテストしてください", "components.Settings.SonarrModal.loadingrootfolders": "ルートフォルダー読込中…", "components.Settings.SonarrModal.loadingprofiles": "画質プロファイル読込中…", "components.Settings.SonarrModal.animerootfolder": "アニメルートフォルダー", "components.Settings.SonarrModal.animequalityprofile": "アニメ画質プロファイル", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "最低リリース状況を選択してください", "components.Settings.RadarrModal.testFirstRootFolders": "ルートフォルダーをロードするには先に接続をテストしてください", "components.Settings.RadarrModal.testFirstQualityProfiles": "画質プロファイルをロードするには先に接続をテストしてください", "components.Settings.RadarrModal.loadingprofiles": "画質プロファイル読込中…", "components.Settings.RadarrModal.loadingrootfolders": "ルートフォルダー読込中…", "components.MovieDetails.studio": "制作会社", "i18n.close": "閉じる", "components.Settings.SettingsAbout.timezone": "タイムゾーン", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON> を応援", "components.Settings.SettingsAbout.helppaycoffee": "開発者のコーヒーのためにチップを", "components.Settings.SettingsAbout.Releases.viewongithub": "GitHub で見る", "components.Settings.SettingsAbout.Releases.viewchangelog": "変更履歴参照", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} の変更履歴", "components.Settings.SettingsAbout.Releases.releases": "リリース", "components.Settings.SettingsAbout.Releases.releasedataMissing": "リリースデータがありません。", "components.Settings.SettingsAbout.Releases.latestversion": "最新のバージョン", "components.Settings.SettingsAbout.Releases.currentversion": "現在のバージョン", "components.MovieDetails.MovieCrew.fullcrew": "フルクルー", "components.MovieDetails.viewfullcrew": "フルクルーを表示", "components.CollectionDetails.requestcollection": "リクエストコレクション", "components.CollectionDetails.overview": "ストーリー", "components.CollectionDetails.numberofmovies": "{count} 本の映画", "i18n.requested": "リクエスト済み", "components.TvDetails.watchtrailer": "予告編を見る", "components.MovieDetails.watchtrailer": "予告編を見る", "components.UserList.importfromplexerror": "Plex からユーザーをインポート中に問題が発生しました。", "components.UserList.importfromplex": "Plex からユーザーをインポート", "components.UserList.importedfromplex": "Plex から新ユーザー {userCount} 名をインポートしました。", "components.TvDetails.viewfullcrew": "フルクルーを表示", "components.TvDetails.firstAirDate": "初放送日", "components.TvDetails.TvCrew.fullseriescrew": "フルシリーズクルー", "components.Settings.Notifications.allowselfsigned": "自己署名証明書を許可する", "components.PersonDetails.crewmember": "クルー", "i18n.retry": "リトライ", "i18n.failed": "失敗", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "ウェブフック URL", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack の通知設定が保存されました。", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Slack の通知設定の保存に失敗しました。", "components.Login.signin": "ログイン", "components.Login.password": "パスワード", "components.Login.loginerror": "ログイン中に問題が発生しました。", "components.Login.forgotpassword": "パスワードを忘れた場合", "components.Login.email": "メールアドレス", "components.Layout.UserDropdown.settings": "設定", "components.Layout.UserDropdown.myprofile": "プロフィール", "components.Discover.discover": "ホーム", "components.CollectionDetails.requestcollection4k": "4K のコレクションをリクエスト", "components.AppDataWarning.dockerVolumeMissingDescription": "<code>{appDataPath}</code> ボリュームマウントが正しく構成されていませんでした。コンテナーが停止または再起動されると、すべてのデータが消去されます。", "components.Login.signingin": "ログイン中…", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "設定保存中に問題が発生しました。", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "設定保存中に問題が発生しました。", "components.Login.validationemailrequired": "有効なメールアドレスを入力してください", "components.Login.signinwithplex": "Plex アカウントを使用する", "components.Login.signinwithoverseerr": "{applicationTitle} アカウントを使用する", "components.Login.signinheader": "続けるにはログインしてください", "components.MediaSlider.ShowMoreCard.seemore": "もっと見る", "components.Login.validationpasswordrequired": "パスワードを入力してください", "components.ResetPassword.validationemailrequired": "有効なメールアドレスを入力してください", "components.Settings.Notifications.validationEmail": "有効なメールアドレスを入力してください", "components.UserList.validationEmail": "有効なメールアドレスを入力してください", "components.ResetPassword.email": "メールアドレス", "components.UserList.email": "メールアドレス", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "エージェントを有効にする", "components.Settings.Notifications.NotificationsPushover.agentenabled": "エージェントを有効にする", "components.Settings.Notifications.NotificationsSlack.agentenabled": "エージェントを有効にする", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "エージェントを有効にする", "components.PermissionEdit.request4k": "4K をリクエスト", "components.UserProfile.UserSettings.menuNotifications": "通知", "components.ResetPassword.password": "パスワード", "components.UserList.password": "パスワード", "components.UserProfile.UserSettings.menuChangePass": "パスワード", "components.UserProfile.UserSettings.UserPasswordChange.password": "パスワード", "i18n.edit": "編集", "components.Settings.menuUsers": "ユーザー", "components.UserList.users": "ユーザー", "components.Settings.SettingsUsers.users": "ユーザー", "components.Settings.SettingsUsers.toastSettingsFailure": "設定保存中に問題が発生しました。", "components.UserProfile.UserSettings.menuGeneralSettings": "一般", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "一般設定", "components.UserProfile.UserSettings.UserGeneralSettings.general": "一般", "pages.internalservererror": "内部サーバーエラー", "pages.somethingwentwrong": "チケットが発生しました", "pages.serviceunavailable": "サービスが利用できません", "pages.pagenotfound": "ページが見つかりません", "pages.errormessagewithcode": "{statusCode}－{error}", "components.Settings.plex": "Plex", "components.Discover.StudioSlider.studios": "制作会社", "components.Discover.NetworkSlider.networks": "テレビ局", "components.Settings.enablessl": "SSL を有効にする", "components.PersonDetails.lifespan": "{birthdate}－{deathdate}", "components.PersonDetails.birthdate": "{birthdate}－", "components.PersonDetails.alsoknownas": "別の呼び方：{names}", "i18n.delimitedlist": "{a}、{b}", "i18n.movie": "映画", "components.RequestModal.QuotaDisplay.movie": "映画", "i18n.request": "リクエスト", "components.PermissionEdit.request": "リクエスト", "i18n.request4k": "4K をリクエスト", "i18n.saving": "保存中…", "i18n.save": "変更を保存", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "ウェブフック URL", "i18n.next": "次", "i18n.all": "すべて", "i18n.loading": "ロード中…", "i18n.testing": "テスト中…", "i18n.test": "テストする", "i18n.status": "状態", "components.TvDetails.originaltitle": "原題", "components.MovieDetails.originaltitle": "原題", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram のテスト通知が送信されました。", "components.Settings.Notifications.toastEmailTestSuccess": "メールテスト通知が送信されました。", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord のテスト通知が送信されました。", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea のテスト通知が送信されました。", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet のテスト通知が送信されました。", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover のテスト通知が送信されました。", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack のテスト通知が送信されました。", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Telegram の通知設定の保存に失敗しました。", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "メール通知設定の保存に失敗しました。", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Discord の通知設定の保存に失敗しました。", "components.Settings.Notifications.telegramsettingsfailed": "Telegram の通知設定の保存に失敗しました。", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "LunaSea の通知設定の保存に失敗しました。", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Pushbullet の通知設定の保存に失敗しました。", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover の通知設定の保存に失敗しました。", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram の通知設定が保存されました。", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "メール通知設定が保存されました。", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord の通知設定が保存されました。", "components.Settings.Notifications.telegramsettingssaved": "Telegram の通知設定が保存されました。", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea の通知設定が保存されました。", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet の通知設定が保存されました。", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover の通知設定が保存されました。", "components.ResetPassword.validationpasswordrequired": "パスワードを入力してください", "components.Layout.VersionStatus.commitsbehind": "", "components.Layout.LanguagePicker.displaylanguage": "表示言語", "components.LanguageSelector.originalLanguageDefault": "すべての言語", "components.LanguageSelector.languageServerDefault": "デフォルト言語（{language}）", "components.DownloadBlock.estimatedtime": "所要時間：{time}", "components.Discover.upcomingtv": "今後リリースされるシリーズ", "components.Discover.TvGenreSlider.tvgenres": "シリーズのジャンル", "components.Discover.DiscoverTvGenre.genreSeries": "{genre}シリーズ", "components.Discover.DiscoverTvLanguage.languageSeries": "{language}のシリーズ", "components.Discover.TvGenreList.seriesgenres": "シリーズのジャンル", "components.Discover.MovieGenreSlider.moviegenres": "映画のジャンル", "components.Discover.MovieGenreList.moviegenres": "映画のジャンル", "components.Discover.DiscoverStudio.studioMovies": "{studio}の映画", "components.Discover.DiscoverNetwork.networkSeries": "{network}シーリーズ", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language}の映画", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre}映画", "components.RequestModal.pending4krequest": "", "components.RequestModal.errorediting": "リクエストを編集するときに問題が発生しました。", "components.RequestModal.edit": "リクエストを編集", "components.RequestModal.autoapproval": "自動承認", "components.RequestModal.alreadyrequested": "すでにリクエスト済み", "components.RequestModal.SearchByNameModal.notvdbiddescription": "リクエストは自動的にマッチできませんでした。以下のリストから正しいマッチングを選択してください。", "components.RequestModal.QuotaDisplay.seasonlimit": "シーズン", "components.RequestModal.QuotaDisplay.season": "シーズン", "components.RequestModal.QuotaDisplay.requiredquotaUser": "このユーザーはこのシーズンをリクエストするには、最低でも <strong>{seasons}</strong> シーズンリクエストが残っている必要があります。", "components.RequestModal.QuotaDisplay.requiredquota": "このシーズンをリクエストするには、最低でも <strong>{seasons}</strong> シーズンリクエストが残っている必要があります。", "components.RequestModal.QuotaDisplay.requestsremaining": "残り <strong>{remaining}</strong> {type}リクエスト", "components.RequestModal.QuotaDisplay.quotaLinkUser": "このユーザーのリクエスト制限は<ProfileLink>プロフィールページ</ProfileLink>で確認できます。", "components.RequestModal.QuotaDisplay.quotaLink": "リクエスト制限は<ProfileLink>プロフィールページ</ProfileLink>で確認できます。", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "シーズンリクエストが足りません。", "components.RequestModal.QuotaDisplay.movielimit": "映画", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "このユーザーは <strong>{days}</strong> 日ごとに <strong>{limit}</strong> {type}をリクエストできます。", "components.RequestModal.QuotaDisplay.allowedRequests": "<strong>{days}</strong> 日ごとに <strong>{limit}</strong> {type}をリクエストできます。", "components.RequestModal.AdvancedRequester.tags": "タグ", "components.RequestModal.AdvancedRequester.selecttags": "タグの選択", "components.RequestModal.AdvancedRequester.rootfolder": "ルートフォルダ", "components.RequestModal.AdvancedRequester.requestas": "別ユーザーとしてリクエスト", "components.RequestModal.AdvancedRequester.qualityprofile": "画質プロファイル", "components.RequestModal.AdvancedRequester.notagoptions": "タグなし。", "components.RequestModal.AdvancedRequester.languageprofile": "言語プロフィール", "components.RequestModal.AdvancedRequester.folder": "{path}（{space}）", "components.RequestModal.AdvancedRequester.destinationserver": "送信先サーバー", "components.RequestModal.AdvancedRequester.default": "{name}（デフォルト）", "components.RequestModal.AdvancedRequester.animenote": "* このシリーズはアニメです。", "components.RequestModal.AdvancedRequester.advancedoptions": "詳細オプション", "components.RequestButton.viewrequest4k": "4Kリクエストを表示", "components.RequestButton.viewrequest": "リクエストを表示", "components.PermissionEdit.viewrequestsDescription": "他のユーザーのリクエストを表示する権限を付与する。", "components.RequestList.sortModified": "最終更新日", "components.RequestList.sortAdded": "最新リクエスト", "components.RequestList.showallrequests": "すべてのリクエストを表示", "components.RequestList.RequestItem.requesteddate": "リクエストユーザー", "components.RequestList.RequestItem.requested": "リクエスト", "components.RequestList.RequestItem.modifieduserdate": "{user}（{date}）", "components.RequestList.RequestItem.modified": "最終更新者", "components.RequestList.RequestItem.mediaerror": "このリクエストのタイトルはもうありません。", "components.RequestList.RequestItem.failedretry": "リクエストを再試行するときに問題が発生しました。", "components.RequestList.RequestItem.editrequest": "リクエストを編集", "components.RequestList.RequestItem.deleterequest": "リクエストを削除", "components.RequestList.RequestItem.cancelRequest": "リクエストをキャンセル", "components.RequestCard.mediaerror": "このリクエストのタイトルはもうありません。", "components.RequestCard.failedretry": "リクエストを再試行するときに問題が発生しました。", "components.RequestCard.deleterequest": "リクエストを削除", "components.RequestButton.requestmore4k": "もっと4Kリクエストする", "components.RequestButton.requestmore": "もっとリクエストする", "components.RequestButton.declinerequests": "{requestCount} つのリクエストを拒否", "components.RequestButton.declinerequest4k": "4Kリクエストを拒否", "components.RequestButton.declinerequest": "リクエストを拒否", "components.RequestButton.decline4krequests": "{requestCount} つの 4K リクエストを拒否", "components.RequestButton.approverequests": "{requestCount} つのリクエストを承認", "components.RequestButton.approverequest4k": "4Kリクエストを承認", "components.RequestButton.approverequest": "リクエストを承認", "components.RequestButton.approve4krequests": "{requestCount} つ 4K リクエストを承認", "components.RequestBlock.server": "送信先サーバー", "components.RequestBlock.rootfolder": "ルートフォルダ", "components.RequestBlock.requestoverrides": "リクエストのオーバーライド", "components.RequestBlock.profilechanged": "画質プロファイル", "components.RegionSelector.regionServerDefault": "デフォルト（{region}）", "components.RegionSelector.regionDefault": "全地域", "components.QuotaSelector.unlimited": "無制限", "components.QuotaSelector.tvRequests": "<quotaUnits>{quotaDays} {days}に </quotaUnits>{quotaLimit} {seasons}", "components.QuotaSelector.seasons": "{count, plural, one {シーズン} other {シーズン}}", "components.QuotaSelector.movies": "映画", "components.QuotaSelector.movieRequests": "<quotaUnits>{quotaDays} {days}に </quotaUnits>{quotaLimit} {movies}", "components.QuotaSelector.days": "日", "components.PermissionEdit.viewrequests": "リクエストを見る", "components.PermissionEdit.usersDescription": "ユーザーを管理する権限を付与する。この権限を持つユーザーは、Admin 権限を持つユーザーの変更や、Admin 権限を付与することはできません。", "components.NotificationTypeSelector.mediarequestedDescription": "ユーザーが承認を必要とする新メディアリクエストをすると通知する。", "components.PermissionEdit.users": "ユーザー管理", "components.PermissionEdit.requestTvDescription": "4K 以外のシリーズをリクエストする権限を与える。", "components.PermissionEdit.requestTv": "シリーズをリクエスト", "components.PermissionEdit.requestMoviesDescription": "4K 以外の映画をリクエストする権限を与える。", "components.PermissionEdit.requestMovies": "映画をリクエスト", "components.PermissionEdit.requestDescription": "4K 以外のメディアをリクエストする権限を与える。", "components.PermissionEdit.request4kTvDescription": "4K シリーズをリクエストする権限を与える。", "components.PermissionEdit.request4kTv": "4K シリーズをリクエスト", "components.PermissionEdit.request4kDescription": "4K メディアをリクエストする権限を与える。", "components.PermissionEdit.request4kMoviesDescription": "4K 映画をリクエストする権限を与える。", "components.PermissionEdit.request4kMovies": "4K映画をリクエスト", "components.PermissionEdit.managerequestsDescription": "リクエストを管理する権限を付与する。この権限を持つユーザーのリクエストは、すべて自動的に承認されます。", "components.PermissionEdit.managerequests": "リクエストを管理", "components.MovieDetails.markavailable": "視聴可能をマーク", "components.MovieDetails.mark4kavailable": "4K視聴可能をマーク", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr安定版", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> 開発版", "components.Layout.VersionStatus.outofdate": "期限切れ", "components.PermissionEdit.autoapprove4kSeriesDescription": "4K シリーズのリクエストを自動的に承認する。", "components.PermissionEdit.autoapprove4kMoviesDescription": "4K映画のリクエストを自動的に承認する。", "components.PermissionEdit.autoapproveSeriesDescription": "4K 以外のシリーズのリクエストを自動的に承認する。", "components.PermissionEdit.autoapproveSeries": "シリーズを自動的に承認する", "components.PermissionEdit.autoapproveMoviesDescription": "4K以外の映画のリクエストを自動的に承認する。", "components.PermissionEdit.autoapproveMovies": "映画を自動的に承認する", "components.PermissionEdit.autoapproveDescription": "4K以外のリクエストをすべて自動的に承認する。", "components.PermissionEdit.autoapprove4kSeries": "4K シリーズを自動的に承認する", "components.PermissionEdit.autoapprove4kMovies": "4K映画の自動的に承認する", "components.PermissionEdit.autoapprove4kDescription": "すべての4Kリクエストを自動的に承認する。", "components.PermissionEdit.autoapprove4k": "4Kの自動承認", "components.PermissionEdit.autoapprove": "自動承認", "components.PermissionEdit.advancedrequestDescription": "高度なメディアリクエスト設定を変更する権限を付与する", "components.PermissionEdit.advancedrequest": "アドバンスド・リクエスト", "components.PermissionEdit.adminDescription": "管理者のフルアクセス。すべての権限チェックをバイパスします。", "components.PermissionEdit.admin": "管理者", "components.NotificationTypeSelector.usermediarequestedDescription": "他のユーザーが承認を必要とする新メディアリクエストをすると通知する。", "components.NotificationTypeSelector.usermediafailedDescription": "RadarrまたはSonarrへのメディアリクエストの追加に失敗したときに通知をする。", "components.NotificationTypeSelector.usermediadeclinedDescription": "メディアリクエストが拒否されたら通知する。", "components.NotificationTypeSelector.usermediaavailableDescription": "メディアリクエストが視聴可能になると通知する。", "components.NotificationTypeSelector.usermediaapprovedDescription": "メディアリクエストが承認されると通知する。", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "他のユーザーの新メディアリクエストが自動的に承認されると通知する。", "components.NotificationTypeSelector.notificationTypes": "通知タイプ", "components.NotificationTypeSelector.mediarequested": "リクエストは未承認", "components.NotificationTypeSelector.mediafailedDescription": "RadarrまたはSonarrへのメディアリクエストの追加に失敗したときに通知をする。", "components.NotificationTypeSelector.mediafailed": "リクエストの処理が失敗した", "components.NotificationTypeSelector.mediadeclinedDescription": "メディアリクエストが拒否されると通知する。", "components.NotificationTypeSelector.mediadeclined": "リクエストは拒否された", "components.NotificationTypeSelector.mediaavailableDescription": "メディアリクエストが視聴可能になりますと通知する。", "components.NotificationTypeSelector.mediaavailable": "リクエスト視聴可能", "components.NotificationTypeSelector.mediaapprovedDescription": "ユーザーのメディアリクエストが手動に承認されると通知する。", "components.NotificationTypeSelector.mediaapproved": "リクエストが承認された", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "ユーザーの新メディアリクエストが自動的に承認されると通知する。", "components.NotificationTypeSelector.mediaAutoApproved": "リクエストを自動的に承認", "components.MovieDetails.showmore": "もっと表示", "components.MovieDetails.showless": "少なく表示", "components.RequestModal.pendingapproval": "リクエストは承認待ちです。", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "デフォルト言語（{language}）", "components.TvDetails.episodeRuntimeMinutes": "{runtime} 分", "components.Settings.RadarrModal.loadingTags": "タグ読込中…", "components.Settings.RadarrModal.notagoptions": "タグなし。", "components.Settings.SonarrModal.animeTags": "アニメタグ", "components.Settings.SonarrModal.animelanguageprofile": "アニメ言語プロフィール", "components.Settings.SonarrModal.notagoptions": "タグなし。", "components.Settings.SonarrModal.selectLanguageProfile": "言語プロフィールを選ぶ", "components.Settings.RadarrModal.selecttags": "タグを選ぶ", "components.Settings.SonarrModal.loadingTags": "タグ読込中…", "components.Settings.SonarrModal.languageprofile": "言語プロフィール", "components.Settings.SonarrModal.loadinglanguageprofiles": "言語プロフィール読込中…", "components.Settings.SonarrModal.validationLanguageProfileRequired": "言語プロフィールを選択してください", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "表示言語", "components.StatusBadge.status": "{status}", "components.ManageSlideOver.downloadstatus": "ダウンロード", "components.ManageSlideOver.manageModalClearMedia": "データを消去", "components.ManageSlideOver.manageModalRequests": "リクエスト", "components.ManageSlideOver.openarr": "{arr} を開く", "components.ManageSlideOver.manageModalClearMediaWarning": "※リクエストを含め、すべての詳細情報が消去されます。この操作は元に戻すことができません。この作品が {mediaServerName} ライブラリに存在する場合、詳細情報は次のスキャンで再作成されます。", "components.ManageSlideOver.openarr4k": "4K {arr} を開く", "components.ManageSlideOver.manageModalNoRequests": "リクエストが有りません。", "components.ManageSlideOver.manageModalTitle": "{mediaType}を管理", "components.ManageSlideOver.movie": "映画", "components.ManageSlideOver.tvshow": "シリーズ", "components.IssueDetails.IssueDescription.edit": "チケット内容を編集", "components.IssueDetails.allepisodes": "全エピソード", "components.IssueDetails.allseasons": "全シーリーズ", "components.IssueDetails.closeissue": "チケットをクローズ", "components.IssueDetails.deleteissue": "チケットの削除", "components.IssueDetails.commentplaceholder": "コメントを追加", "components.IssueDetails.deleteissueconfirm": "こちらのチケットを削除しますか？", "components.IssueDetails.openinarr": "{arr} を開く", "components.IssueDetails.season": "シーズン {seasonNumber}", "components.IssueDetails.toaststatusupdated": "課題のステータスが正常に更新されました！", "components.IssueDetails.toaststatusupdatefailed": "チケットステータスを更新する際に、問題が発生しました。", "components.IssueDetails.unknownissuetype": "不明", "components.IssueDetails.IssueComment.validationComment": "メッセージの入力が必要です", "components.IssueDetails.IssueComment.areyousuredelete": "こちらのコメントを削除しますか？", "components.IssueDetails.IssueComment.delete": "コメントを削除", "components.IssueDetails.IssueComment.edit": "コメントを編集", "components.IssueDetails.IssueComment.postedbyedited": "{username}による{relativeTime}に投稿（編集済み）", "components.IssueDetails.IssueComment.postedby": "{username}による{relativeTime}に投稿", "components.IssueDetails.nocomments": "コメントはありません。", "components.IssueDetails.reopenissue": "チケットを再度開く", "components.IssueDetails.problemseason": "該当シーリーズ", "components.IssueDetails.IssueDescription.deleteissue": "チケットを削除", "components.IssueDetails.IssueDescription.description": "チケット内容", "components.IssueDetails.comments": "コメント一覧", "components.IssueDetails.issuetype": "チケット種類", "components.IssueDetails.lastupdated": "最終更新日", "components.IssueDetails.leavecomment": "コメント", "components.IssueDetails.openin4karr": "4K {arr} を開く", "components.IssueDetails.play4konplex": "Plexで4K再生", "components.IssueDetails.reopenissueandcomment": "コメント追加して再度開く", "components.IssueDetails.playonplex": "Plexで再生する", "components.IssueDetails.problemepisode": "該当エピソード", "components.IssueDetails.closeissueandcomment": "コメント追加してクローズ", "components.IssueDetails.episode": "エピソード {episodeNumber}", "components.IssueDetails.issuepagetitle": "チケット", "components.IssueDetails.openedby": "{username}は#{issueId}を{relativeTime}に開きました", "components.IssueDetails.toasteditdescriptionfailed": "チケット内容編集中に問題が発生しました。", "components.IssueDetails.toasteditdescriptionsuccess": "チケット内容の編集に成功しました！", "components.IssueDetails.toastissuedeleted": "チケットの削除に成功しました！", "components.IssueDetails.toastissuedeletefailed": "チケットの削除する時に問題が発生しました。", "components.ManageSlideOver.mark4kavailable": "4Kで視聴可能にする", "components.MovieDetails.streamingproviders": "現在ストリーミング配信可能なプラットフォーム", "components.NotificationTypeSelector.issuecreated": "チケット報告した", "components.RequestModal.requestseasons4k": "4Kで{seasonCount}{seasonCount, plural, one {シーズン} other {シーズン}}をリクエスト", "components.RequestModal.selectmovies": "映画を選択する", "components.ResetPassword.confirmpassword": "パスワードを確認", "components.ResetPassword.passwordreset": "パスワードをリセット", "components.ResetPassword.requestresetlinksuccessmessage": "パスワードリセットリンクは、有効なユーザーの場合のみ、提供されたメールアドレスに送信されます。", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Gotify の通知設定の保存に失敗しました。", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "メールを <OpenPgpLink>OpenPGP</OpenPgpLink> で暗号化する", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {エピソード} other {エピソード}}", "components.IssueList.IssueItem.unknownissuetype": "不明", "components.IssueList.sortAdded": "最新", "components.IssueModal.CreateIssueModal.allseasons": "すべてのシーズン", "components.IssueList.showallissues": "すべてのチケットを表示", "components.IssueModal.CreateIssueModal.episode": "エピソード {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "特典", "components.IssueModal.CreateIssueModal.problemseason": "該当シーズン", "components.IssueModal.CreateIssueModal.providedetail": "チケットの詳細をご記入ください。", "components.IssueModal.CreateIssueModal.submitissue": "報告を送信", "components.IssueModal.CreateIssueModal.toastFailedCreate": "報告送信時に問題が発生した。", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "<strong>{title}</strong> の報告が送信されました！", "components.IssueModal.CreateIssueModal.toastviewissue": "チケットを表示", "components.IssueModal.CreateIssueModal.whatswrong": "どうしましたか？", "components.IssueModal.issueOther": "その他", "components.IssueModal.issueSubtitles": "字幕", "components.IssueModal.issueVideo": "映像", "components.IssueModal.CreateIssueModal.validationMessageRequired": "チケットの詳細を記入する必要があります", "components.Layout.Sidebar.issues": "チケット", "components.ManageSlideOver.manageModalIssues": "未解決チケット", "components.NotificationTypeSelector.issuecomment": "チケットのコメント", "components.NotificationTypeSelector.userissueresolvedDescription": "自分が報告したチケットが解決された際に通知を受ける", "components.PermissionEdit.viewissuesDescription": "他ユーザーが作成されたチケットを確認できる", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "エージェントを有効にする", "components.TvDetails.seasons": "{seasonCount, plural, one {#シーズン} other {#シーズン}}", "components.Search.search": "検索", "components.ManageSlideOver.opentautulli": "Tautulliで開く", "components.ManageSlideOver.alltime": "現在まで", "components.ManageSlideOver.manageModalAdvanced": "高度設定", "components.ManageSlideOver.manageModalMedia": "メディア", "components.ManageSlideOver.pastdays": "過去{days, number}日", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong>{playCount, plural, one {回} other {回}}", "components.ManageSlideOver.markallseasons4kavailable": "すべてのシーズンを4Kで視聴可能にする", "components.NotificationTypeSelector.issuecommentDescription": "チケットにコメントが追加される際に通知を受ける", "components.NotificationTypeSelector.issuereopened": "チケットを再度開いた", "components.NotificationTypeSelector.userissuecreatedDescription": "他ユーザーがチケットを報告する際に通知を受ける", "components.NotificationTypeSelector.userissuereopenedDescription": "自ら報告したチケットが再度開いた際に通知を受ける", "components.PermissionEdit.manageissuesDescription": "メディアのチケットを管理する権限を付与する", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL の末尾にスラッシュ（ / ）を入力してないでください", "components.Settings.Notifications.NotificationsLunaSea.profileName": "プロフィール名", "components.IssueList.sortModified": "最終更新", "components.IssueModal.CreateIssueModal.allepisodes": "すべてのエピソード", "components.IssueModal.CreateIssueModal.problemepisode": "該当エピソード", "components.NotificationTypeSelector.issueresolved": "チケット解決済み", "components.NotificationTypeSelector.issueresolvedDescription": "チケットが解決される際に通知を受ける", "components.NotificationTypeSelector.userissuecommentDescription": "自分が報告したチケットにコメントが追加した際に通知を受ける", "components.PermissionEdit.createissues": "チケットを報告", "components.PermissionEdit.manageissues": "チケットを管理", "components.PermissionEdit.viewissues": "チケットを表示", "components.RequestModal.approve": "リクエストを承認", "i18n.open": "未解決", "components.RequestBlock.languageprofile": "言語プロフィール", "components.RequestModal.requestApproved": "<strong>{title}</strong> のリクエストは承認されました!", "components.RequestModal.requestcancelled": "<strong>{title}</strong> のリクエストはキャンセルされました。", "components.RequestModal.requestmovies": "{count}{count, plural, one {映画} other {映画}}をリクエスト", "components.RequestModal.requestmovies4k": "{count}{count, plural, one {映画} other {映画}}を4Kでリクエスト", "components.ResetPassword.emailresetlink": "パスワードリセットリンクを送信", "components.ResetPassword.gobacklogin": "ログインページへ戻る", "components.ResetPassword.resetpassword": "パスワードをリセットする", "components.ResetPassword.resetpasswordsuccessmessage": "パスワードリセットが成功しました！", "components.ResetPassword.validationpasswordmatch": "パスワードは一致する必要があります", "components.ResetPassword.validationpasswordminchars": "8文字以上のパスワードを入力してください", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify の通知設定が保存されました！", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify のテスト通知を送信中…", "components.Settings.Notifications.NotificationsGotify.validationTypes": "通知の種類は一つ以上を選択してください", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "有効な URL を入力してください", "components.Settings.Notifications.pgpPasswordTip": "<OpenPgpLink>OpenPGP</OpenPgpLink> を利用してメールを送信", "components.IssueList.IssueItem.issuestatus": "ステータス", "components.IssueList.IssueItem.opened": "報告者/報告日時：", "components.IssueList.IssueItem.openeduserdate": "{user}・{date}", "components.IssueList.IssueItem.problemepisode": "該当エピソード", "components.IssueList.IssueItem.issuetype": "種類", "components.IssueModal.issueAudio": "オーディオ", "components.IssueModal.CreateIssueModal.reportissue": "チケットを報告", "components.IssueModal.CreateIssueModal.season": "シーズン {seasonNumber}", "components.ManageSlideOver.markallseasonsavailable": "すべてのシーズンを視聴可能にする", "components.IssueList.issues": "チケット", "components.IssueList.IssueItem.viewissue": "チケットを閲覧", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {シーズン} other {シーズン}}", "components.ManageSlideOver.playedby": "再生者", "components.Settings.Notifications.pgpPrivateKeyTip": "<OpenPgpLink>OpenPGP</OpenPgpLink> を利用して暗号化されたメールを署名", "components.ManageSlideOver.manageModalMedia4k": "4Kメディア", "components.MovieDetails.productioncountries": "制作{countryCount, plural, one {国} other {国}}", "components.NotificationTypeSelector.issuecreatedDescription": "チケットが報告される際に通知を送信", "components.PermissionEdit.createissuesDescription": "チケットを報告できる権限を付与する", "components.NotificationTypeSelector.adminissuecommentDescription": "他のユーザーがチケットにコメントした際に通知を受ける", "components.NotificationTypeSelector.adminissuereopenedDescription": "他ユーザーがチケットを報告する際に通知を受ける", "components.NotificationTypeSelector.adminissueresolvedDescription": "他ユーザーがチケットを解決した際に通知を受ける", "components.NotificationTypeSelector.issuereopenedDescription": "チケットが再度開く際に通知を受ける", "components.ManageSlideOver.markavailable": "視聴可能にする", "components.RequestModal.requestedited": "<strong>{title}</strong>のリクエストは編集に成功しました!", "components.RequestModal.requesterror": "リクエストの送信中に問題が発生しました。", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "アプリケーショントークンを入力してください", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Gotify のテスト通知の送信に失敗しました。", "components.Settings.Notifications.NotificationsGotify.agentenabled": "エージェントを有効にする", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify のテスト通知を送信しました！", "components.Settings.Notifications.NotificationsGotify.token": "アプリケーショントークン", "components.Settings.Notifications.NotificationsGotify.url": "サーバーの URL", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "通知の種類は一つ以上を選択してください", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "ユーザーまたはデバイスベースの <LunaSeaLink>notification webhook URL</LunaSeaLink> を指定します。", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "有効な URL を入力してください", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "<code>default</code> プロファイルを使用しない場合のみ必要です。", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea テスト通知送信中…", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "ウェブフック URL", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "LunaSea のテスト通知の送信に失敗しました。", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "有効な URL を入力してください", "components.Settings.RadarrModal.validationApplicationUrl": "有効な URL を入力してください", "components.Settings.Notifications.validationUrl": "有効な URL を入力してください", "components.Settings.SonarrModal.validationApplicationUrl": "有効な URL を入力してください", "components.Settings.urlBase": "URL のベース", "components.Settings.validationUrl": "有効な URL を入力してください", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "有効な URL を入力してください"}