{"components.Settings.RadarrModal.testFirstRootFolders": "Testar ligação para carregar as pastas raiz", "components.Settings.RadarrModal.testFirstQualityProfiles": "Testar ligação para carregar perfis de qualidade", "components.Settings.RadarrModal.ssl": "Usar SSL", "components.Settings.SonarrModal.servername": "Nome do Servidor", "components.Settings.SonarrModal.server4k": "Servidor 4K", "components.Settings.RadarrModal.server4k": "Servidor 4K", "components.Settings.SonarrModal.selectRootFolder": "Selecione a pasta raiz", "components.Settings.RadarrModal.selectRootFolder": "Selecione a pasta raiz", "components.Settings.SonarrModal.selectQualityProfile": "Selecione o perfil de qualidade", "components.Settings.RadarrModal.selectQualityProfile": "Selecione o perfil de qualidade", "components.Settings.RadarrModal.selectMinimumAvailability": "Selecione disponibilidade mínima", "components.Settings.SonarrModal.rootfolder": "Pasta Raiz", "components.Settings.RadarrModal.rootfolder": "Pasta Raiz", "components.Settings.RadarrModal.qualityprofile": "Perfil de Qualidade", "components.Settings.RadarrModal.port": "Porta", "components.Settings.RadarrModal.minimumAvailability": "Disponibilidade Mínima", "components.Settings.RadarrModal.loadingrootfolders": "A carregar Pastas Raiz…", "components.Settings.RadarrModal.loadingprofiles": "A carregar Perfis de Qualidade…", "components.Settings.RadarrModal.hostname": "Nome do hospedeiro ou endereço IP", "components.Settings.RadarrModal.servername": "Nome do Servidor", "components.Settings.RadarrModal.editradarr": "Modificar Servidor Radarr", "components.Settings.RadarrModal.defaultserver": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.createradarr": "Adicionar Novo Servidor Radarr", "components.Settings.RadarrModal.baseUrl": "Base do URL", "components.Settings.RadarrModal.apiKey": "Chave API", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.validationSmtpPortRequired": "Deve fornecer um número de porta válido", "components.Settings.Notifications.validationSmtpHostRequired": "Deve fornecer um nome de hospedeiro ou endereço IP válido", "components.Settings.Notifications.validationChatIdRequired": "<PERSON>e fornecer um ID de chat válido", "components.Settings.Notifications.validationBotAPIRequired": "Deve fornecer um token de autorização de bot", "components.Settings.Notifications.telegramsettingssaved": "Definições de notificação Telegram gravadas com sucesso!", "components.Settings.Notifications.telegramsettingsfailed": "Falha ao gravar as definições de notificação Telegram.", "components.Settings.Notifications.smtpPort": "Porta SMTP", "components.Settings.Notifications.smtpHost": "Servidor SMTP", "components.Settings.Notifications.senderName": "Nome do Remetente", "components.Settings.Notifications.emailsettingssaved": "Definições de notificação e-mail gravadas com sucesso!", "components.Settings.Notifications.discordsettingssaved": "Definições de notificação Discord gravadas com sucesso!", "components.Settings.Notifications.discordsettingsfailed": "Falha ao gravar das definições de notificação Discord.", "components.Settings.Notifications.emailsettingsfailed": "Falha ao gravar das definições de notificação e-mail.", "components.Settings.Notifications.emailsender": "Endereço do remetente", "components.Settings.Notifications.chatId": "ID do Chat", "components.Settings.Notifications.botAPI": "Token de Autorização do Bot", "components.Settings.Notifications.authUser": "Utilizador SMTP", "components.Settings.Notifications.authPass": "Palavra-passe SMTP", "components.Settings.Notifications.allowselfsigned": "Permitir Certificados Auto-Assinados", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Definições de notificação Webhook gravadas com sucesso!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Falha ao gravar as definições de notificação Webhook.", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Deve fornecer um conteúdo JSON valido", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Ajuda Com Modelos de Variáveis", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Conteúdo JSON reiniciado com sucesso!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Restaurar Predefini<PERSON>", "components.Settings.Notifications.NotificationsWebhook.customJson": "<PERSON><PERSON><PERSON>do <PERSON>", "components.Settings.Notifications.NotificationsWebhook.authheader": "Cabeçalho de Autorização", "components.Settings.Notifications.agentenabled": "Ativar Agente", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Ativar Agente", "components.Settings.Notifications.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Definições de notificação Slack gravadas com sucesso!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Falha ao gravas as definições de notificação do Slack.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Ativar Agente", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Tem de fornecer um utilizador válido ou uma chave de grupo", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Deve fornecer um token de aplicação válido", "components.Settings.Notifications.NotificationsPushover.userToken": "Chave de Utilizador ou Grupo", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Definições de notificação Pushover gravadas com sucesso!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "<PERSON>al<PERSON> ao gravar as definições de notificação Pushover.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Ativar Agente", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token de Aplicação API", "components.Search.searchresults": "Resultados da Pesquisa", "components.RequestModal.selectseason": "Selecionar Semporada(s)", "components.RequestModal.seasonnumber": "Temporada {number}", "components.RequestModal.season": "Temporada", "components.RequestModal.requestseasons": "Pedir {seasonCount} {seasonCount, plural, one {Temporada} other {Temporadas}}", "components.RequestModal.requestfrom": "O peido de {username} está com aprovação pendente.", "components.RequestModal.requestedited": "Pedido para <strong>{title}</strong> modificado com sucesso!", "components.RequestModal.requestcancelled": "Pedido para <strong>{title}</strong> cancelado.", "components.RequestModal.requestadmin": "Este pedido será aprovado automaticamente.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> pedido com sucesso!", "components.RequestModal.requestCancel": "Pedido para <strong>{title}</strong> cancelado.", "components.RequestModal.pendingrequest": "Solicitação Pendente", "components.RequestModal.pending4krequest": "Solicitação em 4K Pendente", "components.RequestModal.numberofepisodes": "# de Episódios", "components.RequestModal.errorediting": "Ocorreu um erro durante a edição do pedido.", "components.RequestModal.cancel": "<PERSON><PERSON>ar <PERSON>", "components.RequestModal.autoapproval": "Aprovação Automática", "components.RequestModal.AdvancedRequester.rootfolder": "Pasta Raiz", "components.RequestModal.AdvancedRequester.qualityprofile": "Perfil de Qualidade", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (Predefinição)", "components.RequestModal.AdvancedRequester.animenote": "* Esta série é um anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "Avançado", "components.RequestList.sortModified": "Última Alteração", "components.RequestList.sortAdded": "Data do Pedido", "components.RequestList.showallrequests": "Mostrar Todos os Pedidos", "components.RequestList.requests": "Pedidos", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestList.RequestItem.failedretry": "Ocorreu um erro ao voltar a tentar o pedido.", "components.RequestCard.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestButton.viewrequest4k": "Ver Pedido 4K", "components.RequestButton.viewrequest": "Ver Pedido", "components.RequestButton.requestmore4k": "Pedir <PERSON> em 4K", "components.RequestButton.requestmore": "<PERSON><PERSON><PERSON>", "components.RequestButton.declinerequests": "Rejeitar {requestCount, plural, one {Solicitação} other {{requestCount} Solicitações}}", "components.RequestButton.declinerequest4k": "Rejeitar Pedido 4K", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.decline4krequests": "Rejeitar {requestCount, plural, one {Solicitação 4K} other {{requestCount} Solicitações 4K}}", "components.RequestButton.approverequests": "Aprovar {requestCount, plural, one {Solicitação} other {{requestCount} Solicitações}}", "components.RequestButton.approverequest4k": "Aprovar Pedido 4K", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.approve4krequests": "Aprovar {requestCount, plural, one {Solicitação 4K} other {{requestCount} Solicitações 4K}}", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestBlock.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestBlock.rootfolder": "Pasta Raiz", "components.RequestBlock.requestoverrides": "Alterações de Pedidos", "components.RequestBlock.profilechanged": "Perfil de Qualidade", "components.PersonDetails.crewmember": "Equipa Técnica", "components.PersonDetails.ascharacter": "como {character}", "components.PersonDetails.appearsin": "Presenças", "components.NotificationTypeSelector.mediafailedDescription": "Enviar notificações quando os pedidos de multimédia não forem adicionados ao Radarr ou Sonarr.", "components.NotificationTypeSelector.mediarequestedDescription": "Enviar notificações quando os utilizadores enviam novos pedidos de multimédia que requerem aprovação.", "components.NotificationTypeSelector.mediarequested": "Multimédia Pedida", "components.NotificationTypeSelector.mediafailed": "Multimédia Falhou", "components.NotificationTypeSelector.mediadeclinedDescription": "Envia uma notificação quando um pedido de multimédia é rejeitado.", "components.NotificationTypeSelector.mediadeclined": "Multimédia Rejeitada", "components.NotificationTypeSelector.mediaavailableDescription": "Enviar notificações quando os pedidos da multimédia ficarem disponíveis.", "components.NotificationTypeSelector.mediaavailable": "Multimédia Disponível", "components.NotificationTypeSelector.mediaapprovedDescription": "Enviar notificações quando os pedidos de multimédia são aprovados manualmente.", "components.NotificationTypeSelector.mediaapproved": "Multimédia Aprovada", "components.MovieDetails.watchtrailer": "<PERSON>er Trailer", "components.MovieDetails.viewfullcrew": "Ver Equipa Técnica Completa", "components.MovieDetails.studio": "{studioCount, plural, one {Estúdio} other {Estúdios}}", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.runtime": "{minutes} minutos", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Data} other {Datas}} de Estreia", "components.MovieDetails.recommendations": "Recomendações", "components.MovieDetails.overviewunavailable": "Sinopse indisponível.", "components.MovieDetails.overview": "Sinopse", "components.MovieDetails.originallanguage": "Idioma Original", "components.MovieDetails.cast": "Elenco", "components.MovieDetails.budget": "Orçamento", "components.MovieDetails.MovieCrew.fullcrew": "Equipa Técnica Completa", "components.MovieDetails.MovieCast.fullcast": "Elenco Completo", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON>", "components.Login.validationpasswordrequired": "Deve fornecer uma palavra-passe", "components.Login.validationemailrequired": "Deve fornecer um e-mail válido", "components.Login.signinwithoverseerr": "Utilizar a sua conta {applicationTitle}", "components.Login.loginerror": "Ocorreu um erro ao tentar iniciar a sessão.", "components.Login.password": "Palavra-passe", "components.Login.email": "Endereço E-mail", "components.Layout.UserDropdown.signout": "<PERSON><PERSON>", "components.Layout.Sidebar.users": "Utilizadores", "components.Layout.Sidebar.settings": "Definições", "components.Layout.Sidebar.requests": "Pedidos", "components.Layout.Sidebar.dashboard": "Descobrir", "components.Layout.SearchInput.searchPlaceholder": "Pesquisar Filmes e Séries", "components.Discover.upcomingmovies": "Filmes a Estrear", "components.Discover.upcoming": "Filmes a Estrear", "components.Discover.trending": "Tendências", "components.Discover.recentrequests": "Pedidos <PERSON>", "components.Discover.recentlyAdded": "Adicionado <PERSON>", "components.Discover.populartv": "Séries Populares", "components.Discover.popularmovies": "Filmes Populares", "components.CollectionDetails.requestcollection": "Pedir <PERSON>", "components.CollectionDetails.overview": "Sinopse", "components.CollectionDetails.numberofmovies": "{count} Filmes", "pages.returnHome": "Voltar Para Página Inicial", "pages.oops": "Oops", "i18n.tvshows": "Séries", "i18n.retry": "Tentar Novamente", "i18n.requested": "Pedido", "i18n.processing": "A Processar", "i18n.partiallyavailable": "Parcialmente Disponível", "i18n.movies": "Filmes", "i18n.failed": "<PERSON><PERSON><PERSON>", "i18n.experimental": "Experimental", "i18n.deleting": "A eliminar…", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.close": "<PERSON><PERSON><PERSON>", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.approved": "Aprovada", "i18n.approve": "<PERSON><PERSON><PERSON>", "components.UserList.validationpasswordminchars": "Palavra-passe muito curta; necessário ter no mínimo 8 caracteres", "components.UserList.userlist": "Lista de Utilizadores", "components.UserList.userdeleteerror": "Ocorreu um erro ao eliminar o utilizador.", "components.UserList.userdeleted": "Utilizador eliminado com sucesso!", "components.UserList.usercreatedsuccess": "Utilizador criado com sucesso!", "components.UserList.usercreatedfailed": "Ocorreu um erro ao criar o utilizador.", "components.UserList.user": "Utilizador", "components.UserList.totalrequests": "Pedidos", "components.UserList.role": "Função", "components.UserList.plexuser": "Utilizador <PERSON>", "components.UserList.passwordinfodescription": "Configurar um URL de aplicação e ativar as notificações por e-mail para permitir a geração automática de palavra-passe.", "components.UserList.localuser": "Utilizador Local", "components.UserList.importfromplexerror": "Ocorreu um erro ao importar utilizadores do Plex.", "components.UserList.importfrommediaserver": "Importar Utilizadores do {mediaServerName}", "components.UserList.importfromplex": "Importar Utilizadores do Plex", "components.UserList.importedfromplex": "{userCount, plural, one {# novo utilizador} other {# novos utilizadores}} importados do Plex com sucesso!", "components.UserList.email": "Endereço de E-mail", "components.UserList.deleteuser": "<PERSON><PERSON><PERSON> Util<PERSON>", "components.UserList.deleteconfirm": "Tem certeza que deseja apagar este utilizador? Todos os seus dados de pedidos serão removidos permanentemente.", "components.UserList.creating": "A criar…", "components.UserList.createlocaluser": "Criar Utilizador Local", "components.Setup.welcome": "Bem-<PERSON><PERSON> ao <PERSON>", "components.Setup.signinMessage": "Comece iniciando sessão com a sua conta Plex", "components.Setup.finishing": "A finalizar…", "components.Setup.finish": "Finalizar Configurações", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configureservices": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.startscan": "Iniciar <PERSON>", "components.Settings.sonarrsettings": "Definições do Sonarr", "components.Settings.radarrsettings": "Definições Radarr", "components.Settings.port": "Porta", "components.Settings.plexsettingsDescription": "Configure as definições para o seu servidor Plex. <PERSON><PERSON><PERSON><PERSON> sin<PERSON><PERSON> as suas bibliotecas Plex para determinar a disponibilidade de conteúdo.", "components.Settings.plexlibraries": "Bibliotecas do Plex", "components.Settings.plexsettings": "Definições do Plex", "components.Settings.plexlibrariesDescription": "Bibliotecas que Jellyseerr a sincronizar por títulos. Configure e grave as informações de ligação com Plex e clique no botão abaixo se nenhuma biblioteca estiver listada.", "components.Settings.notrunning": "Parado", "components.Settings.notificationsettings": "Definições de Notificação", "components.Settings.menuServices": "Serviços", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notificações", "components.Settings.menuLogs": "Registos", "components.Settings.menuJobs": "Tarefas e Cache", "components.Settings.menuAbout": "Sobre", "components.Settings.manualscanDescription": "Normalmente, isto só será executado uma vez a cada 24 horas. Jellyseerr verificará em detalhes items adicionados recentemente ao seu servidor Plex. Se esta é a primeira vez que configura um servidor Plex, é recomendado uma sincronização completa da sua biblioteca!", "components.Settings.manualscan": "Sincronização Manual da Biblioteca", "components.Settings.cancelscan": "<PERSON><PERSON>ar <PERSON>", "components.Settings.librariesRemaining": "Bibliotecas Restantes: {count}", "components.Settings.hostname": "Nome do hospedeiro ou endereço IP", "components.Settings.menuGeneralSettings": "G<PERSON>", "i18n.edit": "Modificar", "components.Settings.deleteserverconfirm": "Tem certeza que deseja eliminar este servidor?", "i18n.delete": "Eliminar", "components.Settings.default4k": "Predefinição 4K", "components.Settings.default": "Predefinição", "components.Settings.currentlibrary": "Biblioteca Atual: {name}", "components.Settings.copied": "Chave API copiada.", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.address": "Endereço", "components.Settings.addradarr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.activeProfile": "Perfil Ativo", "components.Settings.SonarrModal.validationRootFolderRequired": "Deve selecionar uma pasta raiz", "components.Settings.SonarrModal.validationProfileRequired": "Deve selecionar um perfil de qualidade", "components.Settings.SonarrModal.validationNameRequired": "<PERSON>e fornecer o nome do servidor", "components.Settings.RadarrModal.validationHostnameRequired": "Deve fornecer um nome de hospedeiro ou endereço IP", "components.Settings.SonarrModal.validationHostnameRequired": "Deve fornecer um nome de hospedeiro ou endereço IP", "components.Settings.SonarrModal.validationPortRequired": "Deve fornecer um número de porta válido", "components.Settings.validationPortRequired": "Deve fornecer um número de porta válido", "components.Settings.validationHostnameRequired": "Deve fornecer um nome de hospedeiro ou endereço IP válido", "components.Settings.SonarrModal.validationApiKeyRequired": "Deve fornecer uma chave API", "components.Settings.SonarrModal.testFirstRootFolders": "Testar ligação para carregar as pastas raiz", "components.Settings.SonarrModal.testFirstQualityProfiles": "Testar ligação para carregar perfis de qualidade", "components.Settings.ssl": "SSL", "components.Settings.SonarrModal.ssl": "Usar SSL", "components.Settings.SonarrModal.seasonfolders": "Temporadas Em Pastas", "components.Settings.SonarrModal.qualityprofile": "Perfil de Qualidade", "components.Settings.SonarrModal.port": "Porta", "components.Settings.SonarrModal.loadingrootfolders": "A carregar pastas raiz…", "components.Settings.SonarrModal.loadingprofiles": "A carregar Perfis de Qualidade…", "components.Settings.SonarrModal.hostname": "Nome do hospedeiro ou endereço IP", "components.Settings.SonarrModal.editsonarr": "Modificar Ser<PERSON>", "components.Settings.SonarrModal.defaultserver": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.createsonarr": "Adicionar Nov<PERSON> Servid<PERSON>", "components.Settings.SonarrModal.apiKey": "Chave API", "components.Settings.RadarrModal.validationApiKeyRequired": "Deve fornecer uma chave API", "components.Settings.SonarrModal.baseUrl": "Base do URL", "components.Settings.SonarrModal.animerootfolder": "Pasta Raiz de Anime", "components.Settings.SonarrModal.animequalityprofile": "Perfil de Qualidade Para Anime", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "Vers<PERSON>", "components.Settings.SettingsAbout.totalrequests": "Todos os Pedidos", "components.Settings.SettingsAbout.totalmedia": "Total de Multimédia", "components.Settings.SettingsAbout.timezone": "<PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON> o <PERSON>", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Ajudar a Pagar o Café", "components.Settings.SettingsAbout.githubdiscussions": "Discussões no GitHub", "components.Settings.SettingsAbout.gettingsupport": "Obter Suporte", "components.Settings.SettingsAbout.documentation": "Documentação", "components.Settings.SettingsAbout.Releases.viewongithub": "Ver no GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "<PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.versionChangelog": "Mudanças Nesta Versão", "components.Settings.SettingsAbout.Releases.releases": "Versõ<PERSON>", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Informações de versão indisponíveis. O GitHub está abaixo?", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationRootFolderRequired": "Deve selecionar uma pasta raiz", "components.Settings.RadarrModal.validationProfileRequired": "Deve selecionar um perfil de qualidade", "components.Settings.RadarrModal.validationPortRequired": "Deve fornecer um número de porta válido", "components.Settings.RadarrModal.validationNameRequired": "<PERSON>e fornecer o nome do servidor", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Deve selecionar a disponibilidade mínima", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Ligação ao Radarr estabelecida com sucesso!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Falhar a ligar ao Radarr.", "components.UserList.password": "Palavra-passe", "components.UserList.created": "<PERSON><PERSON><PERSON>", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "<PERSON><PERSON>r <PERSON>-passe Automaticamente", "i18n.request": "Pedir", "components.UserList.admin": "Administrador", "components.TvDetails.watchtrailer": "<PERSON>er Trailer", "components.TvDetails.viewfullcrew": "Ver Equipa Técnica Completa", "i18n.unavailable": "Indisponível", "components.TvDetails.similar": "Séries Semelhantes", "components.TvDetails.showtype": "Tipo de Série", "components.TvDetails.recommendations": "Recomendações", "i18n.pending": "Pendente", "components.TvDetails.overviewunavailable": "Sinopse indisponível.", "components.TvDetails.overview": "Sinopse", "components.TvDetails.originallanguage": "Língua original", "components.TvDetails.network": "{networkCount, plural, one {Emissor} other {Emissores}}", "components.TvDetails.firstAirDate": "Data da Estreia", "i18n.decline": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.cast": "Elenco", "i18n.available": "Disponível", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCrew.fullseriescrew": "Equipa Técnica Completa da Série", "components.TvDetails.TvCast.fullseriescast": "Elenco Completo da Série", "components.StatusBadge.status4k": "4K {status}", "components.Login.signinwithplex": "Iniciar sessão com a sua conta Plex", "components.RequestModal.requesterror": "Ocorreu um erro ao submeter o pedido.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Não conseguimos correlacionar a sua solicitação automaticamente. Por favor selecione a correspondência correta na lista abaixo.", "components.Login.signinheader": "Iniciar sessão para continuar", "components.Login.signingin": "A Iniciar <PERSON>…", "components.Login.signin": "<PERSON><PERSON><PERSON>", "components.Settings.notificationAgentSettingsDescription": "Configurar e ativar agentes de notificação.", "components.UserList.userssaved": "Permissões de utilizador gravadas com sucesso!", "components.UserList.bulkedit": "Edição em Massa", "components.Settings.toastPlexRefreshSuccess": "Lista de servidores Plex obtida com sucesso!", "components.Settings.toastPlexRefreshFailure": "Falha ao recuperar a lista de servidores Plex.", "components.Settings.toastPlexRefresh": "A obter lista de servidores do Plex…", "components.Settings.toastPlexConnectingSuccess": "Ligação ao Plex estabelecida com sucesso!", "components.Settings.toastPlexConnectingFailure": "Falha ao ligar ao Plex.", "components.Settings.toastPlexConnecting": "A tentar ligar ao Plex…", "components.Settings.settingUpPlexDescription": "Para configurar o Plex, pode inserir os detalhes manualmente ou selecionar um dos servidores disponíveis obtidos de <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Clique no botão à direita da lista de servidores disponíveis.", "components.Settings.serverpresetRefreshing": "A obter servidores…", "components.Settings.serverpresetManualMessage": "Configuração Manual", "components.Settings.serverpresetLoad": "Clique no botão para carregar os servidores disponíveis", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverRemote": "remoto", "components.Settings.serverLocal": "local", "components.PermissionEdit.usersDescription": "Conceder permiss<PERSON> para gerir utilizadores Jellyseerr. Os utilizadores com essa permissão não podem modificar os utilizadores ou conceder o privilégio de administrador.", "components.PermissionEdit.users": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.requestDescription": "Conceder per<PERSON><PERSON><PERSON> para pedir multimédia não 4K.", "components.PermissionEdit.request4kTvDescription": "Conceder <PERSON><PERSON><PERSON><PERSON> para pedir séries em 4K.", "components.PermissionEdit.request4kTv": "Pedir Séries 4K", "components.PermissionEdit.request4kMoviesDescription": "Conceder per<PERSON><PERSON><PERSON> para pedir filmes em 4K.", "components.PermissionEdit.request4kMovies": "Pedir Filmes 4K", "components.PermissionEdit.request4kDescription": "Conceder per<PERSON><PERSON><PERSON> para pedir multimédia 4K.", "components.PermissionEdit.request4k": "Pedir 4K", "components.PermissionEdit.request": "Pedir", "components.PermissionEdit.managerequestsDescription": "Conceder permis<PERSON><PERSON> para gerir pedidos Jellyseerr. Todas os pedidos feitos por um utilizador com essa permissão serão aprovados automaticamente.", "components.PermissionEdit.managerequests": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapproveSeriesDescription": "Conceder aprov<PERSON>ção automática para pedidos de séries não 4K.", "components.PermissionEdit.autoapproveSeries": "Aprovar Séries Automaticamente", "components.PermissionEdit.autoapproveMoviesDescription": "Conceder aprovação automática para pedidos de filmes não 4K.", "components.PermissionEdit.autoapproveMovies": "Aprovar Filmes Automaticamente", "components.PermissionEdit.autoapprove": "Aprovação Automática", "components.PermissionEdit.autoapproveDescription": "Conceder aprov<PERSON>ção automática para todas os pedidos não 4K.", "components.PermissionEdit.advancedrequestDescription": "Conceder <PERSON><PERSON><PERSON><PERSON> para fazer pedidos avançados.", "components.PermissionEdit.advancedrequest": "Pedidos <PERSON>", "components.PermissionEdit.adminDescription": "Acesso total de administrador. Ignora todas as outras verificações de permissão.", "components.PermissionEdit.admin": "Administrador", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Ligação Sonarr estabelecida com sucesso!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Falha ao ligar ao Sonarr.", "components.Settings.SonarrModal.syncEnabled": "Ativar Sincronização", "components.Settings.RadarrModal.syncEnabled": "Ativar sincronização", "components.Settings.SonarrModal.externalUrl": "URL Externa", "components.Settings.RadarrModal.externalUrl": "URL Externa", "components.MovieDetails.markavailable": "Marcar como Disponível", "components.MovieDetails.mark4kavailable": "Marcar como Disponível em 4K", "components.Settings.SettingsJobsCache.cachehits": "Acertos", "i18n.advanced": "Avançado", "components.Settings.SettingsJobsCache.runnow": "Executar Agora", "components.Settings.SettingsJobsCache.process": "Processo", "components.Settings.SettingsJobsCache.nextexecution": "Próxima Execução", "components.Settings.SettingsJobsCache.jobtype": "Tipo", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} iniciado(a).", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr executa certas tarefas de manutenção como trabalhos agendados regularmente, mas também podem ser acionados manualmente abaixo. A execução manual de um trabalho não altera sua programação.", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobname": "Nome da Tarefa", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} cancelado(a).", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.command": "Comand<PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Tamanho do Valor", "components.Settings.SettingsJobsCache.cachename": "Nome do Cache", "components.Settings.SettingsJobsCache.cachemisses": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "Cache {cachename} limpo.", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr armazena em cache os pedidos para endpoints de API externos para otimizar o desempenho e evitar chamadas de API desnecessárias.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Preferido", "components.UserList.users": "Utilizadores", "components.Search.search": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.setup": "Configurar", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "A URL não deve terminar com uma barra final", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "A URL não deve terminar com uma barra final", "components.Settings.SonarrModal.validationApplicationUrl": "<PERSON>e fornecer um URL valido", "components.Settings.RadarrModal.validationApplicationUrl": "<PERSON>e fornecer um URL valido", "components.RequestModal.AdvancedRequester.requestas": "Ped<PERSON>", "components.PermissionEdit.viewrequestsDescription": "Conceder <PERSON><PERSON><PERSON><PERSON> para ver pedidos de outros utilizadores.", "components.PermissionEdit.viewrequests": "Ver Pedidos", "components.Discover.discover": "Descobrir", "components.AppDataWarning.dockerVolumeMissingDescription": "O ponto de montagem <code>{appDataPath}</code> não foi configurado corretamente . Todos dados serão perdidos quando o contentor parar ou reiniciar.", "components.TvDetails.nextAirDate": "Data do próximo Episódio", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "URL Base não deve terminar com uma barra", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "URL Base deve iniciar com uma barra", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "URL Base não deve terminar com uma barra", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "URL Base deve iniciar com uma barra", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "<PERSON>e fornecer um URL valido", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "<PERSON>e fornecer um URL valido", "components.Settings.Notifications.validationEmail": "Deve fornecer um e-mail válido", "components.UserList.validationEmail": "Deve fornecer um e-mail válido", "components.ResetPassword.validationpasswordrequired": "Deve fornecer uma palavra-passe", "components.ResetPassword.validationpasswordminchars": "Palavra-passe muito curta; necessário ter no mínimo 8 caracteres", "components.ResetPassword.validationpasswordmatch": "Palavras-passe devem corresponder", "components.ResetPassword.validationemailrequired": "Deve fornecer um e-mail válido", "components.ResetPassword.resetpasswordsuccessmessage": "Palavra-passe reposta com sucesso!", "components.ResetPassword.requestresetlinksuccessmessage": "Um endereço para o recuperar palavra-passe será enviado ao endereço de e-mail fornecido se estiver associado a um utilizador válido.", "components.ResetPassword.password": "Palavra-passe", "components.ResetPassword.gobacklogin": "Voltar a Página de Inicio de Sessão", "components.ResetPassword.resetpassword": "Repor a sua Palavra-passe", "components.ResetPassword.emailresetlink": "Link de Recuperação por E-mail", "components.ResetPassword.email": "Endereço E-mail", "components.ResetPassword.confirmpassword": "Confirmar <PERSON>e", "components.Login.forgotpassword": "Esqueceu a Palavra-passe?", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Deve selecionar um perfil de idioma", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Testar ligação para carregar perfis de idioma", "components.Settings.SonarrModal.selectLanguageProfile": "Selecione um perfil de Idioma", "components.Settings.SonarrModal.loadinglanguageprofiles": "A carregar perfis de Idioma…", "components.Settings.SonarrModal.animelanguageprofile": "Perfil de Idioma de Anime", "components.Settings.SonarrModal.languageprofile": "Perfil de Idioma", "components.RequestModal.AdvancedRequester.languageprofile": "Perfil de Idioma", "components.UserList.sortRequests": "Número de Pedidos", "components.UserList.sortDisplayName": "Nome de Exibição", "components.UserList.sortCreated": "Data de Criação", "components.Settings.Notifications.sendSilentlyTip": "Enviar notificações sem som", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autoapprove4kSeriesDescription": "Conceder aprov<PERSON>ção automática para pedidos de séries 4K.", "components.PermissionEdit.autoapprove4kSeries": "Aprovar Séries 4K Automaticamente", "components.PermissionEdit.autoapprove4kMoviesDescription": "Conceder aprovação automática para pedidos de filmes 4K.", "components.PermissionEdit.autoapprove4kMovies": "Aprovar Filmes 4K Automaticamente", "components.PermissionEdit.autoapprove4kDescription": "Conceder aprov<PERSON>ção automática para todas os pedidos 4K.", "components.PermissionEdit.autoapprove4k": "Aprovar 4K Automaticamente", "components.UserProfile.recentrequests": "Pedidos <PERSON>", "components.UserProfile.UserSettings.menuPermissions": "Permissões", "components.UserProfile.UserSettings.menuNotifications": "Notificações", "components.UserProfile.UserSettings.menuGeneralSettings": "G<PERSON>", "components.UserProfile.UserSettings.menuChangePass": "Palavra-passe", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Permissões gravadas com sucesso!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Ocorreu um erro ao gravar as definições.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Permissões", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Deve fornecer uma nova palavra-passe", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Deve fornecer sua palavra-passe atual", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Palavras-passe devem corresponder", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON>e confirmar a nova palavra-passe", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Palavra-passe gravada com sucesso!", "components.UserProfile.UserSettings.UserPasswordChange.password": "Palavra-passe", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nova Palavra-passe", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Palavra-passe Atual", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Confirmar <PERSON>e", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Definicções de Notificação", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Definições gravadas com sucesso!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Ocorreu um erro ao gravar as definições.", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Utilizador <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Utilizador Local", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Definições Gerais", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Nome de Exibição", "components.UserProfile.ProfileHeader.settings": "Modificar Definições", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserList.edituser": "Modificar Permissões do Utilizador", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "<PERSON>e fornecer um token de acesso", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Ativar Agente", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "<PERSON><PERSON>", "components.Layout.UserDropdown.settings": "Definições", "components.Layout.UserDropdown.myprofile": "Perfil", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Palavra-passe muito curta; necessário ter no mínimo 8 caracteres", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Ocorreu um erro ao gravar a palavra-passe.", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Deve fornecer um ID de utilizador válido", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "O número de <FindDiscordIdLink>ID</FindDiscordIdLink> da sua conta de utilizador", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID de Utilizador", "components.UserList.userfail": "Ocorreu um erro ao gravar as permissões do utilizador.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Definições de notificação Pushbullet gravadas com sucesso!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "<PERSON>al<PERSON> ao gravar as definições de notificação Pushbullet.", "components.CollectionDetails.requestcollection4k": "Pedir Coleção em 4K", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrar conteúdo por disponibilidade da região", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Descubrir <PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrar conteúdo por idioma original", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Descobrir I<PERSON>", "components.Discover.upcomingtv": "Séries a Estrear", "components.RegionSelector.regionDefault": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "components.Settings.webhook": "Webhook", "components.Settings.email": "E-Mail", "components.RegionSelector.regionServerDefault": "Predefinição ({region})", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Não tem permissão para modificar a palavra-passe deste utilizador.", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Utilizador", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Função", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrador", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Tipo de Conta", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.accounttype": "Tipo", "i18n.loading": "A carregar…", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "<PERSON>e fornecer um ID de chat válido", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Iniciar uma conversa</TelegramBotLink>, adicionar <GetIdBotLink>@get_id_bot</GetIdBotLink>, e enviar o comando <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID do Chat", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Envia notificações sem som", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON>", "components.TvDetails.seasons": "{seasonCount, plural, one {# Temporada} other {# Temporadas}}", "components.Settings.SettingsJobsCache.unknownJob": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync-reset": "Reiniciar Sincronização de Transferências", "components.Settings.SettingsJobsCache.download-sync": "Sincronizar Transferências", "components.Settings.Notifications.botUsername": "Utilizador do Bot", "components.Discover.DiscoverTvGenre.genreSeries": "Séries de {genre}", "components.Discover.DiscoverStudio.studioMovies": "Filmes por {studio}", "components.Discover.DiscoverNetwork.networkSeries": "Séries por {network}", "components.Discover.DiscoverMovieGenre.genreMovies": "Filmes de {genre}", "components.Settings.scanning": "A sincronizar…", "components.Settings.scan": "Sincronizar Bibliotecas Plex", "components.Settings.SettingsJobsCache.sonarr-scan": "Sincronizar <PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "Sincronizar Radarr", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Sincronizar Adicionado Recentemente do Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Sincronização Completa da Biblioteca Plex", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Sincronização Completa da Biblioteca Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Sincronizar Adicionado Recentemente do Jellyfin", "components.RequestList.RequestItem.requested": "Pedido", "components.RequestList.RequestItem.modifieduserdate": "{date} por {user}", "components.RequestList.RequestItem.modified": "Modificado", "components.Discover.StudioSlider.studios": "Estúdios", "components.Discover.NetworkSlider.networks": "Emissoras", "components.Settings.Notifications.botAvatarUrl": "URL do Avatar do Bot", "components.Settings.Notifications.validationUrl": "<PERSON>e fornecer um URL valido", "components.Discover.DiscoverTvLanguage.languageSeries": "Séries em {language}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Filmes em {language}", "components.UserProfile.ProfileHeader.userid": "ID de Utilizador: {userid}", "components.UserProfile.ProfileHeader.joindate": "Entrou no {joindate}", "components.Settings.menuUsers": "Utilizadores", "components.Settings.SettingsUsers.userSettingsDescription": "Configurar as definições de utilizador global e predefinição.", "components.Settings.SettingsUsers.userSettings": "Definições de Utilizador", "components.Settings.SettingsUsers.toastSettingsSuccess": "Definições de utilizador gravadas com sucesso!", "components.Settings.SettingsUsers.toastSettingsFailure": "Ocorreu um erro enquanto guardava as definições.", "components.Settings.SettingsUsers.localLogin": "Ativar Inicio de Sessão Local", "components.Settings.SettingsUsers.defaultPermissions": "Permissões Predefinições", "components.UserProfile.UserSettings.unauthorizedDescription": "Não tem permissão para modificar as definições deste utilizador.", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Não pode modificar suas próprias permissões.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Enviar notificações quando os utilizadores apresentarem novos pedidos de multimédia que são automaticamente aprovados.", "components.NotificationTypeSelector.mediaAutoApproved": "Multimédia Aprovada Automaticamente", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutos", "components.TvDetails.episodeRuntime": "Duração do Episódio", "components.Settings.Notifications.pgpPrivateKeyTip": "Assinar mensagens de e-mail encriptadas utilizando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPasswordTip": "Assinar mensagens de e-mail encriptadas utilizando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Chave Privada PGP", "components.Settings.Notifications.pgpPassword": "Palavra-passe PGP", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.alreadyrequested": "<PERSON><PERSON> Pedido", "components.Discover.TvGenreSlider.tvgenres": "Géneros de Série", "components.Discover.TvGenreList.seriesgenres": "Géneros de Série", "components.Discover.MovieGenreSlider.moviegenres": "Géneros de Filmes", "components.Discover.MovieGenreList.moviegenres": "Géneros de Filmes", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.somethingwentwrong": "Ocorreu um erro", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Ocorreu um erro ao grvar a palavra-passe. A sua palavra-passe atual foi inserida corretamente?", "pages.serviceunavailable": "Serviço Indisponível", "pages.pagenotfound": "Página Não Encontrada", "pages.internalservererror": "Erro Interno do Servidor", "i18n.usersettings": "Utilizadores", "i18n.settings": "Definições", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notificações", "components.Settings.services": "Serviços", "components.Settings.plex": "Plex", "components.Settings.notifications": "Notificações", "components.UserProfile.UserSettings.UserGeneralSettings.general": "G<PERSON>", "components.Settings.SettingsUsers.users": "Utilizadores", "components.Settings.SettingsLogs.time": "Marcação Horária", "components.Settings.SettingsLogs.showall": "Mostrar Todos os Registos", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "Pausa", "components.Settings.SettingsLogs.message": "Mensagem", "components.Settings.SettingsLogs.logsDescription": "Também pode ver estes registos diretamente por <code>stdout</code> ou em <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Registos", "components.Settings.SettingsLogs.level": "Gravidade", "components.Settings.SettingsLogs.label": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.filterWarn": "Aviso", "components.Settings.SettingsLogs.filterInfo": "Informação", "components.Settings.SettingsLogs.filterError": "Erro", "components.Settings.SettingsLogs.filterDebug": "Depuração", "components.Settings.SettingsJobsCache.jobsandcache": "Tarefas e Cache", "components.Settings.SettingsAbout.about": "Sobre", "components.ResetPassword.passwordreset": "Repor Palavra-passe", "components.Settings.SettingsLogs.logDetails": "Detalhes do registo", "components.Settings.SettingsLogs.extraData": "<PERSON><PERSON>", "components.Settings.SettingsLogs.copyToClipboard": "Copiar Para a Área de Transferência", "components.Settings.SettingsLogs.copiedLogMessage": "Mensagem do registo copiada para o área de transferência.", "components.Settings.enablessl": "Usar SSL", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "Nas<PERSON><PERSON> {birthdate}", "components.PersonDetails.alsoknownas": "<PERSON><PERSON><PERSON><PERSON> Conhecid<PERSON>: {nomes}", "components.UserList.nouserstoimport": "Não há novos utilizadores para importar do Plex.", "i18n.delimitedlist": "{a}, {b}", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {filme} other {filmes}}", "components.RequestModal.QuotaDisplay.movie": "filme", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Este utilizador tem direito a pedir <strong>{limit}</strong> {type} cada <strong>{days}</strong> dias.", "components.RequestModal.QuotaDisplay.allowedRequests": "Tem direito para pedir <strong>{limit}</strong> {type} cada <strong>{days}</strong> dias.", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.requiredquota": "Precisa ter pelo menos <strong>{seasons}</strong> {seasons, plural, one {pedido de temporada} other {pedidos de temporadas}} para enviar um pedido para esta série.", "components.UserProfile.totalrequests": "Total de Pedidos", "components.UserProfile.seriesrequest": "Pedidos de Séries", "components.UserProfile.requestsperdays": "{limit} restante(s)", "components.UserProfile.pastdays": "{type} (últimos {days} dias)", "components.UserProfile.movierequests": "Pedidos de Filmes", "components.UserProfile.limit": "{remaining} de {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Limite de Pedidos para Séries", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Limite de Pedidos para Filmes", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Substituir o limite global", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Limite Global de Pedidos de Séries", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Limite Global de Pedidos de Filmes", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {temporada} other {temporadas}}", "components.RequestModal.QuotaDisplay.season": "temporada", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {<PERSON><PERSON><PERSON>} other {<strong>#</strong>}} {type} {remaining, plural, one {pedido} other {pedidos}} restantes", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Pode ver um resumo dos limites de pedidos deste utilizador na sua <ProfileLink>página de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Pode ver um resumo dos seus limites de pedidos na sua <ProfileLink>página de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Não há pedidos de temporada suficientes", "i18n.next": "Próxima", "i18n.all": "<PERSON><PERSON>", "i18n.view": "<PERSON>er", "i18n.tvshow": "Séries", "i18n.testing": "A testar…", "i18n.test": "<PERSON>e", "i18n.status": "Estado", "i18n.showingresults": "A mostrar <strong>{from}</strong> para <strong>{to}</strong> de <strong>{total}</strong> resultados", "i18n.saving": "A Gravar…", "i18n.save": "<PERSON><PERSON><PERSON>", "i18n.resultsperpage": "Mostrar {pageSize} resultados por página", "i18n.requesting": "A Pedir…", "i18n.request4k": "Pedir em 4K", "i18n.previous": "Anterior", "i18n.notrequested": "Não Pedido", "i18n.noresults": "Sem resultados.", "i18n.movie": "Filme", "i18n.canceling": "A cancelar…", "i18n.back": "Voltar", "i18n.areyousure": "Tem certeza?", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Este utilizador precisa ter pelo menos <strong>{seasons}</strong> {seasons, plural, one {pedido de temporada} other {pedidos de temporadas}} restantes para enviar um pedido para esta série.", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.LanguageSelector.originalLanguageDefault": "Todos Idiomas", "components.LanguageSelector.languageServerDefault": "Predefinição ({language})", "components.Settings.SonarrModal.loadingTags": "A carregar tags…", "components.Settings.SonarrModal.edit4ksonarr": "Modificar Servidor Sonarr 4K", "components.Settings.SonarrModal.default4kserver": "Servidor 4K Predefinido", "components.Settings.SonarrModal.create4ksonarr": "Adicionar Novo Servidor Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Tags Para Anime", "components.Settings.SonarrModal.testFirstTags": "Testar ligação para carregar tags", "components.Settings.RadarrModal.testFirstTags": "Testar ligação para carregar tags", "components.Settings.SonarrModal.tags": "Tags", "components.Settings.RadarrModal.tags": "Tags", "components.Settings.SonarrModal.selecttags": "Selecionar tags", "components.Settings.RadarrModal.selecttags": "Selecionar tags", "components.Settings.SonarrModal.notagoptions": "<PERSON>enhuma tag.", "components.Settings.RadarrModal.notagoptions": "<PERSON>enhuma tag.", "components.Settings.RadarrModal.loadingTags": "A carregar tags…", "components.Settings.RadarrModal.edit4kradarr": "Modificar Servidor Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Servidor 4K Predefinido", "components.Settings.RadarrModal.create4kradarr": "Adicionar Novo Servidor Radarr 4K", "components.RequestModal.AdvancedRequester.tags": "Tags", "components.RequestModal.AdvancedRequester.selecttags": "Selecionar tags", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON>enhuma tag.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Deve fornecer uma chave pública PGP válida", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Chave Pública PGP", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Encriptar mensagens de e-mail usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.validationPgpPrivateKey": "Deve fornecer uma chave privada PGP válida", "components.Settings.Notifications.validationPgpPassword": "Deve fornecer uma palavra-passe PGP", "components.Settings.Notifications.botUsernameTip": "Permitir que utilizadores iniciem uma conversa com o seu bot e configurem as suas próprias notificações", "components.RequestModal.pendingapproval": "O seu pedido está com aprovação pendente.", "components.RequestList.RequestItem.mediaerror": "O título associado a este pedido não está mais disponível.", "components.RequestList.RequestItem.deleterequest": "Apagar Pedido", "components.RequestCard.mediaerror": "O título associado a este pedido não está mais disponível.", "components.RequestCard.deleterequest": "Apagar Pedido", "components.NotificationTypeSelector.notificationTypes": "Tipos de Notificação", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr Stable", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.outofdate": "Desatualizado", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} atrás", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Definições de notificação Telegram gravadas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Falha ao gravas as definições de notificação Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Definições de notificação e-mail gravadas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Falha ao gravas as definições de notificação e-mail.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-Mail", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Definições de notificação Discord gravadas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Falha ao gravar as definições de notificação Discord.", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON>ar <PERSON>", "components.Settings.serviceSettingsDescription": "Configure o seu(s) servidor(es) {serverType} abaixo. Pode ligar vários servidores {serverType}, mas apenas dois deles podem ser marcados como predefinidos (um não 4K e um 4K). Os administradores podem mudar o servidor usado para processar novos pedidos antes da aprovação.", "components.Settings.noDefaultServer": "Pelo menos um servidor {serverType} deve ser marcado como predefinido para que os pedidos de {mediaType} sejam processados.", "components.Settings.noDefaultNon4kServer": "Se tiver apenas um único servidor {serverType} para conteúdo não 4K e 4K (ou se apenas transfere conteúdo 4K), o seu servidor {serverType} <strong>NÃO</strong> deve ser designado como um servidor 4K.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Atualmente, sua conta não tem uma palavra-passe definida. Configure uma palavra-passe abaixo para permitir o inicio de sessão como um \"utilizador local\" usando o seu e-mail.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Atualmente, a conta deste utilizador não tem uma palavra-passe definida. Configure uma palavra-passe abaixo para permitir que esta conta se conecte como um \"utilizador local\".", "components.Settings.mediaTypeSeries": "séries", "components.Settings.mediaTypeMovie": "filme", "components.Settings.SettingsAbout.uptodate": "Atualizado", "components.Settings.SettingsAbout.outofdate": "Desatualizado", "components.UserList.autogeneratepasswordTip": "Enviar uma palavra-passe gerada pelo servidor para o utilizador por e-mail", "i18n.retrying": "A tentar novamente…", "components.Settings.serverSecure": "seguro", "components.UserList.usercreatedfailedexisting": "A e-mail fornecido já está a ser usado por outro utilizador.", "components.RequestModal.edit": "Modificar Pedido", "components.RequestList.RequestItem.editrequest": "Modificar Pedido", "components.Settings.SonarrModal.enableSearch": "Ativar Pesquisa Automática", "components.Settings.RadarrModal.enableSearch": "Ativar Pesquisa Automática", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Idioma de Exibição", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.Settings.webpush": "Web Push", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Definições de notificação web push gravadas com sucesso!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Falha ao gravar as definições de notificação web push.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Ativar Agente", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Requerido apenas se não estiver a usar o perfil <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Definições de notificação LunaSea gravadas com sucesso!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Falha ao gravar as definições de notificação do LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "<PERSON>e fornecer uma URL valido", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nome do Perfil", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Ativar Agente", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Definições de notificação web push gravadas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Falha ao gravar as definições de notificação web push.", "components.Settings.noDefault4kServer": "Um servidor 4K {serverType} deve ser marcado como predefinido para permitir que os utilizador enviem pedidos 4K de {mediaType}.", "components.Settings.is4k": "4K", "components.Settings.SettingsUsers.newPlexLoginTip": "Permitir que Utilizadores do {mediaServerName} iniciem sessão sem primeiro serem importados", "components.Settings.SettingsUsers.newPlexLogin": "Ativar novo inicio de sessão {mediaServerName}", "components.Settings.Notifications.toastTelegramTestSuccess": "Notificação de teste Telegram enviada!", "components.Settings.Notifications.toastEmailTestSuccess": "Notificação de teste e-mail enviada!", "components.Settings.Notifications.toastDiscordTestSuccess": "Notificação de teste Discord enviada!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Notificação de teste webhook enviada!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Notificação de teste web push enviada!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Notificação de teste Slack enviada!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Notificação de teste Pushover enviada!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Notificação de teste Pushbullet enviada!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Notificação de teste LunaSea enviada!", "components.Settings.Notifications.toastTelegramTestSending": "A enviar notificação de teste Telegram…", "components.Settings.Notifications.toastEmailTestSending": "A enviar notificação de teste e-mail…", "components.Settings.Notifications.toastDiscordTestSending": "A enviar notificação de teste Discord…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "A enviar notificação de teste webhook…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "A enviar notificação de teste web push…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "A enviar notificação de teste Pushover…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "A enviar notificação de teste Pushbullet…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "A enviar notificação de teste ao LunaSea…", "components.Settings.Notifications.toastTelegramTestFailed": "Falha ao enviar notificação de teste Telegram.", "components.Settings.Notifications.toastEmailTestFailed": "Falha ao enviar notificação de teste e-mail.", "components.Settings.Notifications.toastDiscordTestFailed": "Falha ao enviar notificação de teste Discord.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Falha ao enviar notificação de teste webhook.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Falha ao enviar notificação de teste web push.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "A enviar notificação de teste Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Falha ao enviar notificação de teste Slack.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Falha ao enviar notificação de teste Pushover.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Falha ao enviar notificação de teste Pushbullet.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Falha ao enviar notificação de teste ao LunaSea.", "components.PermissionEdit.requestTvDescription": "Conceder <PERSON><PERSON><PERSON><PERSON> para pedir séries não 4K.", "components.PermissionEdit.requestTv": "Pedir Séries", "components.PermissionEdit.requestMoviesDescription": "Conceder per<PERSON><PERSON><PERSON> para pedir filmes não 4K.", "components.PermissionEdit.requestMovies": "Pedir Filmes", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Predefinido ({language})", "components.UserList.localLoginDisabled": "A configuração <strong>Ativar inicio de sessão Local</strong> está desativada de momento.", "components.Settings.webAppUrlTip": "Opcionalmente direcionar os utilizadores para a aplicação de web app no seu servidor, em vez da aplicação de web app \"hospedada\"", "components.Settings.webAppUrl": "URL <WebAppLink>Web App</WebAppLink>", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON>r um <DiscordWebhookLink>webhook de integração</DiscordWebhookLink> no seu servidor", "components.Settings.Notifications.encryptionTip": "Na maioria dos casos, o TLS implícito usa a porta 465 e o STARTTLS usa a porta 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Usar STARTTLS sempre", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Usar TLS Implícito", "components.Settings.Notifications.encryptionDefault": "Usar STARTTLS se disponível", "components.Settings.Notifications.encryption": "Método de Encriptação", "components.Settings.Notifications.chatIdTip": "Iniciar uma conversa com o seu bot, adicione <GetIdBotLink>@get_id_bot</GetIdBotLink> e execute o comando <code>/my_id</code>", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registe uma aplicação</ApplicationRegistrationLink> para usar com Jellyseerr", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Criar um bot</CreateBotLink> para usar com Je<PERSON>seerr", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Para receber notificações via web push, o Jellyseerr deve ser servido via HTTPS.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Criar integração para um <WebhookLink>Webhook de Entrada</WebhookLink>", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "O seu <UsersGroupsLink>identificador de utilizador ou grupo</UsersGroupsLink> contendo 30 caractéres", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Criar um token a partir das suas <PushbulletSettingsLink>Definições de Conta</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Sua <LunaSeaLink>URL de webhook</LunaSeaLink> para notificação baseada em utilizador ou dispositivo", "components.RequestList.RequestItem.requesteddate": "Pedido", "components.RequestCard.failedretry": "Ocorreu um erro ao voltar a tentar o pedido.", "components.DownloadBlock.estimatedtime": "Est<PERSON><PERSON>va {time}", "components.Settings.SettingsUsers.localLoginTip": "Permitir que os utilizadores iniciem sessão usando e-mail e palavra-passe, em vez de Plex OAuth", "components.Settings.SettingsUsers.defaultPermissionsTip": "Permissões iniciais atribuídas a novos utilizadores", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} por {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {temporada} other {temporadas}}", "components.QuotaSelector.movies": "{count, plural, one {fime} other {filmes}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} por {quotaDays} {days}</quotaUnits>", "components.NotificationTypeSelector.usermediaapprovedDescription": "Notificar quando os seus pedidos de multimédia forem aprovados.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Notificar quando outros utilizadores submeteres novos pedidos de multimédia que são automaticamente aprovados.", "components.Settings.Notifications.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Deve selecionar pelo menos um tipo de notificação", "components.NotificationTypeSelector.usermediarequestedDescription": "Notificar quando outros utilizadores enviarem novos pedidos de multimédia que requeiram aprovação.", "components.NotificationTypeSelector.usermediafailedDescription": "Notificar quando os pedidos de multimédia não forem adicionados ao Radarr ou Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Notificar quando seus pedidos de multimédia forem recusados.", "components.NotificationTypeSelector.usermediaavailableDescription": "Notificar quando os seus pedidos de multimédia ficarem disponíveis.", "components.QuotaSelector.days": "{conta, plural, one {dia} other {dias}}", "components.Settings.SettingsAbout.betawarning": "Isto é um software em BETA. As funcionalidades podem estar quebradas e/ou instáveis. Relate qualquer problema no GitHub!", "components.MovieDetails.streamingproviders": "Atualmente a Exibir em", "components.TvDetails.streamingproviders": "Atualmente a Exibir em", "components.MovieDetails.showmore": "<PERSON><PERSON>", "components.Layout.LanguagePicker.displaylanguage": "Idioma da Interface", "components.MovieDetails.showless": "<PERSON><PERSON>", "components.StatusBadge.status": "{status}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Algo deu errado ao gravar tarefa.", "components.StatusBadge.managemedia": "G<PERSON>r {mediaType}", "components.StatusBadge.openinarr": "Abrir em {arr}", "components.IssueDetails.allseasons": "<PERSON><PERSON>", "components.IssueDetails.nocomments": "<PERSON><PERSON><PERSON> come<PERSON>.", "components.IssueDetails.openinarr": "Abrir no {arr}", "components.PermissionEdit.viewissues": "Ver Problemas", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Ativar Agente", "components.TvDetails.rtaudiencescore": "Pontuação de audiência no Rotten Tomatoes", "components.TvDetails.rtcriticsscore": "Pontuação de audiência no Rotten Tomatoes", "components.TvDetails.seasonnumber": "Temporada {seasonNumber}", "components.TvDetails.seasonstitle": "Temporadas", "components.TvDetails.status4k": "4K {status}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Solicitar Séries Automaticamente", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Deve prover um ID válido de utilizador Discord", "i18n.resolved": "Resolvido", "components.PermissionEdit.createissuesDescription": "Concede permissão para reportar problemas com mídias.", "components.PermissionEdit.manageissuesDescription": "Concede permissão para gerir problemas com mídia.", "components.RequestModal.selectmovies": "Selecionar Filme(s)", "components.IssueList.IssueItem.opened": "Abe<PERSON>o", "components.IssueList.IssueItem.openeduserdate": "{date} por {user}", "components.IssueList.IssueItem.unknownissuetype": "Desconhecido", "components.IssueList.IssueItem.viewissue": "Ver Problema", "components.IssueModal.CreateIssueModal.allepisodes": "Todos Episódios", "components.ManageSlideOver.manageModalIssues": "Problemas <PERSON>", "components.MovieDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {Países}} de Produção", "components.PermissionEdit.viewrecent": "Ver Recentemente Adicionados", "components.PermissionEdit.viewrecentDescription": "Concede permissão para ver lista de mídias adicionadas recentemente.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "A cada {jobScheduleMinutes, plural, one {minuto} other {{jobScheduleMinutes} minutos}}", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID do Utilizador Discord", "i18n.restartRequired": "Reinicialização Necessária", "components.StatusChecker.appUpdated": "{applicationTitle} Atualizado", "components.StatusChecker.restartRequiredDescription": "Por favor, reinicie o servidor para aplicar as novas configurações.", "components.TitleCard.cleardata": "Lim<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "O seu <UsersGroupsLink>identificador de utilizador ou grupo</UsersGroupsLink> contendo 30 caractéres", "components.RequestCard.tmdbid": "ID do TMDB", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Configurações de notificação via Pushover salvas com sucesso!", "components.Discover.DiscoverWatchlist.discoverwatchlist": "A sua Lista a Assistir do Plex", "components.IssueDetails.deleteissueconfirm": "Tem certeza que deseja apagar este problema?", "components.IssueDetails.toaststatusupdated": "Estado do problema atualizado com sucesso!", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Espisódio} other {Episódios}}", "components.IssueList.IssueItem.issuestatus": "Estado", "components.IssueModal.CreateIssueModal.problemepisode": "Episódio Afetado", "components.IssueModal.CreateIssueModal.problemseason": "Temporada Afetada", "components.IssueModal.CreateIssueModal.reportissue": "Reportar um Problema", "components.IssueModal.issueAudio": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "G<PERSON>r {mediaType}", "components.RequestList.RequestItem.tmdbid": "ID do TMDB", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sincronizar Lista para assistir do Plex", "components.Settings.deleteServer": "Remover Servidor {serverType}", "components.StatusChecker.appUpdatedDescription": "Por favor, clique no botão abaixo para recarregar a aplicação.", "components.ManageSlideOver.manageModalAdvanced": "Avançado", "components.ManageSlideOver.alltime": "<PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia": "Mí<PERSON>", "components.ManageSlideOver.manageModalMedia4k": "Mídia 4K", "components.ManageSlideOver.markallseasons4kavailable": "<PERSON><PERSON>das Temporadas como Disponíveis em 4K", "components.NotificationTypeSelector.userissuereopenedDescription": "Receber notificação quando problemas que reportou forem re-abertos.", "components.PermissionEdit.viewissuesDescription": "Concede permissão para ver problemas em mídias reportados por outros problemas.", "components.RequestModal.requestmovies": "Solicitar {count} {count, plural, one {<PERSON>e} other {Filmes}}", "components.Settings.validationUrlBaseLeadingSlash": "URL Base deve iniciar com uma barra", "components.Discover.DiscoverWatchlist.watchlist": "Lista a Assistir do Plex", "components.Discover.plexwatchlist": "A sua Lista a Assistir do Plex", "components.IssueDetails.closeissue": "Encerrar Problema", "components.IssueDetails.commentplaceholder": "Adicionar um comentário…", "components.IssueDetails.closeissueandcomment": "<PERSON><PERSON><PERSON> com Comentário", "components.IssueDetails.leavecomment": "Comentar", "components.IssueDetails.comments": "Comentários", "components.IssueDetails.deleteissue": "Apa<PERSON> Problema", "components.IssueDetails.problemseason": "Temporada Afetada", "components.IssueDetails.reopenissue": "Re-abrir <PERSON>a", "components.IssueDetails.reopenissueandcomment": "Re-abrir com Comentário", "components.IssueDetails.season": "Temporada {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Algo deu errado ao editar a descrição do problema.", "components.IssueDetails.toasteditdescriptionsuccess": "Descrição do problema alterada com sucesso!", "components.IssueDetails.toastissuedeleted": "Problema apagado com sucesso!", "components.IssueModal.CreateIssueModal.whatswrong": "O quê há de errado?", "components.IssueModal.issueOther": "Outros", "components.IssueModal.issueSubtitles": "<PERSON>a", "components.NotificationTypeSelector.adminissuereopenedDescription": "Receber notificação quando problemas forem re-abertos por outros utilizadores.", "components.NotificationTypeSelector.issuecomment": "Comentário no Problema", "components.NotificationTypeSelector.issuecommentDescription": "Enviar notificações quando problemas receberem novos comentários.", "components.NotificationTypeSelector.issuecreatedDescription": "Enviar notificações quando problemas são reportados.", "components.NotificationTypeSelector.issueresolvedDescription": "Enviar notificações quando problemas são resolvidos.", "components.NotificationTypeSelector.mediaautorequested": "Pedido automaticamente enviado", "components.PermissionEdit.viewwatchlists": "Ver Lista para assistir do Plex", "components.RequestModal.requestseasons4k": "Solicitar {seasonCount} {seasonCount, plural, one {Temporada} other {Temporadas}} em 4K", "components.Settings.toastTautulliSettingsFailure": "Algo deu errado ao gravar configurações do Tautulli.", "components.TitleCard.tmdbid": "ID do TMDB", "components.TitleCard.tvdbid": "ID do TheTVDB", "components.TvDetails.manageseries": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.adminissueresolvedDescription": "Receber notificação quando problemas são resolvidos por outros utilizadores.", "components.PermissionEdit.autorequest": "Solicitar Automaticamente", "components.NotificationTypeSelector.mediaautorequestedDescription": "Ser notificado quando um novo pedido de mídia de algum item da sua Lista a assistir no Plex for automaticamente enviado.", "components.PermissionEdit.autorequestDescription": "Dar permissão para enviar automaticamente pedidos para mídias não-4K via Lista para assistir do Plex.", "components.PermissionEdit.autorequestMovies": "Solicitar Filmes Automaticamente", "components.PermissionEdit.autorequestMoviesDescription": "Dar permissão para enviar automaticamente pedidos para filmes não-4K via Lista para assistir do Plex.", "components.PermissionEdit.autorequestSeries": "Solicitar Séries Automaticamente", "components.RequestCard.tvdbid": "ID do TheTVDB", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON>", "components.Settings.advancedTooltip": "Configurar incorretamente esta opção pode resultar numa funcionalidade quebrada", "components.StatusChecker.restartRequired": "Reinicialização do Servidor Necessária", "components.TitleCard.mediaerror": "{mediaType} Não Encontrado", "components.Settings.toastTautulliSettingsSuccess": "Configurações do Tautulli salvas com sucesso!", "components.Settings.RadarrModal.released": "Lançado", "components.IssueDetails.IssueComment.areyousuredelete": "Tem certeza que deseja apagar este comentário?", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.postedbyedited": "<PERSON><PERSON> {relativeTime} por {username} (Edited)", "components.IssueDetails.IssueComment.validationComment": "<PERSON>e escrever uma mensagem", "components.IssueDetails.IssueDescription.deleteissue": "Apa<PERSON> Problema", "components.IssueDetails.openedby": "#{issueId} aberto {relativeTime} por {username}", "components.IssueDetails.problemepisode": "Episódio Afetado", "components.IssueList.sortAdded": "<PERSON><PERSON>", "components.IssueList.sortModified": "Última Modificação", "components.NotificationTypeSelector.issuecreated": "Problema Reportado", "components.PermissionEdit.manageissues": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.edit": "Alterar <PERSON>ri<PERSON>", "components.IssueDetails.allepisodes": "Todos Episódios", "components.IssueDetails.toastissuedeletefailed": "Algo deu errado ao apagar problema.", "components.IssueDetails.toaststatusupdatefailed": "Algo deu errado ao atualizar o estado do problema.", "components.IssueDetails.unknownissuetype": "Desconhecido", "components.RequestModal.requestmovies4k": "Solicitar {count} {count, plural, one {<PERSON>e} other {Filmes}} em 4K", "components.TvDetails.tmdbuserscore": "Pontuação de Utilizador do TMDB", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Solicitar Filmes Automaticamente", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Criar um token da sua <PushbulletSettingsLink>Configuração de Conta</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Deve prover um token de acesso", "i18n.open": "Abe<PERSON>o", "components.Settings.SettingsAbout.runningDevelop": "Está usando a versão <code>develop</code> do Jellyseerr que é recomendada apenas para àqueles contribuindo com o desenvolvimento ou ajudando no teste de novas funcionalidades.", "components.AirDateBadge.airedrelative": "Exibido em {relativeTime}", "components.AirDateBadge.airsrelative": "Exibindo {relativeTime}", "components.IssueDetails.IssueDescription.description": "Descrição", "components.IssueDetails.play4konplex": "Assistir em 4K no Plex", "components.IssueDetails.playonplex": "Assistir no Plex", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Relato de problema em <strong>{title}</strong> enviado com sucesso!", "components.IssueModal.CreateIssueModal.toastviewissue": "Ver Problema", "components.PermissionEdit.autorequestSeriesDescription": "Dar permissão para enviar automaticamente pedidos para séries não-4K via Lista para assistir do Plex.", "components.TvDetails.reportissue": "Reportar um Problema", "components.RequestBlock.languageprofile": "Perfil de Idioma", "components.RequestModal.requestApproved": "Solicitação de <strong>{title}</strong> aprovada!", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Falha ao gravar configurações de notificação via Gotify.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Configurações de notificação via Gotify salvas com sucesso!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Falha ao enviar notificação de teste via Gotify.", "components.Settings.Notifications.NotificationsGotify.url": "URL do Servidor", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Deve prover um token de acesso", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Deve selecionar ao menos um tipo de notificação", "components.Settings.Notifications.enableMentions": "Ativar <PERSON>", "components.Settings.RadarrModal.inCinemas": "Nos Cinemas", "components.Settings.tautulliSettings": "Configurações do Tautulli", "components.Settings.tautulliSettingsDescription": "Tem a opção de configurar a integração com o seu servid<PERSON>. Overseer irá obter o histórico de reproduções das suas mídias no Plex através do Tautulli.", "components.Settings.urlBase": "Base do URL", "components.Settings.validationApiKey": "Deve prover uma chave de API válida", "components.Settings.validationUrl": "Deve prover uma URL válida", "components.TvDetails.productioncountries": "{countryCount, plural, one {<PERSON><PERSON>} other {Países}} de Produção", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "O <FindDiscordIdLink>número de identificação</FindDiscordIdLink> associado à sua conta Discord", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Falha ao gravar configurações de notificação via Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Deve prover uma chave válida de acesso", "components.UserProfile.recentlywatched": "Assistidos Recentemente", "i18n.import": "Importar", "i18n.importing": "Importando…", "components.IssueDetails.issuetype": "Tipo", "components.IssueDetails.openin4karr": "Abrir em {arr} 4K", "components.IssueList.IssueItem.problemepisode": "Episódio Afetado", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.ManageSlideOver.manageModalNoRequests": "Nenhuma solicitação.", "components.ManageSlideOver.downloadstatus": "Downloads", "components.ManageSlideOver.manageModalClearMedia": "Lim<PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Isso irá remover em definitivo todos dados desse(a) {mediaType}, incluindo quaisquer solicitações para esse item. Se este item existir na sua biblioteca do Plex, os dados de mídia serão recriados na próxima sincronia.", "components.ManageSlideOver.manageModalRequests": "Solicitações", "components.NotificationTypeSelector.userissuecreatedDescription": "Receber notificação quando outros utilizadores reportarem problemas.", "components.NotificationTypeSelector.userissuecommentDescription": "Receber notificação quando problemas reportados por receberem novos comentários.", "components.NotificationTypeSelector.userissueresolvedDescription": "Receber notificação quando problemas que reportou forem resolvidos.", "components.Settings.SettingsJobsCache.editJobSchedule": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "A cada {jobScheduleHours, plural, one {hora} other {{jobScheduleHours} horas}}", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Deve prover uma chave válida de usúario ou grupo", "components.Settings.SettingsAbout.appDataPath": "Configurações", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Tag do Canal", "components.RequestBlock.edit": "<PERSON><PERSON>", "components.RequestBlock.lastmodifiedby": "Última modificação por", "components.RequestBlock.requestdate": "Data do pedido", "components.RequestBlock.requestedby": "Pedido por", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON> pedido", "components.RequestCard.cancelrequest": "Cancelar pedido", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.editrequest": "<PERSON><PERSON>", "components.RequestModal.approve": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON>", "components.IssueDetails.IssueComment.postedby": "<PERSON><PERSON> {relativeTime} por {username}", "components.IssueDetails.episode": "Episódio {episodeNumber}", "components.IssueDetails.issuepagetitle": "Problema", "components.IssueDetails.lastupdated": "Última Atualização", "components.IssueList.IssueItem.issuetype": "Tipo", "components.IssueList.issues": "Problemas", "components.IssueList.showallissues": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "Episódio {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.providedetail": "Por favor, explique em detalhes o problema que encontrou.", "components.IssueModal.CreateIssueModal.season": "Temporada {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Enviar Problema", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Algo deu errado ao enviar problema.", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Deve prover uma descrição", "components.IssueModal.issueVideo": "Vídeo", "components.Layout.Sidebar.issues": "Problemas", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Pedidos de Filmes", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Pedidos de Séries", "components.Layout.UserDropdown.requests": "Pedidos", "components.ManageSlideOver.mark4kavailable": "Marcar como Disponível em 4K", "components.ManageSlideOver.markallseasonsavailable": "<PERSON><PERSON> Temporadas como Disponíveis", "components.ManageSlideOver.markavailable": "Marcar como Disponível", "components.ManageSlideOver.movie": "filme", "components.ManageSlideOver.openarr": "Abrir no {arr}", "components.ManageSlideOver.openarr4k": "Abrir no {arr} 4K", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON> <PERSON>", "components.ManageSlideOver.pastdays": "Últimos {days, number} Dias", "components.ManageSlideOver.playedby": "Reproduzido por", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {Reprodução} other {Reproduções}}", "components.ManageSlideOver.tvshow": "série", "components.MovieDetails.digitalrelease": "Lançamento Digital", "components.MovieDetails.managemovie": "<PERSON><PERSON><PERSON>", "components.MovieDetails.physicalrelease": "Lançamento em Disco", "components.MovieDetails.reportissue": "Reportar um problema", "components.MovieDetails.rtaudiencescore": "Pontuação de audiência no Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Tomatômetro do <PERSON> Tomatoes", "components.MovieDetails.theatricalrelease": "Lançamento no Cinema", "components.MovieDetails.tmdbuserscore": "Pontuação de utilizador do TMDB", "components.NotificationTypeSelector.adminissuecommentDescription": "Receber notificação quando outros utilizadores comentarem nos problemas.", "components.NotificationTypeSelector.issuereopened": "Problema Re-aberto", "components.NotificationTypeSelector.issuereopenedDescription": "Enviar notificações quando problemas são re-abertos.", "components.NotificationTypeSelector.issueresolved": "Problema Resolvido", "components.PermissionEdit.createissues": "Reportar Problemas", "components.PermissionEdit.viewwatchlistsDescription": "Dar permissão para ver a Lista para assistir do Plex de outros utilizadores.", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON> pedido", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON><PERSON> pedido", "components.RequestBlock.delete": "Deletar pedido", "components.RequestList.RequestItem.tvdbid": "ID do TheTVDB", "components.RequestModal.requestcollection4ktitle": "Solicitar Coleção em 4K", "components.RequestModal.requestcollectiontitle": "Solicitar Coleção", "components.RequestModal.requestseriestitle": "Solicitar Séries", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Enviando notificação de teste via Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Notificação de teste via Gotify enviada!", "components.Settings.Notifications.NotificationsGotify.token": "<PERSON><PERSON>", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Deve prover uma URL válida", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "A URL não deve terminar com uma barra", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Tarefa modificada com sucesso!", "components.Settings.experimentalTooltip": "Ativar essa opção pode resultar num comportamento não esperado da aplicação", "components.Settings.externalUrl": "URL Externa", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> deve ser reiniciado para as mudan<PERSON>s tomarem efeito", "components.Settings.tautulliApiKey": "Chave de <PERSON>", "components.Settings.validationUrlBaseTrailingSlash": "A URL base não pode terminar com uma barra", "components.Settings.validationUrlTrailingSlash": "A URL não pode terminar com uma barra", "components.StatusBadge.playonplex": "Reproduzir no Plex", "components.StatusChecker.reloadApp": "Recarregar {applicationTitle}", "components.TvDetails.Season.somethingwentwrong": "Algo deu errado enquanto os dados da temporada eram adquiridos.", "components.UserList.newplexsigninenabled": "A opção <strong>Ativar Novo Método de Início de Sessão do Plex</strong> está ativada. Utilizadores Plex com acesso à bibliotecas podem se autenticar sem que precisem serem importados.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Configurações de notificação via Pushbullet salvas com sucesso!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registe uma aplicação</ApplicationRegistrationLink> para uso com {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Chave do Utilizador ou Grupo", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Falha ao gravar configurações de notificação via Pushover.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filmes", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nova Frequência", "components.Settings.SettingsJobsCache.imagecachesize": "Tamanho Total do Cache", "components.Settings.SettingsMain.apikey": "Chave de <PERSON>", "components.Settings.SettingsMain.generalsettingsDescription": "Defina configurações globais e padrões para o Jellyseerr.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Espisódio} other {# Episódios}}", "components.Discover.CreateSlider.needresults": "Precisa ter ao menos 1 resultado.", "components.Discover.CreateSlider.nooptions": "Sem resultados.", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Data de lançamento Ascendente", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Data de Lançamento Descendente", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Classificação TMDB Ascendente", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Classificação TMDB Descendente", "components.Discover.DiscoverSliderEdit.remove": "Remover", "components.Discover.DiscoverTv.discovertv": "Séries", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Primeira Data de Exibição Ascendente", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Primeira Data de Exibição Descendente", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularidade Ascendente", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularidade Descendente", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Classificação TMDB Ascendente", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Séries", "components.Discover.FilterSlideover.clearfilters": "Limpar Filtros Ativos", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.firstAirDate": "Primeira Exibição", "components.Discover.FilterSlideover.from": "De", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "Palavras-chave", "components.Discover.FilterSlideover.originalLanguage": "Língua Original", "components.Discover.FilterSlideover.ratingText": "Avaliações entre {minValue} e {maxValue}", "components.Discover.FilterSlideover.runtime": "Duração", "components.Discover.FilterSlideover.runtimeText": "Duração de {minValue}-{maxValue} minutos", "components.Discover.FilterSlideover.streamingservices": "Serviços de Streaming", "components.Discover.FilterSlideover.studio": "Estúdio", "components.Discover.FilterSlideover.tmdbuserscore": "Avaliação de Utilizadores do TMDB", "components.Discover.FilterSlideover.to": "<PERSON><PERSON>", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "A Sua Lista Para Assistir do Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Adicionado <PERSON>", "components.Discover.createnewslider": "<PERSON><PERSON><PERSON> <PERSON>", "components.Discover.customizediscover": "Personalizar Exploração", "components.Discover.resettodefault": "Restaurar o Padrão", "components.Discover.tmdbstudio": "Estúdio no TMDB", "components.Discover.resetwarning": "Todos os deslizadores resetados para os valores padrões. <PERSON><PERSON> também deletará todos os deslizadores personalizados!", "components.Discover.tmdbtvgenre": "Gênero de Série no TMDB", "components.Discover.stopediting": "Cancelar Edição", "components.Discover.tmdbmoviegenre": "Gênero de Filmes no TMDB", "components.Discover.tmdbmoviekeyword": "Palavra-chave de Filme no TMDB", "components.Discover.tmdbnetwork": "Emissora no TMDB", "components.Discover.tmdbsearch": "Pesquisa no TMDB", "components.Discover.tmdbtvkeyword": "Palavra-chave de Série no TMDB", "components.Discover.tvgenres": "Gêneros de Séries", "components.Discover.updatefailed": "Algo deu errado com a mudança das configurações de descoberta personalizadas.", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {seasonNumber} Episódio {episodeNumber}", "components.Layout.Sidebar.browsemovies": "Filmes", "components.Layout.Sidebar.browsetv": "Séries", "components.RequestCard.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.searchStudios": "Pesquisar estúdios…", "components.Selector.showless": "<PERSON><PERSON>", "components.Selector.showmore": "<PERSON><PERSON>", "components.Selector.starttyping": "Comece a digitar para pesquisar.", "components.Settings.SettingsMain.applicationTitle": "Título da Aplicação", "components.Settings.SettingsMain.applicationurl": "URL da Aplicação", "components.Settings.SettingsMain.cacheImages": "Ativar <PERSON><PERSON> de Imagens", "components.Settings.SettingsMain.cacheImagesTip": "Armazenar em cache imagens de origem externa (requer uma quantidade significativa de espaço em disco)", "components.Settings.SettingsMain.general": "G<PERSON>", "components.Settings.SettingsMain.generalsettings": "Configurações Gerais", "components.Settings.SettingsMain.hideAvailable": "<PERSON><PERSON>lta<PERSON>", "components.Settings.SettingsMain.locale": "Idioma da Interface", "components.Settings.SettingsMain.originallanguage": "Idioma de Exploração", "components.Settings.SettingsMain.originallanguageTip": "Filtrar conteúdo pelo idioma original", "components.Settings.SettingsMain.partialRequestsEnabled": "Per<PERSON><PERSON>ões Parciais de Séries", "components.Settings.SettingsMain.toastApiKeyFailure": "Algo deu errado ao gerar a nova chave de API.", "components.Settings.SettingsMain.toastApiKeySuccess": "Nova chave de API gerada com sucesso!", "components.Settings.SettingsMain.toastSettingsFailure": "Algo de errado ao gravar configurações.", "components.Settings.SettingsMain.toastSettingsSuccess": "Configurações salvas com sucesso!", "components.Settings.SettingsMain.validationApplicationTitle": "Deve prover um título para a aplicação", "components.Settings.SettingsMain.validationApplicationUrl": "Deve prover uma URL válida", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "A URL não deve terminar com uma barra", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.TvDetails.Season.noepisodes": "Lista de episódios indisponível.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Solicitar automaticamente filmes na sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Solicitar automaticamente séries na sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink>", "components.UserProfile.plexwatchlist": "Lista Para Assistir do Plex", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addfail": "Falha ao criar novo carrossel.", "components.Discover.CreateSlider.addsuccess": "Novo deslizador criado e configurações de procura customizada salvas.", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON>", "components.Discover.CreateSlider.editfail": "Falha ao editar carrossel.", "components.Discover.CreateSlider.editsuccess": "Deslizador editado e configurações customizadas de procura salvas.", "components.Discover.CreateSlider.providetmdbgenreid": "Forneça um ID de gênero do TMDB", "components.Discover.CreateSlider.providetmdbkeywordid": "Forneça um ID de palavra-chave do TMDB", "components.Discover.CreateSlider.providetmdbnetwork": "Forneça o ID de emissora do TMDB", "components.Discover.CreateSlider.providetmdbsearch": "Forneça um dado para pesquisa", "components.Discover.CreateSlider.providetmdbstudio": "Forneça o ID do estúdio no TMDB", "components.Discover.CreateSlider.searchGenres": "<PERSON><PERSON><PERSON><PERSON>…", "components.Discover.CreateSlider.searchKeywords": "Pesquisar palavras-chave…", "components.Discover.CreateSlider.searchStudios": "Pesquisar estúdios…", "components.Discover.CreateSlider.slidernameplaceholder": "Nome do Carrossel", "components.Discover.CreateSlider.starttyping": "Comece a digitar para pesquisar.", "components.Discover.CreateSlider.validationDatarequired": "<PERSON>e informar um valor.", "components.Discover.CreateSlider.validationTitlerequired": "Deve prover um título.", "components.Discover.DiscoverSliderEdit.deletefail": "Falha ao remover carrossel.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Carrossel removido com sucesso.", "components.Discover.DiscoverSliderEdit.enable": "Alternar a Visibilidade", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Discover.DiscoverMovies.discovermovies": "Filmes", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularidade Ascendente", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularidade Descendente", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Classificação TMDB Descendente", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtro Ativo} other {# Filtros Ativos}}", "components.Discover.FilterSlideover.releaseDate": "Data de Lançamento", "components.Discover.moviegenres": "Gêneros de Filmes", "components.Discover.networks": "Emissora", "components.Discover.resetfailed": "Algo deu errado com o reset das configurações personalizadas de descoberta.", "components.Discover.resetsuccess": "As configurações de descoberta personalizadas foram resetadas com sucesso.", "components.Discover.studios": "Estúdios", "components.Discover.updatesuccess": "Configurações personalizadas de descoberta atualizadas.", "components.Selector.nooptions": "Sem resultados.", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Limpeza de Cache de Imagens", "components.Settings.SettingsJobsCache.imagecacheDescription": "Quando ativado nas configurações, o Overseer irá obter e guardar imagens de fontes externas pré configuradas. As imagens guardadas são salvas na sua pasta de configuração. Pode encontrar os ficheiros em <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.imagecachecount": "Imagens Armazenadas em Cache", "components.UserProfile.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.RequestModal.requestmovie4ktitle": "Solicitar Filme em 4K", "components.RequestModal.requestmovietitle": "Solicitar Filme", "components.RequestModal.requestseries4ktitle": "Solicitar Série em 4K", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Frequência Atual", "components.RequestModal.SearchByNameModal.nomatches": "Fomos incapazes de encontrar um série correspondente à serie solicitada.", "components.Discover.emptywatchlist": "Mídia adicionadas à sua <PlexWatchlistSupportLink>Lista Para Assistir do Plex</PlexWatchlistSupportLink> aparecerão aqui.", "components.RequestList.RequestItem.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Selector.searchGenres": "Selecione os gêneros…", "components.Selector.searchKeywords": "Pesquisar palavras-chave…", "component.BlacklistBlock.blacklistdate": "Data de inclusão na lista negra", "components.Blacklist.blacklistsettings": "Definições da lista negra", "component.BlacklistBlock.blacklistedby": "Colocado na lista negra por", "component.BlacklistModal.blacklisting": "Lista negra", "components.Blacklist.blacklistNotFoundError": "<strong>{title}2</strong> não está na lista negra.", "components.Blacklist.blacklistSettingsDescription": "Faça a gestão dos conteúdos multimédia colocados na lista negra.", "components.Blacklist.blacklistdate": "data", "components.Blacklist.blacklistedby": "{date} por {user}", "components.Blacklist.mediaName": "Nome"}