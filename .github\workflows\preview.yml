name: <PERSON><PERSON><PERSON>rr Preview

on:
  push:
    tags:
      - 'preview-*'

jobs:
  build_and_push:
    name: Build & Publish Docker Preview Images
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Get the version
        id: get_version
        run: echo "VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Log in to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          platforms: linux/amd64,linux/arm64
          push: true
          build-args: |
            COMMIT_TAG=${{ github.sha }}
            BUILD_VERSION=${{ steps.get_version.outputs.VERSION }}
            BUILD_DATE=${{ github.event.repository.updated_at }}
          tags: |
            fallenbagel/jellyseerr:${{ steps.get_version.outputs.VERSION }}
