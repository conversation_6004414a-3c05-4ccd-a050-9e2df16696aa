{"components.CollectionDetails.numberofmovies": "{count} Filmovi", "components.CollectionDetails.overview": "Pregled", "components.CollectionDetails.requestcollection4k": "Zahtjev za serijalom u 4K", "components.CollectionDetails.requestcollection": "Zahtjev za serijalom", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Filmovi", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Filmovi", "components.Discover.DiscoverNetwork.networkSeries": "{network} Serije", "components.Discover.DiscoverStudio.studioMovies": "{studio} Filmovi", "components.Discover.discover": "Otkrivanje", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} <PERSON><PERSON><PERSON>", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Serije", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Vaš Plex popis za gledanje", "components.Discover.MovieGenreList.moviegenres": "Films<PERSON>", "components.Discover.StudioSlider.studios": "Studio", "components.Discover.TvGenreList.seriesgenres": "Serijski Žanrovi", "components.Discover.TvGenreSlider.tvgenres": "Serijski Žanrovi", "components.Discover.plexwatchlist": "Vaš Plex popis za gledanje", "components.Discover.popularmovies": "Popularni Filmovi", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON>", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON>", "components.Discover.trending": "Popular<PERSON>", "components.IssueDetails.IssueComment.edit": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.deleteissue": "Izbriši problem", "components.IssueDetails.IssueDescription.description": "Opis", "components.IssueDetails.IssueDescription.edit": "Uredi opis", "components.IssueDetails.allepisodes": "Sve Epizode", "components.IssueDetails.closeissue": "Zatvori Problem", "components.IssueDetails.closeissueandcomment": "Zatvori s komentarom", "components.IssueDetails.commentplaceholder": "<PERSON><PERSON><PERSON> k<PERSON>…", "components.IssueDetails.comments": "Komentari", "components.IssueDetails.deleteissue": "Izbriši problem", "components.AirDateBadge.airedrelative": "Emitirano {relativeTime}", "components.AirDateBadge.airsrelative": "Emitiranje {relativeTime}", "components.AppDataWarning.dockerVolumeMissingDescription": "Navedeni <code>{appDataPath}</code> mapirani volumen nije ispravno podešen. Svi podaci će biti izbrisani kada se spremnik (container) zaustavi ili ponovno pokrene.", "components.Discover.DiscoverWatchlist.watchlist": "Plex popis za gledanje", "components.Discover.emptywatchlist": "Filmovi dodani na Vaš <PlexWatchlistSupportLink> popis za gledanje </PlexWatchlistSupportLink> pojaviti će se ovdje.", "components.Discover.MovieGenreSlider.moviegenres": "Films<PERSON>", "components.Discover.NetworkSlider.networks": "Striming servisi", "components.Discover.populartv": "<PERSON><PERSON>", "components.Discover.upcomingtv": "Nadolazeće Serije", "components.Discover.upcoming": "Nadolazeći Filmovi", "components.Discover.upcomingmovies": "Nadolazeći Filmovi", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {time}", "components.IssueDetails.IssueComment.areyousuredelete": "Jeste li sigurni da želite izbrisati ovaj komentar?", "components.IssueDetails.IssueComment.delete": "Izbriši Ko<PERSON>", "components.IssueDetails.IssueComment.postedby": "Objavljeno u {relativeTime} od korisnika {username}", "components.IssueDetails.IssueComment.validationComment": "Morate unijeti poruku", "components.IssueDetails.IssueComment.postedbyedited": "Objavljeno u {relativeTime} od koris<PERSON>a {username} (Uređeno)", "components.IssueDetails.allseasons": "Sve <PERSON>", "components.IssueDetails.episode": "Epizode {episodeNumber}", "components.IssueDetails.deleteissueconfirm": "Jeste li sigurni da želite izbrisati ovaj problem?", "components.IssueDetails.lastupdated": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.leavecomment": "Komentar", "components.IssueDetails.nocomments": "Bez komentara.", "components.IssueDetails.openedby": "#{issueId} otvoren u {relativeTime} od korisnka {username}", "components.IssueDetails.openin4karr": "Otvoren u 4K {arr}", "components.IssueDetails.openinarr": "Otvoren u {arr}", "components.IssueDetails.toasteditdescriptionfailed": "Nešto nije u redu prilikom uređivanja opisa problema.", "components.IssueModal.CreateIssueModal.allepisodes": "Sve epizode", "components.IssueDetails.toastissuedeleted": "Problem je uspješno izbrisan!", "components.IssueDetails.unknownissuetype": "Nepoznato", "components.IssueList.issues": "Problem", "components.IssueList.IssueItem.openeduserdate": "{date} od kori<PERSON>a {user}", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON> sezone", "components.IssueModal.issueOther": "Ostalo", "components.IssueModal.issueAudio": "Zvuk", "components.IssueModal.issueSubtitles": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.issueVideo": "Video", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.UserDropdown.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamstable": "<PERSON><PERSON><PERSON>rr stablina verzija", "components.Login.password": "Lozinka", "components.ManageSlideOver.openarr4k": "Otvori 4K u {arr}u", "components.ManageSlideOver.pastdays": "Proteklih {days, number} dana", "components.Login.signinwithplex": "Koristite svoj Plex račun", "components.ManageSlideOver.movie": "film", "components.Login.validationemailrequired": "Morate unijeti valjanu adresu e-pošte", "components.ManageSlideOver.manageModalRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Ovo će nepovratno ukloniti sve podatke za ovaj {mediaType}, uključujući sve zahtjeve. Ako ova stavka postoji u vašoj {mediaServerName} biblioteci, informacije o medijima ponovno će se stvoriti tijekom sljedećeg skeniranja.", "components.ManageSlideOver.manageModalMedia4k": "4K Mediji", "components.ManageSlideOver.manageModalNoRequests": "<PERSON><PERSON>.", "components.ManageSlideOver.manageModalMedia": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalTitle": "Upravljanje {mediaType}", "components.ManageSlideOver.mark4kavailable": "Označi kao dostupno u 4K", "components.MovieDetails.originaltitle": "Izvorni naslov", "components.MovieDetails.overview": "Pregled", "components.ManageSlideOver.openarr": "Ot<PERSON>i u {arr}u", "components.MovieDetails.cast": "<PERSON><PERSON><PERSON>", "components.MovieDetails.budget": "Proračun", "components.ManageSlideOver.opentautulli": "Otvori u Tautulliju", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON> vi<PERSON>e", "components.MovieDetails.markavailable": "Označ<PERSON> kao dos<PERSON>", "components.ManageSlideOver.tvshow": "serije", "components.MovieDetails.productioncountries": "{countryCount, plural, one {Zem<PERSON><PERSON> produkcije} few {Zemlje produkcije} other {Zemlje produkcije}}", "components.MovieDetails.managemovie": "Upravljaj filmom", "components.MovieDetails.overviewunavailable": "<PERSON><PERSON> ne<PERSON>.", "components.MovieDetails.reportissue": "P<PERSON><PERSON><PERSON> problem", "components.MovieDetails.revenue": "Prihod", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes ocjena publike", "components.MovieDetails.showless": "Prika<PERSON><PERSON> manje", "components.MovieDetails.showmore": "Prikaži više", "components.MovieDetails.similar": "Slični naslovi", "components.MovieDetails.streamingproviders": "Trenutačno se prikacuje na", "components.NotificationTypeSelector.issuecommentDescription": "Pošaljite obavijest kada problemi dobiju nove komentare.", "components.NotificationTypeSelector.issueresolved": "Problem riješen", "components.NotificationTypeSelector.issuereopened": "Problem ponovno otvoren", "components.NotificationTypeSelector.issueresolvedDescription": "Pošalji obavijest kada se <PERSON> riješi.", "components.NotificationTypeSelector.issuereopenedDescription": "Pošalji obavijest kada se <PERSON> ponovno otvori.", "components.NotificationTypeSelector.mediaAutoApproved": "Automatsko odobravanje zahtjeva", "components.IssueDetails.issuepagetitle": "Problem", "components.IssueDetails.issuetype": "Vrsta", "components.IssueDetails.play4konplex": "Reproduciraj u 4K na {mediaServerName}u", "components.IssueDetails.playonplex": "Reproduciraj na {mediaServerName}u", "components.IssueDetails.problemseason": "Zahvaćena se<PERSON>a", "components.IssueDetails.problemepisode": "Zahvaćena epizoda", "components.IssueDetails.reopenissue": "Ponovo otvori problem", "components.IssueDetails.reopenissueandcomment": "Ponovo otvori s komentarom", "components.IssueDetails.season": "Sezona {seasonNumber}", "components.IssueDetails.toasteditdescriptionsuccess": "Opis problema je uspješno uređen!", "components.IssueDetails.toastissuedeletefailed": "Nešto nije u redu prilikom brisanja problema.", "components.IssueDetails.toaststatusupdated": "Status problema je uspješno ažuriran!", "components.IssueDetails.toaststatusupdatefailed": "Nešto nije u redu prilikom ažuriranja statusa problema.", "components.IssueList.IssueItem.issuestatus": "Status", "components.IssueList.IssueItem.issuetype": "Vrsta", "components.IssueList.IssueItem.opened": "Otvoren", "components.IssueList.IssueItem.problemepisode": "Zahvaćene Epizode", "components.IssueList.IssueItem.unknownissuetype": "Nepoznato", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Epizoda} other {Epizode}}", "components.IssueList.IssueItem.viewissue": "Pogledaj problem", "components.IssueList.showallissues": "Prikaži sve probleme", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueList.sortModified": "Zadnje promjene", "components.IssueModal.CreateIssueModal.episode": "Epizoda {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemepisode": "Zahvaćene epizode", "components.IssueModal.CreateIssueModal.problemseason": "Zahvaćene sezone", "components.IssueModal.CreateIssueModal.providedetail": "Navedite detaljno objašnjenje problema na koji ste na<PERSON>šli.", "components.IssueModal.CreateIssueModal.reportissue": "Prijavite problem", "components.IssueModal.CreateIssueModal.season": "Sezona {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Poša<PERSON>ji problem", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Nešto nije u redu prilikom slanja problema.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Problem prijav<PERSON><PERSON><PERSON> za <strong>{title}</strong> je usp<PERSON><PERSON><PERSON> predan!", "components.IssueModal.CreateIssueModal.toastviewissue": "Pogledaj problem", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Morate unijeti opis", "components.IssueModal.CreateIssueModal.whatswrong": "Što nije u redu?", "components.LanguageSelector.languageServerDefault": "Default ({language})", "components.LanguageSelector.originalLanguageDefault": "<PERSON><PERSON> jez<PERSON>", "components.Layout.LanguagePicker.displaylanguage": "<PERSON><PERSON><PERSON>", "components.Layout.SearchInput.searchPlaceholder": "Pretražite filmove i TV", "components.Layout.Sidebar.dashboard": "Otkrivanje", "components.Layout.Sidebar.issues": "<PERSON>i", "components.Layout.Sidebar.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Postavke", "components.Layout.Sidebar.users": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Zahtjevi za serije", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Zahtjevi za filmove", "components.Layout.UserDropdown.settings": "Postavke", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON><PERSON> se", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.Login.email": "Adresa e-pošte", "components.Login.forgotpassword": "Zaboravljena lozinka?", "components.Login.loginerror": "Nešto nije u redu prilikom pokušaja prijave.", "components.Login.signin": "Prijavite se", "components.Login.signingin": "<PERSON><PERSON><PERSON><PERSON>…", "components.Layout.VersionStatus.commitsbehind": "", "components.Login.signinheader": "Prijavite se za nastavak", "components.Login.signinwithoverseerr": "Koristite svoj {applicationTitle} račun", "components.Login.validationpasswordrequired": "Morate unijeti lozinku", "components.ManageSlideOver.alltime": "Cijelo vrijeme", "components.ManageSlideOver.downloadstatus": "Preuziman<PERSON>", "components.ManageSlideOver.manageModalAdvanced": "Napredna", "components.ManageSlideOver.manageModalClearMedia": "Obriš<PERSON> pod<PERSON>", "components.ManageSlideOver.manageModalIssues": "Otvoreni problemi", "components.ManageSlideOver.markallseasons4kavailable": "Označi sve sezone kao dostupne u 4K", "components.ManageSlideOver.markallseasonsavailable": "Označi sve sezone kao dostu<PERSON>ne", "components.ManageSlideOver.markavailable": "Označ<PERSON> kao dos<PERSON>", "components.ManageSlideOver.playedby": "Reproducirano od", "components.ManageSlideOver.plays": "<strong>{playCount, broj}</strong> {playCount, plural, one {reproducirano} other {reproducirano}}", "components.MovieDetails.MovieCast.fullcast": "Glumačka postava", "components.MovieDetails.digitalrelease": "Digitalno izdanje", "components.MovieDetails.mark4kavailable": "Označi kao dostupno u 4K", "components.MovieDetails.originallanguage": "Izvorni jezik", "components.MovieDetails.MovieCrew.fullcrew": "Filmska postava", "components.MovieDetails.physicalrelease": "Fizičko izdanje", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}}", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatometer", "components.MovieDetails.runtime": "{minutes} minute", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {<PERSON><PERSON>ji}}", "components.MovieDetails.theatricalrelease": "Izdanje u kinima", "components.MovieDetails.tmdbuserscore": "Ocjena korisnika TMDB-a", "components.MovieDetails.viewfullcrew": "Pogledajte cijelu filmsku postavu", "components.MovieDetails.watchtrailer": "Pogledajte najavu", "components.NotificationTypeSelector.adminissuecommentDescription": "Primite obavijest kada drugi korisnici komentiraju probleme.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Primite obavijest kada problem ponovno otvore drugi korisnici.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Primite obavijest kada drugi korisnici riješe probleme.", "components.NotificationTypeSelector.issuecomment": "<PERSON><PERSON><PERSON><PERSON> problem", "components.NotificationTypeSelector.issuecreated": "Problem prijavljen", "components.NotificationTypeSelector.issuecreatedDescription": "Pošalji obavijest kada se <PERSON> prijavi.", "components.NotificationTypeSelector.userissueresolvedDescription": "Primite obavijest kada problemi koje ste prijavili budu rije<PERSON>.", "components.NotificationTypeSelector.mediaavailableDescription": "Slanje obavijesti kada medijski zahtjevi postanu dostupni.", "components.NotificationTypeSelector.mediadeclinedDescription": "Slanje obavijesti kada su medijski zahtjevi odbijeni.", "components.NotificationTypeSelector.mediarequested": "Zahtjev čeka odobrenje", "components.NotificationTypeSelector.mediarequestedDescription": "Slanje obavijesti kada korisnici pošalju nove medijske zahtjeve koji zahtijevaju odobrenje.", "components.NotificationTypeSelector.mediaautorequested": "Zahtjev je automatski poslan", "components.NotificationTypeSelector.mediaavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediafailedDescription": "Slanje obavijesti kada se medijski zahtjevi ne uspiju dodati u Radarr ili Sonarr.", "components.NotificationTypeSelector.userissuecommentDescription": "Primite obavijest kada problemi koje ste prijavili dobiju nove komentare.", "components.PermissionEdit.autoapprove4kSeries": "Automatsko odobravanje serija u 4K", "components.NotificationTypeSelector.usermediafailedDescription": "Primite obavijest kada se medijski zahtjevi ne uspiju dodati u Radarr ili Sonarr.", "components.NotificationTypeSelector.usermediarequestedDescription": "Primite obavijest kada drugi korisnici pošalju nove medijske zahtjeve koji zahtijevaju odobrenje.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Primite obavijest kada drugi korisnici pošalju nove medijske zahtjeve koji se automatski odobravaju.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Primite obavijest kada vaši medijski zahtjevi budu odbijeni.", "components.PermissionEdit.adminDescription": "Potpuni administratorski pristup. Zaobilazi sve druge provjere dopuštenja.", "components.PermissionEdit.advancedrequest": "Napred<PERSON> z<PERSON>je<PERSON>", "components.PermissionEdit.autoapprove4k": "Automatsko odobravanje 4K", "components.PermissionEdit.autoapproveSeriesDescription": "Dozvolite automatsko odobravanje zahtjeva za serijale koji nisu u 4K.", "components.PermissionEdit.autoapprove4kMoviesDescription": "Dozvolite automatsko odobravanje zahtjeva za filmove u 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Dozvolite automatsko odobravanje zahtjeva za serije u 4K.", "components.QuotaSelector.days": "{count, plural, one {danu} other {danu}}", "components.QuotaSelector.movies": "{count, plural, one {film} other {filmova}}", "components.PermissionEdit.autoapproveMoviesDescription": "Dozvolite automatsko odobravanje zahtjeva za filmove koji nisu u 4K.", "components.RequestButton.approve4krequests": "<PERSON><PERSON><PERSON><PERSON> {requestCount, plural, one {4K Zahtjev} other {{requestCount} 4K Zahtjeve}}", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {film} other {filmova}}", "components.RequestButton.approverequests": "<PERSON><PERSON><PERSON><PERSON> {requestCount, plural, one {<PERSON><PERSON><PERSON><PERSON>} other {{requestCount} Zahtjeve}}", "components.QuotaSelector.seasons": "{count, plural, one {sezona} other {sezone}}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON>} other {Sezone}}", "components.RequestButton.decline4krequests": "Odbiti {requestCount, plural, one {4K Zahtjev} other {{requestCount} 4K Zahtjeve}}", "components.RequestModal.QuotaDisplay.requiredquotaUser": "<PERSON><PERSON>j korisnik treba imati jo<PERSON> barem <strong>{seasons}</strong> {seasons, plural, one {jedan zahtjev za sezonu} other {nekoliko zahtjeva za sezone}} kako bi mogao preadti zahtjev za ovu seriju.", "components.RequestModal.QuotaDisplay.requiredquota": "Morate imati j<PERSON><PERSON> barem <strong>{seasons}</strong> {seasons, plural, one {jedan zahtjev za sezonu} other {nekoliko zahtjeva za sezone}} kako bi mogli preadti zahtjev za ovu seriju.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {sezona} other {sezona/e}}", "components.RequestModal.requestmovies": "{count} {count, plural, one {Zah<PERSON>je<PERSON> za film} other {Zah<PERSON><PERSON><PERSON> za filmove}}", "components.RequestModal.requestmovies4k": "{count} {count, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON> za film} other {<PERSON><PERSON><PERSON><PERSON><PERSON> za filmove}} u 4K", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Slanje obavijesti kada korisnici pošalju novi medijski zahtjev koji se automatski odobrava.", "components.NotificationTypeSelector.mediaapproved": "Zahtjev odobren", "components.NotificationTypeSelector.mediaapprovedDescription": "Slanje obavijesti kada se medijski zahtjev ručno odobri.", "components.NotificationTypeSelector.mediaautorequestedDescription": "Primite obavijest kada se automatski pošalje novi medijski zahtjevi za stavke na vašoj Plex listi koju pratite.", "components.NotificationTypeSelector.mediadeclined": "Zahtjev je odbijen", "components.NotificationTypeSelector.mediafailed": "Obrada zahtjeva nije us<PERSON>la", "components.NotificationTypeSelector.notificationTypes": "Vrste obavijesti", "components.NotificationTypeSelector.userissuecreatedDescription": "Primite obavijest kada drugi korisnici prijave probleme.", "components.NotificationTypeSelector.userissuereopenedDescription": "Primite obavijest kada se problemi koje ste prijavili ponovno otvore.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Primite obavijest kada vaši zahtjevi za medije budu odo<PERSON>ni.", "components.NotificationTypeSelector.usermediaavailableDescription": "Primite obavijest kada vaši medijski zahtjevi postanu dostupni.", "components.PermissionEdit.admin": "Administrator", "components.PermissionEdit.advancedrequestDescription": "Dodajte dozvolu za izmjenu naprednih opcija zahtjeva za medije.", "components.PermissionEdit.autoapprove": "Automatsko odobravanje", "components.PermissionEdit.autoapprove4kMovies": "Automatsko odobravanje 4K filmova", "components.PermissionEdit.autoapprove4kDescription": "Dozvolite automatsko odobravanje svih zahtjeva za 4K medije.", "components.PermissionEdit.autoapproveDescription": "Dozvolite automatsko odobravanje svih zahtjeva koji nisu u 4K mediji.", "components.PermissionEdit.autoapproveMovies": "Automatsko odobravanje filmova", "components.PermissionEdit.autoapproveSeries": "Automatsko odobravanje serija", "components.RequestButton.declinerequests": "Odbij {requestCount, plural, one {zahtjev} few {{requestCount} zahtjeva} other {{requestCount} zahtjeva}}", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {0} other {<strong>#</strong>}} {type} {remaining, plural, one {zahtjev preostao} few {zahtjeva preostala} other {zahtjeva preostalo}}", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktualna učestalost", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nova učestalost", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "{jobScheduleHours, plural, one {<PERSON><PERSON>ki sat} few {Svaka {jobScheduleHours} sata} other {Svakih {jobScheduleHours} sati}}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Dogodila se greška prilikom spremanja zadatka.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Zadatak uspješno promijenjen!", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sinkronizacija Plex popisa gledanja", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON> sada", "components.Settings.SettingsJobsCache.unknownJob": "Nepoznat zadatak", "components.Settings.SettingsLogs.extraData": "Dodatni podaci", "components.Settings.SettingsLogs.copiedLogMessage": "Poruka zapisnika je kopirana u međuspremnik.", "components.Settings.SettingsLogs.filterDebug": "Otklanjanje grešaka", "components.Settings.SettingsLogs.filterError": "Greška", "components.Settings.SettingsLogs.filterInfo": "Informacije", "components.Settings.SettingsLogs.filterWarn": "Upozorenje", "components.Settings.SettingsLogs.label": "Etiketa", "components.Settings.SettingsLogs.level": "Važnost", "components.Settings.SettingsLogs.logDetails": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.logs": "Zapisi", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.showall": "Prikaži sve zapise", "components.Settings.SettingsLogs.time": "Vremenska oznaka", "components.Settings.SettingsLogs.viewdetails": "P<PERSON>z <PERSON>", "components.Settings.SettingsUsers.defaultPermissions": "Zadane dozvole", "components.Settings.SettingsUsers.defaultPermissionsTip": "Početne dozvole dodijeljene novim korisnicima", "components.Settings.SettingsUsers.localLogin": "Aktiviraj lo<PERSON> prijavu", "components.Settings.SettingsUsers.localLoginTip": "Dozvoli korisnicima da se prijave koristeći svoju e-mail adresu i lozinku, umjesto Plex OAutha", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globalno ograničenje zahtjeva za filmove", "components.Settings.SettingsUsers.toastSettingsSuccess": "Korisničke postavke su uspješno spremljene!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globalno ograničenje zahtjeva za serije", "components.Settings.SettingsUsers.userSettings": "Korisničke postavke", "components.Settings.SettingsUsers.userSettingsDescription": "Konfiguriraj globalne i zadane korisničke postavke.", "components.Settings.SettingsUsers.users": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.add": "Dodaj poslužitelj", "components.Settings.SonarrModal.animeTags": "<PERSON>ime oz<PERSON>ke", "components.Settings.SonarrModal.animelanguageprofile": "Anime profil jezika", "components.Settings.SonarrModal.animequalityprofile": "Anime profil kvalitete", "components.Settings.SonarrModal.animerootfolder": "<PERSON>ime po<PERSON>na mapa", "components.Settings.SonarrModal.apiKey": "API ključ", "components.Settings.SonarrModal.baseUrl": "Osnovni URL", "components.Settings.SonarrModal.create4ksonarr": "Dodaj novi 4K Sonarr poslužitelj", "components.Settings.SonarrModal.createsonarr": "Dodaj novi Sonarr p<PERSON>lj", "components.Settings.SonarrModal.default4kserver": "Zadani 4K poslužitelj", "components.Settings.SonarrModal.defaultserver": "Zadani poslužitelj", "components.Settings.SonarrModal.edit4ksonarr": "Uredi 4K Sonarr poslužitelj", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON><PERSON> Sonarr p<PERSON>lj", "components.Settings.SonarrModal.enableSearch": "Aktiviraj automatsku pretragu", "components.Settings.SonarrModal.externalUrl": "Eksterni URL", "components.Settings.SonarrModal.hostname": "Ime računala ili IP adresa", "components.Settings.SonarrModal.languageprofile": "<PERSON><PERSON> jez<PERSON>", "components.Settings.SonarrModal.loadingTags": "Učitavanje <PERSON> …", "components.Settings.SonarrModal.loadingprofiles": "Učitavanje profila kvalitete …", "components.Settings.SonarrModal.loadingrootfolders": "Učitavanje početnih mapa …", "components.Settings.SonarrModal.notagoptions": "<PERSON><PERSON>.", "components.Settings.SonarrModal.port": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.rootfolder": "Početna mapa", "components.Settings.SonarrModal.qualityprofile": "Profil k<PERSON>itete", "components.Settings.SonarrModal.seasonfolders": "Mape za sezone", "components.Settings.SonarrModal.selectLanguageProfile": "Oda<PERSON>i profil jezika", "components.Settings.SonarrModal.selectQualityProfile": "Odaberi profil kvalitete", "components.Settings.SonarrModal.selectRootFolder": "Odaberi početnu mapu", "components.Settings.SonarrModal.selecttags": "Odaberi oz<PERSON>ke", "components.Settings.SonarrModal.server4k": "4K poslužitelj", "components.Settings.SonarrModal.servername": "<PERSON><PERSON>", "components.Settings.SonarrModal.ssl": "Koristi SSL", "components.Settings.SonarrModal.tags": "Oznake", "components.Settings.SonarrModal.testFirstQualityProfiles": "Provjeri vezu za učitavanje profila kvalitete", "components.Settings.SonarrModal.testFirstRootFolders": "Provjeri vezu za učitavanje početnih mapa", "components.Settings.SonarrModal.testFirstTags": "Provjeri vezu za učitavanje oznaka", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Sonarr veza je uspješno uspostavljena!", "components.Settings.SonarrModal.validationApiKeyRequired": "Moraš <PERSON>i valjani API ključ", "components.Settings.SonarrModal.validationApplicationUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Osnovni URL mora početi s kosom crtom", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Osnovni URL ne smije završiti s kosom crtom", "components.Settings.SonarrModal.validationHostnameRequired": "<PERSON><PERSON>š <PERSON>i valjano ime računala ili IP adresu", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON><PERSON><PERSON> profil jezika", "components.Settings.SonarrModal.validationNameRequired": "<PERSON><PERSON><PERSON> ime poslužitelja", "components.Settings.SonarrModal.validationPortRequired": "<PERSON><PERSON><PERSON>i valjani broj priključ<PERSON>", "components.Settings.SonarrModal.validationProfileRequired": "<PERSON><PERSON>š odabrati profil kvalitete", "components.Settings.SonarrModal.validationRootFolderRequired": "<PERSON><PERSON>š odabrati početnu mapu", "components.Settings.activeProfile": "Aktivni profil", "components.Settings.addradarr": "Dodaj Radarr poslužitelj", "components.Settings.address": "<PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON>", "components.Settings.copied": "API ključ je kopiran u međuspremnik.", "components.Settings.currentlibrary": "Aktualna biblioteka: {name}", "components.Settings.default": "Zadano", "components.Settings.default4k": "Zadano 4K", "components.Settings.deleteServer": "Izbriši {serverType} poslužitelj", "components.Settings.deleteserverconfirm": "Stvarno želiš izbrisati ovaj poslužitelj?", "components.Settings.email": "E-mail", "components.Settings.enablessl": "Koristi SSL", "components.Settings.experimentalTooltip": "Aktiviranjem ove postavke može doći do neočekivanog ponašanja programa", "components.Settings.externalUrl": "Eksterni URL", "components.Settings.hostname": "Ime računala ili IP adresa", "components.Settings.is4k": "4K", "components.Settings.librariesRemaining": "Preostale biblioteke: {count}", "components.Settings.menuAbout": "Informacije", "components.Settings.menuGeneralSettings": "<PERSON><PERSON><PERSON>", "components.Settings.menuJobs": "Zadaci i predmemorija", "components.Settings.menuLogs": "Zapisi", "components.Settings.menuNotifications": "Obavijesti", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuUsers": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.noDefault4kServer": "4K {serverType} poslužitelj mora biti označen kao zadani kako bi se korisnicima omogućilo slanje zahtjeva za 4K {mediaType}.", "components.Settings.noDefaultServer": "<PERSON><PERSON> j<PERSON> {serverType} poslužitelj mora biti označen kao zadani kako bi se zahtjevi za {mediaType} mogli obraditi.", "components.Settings.notificationAgentSettingsDescription": "Konfiguriraj i aktiviraj agente obavijesti.", "components.Settings.notifications": "Obavijesti", "components.Settings.notificationsettings": "Postavke obavijesti", "components.Settings.plex": "Plex", "components.Settings.plexlibraries": "Plex biblioteke", "components.Settings.plexsettings": "Plex postavke", "components.Settings.plexsettingsDescription": "Konfiguriraj postavke za tvoj Plex poslužitelj. <PERSON><PERSON><PERSON>rr skenira tvoje Plex biblioteke kako bi utvrdio dostupnost sadržaja.", "components.Settings.port": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON>rr se mora ponovo pokrenuti kako bi promjene ove postavke stupile na snagu", "components.Settings.scan": "Sinkronizraj biblioteke", "components.Settings.scanning": "Sinkronizacija …", "components.Settings.serverLocal": "lokalni", "components.Settings.serverSecure": "<PERSON>gu<PERSON>", "components.Settings.serverpreset": "Poslužitelj", "components.Settings.serverpresetRefreshing": "Dohvaćanje poslužitelja …", "components.Settings.serviceSettingsDescription": "<PERSON><PERSON><PERSON> donfiguriraj svoje {serverType} poslužitelje. <PERSON><PERSON><PERSON><PERSON> povezati više {serverType} pos<PERSON><PERSON><PERSON><PERSON><PERSON>, ali samo dva od njih mogu biti označena kao zadana (jedan ne-4K i jedan 4K). Administratori mogu promijeniti poslužitelj koji se koristi za obradu novih zahtjeva prije odobrenja.", "components.Settings.serverpresetLoad": "Pritisni gumb za učitavanje dostupnih polsužitelja", "components.Settings.serverpresetManualMessage": "Ručna konfiguracija", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.settingUpPlexDescription": "Za postavljanje Plexa unesi detalje ručno ili odaberi poslužitelj dohvaćen s <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Pritisni gumb desno od padajućeg izbornika za dohvaćanje popisa dostupnih poslužitelja.", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.ssl": "SSL", "components.Settings.tautulliApiKey": "API ključ", "components.Settings.tautulliSettingsDescription": "Opcionalno konfiguriraj postavke za tvoj Tautulli poslužitelj. Jellyseerr dohvaća podatke o povijesti gledanja za tvoje Plex medije od Tautullija.", "components.Settings.toastPlexConnecting": "Pokušaj povezivanja na Plex …", "components.Settings.toastPlexConnectingFailure": "Neuspjelo povezivanje na Plex.", "components.Settings.toastPlexConnectingSuccess": "Plex veza je uspješ<PERSON> uspostavljena!", "components.Settings.toastPlexRefresh": "Dohvaćanje popisa poslužitelja od Plexa …", "components.Settings.toastPlexRefreshFailure": "Neuspjelo dohvaćanje popisa Plex poslužitelja.", "components.Settings.toastPlexRefreshSuccess": "Popis Plex poslužitelja je uspješno dohvaćen!", "components.Settings.urlBase": "Osnovni URL", "components.Settings.validationApiKey": "Moraš <PERSON>i valjani API ključ", "components.Settings.webpush": "Web Push", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.finish": "Završi <PERSON>", "components.Setup.welcome": "<PERSON><PERSON><PERSON><PERSON> dobrodošlica", "components.Setup.finishing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Setup.setup": "Postavljanje", "components.Setup.signinMessage": "Za postavljanje se prijavi s tvojim Plex računom", "components.StatusBadge.managemedia": "Upravljaj {mediaType}", "components.StatusBadge.openinarr": "O<PERSON><PERSON>i u {arr}", "components.StatusBadge.playonplex": "Reproduciraj na Plexu", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} a<PERSON><PERSON><PERSON>", "components.TvDetails.TvCast.fullseriescast": "Svi glumci serije", "components.TvDetails.TvCrew.fullseriescrew": "Svi suradnici serije", "components.TvDetails.cast": "<PERSON><PERSON><PERSON>", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# epizoda} few {# epizode} other {# epizoda}}", "components.TvDetails.seasons": "{seasonCount, plural, one {# sezona} few {# sezone} other {# sezona}}", "components.TvDetails.seasonstitle": "Sezone", "components.UserList.creating": "<PERSON><PERSON><PERSON><PERSON> …", "components.UserList.edituser": "Uredi korisničke dozvole", "components.UserList.email": "E-mail adresa", "components.UserList.importedfromplex": "<strong>{userCount}</strong> Plex {userCount, plural, one {korisnik je uspješno uvezen} few {korisnika su uspješno uvezena} other {korisnika su uspješno uvezeni}}!", "components.UserList.importfromplex": "Uvezi Plex korisnike", "components.UserList.localuser": "Lokalni korisnik", "components.UserList.newplexsigninenabled": "Postavka <strong>Aktiviraj novu Plex prijavu</strong> je trenutačno dektivirana. Plex korisnici s pristupom biblioteci se ne moraju uvesti da bi se mogli prijaviti.", "components.UserList.owner": "Vlasnik", "components.UserList.password": "Lozinka", "components.UserList.passwordinfodescription": "Konfiguriraj URL programa i aktiviraj e-mail obavijesti za dozvoljavanje automatskog generiranja lozinke.", "components.UserList.plexuser": "Plex korisnik", "components.UserList.role": "<PERSON><PERSON><PERSON>", "components.UserList.sortCreated": "Datum p<PERSON>", "components.UserList.sortDisplayName": "Prikazano ime", "components.UserList.sortRequests": "<PERSON><PERSON><PERSON>", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.user": "<PERSON><PERSON><PERSON>", "components.UserList.usercreatedfailed": "Dogodila se greška prilikom stvaranja korisnika.", "components.UserList.usercreatedfailedexisting": "Navedenu e-mail adresu koristi već jedan drugi korisnik.", "components.UserList.userdeleteerror": "Dogodila se greška prilikom brisanja korisnika.", "components.UserProfile.ProfileHeader.joindate": "Datum pridruž<PERSON>: {joindate}", "components.UserProfile.ProfileHeader.userid": "ID korisnika: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Vrsta računa", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrator", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Korisnički ID na Discordu", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>Višeznamenkasti ID broj</FindDiscordIdLink> povezan s tvojim korisničkim računom na Discordu", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Prikazano ime", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Mijenjanje globalnih ograničenja", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Zadano ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtriraj sadržaj po izvornom jeziku", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Jezik otkrivanja", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex korisnik", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Automatsko zahtijevanje filmova", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Automatski zatraži filmove na tvom <PlexWatchlistSupportLink>Plex popisu gledanja</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Automatsko zahtijevanje serija", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Ograničenje zahtjeva za serije", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filt<PERSON>raj sadrž<PERSON> prema regionalnoj dostupnosti", "components.UserProfile.UserSettings.UserGeneralSettings.role": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Neuspjelo spremanje e-mail postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "E-mail postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Obavijesti", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Postavke za obavijesti", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Token za pristup", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Pushbullet postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "API token programa", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Registriraj jedan program</ApplicationRegistrationLink> za korištenje s {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Pošalji tiho", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Pošalji obavijesti bez zvuka", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat ID", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Započni chat</TelegramBotLink>, dodaj <GetIdBotLink>@get_id_bot</GetIdBotLink> zadaj naredbu <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Neuspjelo spremanje Telegram postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON>kt<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Ovaj korisnički račun trenutačno nema postavljenu lozinku. Dolje konfiguriraj lozinku kako bi se ovom računu omogućila prijava kao „lokalni korisnik.”", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Tvoj račun trenutačno nema postavljenu lozinku. Konfiguriraj lozinku za omogućavanje prijave kao „lokalni korisnik” koristeći tvoju e-mail adresu.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Nemaš dozvole za mijenjenje lozinke ovog korisnika.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Lozinka", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Dogodila se greška prilikom spremanja lozinke.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Dogodila se greška prilikom spremanja lozinke. Je li tvoja aktualna lozinka ispravno upisana?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Lozinka je uspješno spremljena!", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Moraš navesti tvoju aktualnu lozinku", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "<PERSON><PERSON>š <PERSON>sti novu lozinku", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Lozinka je prekratka; mora sadržati barem 8 znakova", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Dogodila se greška prilikom spremanja postavki.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Dozvole su uspješno spremljene!", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Ne možeš promijeniti vlastite dozvole.", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuNotifications": "Obavijesti", "components.UserProfile.UserSettings.menuPermissions": "Dozvole", "components.UserProfile.UserSettings.unauthorizedDescription": "Nemaš dozvole za mijenjenje postavki ovog korisnika.", "components.UserProfile.emptywatchlist": "Mediji koji su dodani u tvoj <PlexWatchlistSupportLink>Plex popis gledanja</PlexWatchlistSupportLink> pojavit će se ovdje.", "components.UserProfile.movierequests": "Zahtjevi za filmove", "components.UserProfile.pastdays": "{type} (zadnjih {days} dana)", "components.UserProfile.plexwatchlist": "Plex popis gledanja", "components.UserProfile.seriesrequest": "Zahtjevi za serije", "components.UserProfile.totalrequests": "Ukupno zahtjeva", "components.UserProfile.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "Do<PERSON><PERSON><PERSON>", "i18n.back": "Natrag", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "<PERSON><PERSON><PERSON>", "i18n.experimental": "Eksperimentalno", "i18n.pending": "Na čekanju", "i18n.previous": "Prethodno", "i18n.processing": "Obrada", "i18n.request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.request4k": "Zatraži u 4K", "i18n.requested": "Zatraženo", "i18n.resultsperpage": "Prikaži {pageSize} rezultata po stranici", "i18n.retrying": "Pokušaj se ponavlja …", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> …", "i18n.resolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.save": "S<PERSON><PERSON>i promje<PERSON>", "i18n.saving": "S<PERSON><PERSON>an<PERSON> …", "i18n.settings": "Postavke", "i18n.showingresults": "Prikaz <strong>{from}</strong> do <strong>{to}</strong> od <strong>{total}</strong> rezultata", "i18n.test": "<PERSON><PERSON><PERSON><PERSON>", "i18n.testing": "<PERSON><PERSON><PERSON><PERSON><PERSON> …", "i18n.tvshow": "Serije", "i18n.tvshows": "Serije", "i18n.unavailable": "Nedostup<PERSON>", "i18n.usersettings": "Korisničke postavke", "i18n.view": "Prikaz", "pages.errormessagewithcode": "{statusCode} – {error}", "pages.internalservererror": "Interna greška poslužitelja", "pages.pagenotfound": "Stranica nije pronađena", "pages.returnHome": "Vrati se na početnu stranicu", "pages.serviceunavailable": "<PERSON><PERSON><PERSON> nije dos<PERSON>na", "pages.somethingwentwrong": "Dogodila se greška", "components.PermissionEdit.requestTv": "Zahtjev za serijama", "components.PermissionEdit.users": "Upravljanje k<PERSON>", "components.PermissionEdit.viewissues": "Prikaz problema", "components.PermissionEdit.viewrecent": "Prikaz nedavno dodanih", "components.PermissionEdit.viewrequests": "P<PERSON><PERSON>", "components.PermissionEdit.viewwatchlists": "Prikaži Plex popise gledanja", "components.PersonDetails.alsoknownas": "Poznat/a i kao: {names}", "components.PersonDetails.appearsin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PersonDetails.ascharacter": "kao {character}", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.unlimited": "Neo<PERSON><PERSON><PERSON><PERSON>", "components.RegionSelector.regionDefault": "Sve regije", "components.RegionSelector.regionServerDefault": "Zadano ({region})", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON>", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.delete": "Izbriši zahtjev", "components.RequestBlock.edit": "<PERSON><PERSON><PERSON>", "components.RequestBlock.server": "Odredišni poslužitelj", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.approverequest4k": "Odobri 4K zahtjev", "components.RequestButton.requestmore": "Zatraži više", "components.RequestButton.requestmore4k": "Zatraži više u 4K", "components.RequestButton.viewrequest": "P<PERSON><PERSON>", "components.RequestButton.viewrequest4k": "Prikaz 4K zahtjeva", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.cancelrequest": "Otkaži zahtjev", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.deleterequest": "Izbriši zahtjev", "components.RequestCard.editrequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.failedretry": "Dogodila se greška prilikom ponovnog pokušaja slanja zahtjeva.", "components.RequestCard.mediaerror": "{mediaType} nije pronađen", "components.RequestList.RequestItem.requested": "Zatraženo", "components.RequestList.requests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.showallrequests": "Prikaži sve zahtjeve", "components.RequestList.sortModified": "Zadnja promjena", "components.RequestModal.AdvancedRequester.advancedoptions": "Napredno", "components.RequestModal.AdvancedRequester.requestas": "Zatraži kao", "components.RequestModal.AdvancedRequester.selecttags": "Odaberi oz<PERSON>ke", "components.RequestModal.AdvancedRequester.tags": "Oznake", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.errorediting": "Dogodila se greška prilikom uređivanja zahtjeva.", "components.RequestModal.numberofepisodes": "<PERSON><PERSON><PERSON> e<PERSON><PERSON>", "components.RequestModal.pending4krequest": "4K zahtjevi na čekanju", "components.RequestModal.pendingapproval": "Tvoj zahtjev čeka na odobrenje.", "components.RequestCard.tvdbid": "TVDB ID", "components.RequestModal.pendingrequest": "Zahtjevai na čekanju", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON><PERSON> za <strong>{title}</strong> je odo<PERSON>n!", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON> za <strong>{title}</strong> je prekinut.", "components.RequestModal.requestcollection4ktitle": "Zatraži zbirku u 4K", "components.RequestModal.requestcollectiontitle": "Zatraži zbirku", "components.RequestModal.requesterror": "Dogodila se greška prilikom slanja zahtjeva.", "components.RequestModal.requestfrom": "Zahtjev korisnika {username} čeka na odobrenje.", "components.RequestModal.requestmovie4ktitle": "Zatraži film u 4K", "components.RequestModal.requestseries4ktitle": "Zatraži seriju u 4K", "components.RequestModal.requestseriestitle": "Zatraži seriju", "components.RequestModal.season": "Sezona", "components.RequestModal.seasonnumber": "Sezona {number}", "components.RequestModal.selectmovies": "Odaberi filmove", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "components.ResetPassword.gobacklogin": "Vrati se na stranicu za prijavu", "components.ResetPassword.validationpasswordmatch": "Lozinke se moraju poklapati", "components.ResetPassword.validationpasswordminchars": "Lozinka je prekratka; mora sadržati barem 8 znakova", "components.ResetPassword.validationpasswordrequired": "<PERSON><PERSON><PERSON> lo<PERSON>", "components.Search.searchresults": "<PERSON><PERSON><PERSON><PERSON> pretrage", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Neuspjelo spremanje postavki Gotify obavijesti.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "<PERSON><PERSON><PERSON> Gotify obavijesti provjere …", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsGotify.token": "Token programa", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsGotify.url": "URL poslužitelja", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL ne smije završiti s kosom crtom", "components.Settings.Notifications.NotificationsLunaSea.profileName": "<PERSON><PERSON> profila", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Potrebno je samo ako se ne koristi <code>z<PERSON><PERSON></code> profil", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> slanje LunaSea obavijesti provjere.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "S<PERSON>je LunaSea obavijesti provjere …", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "LunaSea obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Token za pristup", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Neuspjelo spremanje postavki Pushbullet obavijesti.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>et obavijesti provjere.", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "<PERSON><PERSON><PERSON> valjani token za pristup", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsPushover.accessToken": "API token programa", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registriraj jedan program</ApplicationRegistrationLink> za korištenje s Jellyseerr", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Neuspjelo spremanje Pushover postavki obavijesti.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> slanje <PERSON>over obavijesti provjere.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "<PERSON><PERSON><PERSON> valjani token programa", "components.Settings.Notifications.NotificationsPushover.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "<PERSON><PERSON><PERSON>sti valjani ključ korisnika ili grupe", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Neuspjelo spremanje Slack postavki obavijesti.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>je <PERSON>ck obavijesti provjere.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "<PERSON><PERSON><PERSON> obavijesti provjere …", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "S<PERSON>je webhook obavijesti provjere …", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.Settings.Notifications.allowselfsigned": "Dozvoli samopotpisane certifikate", "components.Settings.Notifications.authPass": "SMTP lozinka", "components.Settings.Notifications.authUser": "SMTP korisničko ime", "components.Settings.Notifications.botAPI": "Token autorizacije bota", "components.Settings.Notifications.botAvatarUrl": "URL avatara bota", "components.Settings.Notifications.botUsername": "<PERSON><PERSON><PERSON>č<PERSON> ime bota", "components.Settings.Notifications.botUsernameTip": "Dozvoli korisnicima da započnu chat s tvojim botom i da konfiguriraju vlastite obavijesti", "components.Settings.Notifications.encryption": "Metoda šifriranja", "components.Settings.Notifications.encryptionDefault": "Koristi STARTTLS ako je dostupno", "components.Settings.Notifications.encryptionImplicitTls": "<PERSON><PERSON><PERSON> implicitni TLS", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.encryptionOpportunisticTls": "Uvijek koristi STARTTLS", "components.Settings.Notifications.encryptionTip": "U većini slučajeva implicitni TLS koristi priključak 465, a STARTTLS priključak 587", "components.Settings.Notifications.pgpPassword": "PGP lozinka", "components.Settings.Notifications.sendSilentlyTip": "Pošalji obavijesti bez zvuka", "components.Settings.Notifications.senderName": "<PERSON><PERSON>", "components.Settings.Notifications.smtpHost": "SMTP računalo", "components.Settings.Notifications.smtpPort": "SMTP priključak", "components.Settings.Notifications.telegramsettingsfailed": "Neuspjelo spremanje Telegram postavki obavijesti.", "components.Settings.Notifications.telegramsettingssaved": "Telegram postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.toastEmailTestFailed": "Neuspjelo slanje e-mail obavijesti provjere.", "components.Settings.Notifications.toastEmailTestSending": "Slanje e-mail obavijesti provjere …", "components.Settings.Notifications.toastEmailTestSuccess": "E-mail obavijest provjere je poslana!", "components.Settings.Notifications.toastTelegramTestFailed": "Neuspjelo slanje Telegram obavijesti provjere.", "components.Settings.Notifications.validationBotAPIRequired": "Moraš <PERSON>i token autorizacije za bot", "components.Settings.Notifications.validationChatIdRequired": "<PERSON><PERSON><PERSON> val<PERSON>i chat ID", "components.Settings.RadarrModal.baseUrl": "Osnovni URL", "components.Settings.RadarrModal.create4kradarr": "Dodaj novi 4K Radarr poslužitelj", "components.Settings.RadarrModal.createradarr": "Dodaj novi Radarr poslužitelj", "components.Settings.RadarrModal.default4kserver": "Zadani 4K poslužitelj", "components.Settings.RadarrModal.editradarr": "Uredi Radarr poslužitelj", "components.Settings.RadarrModal.enableSearch": "Aktiviraj automatsku pretragu", "components.Settings.RadarrModal.externalUrl": "Eksterni URL", "components.Settings.RadarrModal.hostname": "Ime računala ili IP adresa", "components.Settings.RadarrModal.inCinemas": "U kinima", "components.Settings.RadarrModal.notagoptions": "<PERSON><PERSON>.", "components.Settings.RadarrModal.selectQualityProfile": "Odaberi profil kvalitete", "components.Settings.RadarrModal.port": "P<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.selectRootFolder": "Odaberi početnu mapu", "components.Settings.RadarrModal.selecttags": "Odaberi oz<PERSON>ke", "components.Settings.RadarrModal.server4k": "4K poslužitelj", "components.Settings.RadarrModal.servername": "<PERSON><PERSON>", "components.Settings.RadarrModal.ssl": "Koristi SSL", "components.Settings.RadarrModal.testFirstRootFolders": "Provjeri vezu za učitavanje početnih mapa", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Radarr veza je uspješno uspostavljena!", "components.Settings.RadarrModal.validationApiKeyRequired": "Moraš <PERSON>i valjani API ključ", "components.Settings.RadarrModal.validationApplicationUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL ne smije završiti s kosom crtom", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Osnovni URL mora početi s kosom crtom", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "<PERSON><PERSON>š odabrati najmanju dostupnost", "components.Settings.RadarrModal.validationPortRequired": "<PERSON><PERSON><PERSON>i valjani broj priključ<PERSON>", "components.Settings.RadarrModal.validationProfileRequired": "<PERSON><PERSON>š odabrati profil kvalitete", "components.Settings.SettingsAbout.Releases.currentversion": "Trenutno", "components.Settings.SettingsAbout.Releases.latestversion": "Najnovije", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Podaci izdanja trenutačno nisu nedostupni.", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} – zapisnik promjena", "components.Settings.SettingsAbout.Releases.viewchangelog": "Prikaz promjena", "components.Settings.SettingsAbout.Releases.viewongithub": "Prikaz na GitHubu", "components.Settings.SettingsAbout.about": "Informacije", "components.Settings.SettingsAbout.appDataPath": "Mapa podataka", "components.Settings.SettingsAbout.betawarning": "Ovo je BETA softver. Značajke su možda pokvarene i/ili nestabilne. Prijavi sve probleme na GitHub!", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON>rr informacije", "components.Settings.SettingsAbout.totalrequests": "Ukupno zahtjeva", "components.Settings.SettingsAbout.uptodate": "Aktualno", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr sprema zahtjeve na eksterne točke pristupa API-u za optimiranje izvedbe i izbjegavanje upućivanja nepotrebnih API poziva.", "components.Settings.SettingsJobsCache.cacheflushed": "Predmemorija {cachename} je is<PERSON>.", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.newPlexLogin": "Aktiviraj novu Plex prijavu", "components.Settings.SettingsUsers.toastSettingsFailure": "Dogodila se greška prilikom spremanja postavki.", "components.Settings.SonarrModal.loadinglanguageprofiles": "Učitavanje profila jezika …", "components.Settings.SonarrModal.toastSonarrTestFailure": "Neuspjelo povezivanje na Sonarr.", "components.Settings.manualscan": "Ručno skeniranje biblioteke", "components.Settings.mediaTypeSeries": "<PERSON><PERSON>ja", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.validationUrlBaseLeadingSlash": "Osnovni URL mora početi s kosom crtom", "components.Settings.webAppUrlTip": "Opcionalno usmjeri korisnike na web program na tvom poslužitelju umjesto na „hostirani” web program", "components.StatusChecker.appUpdatedDescription": "Pritisni donji gumb za ponovno pokretanje programa.", "components.StatusChecker.restartRequiredDescription": "Ponovo pokreni poslužitelja kako bi se ažurirane postavke primijenile.", "components.TvDetails.episodeRuntime": "Trajanje epizode", "components.TvDetails.nextAirDate": "Datum sl<PERSON>dećeg emitiranja", "components.TvDetails.rtaudiencescore": "<PERSON><PERSON>jena Rotten Tomatoes publike", "components.TvDetails.seasonnumber": "Sezona {seasonNumber}", "components.UserList.createlocaluser": "Stvori lokalnog korisnika", "components.UserList.deleteuser": "Izbriši korisnika", "components.UserList.importfromplexerror": "Dogodila se greška prilikom uvoza Plex korisnika.", "components.UserList.localLoginDisabled": "Postav<PERSON> <strong><PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON> prija<PERSON></strong> je tren<PERSON><PERSON><PERSON>.", "components.UserList.nouserstoimport": "Nema Plex korisnika za uvoz.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Javni PGP ključ", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "<PERSON><PERSON><PERSON><PERSON> e-mail poruke k<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "<PERSON><PERSON><PERSON> val<PERSON>i chat ID", "components.UserProfile.limit": "{remaining} od {limit}", "components.RequestList.RequestItem.tvdbid": "TVDB ID", "components.PermissionEdit.autorequest": "Automatsko zahtijevanje", "components.PermissionEdit.autorequestMovies": "Automatsko zahtijevanje filmova", "components.PermissionEdit.autorequestSeries": "Automatsko zahtijevanje serija", "components.PermissionEdit.createissues": "Prijavljivanje problema", "components.PermissionEdit.manageissues": "Upravl<PERSON><PERSON>", "components.PermissionEdit.managerequests": "Upravl<PERSON><PERSON>", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.request4k": "Zahtjev za 4K rezoluciju", "components.PermissionEdit.request4kMovies": "Zahtjev za filmove u 4K rezoluciji", "components.PermissionEdit.request4kTv": "Zahtjev za serije u 4K rezoluciji", "components.PermissionEdit.requestMovies": "Zahtjev za filmovima", "components.PersonDetails.birthdate": "<PERSON><PERSON> {birthdate}", "components.PersonDetails.crewmember": "Surad<PERSON>", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} po {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} po {quotaDays} {days}</quotaUnits>", "components.RequestBlock.languageprofile": "<PERSON><PERSON> jez<PERSON>", "components.RequestBlock.lastmodifiedby": "Zadnja promjena od", "components.RequestBlock.profilechanged": "Profil k<PERSON>itete", "components.RequestBlock.requestdate": "<PERSON><PERSON>", "components.RequestBlock.requestedby": "Podnositelj <PERSON>", "components.RequestBlock.requestoverrides": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.rootfolder": "Početna mapa", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.declinerequest4k": "Odbij 4K zahtjev", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestList.RequestItem.cancelRequest": "Prekini zahtjev", "components.RequestList.RequestItem.deleterequest": "Izbriši zahtjev", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.failedretry": "Dogodila se greška prilikom ponovnog pokušaja slanja zahtjeva.", "components.RequestList.RequestItem.mediaerror": "{mediaType} nije pronađen", "components.RequestList.RequestItem.modified": "Promijenjeno", "components.RequestList.RequestItem.modifieduserdate": "{date}, koris<PERSON>: {user}", "components.RequestList.RequestItem.requesteddate": "Zatraženo", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.animenote": "* Ovo je serija anime filmova.", "components.RequestModal.AdvancedRequester.default": "{name} (zadano)", "components.RequestModal.AdvancedRequester.destinationserver": "Odredišni poslužitelj", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON><PERSON> jez<PERSON>", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON><PERSON>.", "components.RequestModal.AdvancedRequester.qualityprofile": "Profil k<PERSON>itete", "components.RequestModal.AdvancedRequester.rootfolder": "Početna mapa", "components.RequestModal.QuotaDisplay.season": "sezona", "components.RequestModal.alreadyrequested": "Već z<PERSON>raženo", "components.RequestModal.approve": "<PERSON><PERSON><PERSON>", "components.RequestModal.autoapproval": "Automatsko odobravanje", "components.RequestModal.cancel": "Prekini zahtjev", "components.RequestModal.edit": "<PERSON><PERSON><PERSON>", "components.RequestModal.requestCancel": "<PERSON><PERSON><PERSON><PERSON><PERSON> za <strong>{title}</strong> je prekinut.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> je uspje<PERSON> zatražen!", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON><PERSON> za <strong>{title}</strong> je usp<PERSON><PERSON><PERSON> ure<PERSON>!", "components.RequestModal.requestmovietitle": "Zatraži film", "components.RequestModal.requestseasons": "Zatraži {seasonCount} {seasonCount, plural, one {sezonu} few {sezone} other {sezona}}", "components.RequestModal.requestseasons4k": "<PERSON><PERSON><PERSON>ži {seasonCount} {seasonCount, plural, one {sezonu} few {sezone} other {sezona}} u 4K", "components.ResetPassword.confirmpassword": "Potvrdi lozinku", "components.ResetPassword.email": "E-mail adresa", "components.ResetPassword.password": "Lozinka", "components.ResetPassword.passwordreset": "<PERSON><PERSON><PERSON>", "components.ResetPassword.requestresetlinksuccessmessage": "Poveznica za ponovno postavljanje lozinke bit će poslana na navedenu e-mail adresu ako je povezana s valjanim koris<PERSON>.", "components.ResetPassword.resetpassword": "Obnovni svoju lozinku", "components.ResetPassword.resetpasswordsuccessmessage": "Lozinka je uspješno ponovno postavljena!", "components.ResetPassword.validationemailrequired": "Mo<PERSON>š z<PERSON> valjanju e‑mail adresu", "components.Search.search": "Pretraga", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> slanje Gotify obavijesti provjere.", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "<PERSON><PERSON><PERSON> token programa", "components.Settings.Notifications.NotificationsGotify.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Neuspjelo spremanje postavki LunaSea obavijesti.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "LunaSea postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Stvori token iz tvojih <PushbulletSettingsLink>postavki računa</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Oznaka kanala", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Pushbullet postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "<PERSON><PERSON><PERSON>et obavijesti provjere …", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Pushbullet obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "<PERSON><PERSON><PERSON>over obavijesti provjere …", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsPushover.userToken": "Ključ korisnika ili grupe", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Tvoj <UsersGroupsLink>identifikator korisnika ili grupe</UsersGroupsLink> s 30 znakova", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Stvori <WebhookLink>ulaznu Webhook</WebhookLink> integraciju", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "<PERSON>eus<PERSON><PERSON>lo slanje Web push obavijesti provjere.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "<PERSON><PERSON>je web push obavijesti provjere …", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push obavijest provjere je poslana!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Neuspjelo spremanje Web push postavki obavijesti.", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Web push postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.NotificationsWebhook.authheader": "Zaglavlje autorizacije", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Resetiraj na zadane vrijednosti", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Neus<PERSON><PERSON>lo slanje Webhook obavijesti provjere.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Neuspjelo spremanje Webhook postavki obavijesti.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.agentenabled": "Aktiviraj agenta", "components.Settings.Notifications.botApiTip": "<CreateBotLink>St<PERSON>i bot</CreateBotLink> za korištenje s Jellyseerr", "components.Settings.Notifications.chatId": "Chat ID", "components.Settings.Notifications.discordsettingsfailed": "Neuspjelo spremanje Discord postavki za obavijesti.", "components.Settings.Notifications.discordsettingssaved": "Discord postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.emailsender": "Adresa p<PERSON>šiljatelja", "components.Settings.Notifications.emailsettingsfailed": "Neuspjelo spremanje e-mail postavki obavijesti.", "components.Settings.Notifications.emailsettingssaved": "E-mail postavke obavijesti su uspješno spremljene!", "components.Settings.Notifications.enableMentions": "Aktiv<PERSON>j <PERSON>", "components.Settings.Notifications.pgpPasswordTip": "Potpiši <PERSON> e-mail poruke k<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Privatni PGP ključ", "components.Settings.Notifications.pgpPrivateKeyTip": "Potpiši <PERSON> e-mail poruke k<PERSON> <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "Pošalji tiho", "components.Settings.Notifications.toastDiscordTestFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> slanje <PERSON>rd obavijesti provjere.", "components.Settings.Notifications.toastDiscordTestSending": "<PERSON><PERSON><PERSON>rd obavijesti provjere …", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord obavijest provjere je poslana!", "components.Settings.Notifications.toastTelegramTestSending": "Slanje Telegram obavijesti provjere …", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram obavijest provjere je poslana!", "components.Settings.Notifications.validationEmail": "<PERSON><PERSON>š <PERSON>i valjan<PERSON> e‑mail adresu", "components.Settings.Notifications.validationPgpPassword": "Moraš navesti PGP lozinku", "components.Settings.Notifications.validationPgpPrivateKey": "Moraš navesti valjan privatni PGP ključ", "components.Settings.Notifications.validationSmtpHostRequired": "<PERSON><PERSON>š <PERSON>i valjano ime računala ili IP adresu", "components.Settings.Notifications.validationSmtpPortRequired": "<PERSON><PERSON><PERSON>i valjani broj priključ<PERSON>", "components.Settings.Notifications.validationTypes": "<PERSON><PERSON><PERSON> o<PERSON> barem jednu vrstu obavijesti", "components.Settings.Notifications.validationUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.Settings.Notifications.webhookUrlTip": "Stvori <DiscordWebhookLink>webhook integraciju</DiscordWebhookLink> u tvom poslužitelju", "components.Settings.RadarrModal.add": "Dodaj poslužitelj", "components.Settings.RadarrModal.announced": "Najavljeno", "components.Settings.RadarrModal.apiKey": "API ključ", "components.Settings.RadarrModal.defaultserver": "Zadani poslužitelj", "components.Settings.RadarrModal.edit4kradarr": "Uredi 4K Radarr poslužitelj", "components.Settings.RadarrModal.loadingTags": "Učitavanje <PERSON> …", "components.Settings.RadarrModal.loadingprofiles": "Učitavanje profila kvalitete …", "components.Settings.RadarrModal.loadingrootfolders": "Učitavanje početnih mapa …", "components.Settings.RadarrModal.minimumAvailability": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.qualityprofile": "Profil k<PERSON>itete", "components.Settings.RadarrModal.released": "Objavljeno", "components.Settings.RadarrModal.rootfolder": "Početna mapa", "components.Settings.RadarrModal.selectMinimumAvailability": "Odaberi najmanju dostupnost", "components.Settings.RadarrModal.tags": "Oznake", "components.Settings.RadarrModal.testFirstQualityProfiles": "Provjeri vezu za učitavanje profila kvalitete", "components.Settings.RadarrModal.testFirstTags": "Provjeri vezu za učitavanje oznaka", "components.Settings.RadarrModal.toastRadarrTestFailure": "Neuspjelo povezivanje na Radarr.", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Osnovni URL ne smije završiti s kosom crtom", "components.Settings.RadarrModal.validationHostnameRequired": "<PERSON><PERSON>š <PERSON>i valjano ime računala ili IP adresu", "components.Settings.RadarrModal.validationNameRequired": "<PERSON><PERSON><PERSON> ime poslužitelja", "components.Settings.RadarrModal.validationRootFolderRequired": "<PERSON><PERSON>š odabrati početnu mapu", "components.Settings.SettingsAbout.Releases.releases": "Izdanja", "components.Settings.SettingsAbout.documentation": "Dokumentacija", "components.Settings.SettingsAbout.gettingsupport": "Dobivanje p<PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "Rasprave na GitHubu", "components.Settings.SettingsAbout.helppaycoffee": "Pomogni platiti kavu", "components.Settings.SettingsAbout.outofdate": "Zastarjela ve<PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Preferirano", "components.Settings.SettingsAbout.runningDevelop": "Pokrećeš <code>razvojnu</code> <PERSON><PERSON><PERSON><PERSON> granu, koja se preporučuje samo onima koji doprinose razvoju ili pomažu najnovijem testiranju.", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Vremenska zona", "components.Settings.SettingsAbout.totalmedia": "Ukupno medija", "components.Settings.SettingsAbout.version": "Verzija", "components.Settings.SettingsJobsCache.cache": "Predmemorija", "components.Settings.SettingsJobsCache.cachekeys": "Ukup<PERSON> ključeva", "components.Settings.SettingsJobsCache.cacheksize": "Veličina ključa", "components.Settings.SettingsJobsCache.cachemisses": "Pro<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachevsize": "Veličina vrijednosti", "components.Settings.SettingsJobsCache.canceljob": "Prekini zadatak", "components.Settings.SettingsJobsCache.command": "Naredba", "components.Settings.SettingsJobsCache.download-sync": "Sinkronizacija preuzimanja", "components.Settings.SettingsJobsCache.download-sync-reset": "Resetiraj sinkronizaciju preuzimanja", "components.Settings.SettingsJobsCache.editJobSchedule": "Promijeni zadatak", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "{jobScheduleHours, plural, one {Svake minute} few {Svake {jobScheduleHours} minute} other {Svakih {jobScheduleHours} minuta}}", "components.Settings.SettingsJobsCache.flushcache": "Isprazni predmemoriju", "components.Settings.SettingsJobsCache.jobcancelled": "Zadatak {jobname} prekinut.", "components.Settings.SettingsJobsCache.jobname": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr obavlja određene zadatke održavanja kao redovito planirane zadatke, ali oni se također mogu dolje ručno pokrenuti. Ručno pokretanje zadatka neće promijeniti njegov plan.", "components.Settings.SettingsJobsCache.jobtype": "Vrsta", "components.Settings.SettingsJobsCache.jobsandcache": "Zadaci i predmemorija", "components.Settings.SettingsJobsCache.jobstarted": "Zadatak {jobname} je pok<PERSON>ut.", "components.Settings.SettingsJobsCache.nextexecution": "Sljedeće izvršavanje", "components.Settings.SettingsJobsCache.process": "Obrada", "components.Settings.SettingsLogs.copyToClipboard": "Kopiraj u međuspremnik", "components.Settings.SettingsLogs.logsDescription": "<PERSON>ve zapise mož<PERSON>š izravno vidjeti i putem <code>stdout</code> ili u <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.message": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.pauseLogs": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.newPlexLoginTip": "Dozvoli Plex korisnicima da se prijave bez da se prethodno uvezu", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Provjeri vezu za učitavanje profila jezika", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL ne smije završiti s kosom crtom", "components.Settings.mediaTypeMovie": "film", "components.Settings.toastTautulliSettingsFailure": "Dogodila se greška prilikom spremanja Tautulli postavki.", "components.Settings.toastTautulliSettingsSuccess": "Tautulli postavke su uspješno spremljene!", "components.Settings.validationHostnameRequired": "<PERSON><PERSON>š <PERSON>i valjano ime računala ili IP adresu", "components.Settings.validationPortRequired": "<PERSON><PERSON><PERSON>i valjani broj priključ<PERSON>", "components.Settings.validationUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.validationUrlBaseTrailingSlash": "Osnovni URL ne smije završiti s kosom crtom", "components.Settings.validationUrlTrailingSlash": "URL ne smije završiti s kosom crtom", "components.Settings.webAppUrl": "URL <WebAppLink>web programa</WebAppLink>", "components.Settings.webhook": "Webhook", "components.Setup.configureservices": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.StatusChecker.reloadApp": "Ponovo pokreni {applicationTitle}", "components.StatusChecker.restartRequired": "Zahtijeva ponovno pokretanje poslužitelja", "components.TitleCard.tvdbid": "TVDB ID", "components.TitleCard.cleardata": "Izbriši podatke", "components.TitleCard.mediaerror": "{mediaType} nije pronađen", "components.TitleCard.tmdbid": "TMDB ID", "components.TvDetails.Season.somethingwentwrong": "Dogodila se greška prilikom dohvaćanja podataka sezona.", "components.TvDetails.anime": "Anime", "components.TvDetails.episodeRuntimeMinutes": "{runtime} min", "components.TvDetails.firstAirDate": "Datum prvog emitiranja", "components.TvDetails.manageseries": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "components.TvDetails.network": "{networkCount, plural, one {mreža} few {mreže} other {mreža}}", "components.TvDetails.originallanguage": "Izvorni jezik", "components.TvDetails.originaltitle": "Izvorni naslov", "components.TvDetails.overview": "Pregled", "components.TvDetails.overviewunavailable": "<PERSON><PERSON> ne<PERSON>.", "components.TvDetails.productioncountries": "{countryCount, plural, one {zemlja produkcije} few {zemlje produkcije} other {zemalja produkcije}}", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON>", "components.TvDetails.reportissue": "P<PERSON><PERSON><PERSON> problem", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatometar", "components.TvDetails.showtype": "Vrste serija", "components.TvDetails.similar": "Slične serije", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.streamingproviders": "Trenutačno se prikazuje na", "components.TvDetails.tmdbuserscore": "TMDB ocjena korisnika", "components.TvDetails.viewfullcrew": "Prikaz svih suradnika", "components.TvDetails.watchtrailer": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "Automatski generiraj lozinku", "components.UserList.autogeneratepasswordTip": "Pošalji korisniku e-mail s lozinkom koju je generirao poslužitelj", "components.UserList.bulkedit": "Grupno uređivanje", "components.UserList.accounttype": "Vrsta", "components.UserList.admin": "Administrator", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.created": "Pridruživanje", "components.UserList.deleteconfirm": "St<PERSON>no želiš izbrisati ovog korisnika? Svi podaci njegovih zahtjeva će se trajno ukloniti.", "components.UserList.usercreatedsuccess": "Korisnik je uspješno stvoren!", "components.UserList.userdeleted": "Korisnik je uspješno izbrisan!", "components.UserList.userfail": "Dogodila se greška prilikom spremanja korisničkih dozvola.", "components.UserList.userlist": "<PERSON><PERSON>", "components.UserList.users": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.userssaved": "Korisničke dozvole su uspješno spremljene!", "components.UserList.validationEmail": "Mo<PERSON>š z<PERSON> valjanju e‑mail adresu", "components.UserList.validationpasswordminchars": "Lozinka je prekratka; mora sadržati barem 8 znakova", "components.UserProfile.ProfileHeader.profile": "Prikaz profila", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Lokalni korisnik", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Ograničenje zahtjeva za filmove", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Vlasnik", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Automatski zatraži serije na tvom <PlexWatchlistSupportLink>Plex popisu gledanja</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Regionalno otkrivanje", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Dogodila se greška prilikom spremanja postavki.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Postavke su uspješno spremljene!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "<PERSON><PERSON>š <PERSON>i valjani Discord korisnički ID", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID korisnika", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>Višeznamenkasti ID broj</FindDiscordIdLink> povezan s tvojim korisničkim računom", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Neuspjelo spremanje Discord postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Stvori token iz tvojih <PushbulletSettingsLink>postavki računa</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Neuspjelo spremanje Pushbullet postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Ključ korisnika ili grupe", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Tvoj <UsersGroupsLink>identifikator korisnika ili grupe</UsersGroupsLink> s 30 znakova", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Neuspjelo spremanje Pushover postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Pushover postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "<PERSON><PERSON>š <PERSON>sti valjani korisnički ID", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "<PERSON><PERSON><PERSON>i valjani javni ključ stvoren PGP-om", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "<PERSON><PERSON><PERSON> valjani token za pristup", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "<PERSON><PERSON><PERSON> valjani token programa", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "<PERSON><PERSON><PERSON>sti valjani ključ korisnika ili grupe", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Neuspjelo spremanje Web push postavki obavijesti.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Web push postavke obavijesti su uspješno spremljene!", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Potvrdi lozinku", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nova lozinka", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Moraš <PERSON>ti novu lozinku", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Lozinke se moraju poklapati", "components.UserProfile.UserSettings.UserPermissions.permissions": "Dozvole", "components.UserProfile.UserSettings.menuChangePass": "Lozinka", "components.UserProfile.recentrequests": "Nedav<PERSON>", "components.UserProfile.recentlywatched": "<PERSON><PERSON><PERSON> gledano", "components.UserProfile.requestsperdays": "{limit} preostalo", "i18n.advanced": "Napredno", "i18n.all": "Sve", "i18n.approve": "<PERSON><PERSON><PERSON>", "i18n.approved": "Odobreno", "i18n.areyousure": "Sigurno?", "i18n.cancel": "Odustani", "i18n.canceling": "Prekidanje …", "i18n.close": "Zatvori", "i18n.decline": "<PERSON><PERSON><PERSON>j", "i18n.declined": "Odbijeno", "i18n.delete": "Izbriši", "i18n.deleting": "<PERSON><PERSON><PERSON><PERSON> …", "i18n.failed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.import": "<PERSON><PERSON><PERSON>", "i18n.importing": "Uvoz …", "i18n.loading": "U<PERSON>ita<PERSON><PERSON> …", "i18n.movie": "Film", "i18n.movies": "Filmovi", "i18n.next": "<PERSON><PERSON>", "i18n.noresults": "<PERSON><PERSON> rezultata.", "i18n.notrequested": "<PERSON>je z<PERSON>raženo", "i18n.open": "Otvori", "i18n.partiallyavailable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.restartRequired": "Zahtijeva ponovno pokretanje", "i18n.retry": "Pokušaj ponovo", "i18n.status": "<PERSON><PERSON>", "pages.oops": "Ups", "components.Settings.advancedTooltip": "Neispravno konfiguriranje ove postavke može pokvariti funkcionalnost", "components.Settings.noDefaultNon4kServer": "<PERSON>ko imaš samo jedan {serverType} poslužitelj za ne-4K i za 4K sadržaj (ili ako preuzimaš samo 4K sadržaj), <strong>NEMOJ</strong> odrediti svoj {serverType} poslužitelj kao 4K poslužitelj.", "components.PermissionEdit.requestMoviesDescription": "Dozvoli automatsko slanje zahtjeva za filmove koje nisu u 4K rezoluciji.", "components.PermissionEdit.autorequestDescription": "Dozvoli automatsko slanje zahtjeva za medije koji nisu u 4K rezoluciji putem Plex popisa gledanja.", "components.PermissionEdit.autorequestMoviesDescription": "Dozvoli automatsko slanje zahtjeva za filmove koji nisu u 4K rezoluciji putem Plex popisa gledanja.", "components.PermissionEdit.autorequestSeriesDescription": "Dozvoli automatsko slanje zahtjeva za serije koje nisu u 4K rezoluciji putem Plex popisa gledanja.", "components.PermissionEdit.requestDescription": "Dozvoli automatsko slanje zahtjeva za medije koje nisu u 4K rezoluciji.", "components.PermissionEdit.requestTvDescription": "Dozvoli automatsko slanje zahtjeva za serije koje nisu u 4K rezoluciji.", "components.PermissionEdit.viewrecentDescription": "Dozvoli prikaz za pregled popisa nedavno dodanih medija.", "components.PermissionEdit.createissuesDescription": "Dozvoli prijavljivanje problema s medijima.", "components.PermissionEdit.manageissuesDescription": "Dozvoli upravljanje problemima s medijima.", "components.PermissionEdit.request4kDescription": "Dozvoli slanje zahtjeva za medije u 4K rezoluciji.", "components.PermissionEdit.request4kMoviesDescription": "Dozvoli slanje zahtjeva za filmove u 4K rezoluciji.", "components.PermissionEdit.request4kTvDescription": "Dozvoli slanje zahtjeva za serijama u 4K rezoluciji.", "components.PermissionEdit.usersDescription": "Dozvoli upravljanje korisnicima. Korisnici s ovom dozvolom ne mogu mijenjati korisnike s administratorskom privilegijom ili dozvoliti administratorske privilegije.", "components.PermissionEdit.viewissuesDescription": "Dozvoli prikaz problema s medijima koje su prijavili drugi korisnici.", "components.PermissionEdit.viewrequestsDescription": "Dozvoli prikaz zahtjeva za medije koje su poslali drugi korisnici.", "components.PermissionEdit.viewwatchlistsDescription": "Dozvoli prikaz Plex popisa gledanja drugih korisnika.", "components.Settings.notrunning": "Ne pokreće se", "components.Settings.serverRemote": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.quotaLink": "Sažetak tvojih ograničenja zahtjeva možeš vidjeti na tvojoj <ProfileLink>stranici profila</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Sažetak ograničenja zahtjeva korisničkih zahtjeva možeš vidjeti na njihovoj <ProfileLink>stranici profila</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "<PERSON>ema dovoljno pre<PERSON>ali<PERSON> zahtjeva za sezone", "components.RequestModal.SearchByNameModal.nomatches": "Nismo uspjeli pronaći seriju koja odgovara ovoj seriji.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Nismo uspjeli automatski naći odgovarajuću seriju. Dolje odaberi odgovarajuću seriju.", "components.RequestModal.requestadmin": "Ovaj će se zahtjev automatski odobriti.", "components.ResetPassword.emailresetlink": "E-mail poveznica za obnavljanje lozinke", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Tvoj korisničke ili na osnovi uređaja <LunaSeaLink>webhook URL obavijesti</LunaSeaLink>", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Za primanje web push obavijesti, <PERSON><PERSON><PERSON><PERSON> se mora posluživati putem HTTPS-a.", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Pomoć za varijablu predloška", "components.Settings.Notifications.chatIdTip": "Započni chat s tvojim botom, dodaj <GetIdBotLink>@get_id_bot</GetIdBotLink> i zadaj naredbu <code>/my_id</code>", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr <PERSON>", "components.Settings.SettingsJobsCache.sonarr-scan": "<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.syncEnabled": "Aktiviraj pretraživanje", "components.Settings.cancelscan": "Prekini pretraživanje", "components.Settings.manualscanDescription": "Obično će se to pokrenuti samo jednom svaka 24 sata. Je<PERSON><PERSON>rr će agresivnije provjeriti tvoj Plex poslužitelj za nedavno dodanim. Ako po prvi put kon<PERSON><PERSON><PERSON><PERSON><PERSON>lex, preporučuje se jednom ručno pretražiti cijelu biblioteku!", "components.Settings.plexlibrariesDescription": "Biblioteke u kojima će Jellyseerr tražiti naslove. Postavi i spremi tvoje postavke Plex veze, a zatim pritisni gumb ispod ako je popis biblioteka prazan.", "components.Settings.startscan": "Pokreni pretraživanje", "components.PermissionEdit.managerequestsDescription": "Dozvoli upravljanje zahtjevima za medije. Svi zahtjevi korisnika s ovom dozvolom će se automatski odobriti.", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON sadržaj", "components.Settings.SettingsJobsCache.plex-full-scan": "Pretraživanje cijele Plex biblioteke", "components.RequestModal.QuotaDisplay.allowedRequests": "<PERSON><PERSON><PERSON><PERSON> <strong>{limit}</strong> {type} svakih <strong>{days}</strong> dana.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "O<PERSON>j korisnik smije zatražiti <strong>{limit}</strong> {type} svakih <strong>{days}</strong> dana.", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON sadržaj je uspješno resetiran!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Mo<PERSON>š zadati valjani JSON sadržaj", "components.Settings.RadarrModal.syncEnabled": "Aktiviraj pretraživanje", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Pretraživanje nedavno dodanih u Plex biblioteku", "components.TvDetails.Season.noepisodes": "Popis epizoda nije dos<PERSON>.", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Brisanje predmemorije slika", "components.Settings.SettingsJobsCache.imagecache": "Predmemorija slika", "components.Settings.SettingsJobsCache.imagecachesize": "Ukupna veličina predmemorije", "components.Settings.SettingsJobsCache.imagecachecount": "Slike spremljene u predmemoriju", "components.Settings.SettingsJobsCache.imagecacheDescription": "Kad je u postavkama aktivirano, <PERSON><PERSON><PERSON><PERSON> će služiti kao posrednik i predmemorirati slike iz unaprijed konfiguriranih vanjskih izvora. Predmemorirane slike spremaju se u mapu konfiguracije. Datoteke možeš pronaći u <code>{appDataPath}/cache/images</code>.", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {seasonNumber} Episoda {episodeNumber}", "components.RequestCard.unknowntitle": "Nepoznat naslov", "components.RequestList.RequestItem.unknowntitle": "Nepoznat naslov", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.DiscoverTv.discovertv": "Serije", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.discovermovies": "Filmovi", "components.Discover.moviegenres": "Žanrovi filmova", "components.Discover.FilterSlideover.keywords": "Ključne riječi", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# aktivni filtar} few {# aktivna filtra} other {# aktivnih filtara}}", "components.Discover.FilterSlideover.firstAirDate": "Datum prvog emitiranja", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Tvoj Plex popis gledanja", "components.Discover.FilterSlideover.genres": "Žanrovi", "components.Discover.FilterSlideover.originalLanguage": "Izvorni jezik", "components.Discover.CreateSlider.editsuccess": "Uređen klizač i spremljene postavke prilagodbe otkrivanja.", "components.Discover.CreateSlider.slidernameplaceholder": "<PERSON>v klizača", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularnost (silazno)", "components.Discover.CreateSlider.searchStudios": "Pretražite studije…", "components.Discover.CreateSlider.providetmdbnetwork": "Navdeite TMDB ID mreže", "components.Discover.CreateSlider.addfail": "Izrada novog klizača nije uspjela.", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularnost (uzlazno)", "components.Discover.CreateSlider.needresults": "Morate imati barem 1 rezultat.", "components.Discover.CreateSlider.addcustomslider": "Stvorite prilagođeni k<PERSON>", "components.Discover.CreateSlider.editSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.validationDatarequired": "Morate navesti vrijednost.", "components.Discover.CreateSlider.providetmdbstudio": "Navedite TMDB ID studija", "components.Discover.CreateSlider.searchGenres": "Traži ž<PERSON>…", "components.Discover.CreateSlider.editfail": "Uređivanje klizača nije us<PERSON>jelo.", "components.Discover.CreateSlider.starttyping": "Počinje tipkati za pretraživanje.", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.providetmdbsearch": "Navedite upit za pretraživanje", "components.Discover.CreateSlider.providetmdbkeywordid": "Navdeite TMDB ID ključne riječi", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} filmovi", "components.Discover.CreateSlider.validationTitlerequired": "Morate nave<PERSON><PERSON> na<PERSON>lov.", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON> rezultata.", "components.Discover.CreateSlider.searchKeywords": "Pretraži ključne riječi…", "components.Discover.CreateSlider.addsuccess": "Stvoren je novi klizač i spremljene su postavke prilagodbe otkrivanja.", "components.Discover.CreateSlider.providetmdbgenreid": "Navedite TMDB ID žanra", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB ocjena (uzlazno)", "components.Discover.FilterSlideover.studio": "Studio", "components.Discover.FilterSlideover.ratingText": "<PERSON><PERSON><PERSON><PERSON> od {minValue} do {maxValue}", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Prvo emitiranje (uzlazno)", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB ocjena (silazno)", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# aktivni filtar} few {# aktivna filtra} other {# aktivnih filtara}}", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB ocjena (uzlazno)", "components.Discover.FilterSlideover.clearfilters": "Obriši aktivne filtre", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON>tum <PERSON> (silazno)", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# aktivni filtar} few {# aktivna filtra} other {# aktivnih filtara}}", "components.Discover.FilterSlideover.streamingservices": "Streaming usluge", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB ocjena korisnika", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularnost (uzlazno)", "components.Discover.DiscoverTv.sortTitleAsc": "Naslov (A-Z) (uzlazno)", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Prvo emitiranje (silazno)", "components.Discover.FilterSlideover.releaseDate": "Datum <PERSON>", "components.Discover.DiscoverSliderEdit.deletefail": "Greška pri uklanjanju klizača.", "components.Discover.FilterSlideover.runtime": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.from": "Od", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) (silazno)", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularnost (silazno)", "components.Discover.FilterSlideover.to": "Do", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON>", "components.Discover.DiscoverSliderEdit.enable": "Vidljivost", "components.Discover.FilterSlideover.runtimeText": "trajanje {minValue}-{maxValue} minuta", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Datum <PERSON> (uzlazno)", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB ocjena (silazno)", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB broj glasova korisnika", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON><PERSON><PERSON><PERSON> uspješno uklonjen.", "components.Discover.DiscoverMovies.sortTitleAsc": "Naslov (A-Z) (uzlazno)", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON> (Z-A) (silazno)", "components.Discover.DiscoverSliderEdit.remove": "Ukloni", "components.Discover.tvgenres": "Žanrovi serija", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} serije", "components.Selector.showmore": "Pokaži više", "components.Selector.searchGenres": "Odaberi žanrove …", "components.Selector.searchStudios": "Traži studija …", "components.Discover.tmdbtvgenre": "TMDB žanr serije", "components.Selector.showless": "Pokaži manje", "components.Selector.starttyping": "Počni tipkati za pretraživanje.", "components.Selector.searchKeywords": "Traži ključne riječi …", "components.Selector.nooptions": "<PERSON><PERSON> rezultata.", "components.Discover.tmdbmoviegenre": "TMDB žanr filmova", "components.Discover.FilterSlideover.voteCount": "<PERSON><PERSON><PERSON> između {minValue} i {maxValue}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "{jobScheduleHours, plural, one {Svake sekunde} few {Svake {jobScheduleHours} sekunde} other {Svakih {jobScheduleHours} sekundi}}", "components.Layout.Sidebar.browsemovies": "Filmovi", "components.Layout.Sidebar.browsetv": "Serije", "components.Discover.updatesuccess": "Aktualiziraj postavke za prilagođavanje otkrivanja.", "components.Discover.updatefailed": "Dogodila se greška tiijekom aktualiziranja postavki za prilagođavanje otkrivanja.", "components.Settings.SettingsMain.toastApiKeyFailure": "Dogodila se greška prilikom generiranja novog API ključa.", "components.Settings.SettingsMain.toastSettingsFailure": "Dogodila se greška prilikom spremanja postavki.", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Mediji koji su dodani u tvoj <PlexWatchlistSupportLink>Plex popis gledanja</PlexWatchlistSupportLink> pojavit će se ovdje.", "components.Discover.networks": "Mreže", "components.Discover.studios": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.hideAvailable": "<PERSON><PERSON><PERSON><PERSON> medije", "components.Settings.Notifications.NotificationsPushover.sound": "Zvuk obavijesti", "components.Discover.tmdbmoviekeyword": "TMDB ključna riječ filmova", "components.Discover.stopediting": "Prekini uređivanje", "components.Discover.resetsuccess": "Uspješno resetiranje postavki prilagodbe otkrivanja.", "components.Discover.customizediscover": "Prilagodi otkrivanje", "components.Settings.SettingsMain.locale": "<PERSON><PERSON><PERSON>", "components.Discover.tmdbtvkeyword": "TMDB ključna riječ serija", "components.Discover.tmdbnetwork": "TMDB mreža", "components.Settings.SettingsMain.applicationTitle": "Naslov programa", "components.Settings.SettingsMain.originallanguage": "Jezik otkrivanja", "components.Discover.tmdbstudio": "TMDB studio", "components.Settings.SettingsMain.generalsettings": "<PERSON><PERSON><PERSON>", "components.MovieDetails.imdbuserscore": "IMDB ocjena korisnika", "components.Discover.tmdbtvstreamingservices": "TMDB streaming usluge za TV", "components.Settings.SettingsMain.apikey": "API ključ", "components.Settings.SettingsMain.cacheImages": "Aktiviraj predmemoriranje slika", "components.Discover.tmdbmoviestreamingservices": "TMDB streaming usluge filmova", "components.Discover.resetfailed": "Dogodila se greška prilikom resetiranja postavki prilagodbe otkrivanja.", "components.Discover.tmdbsearch": "TMDB pretraga", "components.Settings.SettingsMain.applicationurl": "URL programa", "components.Settings.SettingsMain.general": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Zvuk obavijesti", "components.Discover.resettodefault": "Resetiraj na zadane vrijednosti", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "<PERSON><PERSON>ne <PERSON>av<PERSON>", "components.Settings.SettingsMain.toastSettingsSuccess": "Postavke su uspješno spremljene!", "i18n.collection": "Zbirka", "components.Settings.SettingsMain.originallanguageTip": "Filtriraj sadržaj po izvornom jeziku", "components.Settings.SettingsMain.cacheImagesTip": "Predmemoriraj slike eksternih izvora (zahtijeva značajnu količinu memorije na disku)", "components.Settings.SettingsJobsCache.availability-sync": "Sinkronizacija dostupnosti medija", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL ne smije završiti s kosom crtom", "components.Settings.SonarrModal.animeSeriesType": "Vrsta anime serije", "components.Settings.SettingsMain.generalsettingsDescription": "Konfiguriraj globalne i zadane postavke za Overseerr.", "components.Settings.SettingsMain.toastApiKeySuccess": "Novi API ključ je uspješno generiran!", "components.Settings.SonarrModal.seriesType": "Vrsta serije", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "<PERSON><PERSON>ne <PERSON>av<PERSON>", "components.Settings.SettingsMain.validationApplicationUrl": "<PERSON><PERSON><PERSON>i valjani URL", "components.Settings.SettingsMain.validationApplicationTitle": "<PERSON><PERSON><PERSON> na<PERSON>lov <PERSON>a", "components.Discover.resetwarning": "Vrati sve klizače na zadane postavke. To će također izbrisati sve prilagođene klizače!", "components.Settings.SonarrModal.tagRequestsInfo": "Automatski dodaj dodatnu oznaku s korisničkim ID-om i prikaznim imenom podnositelja zahtjeva", "components.Settings.RadarrModal.tagRequestsInfo": "Automatski dodaj dodatnu oznaku s korisničkim ID-om i prikaznim imenom podnositelja zahtjeva", "components.Discover.createnewslider": "Stvori novi klizač", "components.Settings.SettingsMain.partialRequestsEnabled": "Dopusti djelomične zahtjeve za seriju", "components.Settings.SonarrModal.tagRequests": "Označi zahtjeve", "components.Settings.RadarrModal.tagRequests": "Označi zahtjeve"}