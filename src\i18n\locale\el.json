{"components.PermissionEdit.users": "Διαχείρι<PERSON>η Χρηστών", "components.PermissionEdit.requestTvDescription": "Χορήγη<PERSON>η άδειας για υποβολής αιτημάτων σειρών που δεν είναι 4K.", "components.PermissionEdit.requestTv": "Αιτήματα για Σειρές", "components.PermissionEdit.requestMoviesDescription": "Χορήγη<PERSON>η άδειας για υποβολή αιτημάτων ταινιών που δεν είναι 4K.", "components.PermissionEdit.requestMovies": "Αιτήματα για Ταινίες", "components.PermissionEdit.requestDescription": "Χορήγη<PERSON>η άδειας για υποβολή αιτημάτων περιεχομένου που δεν είναι 4Κ.", "components.PermissionEdit.request4kTvDescription": "Χορήγη<PERSON>η άδειας για υποβολή αιτημάτων 4K σειρών.", "components.PermissionEdit.request4kTv": "Αιτήματα για 4K Σειρές", "components.PermissionEdit.request4kMoviesDescription": "Χορήγη<PERSON>η άδειας για υποβολή αιτημάτων 4K ταινιών.", "components.PermissionEdit.request4kMovies": "Αιτήματα για 4K Ταινίες", "components.PermissionEdit.request4k": "Αίτημα για 4K", "components.PermissionEdit.request4kDescription": "Χορήγη<PERSON>η άδειας για υποβολή αιτημάτων 4Κ περιεχομένου.", "components.PermissionEdit.request": "Αίτημα", "components.PermissionEdit.managerequestsDescription": "Χορήγηση άδειας για τη διαχείριση αιτημάτων. Όλα τα αιτήματα που υποβάλλει ένας χρήστης με αυτήν την άδεια θα εγκρίνονται αυτόματα.", "components.PermissionEdit.managerequests": "Διαχείριση Αιτημάτων", "components.PermissionEdit.autoapproveSeriesDescription": "Εκχώρηση αυτόματης έγκρισης για αιτήματα σειρών που δεν είναι 4K.", "components.PermissionEdit.autoapproveSeries": "Αυτόματη έγκριση Σειρών", "components.PermissionEdit.autoapproveMoviesDescription": "Εκχώρηση αυτόματης έγκρισης για αιτήματα ταινιών που δεν είναι 4K.", "components.PermissionEdit.autoapproveMovies": "Αυτόματη έγκριση Ταινιών", "components.PermissionEdit.autoapproveDescription": "Εκχώρηση αυτόματης έγκρισης για όλα τα αιτήματα που δεν είναι 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Εκχώρηση αυτόματης έγκρισης για αιτήματα σειρών 4K.", "components.PermissionEdit.autoapprove4kSeries": "Αυτόματη έγκριση Σειρών 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Εκχώρηση αυτόματης έγκρισης για αιτήματα ταινιών 4K.", "components.PermissionEdit.autoapprove4kMovies": "Αυτόματη Έγκριση Ταινιών 4K", "components.PermissionEdit.autoapprove4kDescription": "Εκχώρηση αυτόματης έγκρισης για όλα τα αιτήματα 4K.", "components.PermissionEdit.autoapprove4k": "Αυτόματη Έγκριση 4K", "components.PermissionEdit.autoapprove": "Αυτό-Έγκριση", "components.PermissionEdit.advancedrequestDescription": "Εκχώρηση δικαιωμάτων για χρήση των επιλογών αιτημάτων για προχωρημένους.", "components.PermissionEdit.advancedrequest": "Αιτήματα για Προχωρημένους", "components.PermissionEdit.adminDescription": "Πλήρης πρόσβαση διαχειριστή. Παρακάμπτει όλους τους άλλους ελέγχους δικαιωμάτων.", "components.PermissionEdit.admin": "Διαχειριστής", "components.NotificationTypeSelector.notificationTypes": "Τύποι Ειδοποιήσεων", "components.NotificationTypeSelector.mediarequestedDescription": "Αποστολ<PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν έχουν ζητηθεί μέσα και χρειάζονται έγκριση.", "components.NotificationTypeSelector.mediarequested": "Αίτημα σε αναμονή έγκρισης", "components.NotificationTypeSelector.mediafailedDescription": "Αποστολ<PERSON> ειδοποιήσεων όταν τα αιτούμενα μέσα δεν μπόρεσαν να προστεθούν στο Radarr ή στο Sonarr.", "components.NotificationTypeSelector.mediafailed": "Η επεξεργασία του αιτήματος απέτυχε", "components.NotificationTypeSelector.mediaavailableDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON>ων όταν τα αιτούμενα μέσα γίνονται διαθέσιμα.", "components.NotificationTypeSelector.mediadeclined": "Το αίτημα απορρίφθηκε", "components.NotificationTypeSelector.mediadeclinedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν ένα αίτημα μέσου απορρίπτεται.", "components.NotificationTypeSelector.mediaavailable": "Αίτημα διαθέσιμο", "components.NotificationTypeSelector.mediaapprovedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν αιτήματα εγκρίνονται χειροκίνητα.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON>ων όταν τα μέσα εγκρίνονται αυτόματα.", "components.NotificationTypeSelector.mediaapproved": "Το αίτημα εγκρίθηκε", "components.NotificationTypeSelector.mediaAutoApproved": "Το αίτημα εγκρίθηκε αυτόματα", "components.MovieDetails.watchtrailer": "Δες το Τρέιλερ", "components.MovieDetails.viewfullcrew": "Προβολή Πλήρους Συνεργείου", "components.MovieDetails.similar": "Παρόμοιοι Τίτλοι Ταινιών", "components.MovieDetails.runtime": "{minutes} λεπτά", "components.MovieDetails.revenue": "Έσοδα", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Release Date} other {Release Dates}}", "components.MovieDetails.recommendations": "Προτάσεις", "components.MovieDetails.overviewunavailable": "Επισκόπηση μη διαθέσιμη.", "components.MovieDetails.overview": "Επισκόπηση", "components.MovieDetails.originaltitle": "Αρ<PERSON><PERSON><PERSON><PERSON><PERSON>λ<PERSON>", "components.MovieDetails.originallanguage": "Αρχική Γλώσσα", "components.MovieDetails.markavailable": "Σήμανση ως Διαθέσιμο", "components.MovieDetails.mark4kavailable": "Σήμανση ως Διαθέσιμο σε 4K", "components.MovieDetails.MovieCast.fullcast": "Όλοι οι Ηθοποιοί", "components.MovieDetails.cast": "Ηθοποιοί", "components.MovieDetails.MovieCrew.fullcrew": "Πλή<PERSON><PERSON><PERSON>", "components.MovieDetails.budget": "Προϋπολογισμός", "components.MediaSlider.ShowMoreCard.seemore": "Δε<PERSON>σσ<PERSON>τε<PERSON>α", "components.Login.validationpasswordrequired": "Πρέπει να βάλεις έναν κωδικό πρόσβασης", "components.Login.validationemailrequired": "Πρέπει να δώσεις μια έγκυρη διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.Login.signinwithplex": "Χρησιμοποίησε τον Plex λογαριασμό σου", "components.Login.signinwithoverseerr": "Χρησιμοποίησε τον {applicationTitle} λογαριασμό σου", "components.Login.signinheader": "Συνδέσου για να συνεχίσεις", "components.Login.signingin": "Συνδέεται …", "components.Login.signin": "Σύνδεση", "components.Login.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "components.Login.loginerror": "Κάτι πήγε στραβά κατά την προσπάθεια σύνδεσης.", "components.Login.forgotpassword": "Ξέχασες τον κωδικό πρόσβασης;", "components.Login.email": "Διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.Layout.VersionStatus.streamstable": "Overseer Σταθερή Έκδοση", "components.Discover.popularmovies": "Δημοφιλείς Ταινίες", "components.Discover.discover": "Ανακάλυψε", "components.Discover.TvGenreSlider.tvgenres": "Είδη Σειρών", "components.Discover.TvGenreList.seriesgenres": "Είδη Σειρών", "components.Discover.StudioSlider.studios": "Στούντιο", "components.Discover.NetworkSlider.networks": "Δίκτυα", "components.Discover.MovieGenreSlider.moviegenres": "Είδη Tαινιών", "components.Discover.MovieGenreList.moviegenres": "Είδη Tαινιών", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Σειρά", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} Σειρά", "components.Discover.DiscoverStudio.studioMovies": "{studio} Ταινίες", "components.Discover.DiscoverNetwork.networkSeries": "{network} Σειρά", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Ταινίες", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Ταινίες", "components.CollectionDetails.requestcollection4k": "Αίτημα Συλλογής σε 4K", "components.CollectionDetails.requestcollection": "Αίτημα Συλλογής", "components.CollectionDetails.overview": "Επισκόπηση", "components.CollectionDetails.numberofmovies": "{count} Ταιν<PERSON>ες", "pages.somethingwentwrong": "Κάτι πήγε στραβά", "components.Layout.UserDropdown.signout": "Αποσύνδεση", "components.Layout.UserDropdown.settings": "Ρυθμίσεις", "components.Layout.UserDropdown.myprofile": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.settings": "Ρυθμίσεις", "components.Layout.Sidebar.requests": "Αιτήματα", "components.Layout.Sidebar.dashboard": "Ανακαλύψτε", "components.Layout.SearchInput.searchPlaceholder": "Αναζήτηση Ταινιών και Τηλεοπτικών <PERSON>ειρών", "components.LanguageSelector.originalLanguageDefault": "Όλες οι Γλώσσες", "components.LanguageSelector.languageServerDefault": "Προεπιλεγμένη ({language})", "components.DownloadBlock.estimatedtime": "Εκτιμώμενος {time}", "components.Discover.upcomingtv": "Επερχόμενες Σειρές", "components.Discover.upcomingmovies": "Επερχόμενες Ταινίες", "components.Discover.upcoming": "Επερχόμενες Ταινίες", "components.Discover.trending": "Τάσεις", "components.Discover.recentrequests": "Πρόσφατα Αιτήματα", "components.Discover.recentlyAdded": "Προστέθηκαν πρόσφατα", "components.Discover.populartv": "Δημοφιλείς <PERSON>ειρές", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Η δοκιμαστική ειδοποίηση LunaSea εστάλη!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Αποστολή δοκιμαστικής ειδοποίησης Luna<PERSON>ea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Αποτυχ<PERSON><PERSON> αποστολής δοκιμαστικής ειδοποίησης LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Οι ρυθμίσεις ειδοποιήσεων LunaSea αποθηκεύτηκαν με επιτυχία!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Οι ρυθμίσεις των ειδοποιήσεων LunaSea δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Χρειά<PERSON><PERSON><PERSON><PERSON><PERSON> μόνο εφόσον δεν χρησιμοποιείται το <code>default</code> προφίλ", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Όνομα Προφίλ", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Search.searchresults": "Αποτελέσματα αναζήτησης", "components.Search.search": "Αναζήτηση", "components.ResetPassword.validationpasswordrequired": "Πρέπει να βάλεις έναν κωδικό πρόσβασης", "components.ResetPassword.validationpasswordminchars": "Ο κωδικός πρόσβασης είν<PERSON><PERSON> πολύ σύντομος- θα πρέπει να αποτελείται από τουλάχιστον 8 χαρακτήρες", "components.ResetPassword.validationpasswordmatch": "Οι κωδικοί πρόσβασης πρέπει να ταιριάζουν", "components.ResetPassword.validationemailrequired": "Πρέπει να δώσεις μια έγκυρη διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.ResetPassword.resetpasswordsuccessmessage": "Η επαναφορά κωδικού πρόσβασης ολοκληρώθηκε με επιτυχία!", "components.ResetPassword.resetpassword": "Επαναφορ<PERSON> του κωδικού πρόσβασής σου", "components.ResetPassword.requestresetlinksuccessmessage": "Ένας σύνδεσμος επανα<PERSON><PERSON><PERSON><PERSON><PERSON> κωδικού πρόσβασης θα αποσταλεί στην παρεχόμενη διεύθυνση ηλεκτρονικού ταχυδρομείου, εάν αυτή σχετίζεται με έγκυρο χρήστη.", "components.ResetPassword.passwordreset": "Επαναφ<PERSON><PERSON><PERSON> κωδικού", "components.ResetPassword.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "components.ResetPassword.gobacklogin": "Επιστροφή στη σελίδα Εισόδου", "components.ResetPassword.emailresetlink": "Αποστολή συνδέσμου ανάκτησης με email", "components.ResetPassword.email": "Διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.ResetPassword.confirmpassword": "Επιβεβαίωση κωδικού πρόσβασης", "components.RequestModal.selectseason": "Επιλογή Σεζόν", "components.RequestModal.seasonnumber": "Σεζόν {number}", "components.RequestModal.season": "Σεζόν", "components.RequestModal.requestseasons": "Ζήτα {seasonCount} {seasonCount, plural, one {Σεζόν} other {Σεζόν}}", "components.RequestModal.requestfrom": "Το αίτημα του/της {username} εκκρεμεί προς έγκριση.", "components.RequestModal.requesterror": "Κάτι πήγε στραβ<PERSON> κατά την υποβολή του αιτήματος.", "components.RequestModal.requestedited": "Το αίτημα για <strong>{title}</strong> επεξεργάστηκε με επιτυχία!", "components.RequestModal.requestcancelled": "Το αίτημα για <strong>{title}</strong> ακυρώθηκε.", "components.RequestModal.requestadmin": "Αυτό το αίτημα θα εγκριθεί αυτόματα.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> ζητήθηκε επιτυχώς!", "components.RequestModal.requestCancel": "Το αίτημα για <strong>{title}</strong> ακυρώθηκε.", "components.RequestModal.pendingrequest": "Εκκρεμές αίτημα", "components.RequestModal.pendingapproval": "Το αίτημα σου εκκρεμεί προς έγκριση.", "components.RequestModal.pending4krequest": "Εκκρεμές αίτημα 4K", "components.RequestModal.errorediting": "Κάτι πήγε στραβ<PERSON> κατά την επεξεργασία του αιτήματος.", "components.RequestModal.edit": "Επεξεργασία Αιτήματος", "components.RequestModal.cancel": "Ακύρωση Αιτήματος", "components.RequestModal.autoapproval": "Αυτόματη Έγκριση", "components.RequestModal.alreadyrequested": "Έχει ήδη ζητηθεί", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Δεν μπορέσαμε να αντιστοιχίσουμε αυτόματα αυτή τη σειρά. Παρα<PERSON><PERSON><PERSON><PERSON> επίλεξε τη σωστή αντιστοιχία παρακάτω.", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {σεζόν} other {σεζόν}}", "components.RequestModal.QuotaDisplay.season": "σεζόν", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Αυτ<PERSON>ς ο χρήστης χρειάζεται τουλάχιστον <strong>{seasons}</strong> {seasons, plural, one {αίτημα σεζόν} other {αιτήματα σεζόν}} απομένουν έτσι ώστε να γίνει αίτημα για αυτή τη σειρά.", "components.RequestModal.QuotaDisplay.requiredquota": "Πρέπει να έχεις τουλάχιστον <strong>(seasons)</strong> {seasons, plural, one {αίτημα σεζόν} other {αιτήματα σεζόν}} απομένουν έτσι ώστε να γίνει υποβολή αιτήματος για αυτή τη σειρά.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {Κανένα} other {<strong>#</strong>}} {type} {remaining, plural, one {αίτημα} other {αιτήματα}} απομένουν", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Μπορείς να δείς την σύνοψη των ορίων των αιτημάτων του χρήστη στη <ProfileLink> σελίδα προφίλ του</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Μπορείς να δεις μια σύνοψη των ορίων των αιτημάτων σου στη <ProfileLink> σελίδα προφίλ σου </ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Δεν απομένουν άλλα αιτήματα για σεζόν", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {ταινία} other {ταινίες}}", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.default": "{name} (Προεπιλογή)", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {Σεζόν(πλυθ.)}}", "components.RequestList.RequestItem.modifieduserdate": "{date} από {user}", "components.RequestCard.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {Σεζόν(πλυθ.)}}", "components.RequestButton.declinerequests": "Απόρριψη {requestCount, plural, one {Αίτημα} other {{requestCount} Αιτήματα}}", "components.RequestButton.decline4krequests": "Απόρριψη {requestCount, plural, one {4K Αίτημα} other {{requestCount} 4K Αιτήματα}}", "components.RequestButton.approverequests": "Έγκριση {requestCount, plural, one {Αίτημα} other {{requestCount} Αιτήματα}}", "components.RequestButton.approve4krequests": "Έγκριση {requestCount, plural, one {4K Αίτημα} other {{requestCount} 4K Αιτήματα}}", "components.RequestBlock.server": "Προοριζ<PERSON><PERSON><PERSON><PERSON>ος Διακομιστής", "components.RequestBlock.seasons": "{seasonCount, plural, one {<PERSON><PERSON><PERSON><PERSON><PERSON>} other {Σεζόν(πλυθ.)}}", "components.RequestBlock.rootfolder": "Ριζικός φάκελος", "components.RegionSelector.regionServerDefault": "Προεπιλογή ({region})", "components.QuotaSelector.unlimited": "Απεριόριστο", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.ascharacter": "ως {character}", "components.PermissionEdit.usersDescription": "Χορήγη<PERSON>η άδειας για διαχείρηση των χρηστών. Οι χρήστες με αυτή την άδεια δεν μπορούν να τροποποιήσουν τους χρήστες με το προνόμιο του διαχειριστή ή να κάνουν κάποιον διαχειριστή.", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON>", "components.AppDataWarning.dockerVolumeMissingDescription": "Η <code>{appDataPath}</code> προσάρτηση τόμου δεν έχει ρυθμιστεί σωστά. Όλα τα δεδομένα θα διαγραφούν όταν ο περιέχοντας σταματήσει ή επανεκκινήσει.", "components.RequestModal.AdvancedRequester.rootfolder": "Ριζικός φάκελος", "components.RequestModal.QuotaDisplay.movie": "ταινία", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Αυτός ο χρήστης επιτρέπεται να ζητάει <strong>{limit}</strong> {type} κάθε <strong>{days}</strong> ημέρες.", "components.RequestModal.QuotaDisplay.allowedRequests": "Επιτρέπεται να ζητήσεις <strong>{limit}</strong> {type} κάθε <strong>{days}</strong> ημέρες.", "components.RequestModal.AdvancedRequester.tags": "Ετικέτες", "components.RequestModal.AdvancedRequester.selecttags": "Επιλογή ετικετών", "components.RequestModal.AdvancedRequester.requestas": "Αίτημα ως", "components.RequestModal.AdvancedRequester.qualityprofile": "<PERSON>ρο<PERSON><PERSON><PERSON>ς", "components.RequestModal.AdvancedRequester.notagoptions": "Δεν υπάρχουν ετικέτες.", "components.RequestModal.AdvancedRequester.languageprofile": "<PERSON>ρο<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.destinationserver": "Προορισμός Διακομιστή", "components.RequestModal.AdvancedRequester.animenote": "* Αυτή η σειρά είναι anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "Για προχωρημένους", "components.RequestList.sortModified": "Τελευταία Τροποποίηση", "components.RequestList.sortAdded": "Πιο πρόσφατα", "components.RequestList.showallrequests": "Εμφάνιση Όλων των Αιτημάτων", "components.RequestList.requests": "Αιτήματα", "components.RequestList.RequestItem.requested": "Ζητήθηκε", "components.RequestList.RequestItem.modified": "Τροποποιήθηκε", "components.RequestList.RequestItem.mediaerror": "{mediaType} Δε βρέθηκε", "components.RequestList.RequestItem.failedretry": "Κάτι πήγε στρα<PERSON><PERSON> κατά την επανάληψη του αιτήματος.", "components.RequestList.RequestItem.editrequest": "Επεξεργασία Αιτήματος", "components.RequestList.RequestItem.deleterequest": "Διαγρα<PERSON><PERSON> Αιτήματος", "components.RequestList.RequestItem.cancelRequest": "Ακύρωση Αιτήματος", "components.RequestCard.mediaerror": "{mediaType} Δε βρέθηκε", "components.RequestCard.deleterequest": "Διαγρα<PERSON><PERSON> Αιτήματος", "components.RequestButton.viewrequest4k": "Προβολή 4K Αιτήματος", "components.RequestButton.viewrequest": "Προβολή <PERSON>ιτή<PERSON>τος", "components.RequestButton.requestmore4k": "Ζήτα περισσότερα σε 4K", "components.RequestButton.requestmore": "Ζήτα Περισσότερα", "components.RequestButton.declinerequest4k": "Απόρριψη 4K Αιτήματος", "components.RequestButton.declinerequest": "Απόρριψη Αιτήματος", "components.RequestButton.approverequest4k": "Έγκριση 4K Αιτήματος", "components.RequestButton.approverequest": "Έγκριση Αιτήματος", "components.RequestBlock.requestoverrides": "Παρακάμψεις των Αιτημάτων", "components.RequestBlock.profilechanged": "<PERSON>ρο<PERSON><PERSON><PERSON>ς", "components.RegionSelector.regionDefault": "Όλες οι Περιοχές", "components.PersonDetails.crewmember": "Συνεργείο", "components.PersonDetails.appearsin": "Εμφανίσεις", "components.PersonDetails.alsoknownas": "Επίσης Γνωστός/ή ως: {names}", "components.PermissionEdit.viewrequestsDescription": "Χορήγη<PERSON><PERSON> άδειας προβολής αιτημάτων που υποβλήθηκαν από άλλους χρήστες.", "components.PermissionEdit.viewrequests": "Προβολή <PERSON>τημάτων", "components.Settings.Notifications.validationChatIdRequired": "Πρέπει να δώσεις ένα έγκυρο αναγνωριστικό συνομιλίας", "components.Settings.Notifications.validationBotAPIRequired": "Πρέπει να δώσεις μια άδεια εξουσιοδότησης bot", "components.Settings.Notifications.toastTelegramTestSuccess": "Η δοκιμαστική ειδοποίηση Telegram εστάλη!", "components.Settings.Notifications.toastTelegramTestSending": "Γίνεται αποστολή δοκιμαστικής ειδοποίησης Telegram…", "components.Settings.Notifications.toastTelegramTestFailed": "Η δοκιμαστική ειδοποίηση Telegram δεν κατάφερε να σταλεί.", "components.Settings.Notifications.toastEmailTestSuccess": "Η δοκιμαστική ειδοποίηση email εστάλη!", "components.Settings.Notifications.toastEmailTestSending": "Γίνεται αποστολή δοκιμαστικής ειδοποίησης email…", "components.Settings.Notifications.toastEmailTestFailed": "Η δοκιμαστική ειδοποίηση email δεν κατάφερε να σταλεί.", "components.Settings.Notifications.toastDiscordTestSuccess": "Η δοκιμαστική ειδοποίηση Discord εστάλη!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Η δοκιμαστική ειδοποίηση Slack εστάλη!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Γίνεται αποστολή δοκιμαστικής ειδοποίησης <PERSON>…", "components.Settings.Notifications.toastDiscordTestSending": "Γίνεται αποστολή δοκιμαστικής ειδοποίησης Discord…", "components.Settings.Notifications.toastDiscordTestFailed": "Η δοκιμαστική ειδοποίηση Discord δεν κατάφερε να σταλεί.", "components.Settings.Notifications.telegramsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων Telegram αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.telegramsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων Telegram δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.smtpPort": "Θύρα SMTP", "components.Settings.Notifications.smtpHost": "Κεντρι<PERSON><PERSON>ς υπολογιστής SMTP", "components.Settings.Notifications.senderName": "Όνομα αποστολέα", "components.Settings.Notifications.sendSilentlyTip": "Στείλε ειδοποιή<PERSON>εις χωρίς ήχο", "components.Settings.Notifications.sendSilently": "Αθόρυβη Αποστολή", "components.Settings.Notifications.pgpPrivateKeyTip": "Υπογραφή κρυπτογραφημένων μηνυμάτων email χρησιμοποιώντας <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Ιδιωτικό κλειδί PGP", "components.Settings.Notifications.pgpPasswordTip": "Υπογραφή κρυπτογραφημένων μηνυμάτων email χρησιμοποιώντας <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Κω<PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης PGP", "components.Settings.Notifications.encryptionTip": "Στις περισσότερες περιπτώσει<PERSON>, το Implicit TLS χρησιμοποιεί τη θύρα 465 και το STARTTLS τη θύρα 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Να χρησιμοποιείς πάντα το STARTTLS", "components.Settings.Notifications.encryptionNone": "Καμιά", "components.Settings.Notifications.encryptionImplicitTls": "Χρήση Implicit TLS", "components.Settings.Notifications.encryptionDefault": "Χρήση STARTTLS αν υπάρχει", "components.Settings.Notifications.encryption": "<PERSON><PERSON>θ<PERSON><PERSON><PERSON> κρυπτογράφησης", "components.Settings.Notifications.emailsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων για το email αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.emailsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων για το email δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.emailsender": "Διεύθυνση αποστολέα", "components.Settings.Notifications.discordsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων Discord αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.discordsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων Discord δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.chatIdTip": "Ξεκίνα μια συνομιλία με το bot σου, πρόσθεσε <GetIdBotLink>@get_id_bot</GetIdBotLink>, και έκδωσε την εντολή <code>/my_id</code>", "components.Settings.Notifications.chatId": "Αναγνωριστικ<PERSON> συνομιλίας", "components.Settings.Notifications.botUsernameTip": "Επίτρεψε στους χρήστες να ξεκινήσουν επίσης μια συνομιλία με το bot σου και να ρυθμίσουν τις δικές τους ειδοποιήσεις", "components.Settings.Notifications.botUsername": "Όνομα χρήστη Bot", "components.Settings.Notifications.botAvatarUrl": "Σύνδεσμος για το Avatar του Bot", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Δημιουργία bot</CreateBotLink> για χρήση με Je<PERSON>rr", "components.Settings.Notifications.botAPI": "Διακριτι<PERSON><PERSON> εξουσιοδότη<PERSON><PERSON><PERSON>", "components.Settings.Notifications.authUser": "Όνομα χρήστη SMTP", "components.Settings.Notifications.authPass": "Κωδι<PERSON><PERSON><PERSON> πρόσβασης SMTP", "components.Settings.Notifications.allowselfsigned": "Επίτρεψε Αυτο-Υπογεγραμμένα Πιστοποιητικά", "components.Settings.Notifications.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων Webhook αποθηκεύτηκαν με επιτυχία!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Οι ρυθμίσεις των ειδοποιή<PERSON><PERSON><PERSON><PERSON> Slack δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Οι ρυθμίσεις των ειδοποιή<PERSON><PERSON><PERSON><PERSON><PERSON> αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Οι ρυθμίσεις των ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Οι ρυθμίσεις των ειδοποιή<PERSON><PERSON>ων <PERSON>et δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων Webhook δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Πρέπει να βάλεις ένα έγκυρο φορτίο πληροφοριών JSON", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Η δοκιμαστική ειδοποίηση webhook εστάλη!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Αποστολή δοκιμαστικής ειδοποίησης webhook…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Η δοκιμαστική ειδοποίηση webhook δεν κατάφερε να σταλεί.", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Η επαναφορά του φορτίου πληροφοριώ<PERSON> Jason ολοκληρώθηκε με επιτυχία!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Επαναφορά προεπιλογών", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Φορτίο Πληροφοριών", "components.Settings.Notifications.NotificationsWebhook.authheader": "Κεφαλίδα Εξουσιοδότησης", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων push αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων push δεν κατάφεραν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Η δοκιμαστική ειδοποίηση push εστάλη!", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Για να λάβεις ειδοποιήσεις push, το <PERSON><PERSON><PERSON>rr πρέπει να προβάλλεται μέσω HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Η δοκιμαστική ειδοποιήση push δεν κατάφερε να σταλεί.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Αποστολή δοκιμαστικής ειδοποίησης push…", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Δημιούργησε ένα <WebhookLink> Εισερχόμενο Webhook</WebhookLink>", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Αποτυχ<PERSON><PERSON> αποστολής δοκιμαστικής ειδοποίησης <PERSON>.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Οι ρυθμίσεις ειδοποιήσεων <PERSON>ck αποθηκεύτηκαν με επιτυχία!", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Πρέπει να δώσετε ένα έγκυρο κλειδί χρήστη ή ομάδας", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Πρέπει να δώσεις ένα έγκυρο διακριτικό εφαρμογής", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "<UsersGroupsLink>Το αναγνωριστικό χρήστη ή ομάδας 30 χαρακτήρων</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.userToken": "Κλειδί Χρήστη ή Ομάδας", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Η δοκιμαστική ειδοποίηση Pushover εστάλη!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Η δοκιμαστική ειδοποίηση Pushbullet εστάλη!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Αποστολή δοκιμαστικής ειδοποίη<PERSON>η<PERSON> …", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Αποστολή δοκιμαστικής ειδοποί<PERSON><PERSON><PERSON><PERSON> …", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Αποτυχ<PERSON><PERSON> αποστολής δοκιμαστικής ειδοποιήσης <PERSON>.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Αποτυχ<PERSON><PERSON> αποστολής δοκιμαστικής ειδοποίησης <PERSON>.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Καταχώρηση εφαρμογής</ApplicationRegistrationLink> για χρήση με Je<PERSON>seerr", "components.Settings.Notifications.NotificationsPushover.accessToken": "Διακριτικ<PERSON> API Εφαρμογής", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Πρέπει να δώσεις ένα διακριτικό πρόσβασης", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Οι ρυθμίσεις για τις ειδοποιή<PERSON><PERSON>ις Push<PERSON>et αποθηκεύτηκαν με επιτυχία!", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Ενεργοποίηση του Μεταφορέα", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Δημιουργία διακριτικού πρόσβασης από <PushbulletSettingsLink>τις Ρυθμίσεις Λογαρισμού</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Διακριτικ<PERSON> πρόσβασης", "components.Settings.RadarrModal.create4kradarr": "Προσθήκη νέου διακομιστή 4K Radarr", "components.Settings.RadarrModal.baseUrl": "Βασική Διεύθυνση URL", "components.Settings.RadarrModal.apiKey": "Κλειδί API", "components.Settings.RadarrModal.add": "Προσθήκη διακομιστή", "components.Settings.Notifications.webhookUrlTip": "Δημιουργία ενός <DiscordWebhookLink>webhook integration</DiscordWebhookLink> στον σέρβερ σου", "components.Settings.Notifications.webhookUrl": "Webhook URL", "components.Settings.Notifications.validationUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.Notifications.validationSmtpPortRequired": "Πρέπει να δώσεις έναν έγκυρο αριθμό θύρας", "components.Settings.Notifications.validationSmtpHostRequired": "Πρέπει να δώσεις ένα έγκυρο όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.validationHostnameRequired": "Πρέπει να δώσεις ένα έγκυρο όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.Notifications.validationPgpPrivateKey": "Πρέπει να δώσετε ένα έγκυρο ιδιωτικό κλειδί PGP", "components.Settings.Notifications.validationPgpPassword": "Πρέπει να δώσετε έναν κωδικό πρόσβασης PGP", "components.Settings.Notifications.validationEmail": "Πρέπει να δώσεις μια έγκυρη διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Καθολικό όριο αιτημάτων στις Ταινίες", "components.Settings.SettingsUsers.localLogin": "Ενεργοποίηση τοπικής σύνδεσης", "components.Settings.SettingsUsers.defaultPermissions": "Προεπιλεγμένα δικαιώματα", "components.Settings.SettingsLogs.time": "Χρονική σήμανση", "components.Settings.SettingsLogs.showall": "Εμφάνιση όλων των αρχείων καταγραφής", "components.Settings.SettingsLogs.resumeLogs": "Συνέχιση", "components.Settings.SettingsLogs.pauseLogs": "Παύση", "components.Settings.SettingsLogs.message": "Μήνυμα", "components.Settings.SettingsLogs.logsDescription": "Μπορείτε επίσης να δείτε αυτά τα αρχεία καταγραφής απευθείας μέσω του <code>stdout</code> ή στο <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Αρχεία καταγραφής", "components.Settings.SettingsLogs.logDetails": "Λεπτομέρειες αρχείου καταγραφής", "components.Settings.SettingsLogs.level": "Σοβαρότητα", "components.Settings.SettingsLogs.label": "Ετικέτα", "components.Settings.SettingsLogs.filterWarn": "Προσοχή", "components.Settings.SettingsLogs.filterInfo": "Πληροφορίες", "components.Settings.SettingsLogs.filterError": "Σφάλμα", "components.Settings.SettingsLogs.filterDebug": "Εντοπισμός σφαλμάτων", "components.Settings.SettingsLogs.extraData": "Επιπρόσθετα Δεδομένα", "components.Settings.SettingsLogs.copyToClipboard": "Αντιγρα<PERSON><PERSON> στο Πρόχειρο", "components.Settings.SettingsLogs.copiedLogMessage": "Αντιγράφηκε το μήνυμα καταγραφής στο πρόχειρο.", "components.Settings.SettingsJobsCache.unknownJob": "Άγνωστη εργασία", "components.Settings.SettingsJobsCache.sonarr-scan": "Σάρωση Sonarr", "components.Settings.SettingsJobsCache.runnow": "Τρέξε τώρα", "components.Settings.SettingsJobsCache.radarr-scan": "Σάρωση Radarr", "components.Settings.SettingsJobsCache.jobsandcache": "Εργα<PERSON>ίες & Κρυφή μνήμη", "components.Settings.SettingsJobsCache.jobs": "Εργασίες", "components.Settings.SettingsJobsCache.jobname": "Όνομα εργασίας", "components.Settings.SettingsJobsCache.canceljob": "Ακύρωση Εργασίας", "components.Settings.SettingsJobsCache.process": "Διεργασία", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Σάρωση Plex για μέσα που προστέθηκαν πρόσφατα", "components.Settings.SettingsJobsCache.plex-full-scan": "Σάρωση πλήρους βιβλιοθήκης Plex", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Σάρωση πλήρους βιβλιοθή<PERSON>η<PERSON>", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Σάρω<PERSON><PERSON> <PERSON><PERSON>fin για μέσα που προστέθηκαν πρόσφατα", "components.Settings.SettingsJobsCache.nextexecution": "Επόμενη εκτέλεση", "components.Settings.SettingsJobsCache.jobtype": "Είδος", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} ξε<PERSON>ίνησε.", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} ακυρώθηκε.", "components.Settings.SettingsJobsCache.flushcache": "Εκκαθάριση κρυφής μνήμης", "components.Settings.SettingsJobsCache.download-sync-reset": "Επαναρύθμιση Λήψης Συγχρονισμού", "components.Settings.SettingsJobsCache.download-sync": "Λήψη συγχρονισμού", "components.Settings.SettingsJobsCache.command": "Εντολή", "components.Settings.SettingsJobsCache.cachevsize": "Μέγεθος", "components.Settings.SettingsJobsCache.cachename": "Όνομα κρυφής μνήμης", "components.Settings.SettingsJobsCache.cachemisses": "Χαμένες", "components.Settings.SettingsJobsCache.cacheksize": "Μέγεθος κλειδιού", "components.Settings.SettingsJobsCache.cachekeys": "Σύνολο κλειδιών", "components.Settings.SettingsJobsCache.cachehits": "Κλήσεις", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} εκκαθαρίστηκε η κρυφή μνήμη.", "components.Settings.SettingsJobsCache.cacheDescription": "Τ<PERSON> <PERSON>rr αποθηκεύει προσωρινά αιτήματα σε εξωτερικά τελικά σημεία API για τη βελτιστοποίηση της απόδοσης και την αποφυγή περιττών κλήσεων API.", "components.Settings.SettingsJobsCache.cache": "Κρυφή μνήμη", "components.Settings.SettingsAbout.version": "Έκδοση", "components.Settings.SettingsAbout.Releases.releases": "Εκδόσεις", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Τα δεδομένα έκδοσης δεν είναι διαθέσιμα αυτή τη στιγμή.", "components.Settings.SettingsAbout.Releases.latestversion": "Πιο πρόσφατη", "components.Settings.SettingsAbout.Releases.currentversion": "Τρέχουσα", "components.Settings.RadarrModal.validationRootFolderRequired": "Πρέπει να επιλέξεις ένα ριζικ<PERSON> φάκελο", "components.Settings.RadarrModal.validationProfileRequired": "Πρέπει να επιλέξεις ένα προφίλ ποιότητας", "components.Settings.RadarrModal.validationPortRequired": "Πρέπει να δώσεις έναν έγκυρο αριθμό θύρας", "components.Settings.RadarrModal.validationNameRequired": "Πρέπει να δώσεις ένα όνομα διακομιστή", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Πρέπει να επιλέξεις μια ελάχιστη διαθεσιμότητα", "components.Settings.RadarrModal.validationHostnameRequired": "Πρέπει να δώσετε ένα έγκυρο όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Η βάση URL δεν πρέπει να τελειώνει με κάθετο", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Η βάση URL πρέπει να έχει μια κάθετο μπροστά", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Η διεύθυνση URL δεν πρέπει να τελειώνει με κάθετο", "components.Settings.RadarrModal.validationApplicationUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.RadarrModal.validationApiKeyRequired": "Πρέπει να βάλεις ένα κλειδί API", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Η σύνδεση με το Radarr ολοκληρώθηκε με επιτυχία!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Αποτυχία σύνδεσης με το Radarr.", "components.Settings.RadarrModal.testFirstTags": "Δοκι<PERSON>ή σύνδεσης για την φόρτωση των ετικετών", "components.Settings.RadarrModal.testFirstQualityProfiles": "Δοκι<PERSON><PERSON> σύνδεσης για την φόρτωση των προφίλ ποιότητας", "components.Settings.RadarrModal.testFirstRootFolders": "Δοκι<PERSON>ή σύνδεσης για την φόρτωση των ριζικών φακέλων", "components.Settings.RadarrModal.tags": "Ετικέτες", "components.Settings.RadarrModal.syncEnabled": "Ενεργοποίηση σάρωσης", "components.Settings.RadarrModal.ssl": "Χρήση SSL", "components.Settings.RadarrModal.servername": "Όνομα διακομιστή", "components.Settings.RadarrModal.server4k": "Διακομιστής 4K", "components.Settings.RadarrModal.rootfolder": "Ριζικός φάκελος", "components.Settings.RadarrModal.selecttags": "Επιλογή ετικετών", "components.Settings.RadarrModal.selectRootFolder": "Επίλεξε ριζικ<PERSON> φάκελο", "components.Settings.RadarrModal.selectQualityProfile": "Επίλεξε προφίλ ποιότητας", "components.Settings.RadarrModal.selectMinimumAvailability": "Επιλογή ελάχιστης διαθεσιμότητας", "components.Settings.RadarrModal.qualityprofile": "<PERSON>ρο<PERSON><PERSON><PERSON>ς", "components.Settings.RadarrModal.port": "Θύρα", "components.Settings.RadarrModal.notagoptions": "Δεν υπάρχουν ετικέτες.", "components.Settings.RadarrModal.minimumAvailability": "Ελάχιστη διαθεσιμότητα", "components.Settings.RadarrModal.loadingrootfolders": "Φόρτωση ριζικών φακέλων…", "components.Settings.RadarrModal.loadingprofiles": "Φόρτωση των προφίλ ποιότητας…", "components.Settings.RadarrModal.loadingTags": "Φόρτωση ετικετών…", "components.Settings.RadarrModal.hostname": "Όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.RadarrModal.externalUrl": "Εξωτερική διεύθυνση URL", "components.Settings.RadarrModal.enableSearch": "Ενεργοποίηση αυτόματης αναζήτησης", "components.Settings.RadarrModal.editradarr": "Επεξεργασία διακομιστή Radarr", "components.Settings.RadarrModal.edit4kradarr": "Επεξεργασία διακομιστή 4K Radarr", "components.Settings.RadarrModal.defaultserver": "Προεπιλεγμένος διακομιστής", "components.Settings.RadarrModal.default4kserver": "Προεπιλεγμένος διακομιστής 4K", "components.Settings.RadarrModal.createradarr": "Προσθήκη νέου διακομιστή Radarr", "components.Settings.SettingsAbout.uptodate": "Ενημερωμένο", "components.Layout.VersionStatus.outofdate": "Ξεπερασμένο", "components.Settings.SettingsAbout.outofdate": "Ξεπερασμένο", "components.Settings.SettingsAbout.totalrequests": "Σύνολο αιτημάτων", "components.Settings.SettingsAbout.totalmedia": "Σύνολο μέσων", "components.Settings.SettingsAbout.timezone": "Ζώνη ώρας", "components.Settings.SettingsAbout.supportoverseerr": "Υποστήριξε το <PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Προτιμώνενο", "components.Settings.SettingsAbout.overseerrinformation": "Σχετικά με το <PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Κέρασε κάνα Καφεδάκι", "components.Settings.SettingsAbout.githubdiscussions": "Συζητήσεις στο <PERSON>", "components.Settings.SettingsAbout.gettingsupport": "Λήψη Υποστήριξης", "components.Settings.SettingsAbout.documentation": "Εγχειρίδιο", "components.Settings.SettingsAbout.about": "Σχετικά", "components.Settings.SettingsAbout.Releases.viewongithub": "Προβολή στο <PERSON>", "components.Settings.SettingsAbout.Releases.versionChangelog": "{version} Αλλαγές", "components.Settings.SettingsAbout.Releases.viewchangelog": "Προβολή καταλόγου με τις αλλαγές", "i18n.settings": "Ρυθμίσεις", "i18n.saving": "Αποθηκεύεται…", "i18n.save": "Αποθήκευση αλλαγών", "i18n.retrying": "Γίνεται επαναπροσπάθεια…", "i18n.retry": "Δοκίμασε ξανά", "i18n.resultsperpage": "Εμφάνιση {pageSize} αποτελεσμάτων ανά σελίδα", "i18n.requesting": "Γίνεται το αίτημα…", "i18n.requested": "Ζητήθηκε", "i18n.request4k": "Ζήτα το σε 4K", "i18n.request": "Ζήτα το", "i18n.processing": "Επεξεργάζεται", "i18n.previous": "Προηγούμενο", "i18n.pending": "Εκκρεμεί", "i18n.partiallyavailable": "Μερικώς Διαθέσιμο", "i18n.notrequested": "Δεν έχει ζητηθεί", "i18n.noresults": "Δεν υπάρχουν αποτελέσματα.", "i18n.next": "Επόμενο", "i18n.movies": "Ταινίες", "i18n.movie": "Ταινία", "i18n.loading": "Φόρτωση…", "i18n.failed": "Απέτυχε", "i18n.experimental": "Πειραματικό", "i18n.edit": "Επεξεργασία", "i18n.delimitedlist": "{a}, {b}", "i18n.deleting": "Διαγράφεται…", "i18n.delete": "Διαγραφή", "i18n.declined": "Απορρίφθηκε", "i18n.decline": "Απόρριψη", "i18n.close": "Κλείσιμο", "i18n.canceling": "Ακυρώνεται…", "i18n.cancel": "Ακύρωση", "i18n.back": "Πίσω", "i18n.available": "Διαθέσιμο", "i18n.areyousure": "Είσαι σίγουρος;", "i18n.approved": "Εγκρίθηκε", "i18n.approve": "Έγκριση", "i18n.all": "Όλα", "i18n.advanced": "Για προχωρημένους", "components.UserProfile.unlimited": "Απεριόριστο", "components.UserProfile.totalrequests": "Σύνολο αιτημάτων", "components.UserProfile.seriesrequest": "Αιτήματ<PERSON>ειρών", "components.UserProfile.requestsperdays": "Απομένει {limit}", "components.UserProfile.recentrequests": "Πρόσφατα Αιτήματα", "components.UserProfile.pastdays": "{type} (περασμένες {days} ημέρες)", "components.UserProfile.movierequests": "Αιτήματα Ταινιών", "components.UserProfile.limit": "{remaining} από {limit}", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "Δεν έχεις άδεια να τροποποιήσεις τον κωδικό πρόσβασης αυτού του χρήστη.", "components.UserProfile.UserSettings.unauthorizedDescription": "Δεν έχεις άδεια να τροποποιήσεις τις ρυθμίσεις αυτού του χρήστη.", "components.UserProfile.UserSettings.menuPermissions": "Άδειες", "components.UserProfile.UserSettings.menuNotifications": "Ειδοποιήσεις", "components.UserProfile.UserSettings.menuGeneralSettings": "Γενικ<PERSON>ς", "components.UserProfile.UserSettings.menuChangePass": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Δεν μπορείς να τροποποιήσεις τις δικές σου άδειες.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Οι άδειες αποθηκεύτηκαν με επιτυχία!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση των ρυθμίσεων.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Άδειες", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Ο κωδικός πρόσβασης είν<PERSON><PERSON> πολύ σύντομος- θα πρέπει να αποτελείται από τουλάχιστον 8 χαρακτήρες", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Πρέπει να βάλεις έναν νέο κωδικό πρόσβασης", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Πρέπει να δώσεις τον τρέχοντα κωδικό πρόσβασής σου", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Οι κωδικοί πρόσβασης πρέπει να ταιριάζουν", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Πρέπει να επιβεβαιώσεις τον νέο κωδικό πρόσβασης", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Ο κωδικός πρόσβασης αποθηκεύτηκε με επιτυχία!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Κάτι πήγε στραβά κατά την αποθήκευση του κωδικού πρόσβασης. Εισήχθη σωστά ο τρέχων κωδικός πρόσβασης;", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση του κωδικού πρόσβασης.", "components.UserProfile.UserSettings.UserPasswordChange.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Ο λογαριασμός σου προς το παρόν δεν έχει ορίσει κωδικός πρόσβασης. Διαμόρφωσε έναν κωδικ<PERSON> πρόσβασης παρακάτω για να μπορέσεις να συνδεθείς ως \"τοπικός χρήστης\" χρησιμοποιώντας τη διεύθυνση ηλεκτρονικού ταχυδρομείου σου.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Αυτός ο λογαριασμός χρήστη δεν έχει ορίσει έναν κωδικό πρόσβασης. Διαμόρφωσε έναν κωδικό πρόσβασης παρακάτω για να μπορέσει αυτός ο λογαριασμός να συνδεθεί ως \"τοπικός χρήστης\".", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON><PERSON> κωδικ<PERSON>ς πρόσβασης", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Τρέχων κωδικ<PERSON>ς πρόσβασης", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Επιβεβαίωση κωδικού πρόσβασης", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων push αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων push δεν κατάφεραν να αποθηκευτούν.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Πρέπει να δώσεις ένα έγκυρο αναγνωριστικό συνομιλίας", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Πρέπει να βάλεις ένα έγκυρο δημόσιο κλειδί PGP", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Πρέπει να δώσεις ένα έγκυρο αναγνωριστικό χρήστη", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων Telegram αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων Telegram δεν κατάφεραν να αποθηκευτούν.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Ξεκίνα μια συνομιλία</TelegramBotLink>, πρόσθεσε το <GetIdBotLink>@get_id_bot</GetIdBotLink> και δώσε την εντολή <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Αναγνωριστικ<PERSON> συνομιλίας", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Στείλε ειδοποιή<PERSON>εις χωρίς ήχο", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Αθόρυβη Αποστολή", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Κρυπτογράφηση μηνυμάτων ηλεκτρονικού ταχυδρομείου χρησιμοποιώντας <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Δημόσιο κλειδί PGP", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Ρυθμίσεις ειδοποιήσεων", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Ειδοποιήσεις", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων για το email αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων για το email δεν κατάφεραν να αποθηκευτούν.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Οι ρυθμίσεις των ειδοποιήσεων Discord αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Οι ρυθμίσεις των ειδοποιήσεων Discord δεν κατάφεραν να αποθηκευτούν.", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "Ο <FindDiscordIdLink>multi-digit ID αριθμός</FindDiscordIdLink> που σχετίζεται με τον λογαριασμό σας", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Αναγνωριστικ<PERSON> χρήστη", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Οι ρυθμίσεις αποθηκεύτηκαν με επιτυχία!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση των ρυθμίσεων.", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Όριο Αιτημάτων Σειρών", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Ρόλος", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Φιλτράρει το περιεχόμενο βάσει της διαθεσιμότητας του περιεχομένου", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Ανακάλυψη βάσει περιοχής", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "<PERSON>ρή<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Ιδιοκτήτης", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Φίλτραρε το περιεχόμενο με βάση την αρχική γλώσσα", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Ανακάλυψη με βάση την γλώσσα", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Όριο Αιτημάτων Ταινιών", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Το<PERSON><PERSON><PERSON><PERSON><PERSON> χρήστης", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Προεπιλεγμένη ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Γενικές Ρυθμίσεις", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Γενικ<PERSON>ς", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Παράκαμψη καθολικού ορίου", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Εμφανιζόμενο όνομα", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Προβολ<PERSON>λώσσ<PERSON>ς", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Διαχειριστής", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Τύπος Λογαριασμού", "components.UserProfile.ProfileHeader.userid": "Αναγνωριστικό χρήστη: {userid}", "components.UserProfile.ProfileHeader.settings": "Επεξεργασία ρυθμίσεων", "components.UserProfile.ProfileHeader.profile": "Προβολή προφίλ", "components.UserProfile.ProfileHeader.joindate": "Έγινε μέλος {joindate}", "components.UserList.validationpasswordminchars": "Ο κωδικός πρόσβασης είν<PERSON><PERSON> πολύ σύντομος- θα πρέπει να αποτελείται από τουλάχιστον 8 χαρακτήρες", "components.UserList.validationEmail": "Πρέπει να δώσεις μια έγκυρη διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.UserList.userssaved": "Τα δικαιώματα χρήστη αποθηκεύτηκαν με επιτυχία!", "components.UserList.users": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.userlist": "Λίστα χρηστών", "components.UserList.userfail": "Κάτι πήγε στρα<PERSON><PERSON> κατά την αποθήκευση των δικαιωμάτων του χρήστη.", "components.UserList.userdeleteerror": "Κάτι πήγε στραβά κατά τη διαγραφή του χρήστη.", "components.UserList.userdeleted": "Ο χρήστης διαγράφηκε επιτυχώς!", "components.UserList.usercreatedsuccess": "Ο χρήστης δημιουργήθηκε με επιτυχία!", "components.UserList.usercreatedfailedexisting": "Η παρεχόμενη διεύθυνση email χρησιμοποιείται ήδη από άλλο χρήστη.", "components.UserList.usercreatedfailed": "Κάτι πήγε στραβά κατά τη δημιουργία του χρήστη.", "components.UserList.user": "<PERSON>ρή<PERSON><PERSON><PERSON>ς", "components.UserList.totalrequests": "Αιτήματα", "components.UserList.sortRequests": "Αριθμός αιτημάτων", "components.UserList.sortDisplayName": "Εμφανιζόμενο όνομα", "components.UserList.sortCreated": "Ημερομηνία που έγινε μέλος", "components.UserList.role": "Ρόλος", "components.UserList.plexuser": "<PERSON>ρή<PERSON><PERSON><PERSON><PERSON>", "components.UserList.passwordinfodescription": "Ορίστε ένα URL εφαρμογής και ενεργοποιήστε τις ειδοποιήσεις με email για να επιτρέψετε την αυτόματη δημιουργία κωδικού.", "components.UserList.password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πρόσβασης", "components.UserList.owner": "Ιδιοκτήτης", "components.UserList.nouserstoimport": "Δεν υπάρχουν χρήστες για εισαγωγή από το Plex.", "components.UserList.localuser": "Το<PERSON><PERSON><PERSON><PERSON><PERSON> χρήστης", "components.UserList.localLoginDisabled": "Η ρύθμιση <strong>Ενεργοποίηση τοπικής σύνδεσης</strong> είναι προς το παρόν απενεργοποιημένη.", "components.UserList.importfromplexerror": "Κάτι πήγε στραβά κατά την εισαγωγή χρηστών από το Plex.", "components.UserList.importfrommediaserver": "Εισαγωγ<PERSON> χρηστών {mediaServerName}", "components.UserList.importfromplex": "Εισαγωγ<PERSON> χρηστών από το Plex", "components.UserList.importedfromplex": "{userCount, plural, one {# νέου χρήστη} other {#νέοι χρήστες}} εισήχθησαν απο το Plex επιτυχώς!", "components.UserList.email": "Διεύθυνση ηλεκτρονικού ταχυδρομείου", "components.UserList.edituser": "Επεξεργα<PERSON>ία δικαιωμάτων χρήστη", "components.UserList.deleteuser": "Διαγρα<PERSON><PERSON> χρήστη", "components.UserList.deleteconfirm": "Είσαι σίγουρος ότι θες να διαγράψεις αυτόν τον χρήστη; Όλα τα αποθηκευμένα αιτήματα του θα διαγραφούν οριστικά.", "components.UserList.creating": "Δημιουργείται…", "components.UserList.createlocaluser": "Δημιουργία τοπικού χρήστη", "components.UserList.created": "Έγινε μέλος", "components.UserList.create": "Δημιουργία", "components.UserList.bulkedit": "Μαζική επεξεργασία", "components.UserList.autogeneratepasswordTip": "Στείλε με μήνυμα ηλεκτρονικού ταχυδρομείου έναν κωδικό πρόσβασης που δημιουργείται από τον διακομιστή στον χρήστη", "components.UserList.autogeneratepassword": "Αυτόματη δημιουργ<PERSON>α κωδικού πρόσβασης", "components.UserList.admin": "Διαχειριστής", "components.UserList.accounttype": "Τύπος", "components.TvDetails.watchtrailer": "Δες το Τρέιλερ", "components.TvDetails.viewfullcrew": "Προβολή Πλήρους Συνεργείου", "components.TvDetails.similar": "Παρόμοιες σειρές", "components.TvDetails.showtype": "Τύ<PERSON><PERSON> σειράς", "components.TvDetails.seasons": "{seasonCount, plural, one {# Σεζόν} other {# Σεζόν}}", "components.TvDetails.recommendations": "Προτάσεις", "components.TvDetails.overviewunavailable": "Επισκόπηση μη διαθέσιμη.", "components.TvDetails.overview": "Επισκόπηση", "components.TvDetails.originaltitle": "Αρχικ<PERSON>ς τίτλος", "components.TvDetails.originallanguage": "Αρχική Γλώσσα", "components.TvDetails.nextAirDate": "Επόμενη ημερομηνία προβολής", "components.TvDetails.network": "{networkCount, plural, one {Δίκτυο} other {Δίκτυα}}", "components.TvDetails.firstAirDate": "Ημερομηνία πρώτης προβολής", "components.TvDetails.episodeRuntimeMinutes": "{runtime} λεπτά", "components.TvDetails.episodeRuntime": "Διάρκεια επεισοδίου", "components.TvDetails.cast": "Πρωταγωνιστές", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCrew.fullseriescrew": "Όλο το Πλήρωμα της Σειράς", "components.TvDetails.TvCast.fullseriescast": "Όλοι οι Ηθοποιοί της Σειράς", "components.StatusBadge.status4k": "4K {status}", "components.Setup.welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON> ήρθες στο <PERSON>", "components.Setup.signinMessage": "Ξεκίνα με την σύνδεση στον λογαριασμό του Plex σου", "components.Setup.setup": "Εγκατάσταση", "components.Setup.finishing": "Ολοκληρώνει…", "components.Setup.finish": "Ολοκλήρωση εγκατάστασης", "components.Setup.continue": "Συνέχεια", "components.Setup.configureservices": "Διαμόρφωση υπηρεσιών", "components.Settings.webpush": "Web Push", "components.Settings.webhook": "Webhook", "components.Settings.webAppUrlTip": "Προαιρ<PERSON><PERSON><PERSON><PERSON><PERSON> κατεύθυνε τους χρήστες στην εφαρμογή ιστού στον διακομιστή σας αντί για την εφαρμογή ιστού που \"φιλοξενείται\"", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> διεύθυνση URL", "components.Settings.validationPortRequired": "Πρέπει να δώσεις έναν έγκυρο αριθμό θύρας", "components.Settings.toastPlexRefreshSuccess": "Η λίστα διακομιστών Plex ανακτήθηκε με επιτυχία!", "components.Settings.toastPlexRefreshFailure": "Απέτυχε η ανάκτηση της λίστας με τους διακομιστές Plex.", "components.Settings.toastPlexRefresh": "Ανάκτηση λίστας διακομιστών από το Plex…", "components.Settings.toastPlexConnectingSuccess": "Η σύνδεση Plex δημιουργήθηκε με επιτυχία!", "components.Settings.toastPlexConnectingFailure": "Απέτυχε να συνδεθεί στο Plex.", "components.Settings.toastPlexConnecting": "Προσπάθεια σύνδεσης στο Plex…", "components.Settings.startscan": "Έναρξη σάρωσης", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "Ρυθμίσεις <PERSON><PERSON>r", "components.Settings.settingUpPlexDescription": "Για να ρυθμίσεις το <PERSON><PERSON>, μπορ<PERSON><PERSON>ς είτε να εισαγάγεις τα στοιχεία χειροκίνητα είτε να επιλέξεις έναν διακομιστή που ανακτήθηκε από το <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Πάτα το κουμπί στα δεξιά του αναπτυσσόμενου μενού για να ανακτήσεις τη λίστα με τους διαθέσιμους διακομιστές.", "components.Settings.services": "Υπηρεσίες", "components.Settings.serviceSettingsDescription": "Διαμόρφωσε τον {serverType} διακομιστή(-ές) παρακάτω . Μπορείτε να συνδέσεις πολλούς {serverType} διακ<PERSON>μιστές, <PERSON><PERSON><PERSON><PERSON> μόνο δύο από αυτούς μπορούν να χαρακτηριστούν ως προεπιλεγμένοι (ένας μη-4K και ένας 4K). Οι διαχειριστές έχουν τη δυνατότητα να παρακάμψουν τον διακομιστή που χρησιμοποιείται για την επεξεργασία νέων αιτημάτων πριν από την έγκριση.", "components.Settings.serverpresetRefreshing": "Ανάκτηση διακομιστών…", "components.Settings.serverpresetManualMessage": "Χειροκίνητη διαμόρφωση", "components.Settings.serverpresetLoad": "Πάτα το κουμπί για να φορτώσει τους διαθέσιμους διακομιστές", "components.Settings.serverpreset": "Διακομιστής", "components.Settings.serverSecure": "ασφαλές", "components.Settings.serverRemote": "απομακρυσμένα", "components.Settings.serverLocal": "τοπικά", "components.Settings.scanning": "Συγχρονίζει…", "components.Settings.scan": "Συγχρονισμός των βιβλιοθήκων", "components.Settings.radarrsettings": "Ρυθμίσεις Radarr", "components.Settings.port": "Θύρα", "components.Settings.plexsettingsDescription": "Διαμόρφωσε τις ρυθμίσεις του Plex διακομιστή σου. Το <PERSON><PERSON>seerr σαρώνει τις βιβλιοθήκες του Plex για να προσδιορίσει τη διαθεσιμότητα περιεχομένου.", "components.Settings.plexsettings": "Ρυθμίσεις Plex", "components.Settings.plexlibrariesDescription": "Οι βιβλιοθήκες που το <PERSON><PERSON><PERSON>rr θα σαρώνει για τίτλους. Ρύθμισε και αποθήκευσε τις ρυθμίσεις της σύνδεσης του Plex και, στη συνέχεια, κάν<PERSON> κλικ στο παρακάτω κουμπί, εάν δεν εμφανιστύν οι βιβλιοθήκες.", "components.Settings.plexlibraries": "Βιβλιοθήκες Plex", "components.Settings.plex": "Plex", "components.Settings.notrunning": "Δεν τρέχει", "components.Settings.notificationsettings": "Ρυθμίσεις Ειδοποιήσεων", "components.Settings.notifications": "Ειδοποιήσεις", "components.Settings.notificationAgentSettingsDescription": "Διαμόρφωσε και ενεργοποίησε τους πράκτορες ειδοποιήσεων.", "components.Settings.noDefaultServer": "Τουλάχιστον ένας διακομιστής {serverType} πρέπει να έχει επισημανθεί ως προεπιλεγμένος προκειμένου να διεκπεραιωθούν τα αιτήματα {mediaType}.", "components.Settings.noDefaultNon4kServer": "Εάν έχεις μόνο έναν διακομιστή {serverType} τόσο για περιεχόμενο που δεν είναι 4K όσο και για περιεχόμενο 4K (ή εάν κάνεις λήψη μόνο περιεχομένου 4K), ο διακομιστής {serverType} θα πρέπει <strong>να ΜΗΝ</strong> οριστεί ως διακομιστής 4K.", "components.Settings.noDefault4kServer": "Ένας διακομιστής 4K {serverType} πρέπει να επισημανθεί ως προεπιλεγμένος για να μπορούν οι χρήστες να υποβάλλουν αιτήσεις 4K {mediaType}.", "components.Settings.menuUsers": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.menuServices": "Υπηρεσίες", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Ειδοποιήσεις", "components.Settings.menuLogs": "Αρχεία καταγραφής", "components.Settings.menuJobs": "Εργα<PERSON>ίες & Κρυφή μνήμη", "components.Settings.menuGeneralSettings": "Γενικ<PERSON>ς", "components.Settings.menuAbout": "Σχετικά", "components.Settings.mediaTypeSeries": "σειρά", "components.Settings.mediaTypeMovie": "ταινία", "components.Settings.manualscanDescription": "Κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, αυτ<PERSON> εκτελείται μόνο μία φορά κάθε 24 ώρες. Το Je<PERSON>seerr θα ελέγχει πιο επιθετικά τις προσθήκες που έγιναν πρόσφατα στον διακομιστή Plex. Εάν αυτή είναι η πρώτη φορά που ρυθμίζεις το Plex, συνιστάται μια εφάπαξ πλήρης χειροκίνητη σάρωση βιβλιοθήκης!", "components.Settings.manualscan": "Χειροκίνητη σάρωση βιβλιοθήκης", "components.Settings.librariesRemaining": "Βιβλιοθήκες που απομένουν: {count}", "components.Settings.is4k": "4K", "components.Settings.hostname": "Όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.enablessl": "Χρήση SSL", "components.Settings.email": "Email", "components.Settings.deleteserverconfirm": "Είσαι σίγουρος ότι θες να διαγράψεις αυτόν τον διακομιστή;", "components.Settings.default4k": "Προεπιλεγμένο 4K", "components.Settings.default": "Προκαθορισμένο", "components.Settings.currentlibrary": "Τρέχουσα βιβλιοθήκη: {name}", "components.Settings.copied": "Αντιγράφηκε το κλειδί API στο πρόχειρο.", "components.Settings.cancelscan": "Ακύρωση σάρωσης", "components.Settings.addsonarr": "Προσθήκη διακομιστή Sonarr", "components.Settings.address": "Διεύθυνση", "components.Settings.addradarr": "Προσθήκη διακομιστή Radarr", "components.Settings.activeProfile": "Ενεργό προφίλ", "components.Settings.SonarrModal.validationRootFolderRequired": "Πρέπει να επιλέξεις ένα ριζικ<PERSON> φάκελο", "components.Settings.SonarrModal.validationProfileRequired": "Πρέπει να επιλέξεις ένα προφίλ ποιότητας", "components.Settings.SonarrModal.validationPortRequired": "Πρέπει να δώσεις έναν έγκυρο αριθμό θύρας", "components.Settings.SonarrModal.validationNameRequired": "Πρέπει να δώσεις ένα όνομα διακομιστή", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Πρέπει να επιλέξεις ένα προφίλ γλώσσας", "components.Settings.SonarrModal.validationHostnameRequired": "Πρέπει να δώσετε ένα έγκυρο όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Η βασική διεύθυνση URL δεν πρέπει να τελειώνει με κάθετο", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Η βασική διεύθυνση URL πρέπει να έχει μια κάθετο μπροστά", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Η διεύθυνση URL δεν πρέπει να τελειώνει με κάθετο", "components.Settings.SonarrModal.validationApplicationUrl": "Πρέπει να βάλεις μια έγκυρη διεύθυνση URL", "components.Settings.SonarrModal.validationApiKeyRequired": "Πρέπει να βάλεις ένα κλειδί API", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Η σύνδεση με το Sonarr δημιουργήθηκε επιτυχώς!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Η σύνδεση με το Sonarr δεν ήταν επιτυχής.", "components.Settings.SonarrModal.testFirstTags": "Δοκι<PERSON>ή σύνδεσης για την φόρτωση των ετικετών", "components.Settings.SonarrModal.testFirstRootFolders": "Δοκι<PERSON>ή σύνδεσης για την φόρτωση των ριζικών φακέλων", "components.Settings.SonarrModal.testFirstQualityProfiles": "Δοκι<PERSON><PERSON> σύνδεσης για την φόρτωση των προφίλ ποιότητας", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Δοκι<PERSON>ή σύνδεσης για την φόρτωση των προφίλ γλώσσας", "components.Settings.SonarrModal.tags": "Ετικέτες", "components.Settings.SonarrModal.syncEnabled": "Ενεργοποίηση σάρωσης", "components.Settings.SonarrModal.ssl": "Χρήση SSL", "components.Settings.SonarrModal.servername": "Όνομα διακομιστή", "components.Settings.SonarrModal.server4k": "Διακομιστής 4K", "components.Settings.SonarrModal.selecttags": "Επιλογή ετικετών", "components.Settings.SonarrModal.selectRootFolder": "Επίλεξε ριζικ<PERSON> φάκελο", "components.Settings.SonarrModal.selectQualityProfile": "Επίλεξε προφίλ ποιότητας", "components.Settings.SonarrModal.selectLanguageProfile": "Επιλέξτε προφίλ γλώσσας", "components.Settings.SonarrModal.seasonfolders": "Φάκελοι των Σεζόν", "components.Settings.SonarrModal.rootfolder": "Ριζικός φάκελος", "components.Settings.SonarrModal.qualityprofile": "<PERSON>ρο<PERSON><PERSON><PERSON>ς", "components.Settings.SonarrModal.port": "Θύρα", "components.Settings.SonarrModal.notagoptions": "Δεν υπάρχουν ετικέτες.", "components.Settings.SonarrModal.loadingrootfolders": "Φόρτωση ριζικών φακέλων…", "components.Settings.SonarrModal.loadingprofiles": "Φόρτωση των προφίλ ποιότητας…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Φόρτωση προφίλ γλώσσας…", "components.Settings.SonarrModal.loadingTags": "Φόρτωση ετικετών…", "components.Settings.SonarrModal.languageprofile": "<PERSON>ρο<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.hostname": "Όνομα κεντρικού υπολογιστή ή διεύθυνση IP", "components.Settings.SonarrModal.externalUrl": "Εξωτερική διεύθυνση URL", "components.Settings.SonarrModal.enableSearch": "Ενεργοποίηση αυτόματης αναζήτησης", "components.Settings.SonarrModal.editsonarr": "Επεξεργασία διακομιστή Sonarr", "components.Settings.SonarrModal.edit4ksonarr": "Επεξεργασία διακομιστή 4K Sonarr", "components.Settings.SonarrModal.defaultserver": "Προεπιλεγμένος διακομιστής", "components.Settings.SonarrModal.default4kserver": "Προεπιλεγμένος διακομιστής 4K", "components.Settings.SonarrModal.createsonarr": "Προσθήκη νέου διακομιστή Sonarr", "components.Settings.SonarrModal.create4ksonarr": "Προσθήκη νέου διακομιστή 4K Sonarr", "components.Settings.SonarrModal.baseUrl": "Βασική Διεύθυνση URL", "components.Settings.SonarrModal.apiKey": "Κλειδί API", "components.Settings.SonarrModal.animerootfolder": "Ριζικός φάκελος Anime", "components.Settings.SonarrModal.animequalityprofile": "Προ<PERSON><PERSON><PERSON> ποιότη<PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animelanguageprofile": "Προ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.animeTags": "Ετικέτες <PERSON>", "components.Settings.SonarrModal.add": "Προσθήκη διακομιστή", "components.Settings.SettingsUsers.users": "<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.userSettings": "Ρυθμίσεις χρήστη", "components.Settings.SettingsUsers.userSettingsDescription": "Διαμόρφωση των γενικών και προεπιλεγμένων ρυθμίσεων του χρήστη.", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Καθολικό όριο των αιτημάτων στις Σειρές", "components.Settings.SettingsUsers.toastSettingsSuccess": "Οι ρυθμίσεις του χρήστη αποθηκεύτηκαν επιτυχώς!", "components.Settings.SettingsUsers.toastSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση των ρυθμίσεων.", "components.Settings.SettingsUsers.newPlexLoginTip": "Επίτρεψε στους χρήστες του {mediaServerName} να συνδεθούν χωρίς να εισαχθούν πρώτα", "components.Settings.SettingsUsers.newPlexLogin": "Ενεργοποίηση νέας σύνδεσης {mediaServerName}", "components.Settings.SettingsJobsCache.jobsDescription": "Το <PERSON><PERSON>seerr εκτελεί ορισμένες εργασίες συντήρησης ως τακτικά προγραμματισμένες εργασίες, αλ<PERSON><PERSON> μπορούν επίσης να ενεργοποιηθούν χειροκίνητα παρακάτω. Η χειροκίνητη εκτέλεση μιας εργασίας δεν θα αλλάξει το χρονοδιάγραμμα του.", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Template Variable Help", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Ο χρήστης σου ή η συσκευή <LunaSeaLink>ειδοποίηση webhook URL</LunaSeaLink>", "components.RequestModal.numberofepisodes": "# Αριθμός Επεισοδίων", "components.MovieDetails.studio": "{studioCount, plural, one {Στούντιο} other {Στούντιο}}", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} πίσω", "pages.serviceunavailable": "Η υπηρεσία δεν είναι διαθέσιμη", "pages.returnHome": "Επιστροφή στην Αρχική", "pages.pagenotfound": "Η σελίδα δεν βρέθηκε", "pages.oops": "Ουπς", "pages.internalservererror": "Εσωτερικ<PERSON> Σφάλμα Διακομιστή", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.view": "Προβολή", "i18n.usersettings": "Ρυθμίσεις χρήστη", "i18n.unavailable": "Μη Διαθέσιμο", "i18n.tvshows": "Σειρές", "i18n.tvshow": "Σειρά", "i18n.testing": "Γίνεται Δοκιμή…", "i18n.test": "Δοκιμή", "i18n.status": "Κατάσταση", "i18n.showingresults": "Εμφάνιση <strong>{from}</strong> έως <strong>{to}</strong> από <strong>{total}</strong> αποτελέσματα", "components.Settings.SettingsAbout.betawarning": "Αυτό είναι λογισμικό BETA. Οι λειτουργίες ενδέχεται να είναι σπασμένες ή/και ασταθείς. <PERSON><PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> αναφέρετε τυχόν προβλήματα με το GitHub!", "components.StatusBadge.status": "{status}", "components.Settings.validationUrlBaseLeadingSlash": "Η βάση URL πρέπει να έχει μια κάθετο μπροστά", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Αυτόματη αίτηση ταινιών", "components.IssueModal.CreateIssueModal.reportissue": "Αναφορά προβλήματος", "i18n.resolved": "Επιλύθηκε", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "i18n.import": "Εισαγωγή", "components.Discover.FilterSlideover.tmdbuservotecount": "Αριθμός ψήφων χρηστών TMDB", "components.Discover.FilterSlideover.voteCount": "Αριθμός ψήφων μεταξύ {minValue} και {maxValue}", "components.IssueDetails.comments": "Σχόλια", "components.IssueDetails.season": "<PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Episode} other {Episodes}}", "components.IssueList.IssueItem.openeduserdate": "{date} από {user}", "components.IssueModal.CreateIssueModal.providedetail": "Π<PERSON>ρ<PERSON><PERSON><PERSON><PERSON><PERSON> δώστε λεπτομερή εξήγηση του προβλήματος που αντιμετωπίσατε.", "components.IssueModal.CreateIssueModal.submitissue": "Υποβολή προβλήματος", "components.IssueModal.issueSubtitles": "Υπότιτλος", "components.ManageSlideOver.movie": "ταινία", "components.ManageSlideOver.openarr": "Άνοιγμα σε {arr}", "components.MovieDetails.rtcriticsscore": "Τοματόμετρο Rotten Tomatoes", "components.MovieDetails.showmore": "Εμφάνιση περισσότερων", "components.MovieDetails.streamingproviders": "Γίνεται Streaming στο", "components.NotificationTypeSelector.issuecomment": "Σχόλιο προβλήματος", "components.NotificationTypeSelector.issuereopened": "Το πρόβλημα ανοίχθηκε ξανά", "components.NotificationTypeSelector.issueresolved": "Το πρόβλημα επιλύθηκε", "components.NotificationTypeSelector.usermediafailedDescription": "Ειδοποιηθείτε όταν αιτήματα αποτυγχάνουν να προστεθούν στο Radarr ή το Sonarr.", "components.PermissionEdit.createissuesDescription": "Χορήγη<PERSON><PERSON> άδειας για την αναφορ<PERSON> προβλημάτων.", "components.PermissionEdit.manageissues": "Διαχείριση προβλημάτων", "components.PermissionEdit.viewissuesDescription": "Χορήγη<PERSON><PERSON> άδειας για την προβολή προβλημάτων που αναφέρθηκαν από άλλους χρήστες.", "components.PermissionEdit.viewwatchlists": "Προβολή λιστών παρακολούθησης Plex", "components.QuotaSelector.days": "{count, plural, one {day} other {days}}", "components.RequestBlock.requestedby": "Αιτήθηκε από", "components.RequestCard.approverequest": "Έγκριση αίτησης", "components.RequestCard.declinerequest": "Απόρριψη αίτησης", "components.RequestCard.editrequest": "Επεξεργασία αίτησης", "components.RequestCard.failedretry": "Κάτι πήγε στραβά με την επανάληψη της αίτησης.", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestModal.requestseries4ktitle": "Αίτηση σειράς σε 4Κ", "components.RequestModal.selectmovies": "Επιλέξτε ταινία(ες)", "components.Selector.nooptions": "Δεν υπάρχουν αποτελέσματα.", "components.Selector.searchKeywords": "Αναζήτηση λέξεων-κλειδιών…", "components.Selector.searchStudios": "Αναζήτηση στούντιο…", "components.Selector.showless": "Εμφάνιση λιγότερων", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Οι ρυθμίσεις ειδοποιήσεων Gotify αποθηκεύτηκαν επιτυχώς!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Η δοκιμαστική αποστολή ειδοποίησης Gotify απέτυχε.", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "Ο σύνδεσμος δε μπορεί να τελειώνει σε κάθετο", "components.Settings.SettingsMain.applicationTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> εφαρμογής", "components.StatusBadge.playonplex": "Αναπαραγωγή στο Plex", "components.TvDetails.rtaudiencescore": "Βαθμολογία κοινού Rotten Tomatoes", "components.TvDetails.tmdbuserscore": "Βαθμολογία χρηστών TMDB", "components.TvDetails.reportissue": "Αναφέρετε ένα πρόβλημα", "components.Settings.RadarrModal.tagRequests": "Αιτήματα ετικετών", "components.Settings.RadarrModal.tagRequestsInfo": "Αυτόματη προσθήκη πρόσθετης ετικέτας με το αναγνωριστικό χρήστη και το όνομα χρήστη του αιτούντος", "components.Settings.SettingsJobsCache.editJobSchedule": "Μεταβολή εργασίας", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Κάτι πήγε στραβά με την αποθήκευση της εργασίας.", "components.Settings.SettingsMain.cacheImages": "Ενεργοποίηση caching εικόνων", "components.Settings.SettingsMain.cacheImagesTip": "Κάνε cache εικόνες από εξωτερικές πηγές (απαιτεί πολύ αποθηκευτικό χώρο στο δίσκο)", "components.Settings.SettingsMain.general": "Γενικά", "components.Settings.SettingsMain.generalsettingsDescription": "Διαμόρφωση των γενικών και προεπιλεγμένων ρυθμίσεων για το <PERSON><PERSON>.", "components.Settings.SettingsMain.toastSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση των ρυθμίσεων.", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "Το URL δε μπορεί να τελειώνει με κάθετο", "components.Settings.SettingsUsers.defaultPermissionsTip": "Αρχικά δικαιώματα που ορίζονται σε νέους χρήστες", "components.Settings.deleteServer": "Διαγραφή {serverType} διακομιστή", "components.Settings.tautulliSettingsDescription": "Προαιρετικό: Διαμορφώστε τις ρυθμίσεις του Tau<PERSON> διακομιστή σας. Τ<PERSON> <PERSON><PERSON>rr τραβάει το ιστορικό προβολών του Plex από το <PERSON>.", "components.Settings.validationApiKey": "Πρέπει να δώσετε ένα κλειδί API", "components.StatusChecker.restartRequiredDescription": "Παρα<PERSON><PERSON><PERSON><PERSON> επανεκκινήστε τον διακομιστή ώστε να εφαρμοστούν οι ενημερωμένες ρυθμίσεις.", "components.TvDetails.Season.somethingwentwrong": "Κάτι πήγε στρα<PERSON><PERSON> κατά την ανάκτηση των δεδομένων της σεζόν.", "components.TvDetails.manageseries": "Διαχείριση σειράς", "components.TvDetails.productioncountries": "Παραγωγή {countryCount, plural, one {Country} other {Countries}}", "components.TvDetails.rtcriticsscore": "Τοματόμετρο Rotten Tomatoes", "components.TvDetails.seasonnumber": "<PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Πρέπει να εισάγετε ένα έγκυρο αναγνωριστικό χρήστη Discord", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Πρέπει να δώσετε ένα έγκυρο κλειδί χρήστη ή ομάδας", "components.UserProfile.emptywatchlist": "Περιεχόμενο που εισάγεται στη <PlexWatchlistSupportLink>λίστα παρακολούθησης του Plex</PlexWatchlistSupportLink> θα εμφανίζεται εδώ.", "components.UserProfile.recentlywatched": "Είδατε πρόσφατα", "i18n.collection": "Συλλογή", "components.MovieDetails.digitalrelease": "Ψηφιακή κυκλοφορία", "components.MovieDetails.physicalrelease": "Κυκλοφορία σε φυσική έκδοση", "components.RequestModal.approve": "Έγκριση αίτησης", "components.IssueDetails.deleteissueconfirm": "Είστε σίγουροι πως θέλετε να διαγράψετε αυτό το πρόβλημα;", "components.IssueDetails.commentplaceholder": "Προσθέστε ένα σχόλιο…", "components.IssueDetails.playonplex": "Αναπαραγωγή στο Plex", "components.IssueList.IssueItem.unknownissuetype": "Άγνωστο", "components.IssueDetails.unknownissuetype": "Άγνωστο", "components.IssueList.IssueItem.viewissue": "Προβολή προβλήματος", "components.ManageSlideOver.manageModalTitle": "Διαχείριση {mediaType}", "components.MovieDetails.showless": "Εμφάνιση λιγότερων", "components.QuotaSelector.seasons": "{count, plural, one {season} other {seasons}}", "components.Discover.tmdbmoviekeyword": "Λέξη-κλειδί ταινίας από TMDB", "components.IssueDetails.IssueDescription.description": "Περιγραφή", "components.ManageSlideOver.openarr4k": "Άνοιγμα σε 4K {arr}", "components.NotificationTypeSelector.adminissuecommentDescription": "Ειδοποιηθείτ<PERSON> όταν άλλοι χρήστες σχολιάζουν σε προβλήματα.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Ειδοποιηθείτ<PERSON> όταν άλλοι χρήστες ξανανοίγουν προβλήματα.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Ειδοποιηθείτε όταν προβλήματα λύνονται από άλλους χρήστες.", "components.NotificationTypeSelector.mediaautorequestedDescription": "Ειδοποιηθείτε όταν νέα αιτήματα καταχωρούνται αυτόματα από τη λίστα παρακολούθησης Plex.", "components.RequestBlock.approve": "Έγκριση αίτησης", "components.PermissionEdit.viewissues": "Προβολή προβλημάτων", "components.RequestBlock.edit": "Επεξεργασία αίτησης", "components.RequestCard.cancelrequest": "Ακύρωση αίτησης", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Πρέπει να δώσετε ένα token εφαρμογής", "components.Settings.Notifications.NotificationsGotify.url": "URL διακομιστή", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Πρέπει να δώσετε έναν έγκυρο σύνδεσμο", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Πρέπει να δώσετε ένα token πρόσβασης", "components.Settings.SettingsMain.applicationurl": "URL εφαρμογής", "components.Settings.SettingsMain.originallanguage": "Γλώσ<PERSON><PERSON> σελίδας ανακάλυψης", "components.Settings.SettingsMain.toastApiKeySuccess": "Το νέο κλειδί API δημιουργήθηκε με επιτυχία!", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Token πρόσβασης", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "API Token εφαρμογής", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Οι ρυθμίσεις ειδοποιήσεων <PERSON> αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Οι ρυθμίσεις ειδοποιήσεων του <PERSON><PERSON>over αποθηκεύτηκαν επιτυχώς!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Οι ρυθμίσεις ειδοποιήσεων του <PERSON><PERSON><PERSON> απέτυχαν να αποθηκευτούν.", "components.PermissionEdit.viewrecent": "Προβολ<PERSON> αυτών που προστέθηκαν πρόσφατα", "components.Settings.SettingsAbout.appDataPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ων", "components.RequestModal.requestApproved": "Η αίτηση για το <strong>{title}</strong> εγκρίθηκε!", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Ενεργοποίηση Agent", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Ειδοποιηθ<PERSON><PERSON><PERSON><PERSON> όταν άλλοι χρήστες κάνουν αιτήματα που εγκρίνονται αυτόματα.", "components.IssueModal.issueOther": "Άλλο", "components.ManageSlideOver.manageModalIssues": "Ανοιχτά προβλήματα", "components.TitleCard.tmdbid": "TMDB ID", "components.RequestCard.tmdbid": "TMDB ID", "components.NotificationTypeSelector.issuecreatedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν αναφέρονται προβλήματα.", "components.Discover.DiscoverWatchlist.watchlist": "Λίστα παρακολούθησης Plex", "components.IssueDetails.closeissueandcomment": "Κλείσιμο με σχόλιο", "components.IssueDetails.deleteissue": "Διαγραφή προβλήματος", "components.IssueDetails.nocomments": "Δεν υπάρχουν σχόλια.", "components.IssueDetails.problemepisode": "Επηρεασμένο επεισόδιο", "components.IssueDetails.reopenissue": "Ανοίξτε ξανά το πρόβλημα", "components.IssueModal.issueAudio": "Ήχος", "components.ManageSlideOver.manageModalClearMedia": "Καθαρισμός δεδομένων", "components.ManageSlideOver.opentautulli": "Άνοιγμα στο <PERSON>", "components.MovieDetails.managemovie": "Διαχείριση ταινίας", "components.MovieDetails.reportissue": "Αναφορά προβλήματος", "components.PermissionEdit.createissues": "Αναφορά προβλημάτων", "components.Settings.SettingsLogs.viewdetails": "Προβολή λεπτομερειών", "components.IssueDetails.IssueComment.delete": "Διαγρα<PERSON><PERSON> σχολίου", "components.IssueList.IssueItem.issuetype": "Τύπος", "components.Settings.Notifications.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.RequestList.RequestItem.requesteddate": "Ζητήθηκε", "components.Discover.tmdbmoviestreamingservices": "Υπηρεσίες streaming ταινίας από TMDB", "components.Discover.tmdbtvstreamingservices": "TMDB Υπηρεσίες streaming τηλεόρασης", "components.IssueDetails.allseasons": "Όλες οι σεζόν", "components.Discover.updatesuccess": "Ενημέρωση των ρυθμίσεων προσαρμογής της ανακάλυψης.", "components.IssueDetails.closeissue": "Κλείσιμο προβλήματος", "components.IssueDetails.openedby": "#{issueId} ανοίχτηκε {relativeTime} από {username}", "components.IssueDetails.leavecomment": "Σχόλιο", "components.IssueDetails.openinarr": "Άνοιγμα σε {arr}", "components.IssueModal.CreateIssueModal.extras": "Πρόσθετα", "components.IssueDetails.toasteditdescriptionfailed": "Κάτι πήγε στρα<PERSON><PERSON> κατά την επεξεργασία της περιγραφής του προβλήματος.", "components.IssueDetails.toasteditdescriptionsuccess": "Η περιγραφή του προβλήματος επεξεργάστηκε με επιτυχία!", "components.Layout.Sidebar.issues": "Προβλήματα", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Κάτι πήγε στραβά με την υποβολή του προβλήματος.", "components.ManageSlideOver.manageModalMedia": "Περιεχόμενο", "components.ManageSlideOver.downloadstatus": "Λήψεις", "components.ManageSlideOver.manageModalMedia4k": "4Κ περιεχόμενο", "components.ManageSlideOver.mark4kavailable": "Επισήμανση ως διαθέσιμο σε 4K", "components.ManageSlideOver.markavailable": "Επισήμανση ως διαθέσιμο", "components.ManageSlideOver.playedby": "Προβλήθηκε από", "components.ManageSlideOver.pastdays": "Προηγούμενες {days, number} Ημέρες", "components.MovieDetails.theatricalrelease": "Κινηματογραφική κυκλοφορία", "components.MovieDetails.tmdbuserscore": "Βαθμολογία χρηστών TMDB", "components.NotificationTypeSelector.issuecreated": "Αναφέρθηκε πρόβλημα", "components.NotificationTypeSelector.issuecommentDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν υπάρχουν νέα σχόλια σε προβλήματα.", "components.NotificationTypeSelector.mediaautorequested": "Καταχωρήθηκε αίτημα αυτόματα", "components.NotificationTypeSelector.userissuereopenedDescription": "Ειδοποιηθείτε όταν προβλήματα που αναφέρατε ξανανοίγουν.", "components.NotificationTypeSelector.userissueresolvedDescription": "Ειδοποιηθείτε όταν προβλήματα που αναφέρατε επιλύονται.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Ειδοποιηθείτε όταν τα αιτήματα σας εγκρίνονται.", "components.NotificationTypeSelector.usermediaavailableDescription": "Ειδοποιηθείτε όταν τα αιτήματα σας γίνονται διαθέσιμα.", "components.NotificationTypeSelector.userissuecreatedDescription": "Ειδοποιηθείτ<PERSON> όταν άλλοι χρήστες αναφέρουν προβλήματα.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Ειδοποιηθείτε όταν τα αιτήματα σας απορρίπτονται.", "components.PermissionEdit.autorequest": "Αυτόματη αίτηση", "components.PermissionEdit.autorequestMovies": "Αυτόματη αίτηση ταινιών", "components.PermissionEdit.autorequestSeriesDescription": "Χορήγη<PERSON>η άδειας για την αυτόματη υποβολή αιτημάτων για μη-4Κ σειρές από τη λίστα προβολής του Plex.", "components.PermissionEdit.manageissuesDescription": "Χορήγηση άδειας για τη διαχείριση προβλημάτων.", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} ανά {quotaDays} {days}</quotaUnits>", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestModal.SearchByNameModal.nomatches": "Δε μπορέσαμε να αντιστοιχήσουμε αυτή τη σειρά.", "components.RequestModal.requestmovies": "Αίτηση {count} {count, plural, one {Movie} other {Movies}}", "components.Selector.showmore": "Εμφάνιση περισσότερων", "components.Selector.starttyping": "Αρχίστε να πληκτρολογείτε για αναζήτηση.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Αποστολή δοκιμαστικής ειδοποίη<PERSON>ης <PERSON>…", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Ετικέτα καναλιού", "components.Settings.RadarrModal.announced": "Ανακοινώθηκε", "components.Settings.RadarrModal.inCinemas": "Στ<PERSON><PERSON><PERSON>ινηματογράφους", "components.Settings.SettingsJobsCache.availability-sync": "Συγχρονισμός διαθεσιμότητας περιεχομένου", "components.Settings.SettingsJobsCache.imagecachecount": "Οι εικόνες έγιναν cache", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Καθ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cached εικόνων", "components.Settings.SettingsJobsCache.imagecache": "Καθα<PERSON>ισ<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Συγχρο<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> λίστας παρακολούθησης Plex", "components.Settings.SettingsMain.generalsettings": "Γενικές ρυθμίσεις", "components.Settings.SettingsMain.hideAvailable": "Απόκρυψη διαθέσιμου περιεχομένου", "components.Settings.restartrequiredTooltip": "Τ<PERSON> <PERSON>rr πρέπει να κάνει επανεκκίνηση ώστε να εφαρμοστεί αυτή η ρύθμιση", "components.Settings.tautulliSettings": "Ρυθμίσεις <PERSON><PERSON><PERSON>", "components.Settings.toastTautulliSettingsSuccess": "Οι ρυθμίσεις του Tau<PERSON>lli αποθηκεύτηκαν επιτυχώς!", "components.Settings.validationUrlBaseTrailingSlash": "Η βάση URL δεν πρέπει να τελειώνει με κάθετο", "components.Settings.validationUrlTrailingSlash": "Το URL δε μπορεί να τελειώνει με κάθετο", "components.StatusChecker.reloadApp": "Επαναφόρτωση {applicationTitle}", "components.StatusBadge.seasonepisodenumber": "Σ{seasonNumber}Ε{episodeNumber}", "components.TitleCard.cleardata": "Καθαρισμός δεδομένων", "components.TvDetails.seasonstitle": "Σεζόν", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Ζητήστε αυτόματα ταινίες που βρίσκονται στη <PlexWatchlistSupportLink>λίστα παρακολούθησης του Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Το αναγνωριστικό 30 χαρακτήρων <UsersGroupsLink>του χρήστη ή της ομάδας σας</UsersGroupsLink>", "components.AirDateBadge.airedrelative": "Προβλήθηκε {relativeTime}", "components.ManageSlideOver.manageModalRequests": "Αιτήματα", "components.ManageSlideOver.markallseasons4kavailable": "Επισημάνετε όλες τις σεζόν ως διαθέσιμες σε 4Κ", "components.Discover.CreateSlider.providetmdbgenreid": "Δώστε ένα TMDB Genre ID", "components.Discover.CreateSlider.providetmdbkeywordid": "Δώστε ένα TMDB Keyword ID", "components.Discover.CreateSlider.providetmdbnetwork": "Δώστε το TMDB Network ID", "components.Discover.CreateSlider.providetmdbsearch": "Δώστε ένα ερώτημα αναζήτησης", "components.Discover.CreateSlider.providetmdbstudio": "Δώστε το TMDB Studio ID", "components.Discover.CreateSlider.searchGenres": "Αναζήτηση ειδών…", "components.Discover.CreateSlider.searchKeywords": "Αναζήτηση λέξεων-κλειδιών…", "components.Discover.CreateSlider.searchStudios": "Αναζήτηση στούντιο…", "components.Discover.CreateSlider.slidernameplaceholder": "Όνομα Slider", "components.Discover.CreateSlider.starttyping": "Αρχίστε να πληκτρολογείτε για αναζήτηση.", "components.Discover.CreateSlider.validationDatarequired": "Πρέπει να δώσετε μια τιμή.", "components.Discover.CreateSlider.validationTitlerequired": "Πρέπει να δώσετε έναν τίτλο.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Ταινίες", "components.Discover.DiscoverSliderEdit.enable": "Εναλλαγή ορατότητας", "components.Discover.DiscoverSliderEdit.remove": "Αφαίρεση", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Σειρά", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Η λίστα παρακολούθησης σας του Plex", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Η λίστα παρακολούθησης σας του Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Προστέθηκαν πρόσφατα", "components.Discover.createnewslider": "Δημιουργ<PERSON><PERSON> νέου Slider", "components.Discover.customizediscover": "Προσαρμογή σελίδας ανακάλυψης", "components.Discover.plexwatchlist": "Η λίστα παρακολούθησης σας του Plex", "components.Discover.networks": "Δίκτυα", "components.Discover.resetwarning": "Επανα<PERSON><PERSON><PERSON><PERSON> όλων των sliders την προεπιλογή. Αυτό θα διαγράψει και οποιοδήποτε εξατομικευμένο slider!", "components.Discover.stopediting": "Διακοπή επεξεργασίας", "components.Discover.studios": "Στούντιο", "components.Discover.tmdbmoviegenre": "Είδος Ταινίας TMDB", "components.Discover.updatefailed": "Κάτι πήγε στραβά με την ενημέρωση των ρυθμίσεων προσαρμογής της ανακάλυψης.", "components.Discover.tvgenres": "Είδη σειρών", "components.IssueDetails.episode": "Επεισόδιο {episodeNumber}", "components.IssueDetails.openin4karr": "Άνοιγμα σε 4K {arr}", "components.IssueDetails.IssueDescription.deleteissue": "Διαγραφή προβλήματος", "components.IssueModal.CreateIssueModal.allseasons": "Όλες οι σεζόν", "components.IssueList.IssueItem.issuestatus": "Κατάσταση", "components.IssueList.IssueItem.opened": "Ανοίχτηκε", "components.IssueList.IssueItem.problemepisode": "Επηρεασμένο επεισόδιο", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.IssueModal.CreateIssueModal.problemepisode": "Επηρεασμένο επεισόδιο", "components.IssueModal.CreateIssueModal.problemseason": "Επηρεασμένη σεζόν", "components.IssueModal.issueVideo": "Βίντεο", "components.IssueModal.CreateIssueModal.whatswrong": "Τι συμβαίνει;", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Αιτήματα ταινιών", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Αιτήματα σειρών", "components.Layout.UserDropdown.requests": "Αιτήματα", "components.ManageSlideOver.tvshow": "σειρά", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {play} other {plays}}", "components.RequestBlock.decline": "Απόρριψη αίτησης", "components.RequestBlock.delete": "Διαγρα<PERSON>ή αίτησης", "components.RequestBlock.lastmodifiedby": "Τελευταία τροποποίηση από", "components.RequestBlock.requestdate": "Ημ/νία αίτησης", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episode} other {# Episodes}}", "components.TvDetails.streamingproviders": "Γίνεται Streaming στο", "components.RequestBlock.languageprofile": "Προ<PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.Settings.Notifications.enableMentions": "Ενεργοποίηση αναφορών", "i18n.importing": "Γίνεται εισαγωγή…", "components.IssueDetails.issuetype": "Τύπος", "components.IssueDetails.toaststatusupdated": "Η κατάσταση του προβλήματος ενημερώθηκε επιτυχώς!", "components.IssueModal.CreateIssueModal.allepisodes": "Όλα τα επεισόδια", "components.IssueList.showallissues": "Προβολή όλων των προβλημάτων", "components.IssueList.sortAdded": "Πιο πρόσφατα", "components.IssueModal.CreateIssueModal.toastviewissue": "Προβολή προβλήματος", "components.AirDateBadge.airsrelative": "Θα προβληθεί σε {relativeTime}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Κάθε {jobScheduleSeconds, plural, one {second} other {{jobScheduleSeconds} seconds}}", "components.Settings.SettingsJobsCache.imagecachesize": "Συνολικό μέγεθος cache", "components.Settings.SettingsMain.validationApplicationTitle": "Πρέπει να δώσετε έναν τίτλο εφαρμογής", "components.Settings.advancedTooltip": "Λανθασμένη παραμετροποίηση αυτή της ρύθμισης μπορεί να οδηγήσει σε διακοπή της λειτουργίας", "components.Settings.experimentalTooltip": "Η ενεργοποίηση αυτής της ρύθμισης μπορεί να οδηγήσει σε απρόσμενη συμπεριφορά της εφαρμογής", "components.Settings.urlBase": "Βάση URL", "components.Settings.validationUrl": "Πρέπει να δώσετε ένα έγκυρο URL", "components.StatusBadge.managemedia": "Διαχείριση {mediaType}", "components.StatusBadge.openinarr": "Άνοιγμα σε {arr}", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Αυτόματη αίτηση σειρών", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Δημιουργήστε ένα token από τις <PushbulletSettingsLink>Ρυθμίσεις Λογαριασμού</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Καταχωρήστε μια εφαρμογή</ApplicationRegistrationLink> για χρήση με το {applicationTitle}", "components.Discover.DiscoverMovies.discovermovies": "Ταινίες", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Α-Ω) Αύξουσα σειρά", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ω-<PERSON>) φθίνουσα σειρά", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Βαθμολογία TMDB αύξουσα", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Βαθμολογία TMDB φθίνουσα", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.FilterSlideover.clearfilters": "Εκκαθάριση ενεργών φίλτρων", "components.Discover.FilterSlideover.filters": "Φίλτρα", "components.Discover.FilterSlideover.firstAirDate": "Ημ/νία πρώτης προβολής", "components.Discover.FilterSlideover.from": "Από", "components.Discover.FilterSlideover.genres": "Είδη", "components.Discover.FilterSlideover.keywords": "Λέξεις κλειδιά", "components.Discover.FilterSlideover.originalLanguage": "Πρωτότυπη γλώσσα", "components.Discover.FilterSlideover.ratingText": "Βαθμολογίες από {minValue} έως {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Ημερομηνία κυκλοφορίας", "components.Discover.FilterSlideover.runtime": "Διάρκεια", "components.PermissionEdit.autorequestMoviesDescription": "Χορήγηση άδειας για την αυτόματη υποβολή αιτήσεων για ταινίες μη-4K μέσω της λίστας παρακολούθησης του Plex.", "components.PermissionEdit.autorequestSeries": "Αυτόματη αίτηση σειρών", "components.RequestModal.requestcollection4ktitle": "Αίτηση συλλογής σε 4K", "components.RequestModal.requestcollectiontitle": "Αίτηση συλλογής", "components.Selector.searchGenres": "Επιλέξτε είδη…", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Νέα συχνότητα", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Κάθε {jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Κάθε {jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.Settings.SettingsJobsCache.imagecacheDescription": "Όταν είναι ενεργοποιημένο στις ρυθμίσεις, το <PERSON><PERSON>rr θα κάνει proxy και cache εικόνες από προκαθορισμένες εξωτερικές πηγές. Οι εικόνες που έχουν γίνει cached θα αποθηκευτούν στον φάκελο των αρχείων. Μπορείτε να βρείτε τα αρχεία στο <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsMain.locale": "Γλώσ<PERSON><PERSON> εμφάνισης", "components.Settings.SettingsMain.originallanguageTip": "Φιλτράρετε το περιεχόμενο με βάση τη πρωτότυπη γλώσσα", "components.Settings.SettingsMain.partialRequestsEnabled": "Επιτρέψτε τις αιτήσεων σειρών μερικώς", "components.Settings.SettingsUsers.localLoginTip": "Χορήγη<PERSON>η άδειας σε χρήστες να συνδεθούν χρησιμοποιώντας το email τους και τον κωδικό τους, αντί να συνδεθούν μέσω Plex", "components.Settings.externalUrl": "Εξωτερικό URL", "components.Settings.tautulliApiKey": "Κλειδί API", "components.Settings.toastTautulliSettingsFailure": "Κάτι πήγε στραβ<PERSON> κατά την αποθήκευση των ρυθμίσεων του <PERSON>.", "components.StatusChecker.appUpdated": "{applicationTitle} Ενημερώθηκε", "components.StatusChecker.restartRequired": "Απαιτεί<PERSON><PERSON><PERSON> επανεκκίνηση του διακομιστή", "components.TitleCard.mediaerror": "{mediaType} Δε βρέθηκε", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TvDetails.Season.noepisodes": "Η λίστα επεισοδίων δεν είναι διαθέσιμη.", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "Ο <FindDiscordIdLink>multi-digit ID αριθμός</FindDiscordIdLink> που σχετίζεται με τον λογαριασμό σας στο Discord", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Οι ρυθμίσεις ειδοποιήσεων <PERSON> απέτυχαν να αποθηκευτούν.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Κλειδί χρήστη ή ομάδας", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Πρέπει να δώσετε ένα έγκυρο token εφαρμογής", "i18n.open": "Άνοιγμα", "i18n.restartRequired": "Απαιτείτ<PERSON><PERSON> επανεκκίνηση", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber} Επεισόδιο {episodeNumber}", "components.NotificationTypeSelector.issueresolvedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON>ω<PERSON> όταν λύνονται προβλήματα.", "components.RequestList.RequestItem.unknowntitle": "Άγνωστος τίτλος", "components.RequestModal.requestseriestitle": "Αίτηση σειράς", "components.Discover.moviegenres": "Είδη ταινιών", "components.Discover.resetfailed": "Κάτι πήγε στραβά με την επαναφορά των ρυθμίσεων εξατομίκευσης ανακάλυψης.", "components.Discover.resetsuccess": "Επιτυχής επανα<PERSON>ορά των ρυθμίσεων προσαρμογής ανακάλυψης.", "components.Discover.FilterSlideover.studio": "Στούντιο", "components.Discover.FilterSlideover.tmdbuserscore": "Βαθμολογία χρηστών TMDB", "components.Discover.FilterSlideover.to": "Εως", "components.Discover.tmdbnetwork": "Δίκτυο TMDB", "components.Discover.tmdbsearch": "TMDB Αναζήτηση", "components.Discover.tmdbtvgenre": "<PERSON><PERSON><PERSON><PERSON> σειράς TMDB", "components.IssueDetails.IssueComment.postedby": "Δημοσιεύθηκε {relativeTime} από {username}", "components.IssueDetails.IssueComment.postedbyedited": "Δημοσιεύθηκε {relativeTime} απ<PERSON> {username} (Επεξεργάστηκε)", "components.IssueDetails.play4konplex": "Αναπαραγωγή σε 4K στο Plex", "components.IssueDetails.toastissuedeleted": "Το πρόβλημα διαγράφηκε επιτυχώς!", "components.Layout.LanguagePicker.displaylanguage": "Γλώσ<PERSON><PERSON> εμφάνισης", "components.Layout.Sidebar.browsemovies": "Ταινίες", "components.Layout.Sidebar.browsetv": "Σειρές", "components.RequestCard.unknowntitle": "Άγνωστος τίτλος", "components.Discover.CreateSlider.addcustomslider": "Δημιουργία προσαρμοσμένου Slider", "components.Discover.CreateSlider.editSlider": "Επεξεργα<PERSON><PERSON><PERSON>lider", "components.Discover.CreateSlider.addSlider": "Προσθή<PERSON><PERSON> Slider", "components.Discover.CreateSlider.addfail": "Απέτυχε η δημιουργία νέου slider.", "components.Discover.CreateSlider.addsuccess": "Δημιουργήθηνε νέο slider και αποθηκεύτηκαν οι προσαρμοσμένες ρυθμίσεις ανακάλυψης.", "components.Discover.CreateSlider.nooptions": "Δεν υπάρχουν αποτελέσματα.", "components.Discover.CreateSlider.editfail": "Αποτυχία επεξεργασίας slider.", "components.Discover.CreateSlider.editsuccess": "Επεξερ<PERSON><PERSON>στηκε το slider και αποθηκεύτηκαν οι προσαρμοσμένες ρυθμίσεις ανακάλυψης.", "components.Discover.CreateSlider.needresults": "Πρέπει να έχετε τουλάχιστον 1 αποτέλεσμα.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.DiscoverMovies.sortPopularityAsc": "Δημοτικότητα Αύξουσα", "components.Discover.DiscoverMovies.sortPopularityDesc": "Δημοτικότητα φθίνουσα", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Ημ/νία κυκλοφορίας αύξουσα", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ω-<PERSON>) φθίνουσα σειρά", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Ημ/νία κυκλοφορίας φθίνουσα", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Α-Ω) Αύξουσα σειρά", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Βαθμολογία TMDB αύξουσα", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Βαθμολογία TMDB φθίνουσα", "components.Discover.DiscoverSliderEdit.deletefail": "Αποτυχία δια<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.Discover.DiscoverSliderEdit.deletesuccess": "<PERSON><PERSON> διαγράφηκε επιτυχώς.", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Active Filter} other {# Active Filters}}", "components.Discover.DiscoverTv.sortPopularityDesc": "Δημοτικότητα φθίνουσα", "components.Discover.DiscoverTv.discovertv": "Σειρές", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Πρώτη ημ/νία προβολής φθίνουσα", "components.Discover.DiscoverTv.sortPopularityAsc": "Δημοτικότητα αύξουσα", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Πρώτη ημ/νία προβολής αύξουσα", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} λεπτά διάρκειας", "components.Discover.FilterSlideover.streamingservices": "Υπηρεσίες streaming", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Περιεχόμενο που προστέθηκε στη <PlexWatchlistSupportLink>Λίστα παρακολούθησης Plex</PlexWatchlistSupportLink> θα εμφανίζεται εδώ.", "components.Discover.resettodefault": "Επανα<PERSON><PERSON><PERSON><PERSON> στην προεπιλογή", "components.Discover.emptywatchlist": "Περιεχόμενο που προστέθηκε στη <PlexWatchlistSupportLink>Λίστα παρακολούθησης Plex</PlexWatchlistSupportLink> θα εμφανίζεται εδώ.", "components.Discover.tmdbstudio": "Στούντιο TMDB", "components.Discover.tmdbtvkeyword": "Λέξη-κλειδ<PERSON> σειράς TMDB", "components.IssueDetails.IssueComment.areyousuredelete": "Είστε σίγουροι πως θέλετε να διαγράψετε αυτό το σχόλιο;", "components.IssueDetails.IssueComment.edit": "Επεξεργασ<PERSON>α σχολίου", "components.IssueDetails.IssueComment.validationComment": "Πρέπει να εισάγετε ένα μήνυμα", "components.IssueDetails.IssueDescription.edit": "Επεξεργασία περιγραφής", "components.IssueDetails.allepisodes": "Όλα τα επεισόδια", "components.IssueDetails.issuepagetitle": "Πρόβλημα", "components.IssueDetails.lastupdated": "Τελευτα<PERSON>α ενημέρωση", "components.IssueDetails.reopenissueandcomment": "Ανοίξτε ξανά με σχόλιο", "components.IssueDetails.problemseason": "Επηρεασμένη σεζόν", "components.IssueDetails.toastissuedeletefailed": "Κάτι πήγε στραβά κατά τη διαγραφή του προβλήματος.", "components.IssueDetails.toaststatusupdatefailed": "Κάτι πήγε στραβά με την ενημέρωση της κατάστασης του προβλήματος.", "components.IssueList.sortModified": "Τελευταία τροποποιημένα", "components.IssueModal.CreateIssueModal.season": "<PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber}", "components.IssueList.issues": "Προβλήματα", "components.IssueModal.CreateIssueModal.episode": "Επεισόδιο {episodeNumber}", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Η αναφορά προβλήματος για το <strong>{title}</strong> υποβλήθηκε επιτυχώς!", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Πρέπει να δώσετε μια περιγραφή", "components.ManageSlideOver.alltime": "Από πάντα", "components.ManageSlideOver.manageModalAdvanced": "Για προχωρημένους", "components.ManageSlideOver.manageModalClearMediaWarning": "* Αυτό θα αφαιρέσει αμετάκλητα όλα τα δεδομένα για αυτό το {mediaType}, συμπεριλαμβανομένων τυχόν αιτήσεων. <PERSON><PERSON><PERSON> αυτό το στοιχείο υπάρχει στη βιβλιοθήκη Plex, οι πληροφορίες του μέσου θα δημιουργηθούν εκ νέου κατά την επόμενη σάρωση.", "components.ManageSlideOver.manageModalNoRequests": "Δεν υπάρχουν αιτήματα.", "components.ManageSlideOver.markallseasonsavailable": "Επισημάνετε όλες τις σεζόν ως διαθέσιμες", "components.MovieDetails.productioncountries": "Παραγωγή {countryCount, plural, one {Country} other {Countries}}", "components.MovieDetails.rtaudiencescore": "Βαθμολογία κοινού Rotten Tomatoes", "components.NotificationTypeSelector.issuereopenedDescription": "Αποστο<PERSON><PERSON> ειδοποι<PERSON><PERSON><PERSON><PERSON><PERSON> όταν ξανανοίγονται προβλήματα.", "components.NotificationTypeSelector.userissuecommentDescription": "Ειδοποιηθείτε όταν προβλήματα που έχετε αναφέρει λαμβάνουν νέα σχόλια.", "components.NotificationTypeSelector.usermediarequestedDescription": "Ειδοποιηθείτ<PERSON> όταν άλλοι χρήστες υποβάλλουν αιτήματα τα οποία απαιτούν έγκριση.", "components.PermissionEdit.autorequestDescription": "Χορήγηση άδειας για την αυτόματη υποβολή αιτήσεων για μέσα μη-4K μέσω της λίστας παρακολούθησης του Plex.", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} ανά {quotaDays} {days}</quotaUnits>", "components.PermissionEdit.viewrecentDescription": "Χορήγη<PERSON><PERSON> άδειας για την προβολή της λίστας του περιεχομένου που προστέθηκε πρόσφατα.", "components.PermissionEdit.viewwatchlistsDescription": "Χορήγη<PERSON><PERSON> άδειας για την προβολή λιστών παρακολούθησης Plex άλλων χρηστών.", "components.QuotaSelector.movies": "{count, plural, one {movie} other {movies}}", "components.RequestModal.requestmovie4ktitle": "Αίτηση ταινίας σε 4Κ", "components.RequestModal.requestmovies4k": "Αίτηση {count} {count, plural, one {Movie} other {Movies}} σε 4Κ", "components.RequestModal.requestmovietitle": "Αίτηση ταινίας", "components.RequestModal.requestseasons4k": "Αίτηση {seasonCount} {seasonCount, plural, one {Season} other {Seasons}} σε 4Κ", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Οι ρυθμίσεις ειδοποιήσεων Gotify απέτυχαν να αποθηκευτούν.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Η δοκιμαστική ειδοποίηση Gotify στάλθηκε!", "components.Settings.Notifications.NotificationsGotify.token": "<PERSON><PERSON> εφαρμογής", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Πρέπει να επιλέξετε τουλάχιστον έναν τύπο ειδοποιήσεων", "components.Settings.RadarrModal.released": "Κυκλοφόρησε", "components.Settings.SettingsAbout.runningDevelop": "Εκτελείτε την έκδοση <code>αν<PERSON><PERSON><PERSON><PERSON><PERSON>ης</code> το<PERSON>, το οποίο προτείνεται μόνο για αυτούς που προσφέρουν την ανάπτυξη της εφαρμογής ή σε αυτούς που βοηθούν με τον έλεγχο των τελευταίων εκδόσεων.", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Τρέχουσα συχνότητα", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Η εργασία μεταβλήθηκε επιτυχώς!", "components.Settings.SettingsMain.apikey": "Κλειδί API", "components.Settings.SettingsMain.toastApiKeyFailure": "Κάτι πήγε στραβά κατά τη δημιουργία νέου κλειδιού API.", "components.Settings.SettingsMain.toastSettingsSuccess": "Οι ρυθμίσεις αποθηκεύτηκαν με επιτυχία!", "components.Settings.SettingsMain.validationApplicationUrl": "Πρέπει να δώσετε ένα έγκυρο URL", "components.Settings.SonarrModal.tagRequests": "Ετικέτες αιτημάτων", "components.Settings.SonarrModal.tagRequestsInfo": "Αυτόματη προσθήκη πρόσθετης ετικέτας με το αναγνωριστικό χρήστη και το όνομα χρήστη του αιτούντος", "components.StatusChecker.appUpdatedDescription": "Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πατήστε το κουμπί παρακάτω ώστε να γίνει επαναφόρτωση της εφαρμογής.", "components.TvDetails.status4k": "4Κ {status}", "components.UserList.newplexsigninenabled": "Η ρύθμιση <strong>Ενεργοποίηση νέας εισόδου Plex</strong> είναι ενεργοποιημένη. Οι χρήστες Plex με πρόσβαση στη βιβλιοθήκη δε χρειάζεται να γίνουν εισαγωγή ώστε να συνδεθούν.", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Αναγνωριστικ<PERSON> χρήστη Discord", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Ζητήστε αυτόματα σειρές που βρίσκονται στη <PlexWatchlistSupportLink>λίστα παρακολούθησης του Plex</PlexWatchlistSupportLink>", "components.UserProfile.plexwatchlist": "Λίστα παρακολούθησης Plex", "components.MovieDetails.imdbuserscore": "Βαθμολογία χρηστών IMDB", "components.Settings.SonarrModal.animeSeriesType": "Τύ<PERSON>ος σειράς άνιμε", "components.Settings.SonarrModal.seriesType": "Τύ<PERSON><PERSON> σειράς", "components.Settings.Notifications.NotificationsPushover.sound": "Ήχος ειδοποίησης", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Προεπιλογή συσκευής", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Ήχος ειδοποίησης", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Προεπιλογή συσκευής"}