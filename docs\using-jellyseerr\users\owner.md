---
title: Owner Account
description: Your owner account is the primary account for managing Jellyseerr.
sidebar_position: 1
---

# Owner Account

The user account created during Jellyseerr setup is the "Owner" account, which cannot be deleted or modified by other users. This account's credentials are used to authenticate with your media server and configure Jellyseerr settings.

:::note
In case of Jellyfin/Emby, the owner account is also used for API access to your media server. This account should have a valid authentication token for your media server.
:::

:::tip
If your authentication token is ever invalidated or changed, you can refresh it by re-authenticating with your media server.
:::
