apiVersion: v1
kind: Service
metadata:
  name: {{ include "jellyseerr.fullname" . }}
  labels:
    {{- include "jellyseerr.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "jellyseerr.selectorLabels" . | nindent 4 }}
  ipFamilyPolicy: PreferDualStack
