---
title: Reverse Proxy
description: Configure a reverse proxy for Jellyseerr.
sidebar_position: 1
---

# Reverse Proxy

:::warning
Base URLs cannot be configured in Jellyseerr. With this limitation, only subdomain configurations are supported.

A Nginx subfolder workaround configuration is provided below, but it is not officially supported.
:::

## Nginx

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

<Tabs groupId="nginx-reverse-proxy" queryString>
    <TabItem value="subdomain" label="Subdomain">
Add the following configuration to a new file `/etc/nginx/sites-available/jellyseerr.example.com.conf`:

```nginx
server {
    listen 80;
    server_name jellyseerr.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name jellyseerr.example.com;

    ssl_certificate /etc/letsencrypt/live/jellyseerr.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/jellyseerr.example.com/privkey.pem;

    proxy_set_header Referer $http_referer;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Real-Port $remote_port;
    proxy_set_header X-Forwarded-Host $host:$remote_port;
    proxy_set_header X-Forwarded-Server $host;
    proxy_set_header X-Forwarded-Port $remote_port;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Ssl on;

    location / {
        proxy_pass http://127.0.0.1:5055;
    }
}
```

Then, create a symlink to `/etc/nginx/sites-enabled`:

```bash
sudo ln -s /etc/nginx/sites-available/jellyseerr.example.com.conf /etc/nginx/sites-enabled/jellyseerr.example.com.conf
```

    </TabItem>

    <TabItem value="subfolder" label="Subfolder">

:::warning
This Nginx subfolder reverse proxy is an unsupported workaround, and only provided as an example. The filters may stop working when Jellyseerr is updated.

If you encounter any issues with Jellyseerr while using this workaround, we may ask you to try to reproduce the problem without the Nginx proxy.
:::

Add the following location block to your existing `nginx.conf` file.

```nginx
location ^~ /jellyseerr {
    set $app 'jellyseerr';

    # Remove /jellyseerr path to pass to the app
    rewrite ^/jellyseerr/?(.*)$ /$1 break;
    proxy_pass http://127.0.0.1:5055; # NO TRAILING SLASH

    # Redirect location headers
    proxy_redirect ^ /$app;
    proxy_redirect /setup /$app/setup;
    proxy_redirect /login /$app/login;

    # Sub filters to replace hardcoded paths
    proxy_set_header Accept-Encoding "";
    sub_filter_once off;
    sub_filter_types *;
    sub_filter 'href="/"' 'href="/$app"';
    sub_filter 'href="/login"' 'href="/$app/login"';
    sub_filter 'href:"/"' 'href:"/$app"';
    sub_filter '\/_next' '\/$app\/_next';
    sub_filter '/_next' '/$app/_next';
    sub_filter '/api/v1' '/$app/api/v1';
    sub_filter '/login/plex/loading' '/$app/login/plex/loading';
    sub_filter '/images/' '/$app/images/';
    sub_filter '/imageproxy/' '/$app/imageproxy/';
    sub_filter '/avatarproxy/' '/$app/avatarproxy/';
    sub_filter '/android-' '/$app/android-';
    sub_filter '/apple-' '/$app/apple-';
    sub_filter '/favicon' '/$app/favicon';
    sub_filter '/logo_' '/$app/logo_';
    sub_filter '/site.webmanifest' '/$app/site.webmanifest';
}
```

    </TabItem>

    <TabItem value="swag" label="SWAG">

A sample proxy configuration is included in [SWAG (Secure Web Application Gateway)](https://github.com/linuxserver/docker-swag).

However, this page is still the only source of truth, so the SWAG sample configuration is not guaranteed to be up-to-date. If you find an inconsistency, please [report it to the LinuxServer team](https://github.com/linuxserver/reverse-proxy-confs/issues/new) or [submit a pull request to update it](https://github.com/linuxserver/reverse-proxy-confs/pulls).

To use the bundled configuration file, simply rename `jellyseerr.subdomain.conf.sample` in the `proxy-confs` folder to `jellyseerr.subdomain.conf`.

Alternatively, you can create a new file `jellyseerr.subdomain.conf` in `proxy-confs` with the following configuration:

```nginx
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;

    server_name jellyseerr.*;

    include /config/nginx/ssl.conf;

    client_max_body_size 0;

    location / {
        include /config/nginx/proxy.conf;
        resolver 127.0.0.11 valid=30s;
        set $upstream_app jellyseerr;
        set $upstream_port 5055;
        set $upstream_proto http;
        proxy_pass $upstream_proto://$upstream_app:$upstream_port;
    }

}
```

</TabItem>

    <TabItem value="nginx-proxy-manager" label="Nginx Proxy Manager">

Add a new proxy host with the following settings:

### Details

- **Domain Names:** Your desired external Jellyseerr hostname; e.g., `jellyseerr.example.com`
- **Scheme:** `http`
- **Forward Hostname / IP:** Internal Jellyseerr hostname or IP
- **Forward Port:** `5055`
- **Cache Assets:** yes
- **Block Common Exploits:** yes

### SSL

- **SSL Certificate:** Select one of the options; if you are not sure, pick “Request a new SSL Certificate”
- **Force SSL:** yes
- **HTTP/2 Support:** yes

Then, click “Save” and “Apply Changes”.

      </TabItem>

  </Tabs>

## Caddy (v2)

Create a Caddyfile with the following content:

```caddyfile
jellyseerr.example.com {
    reverse_proxy http://127.0.0.1:5055
}
```

Deploy the Caddyfile by running:

```bash

sudo caddy run --config /path/to/Caddyfile
```

Verify by visiting https://jellyseerr.example.com in your browser.

:::note
Caddy will automatically obtain and renew SSL certificates for your domain.
:::

## Traefik (v2)

Add the following labels to the Jellyseerr service in your `compose.yaml` file:

```yaml
labels:
  - 'traefik.enable=true'
  ## HTTP Routers
  - 'traefik.http.routers.jellyseerr-rtr.entrypoints=https'
  - 'traefik.http.routers.jellyseerr-rtr.rule=Host(`jellyseerr.domain.com`)'
  - 'traefik.http.routers.jellyseerr-rtr.tls=true'
  ## HTTP Services
  - 'traefik.http.routers.jellyseerr-rtr.service=jellyseerr-svc'
  - 'traefik.http.services.jellyseerr-svc.loadbalancer.server.port=5055'
```

For more information, please refer to the [Traefik documentation](https://doc.traefik.io/traefik/user-guides/docker-compose/basic-example/).

## Apache2 HTTP Server

<Tabs groupId="apache2-reverse-proxy" queryString>
    <TabItem value="subdomain" label="Subdomain">

Add the following Location block to your existing Server configuration.

```apache
# Jellyseerr
		ProxyPreserveHost On
		ProxyPass / http://localhost:5055 retry=0 connectiontimeout=5 timeout=30 keepalive=on
		ProxyPassReverse http://localhost:5055 /
		RequestHeader set Connection ""
```

</TabItem>

<TabItem value="subfolder" label="Subfolder">

:::warning
This Apache2 subfolder reverse proxy is an unsupported workaround, and only provided as an example. The filters may stop working when Jellyseerr is updated.

If you encounter any issues with Jellyseerr while using this workaround, we may ask you to try to reproduce the problem without the Apache2 proxy.
:::

Add the following Location block to your existing Server configuration.

```apache
# Jellyseerr
# We will use "/jellyseerr" as subfolder
# You can replace it with any that you like
	<Location /jellyseerr>
		ProxyPreserveHost On
		ProxyPass http://localhost:5055 retry=0 connectiontimeout=5 timeout=30 keepalive=on
		ProxyPassReverse http://localhost:5055
		RequestHeader set Connection ""

		# Header update, to support subfolder
		# Please Replace "FQDN" with your domain
		Header edit location ^/login https://FQDN/jellyseerr/login
		Header edit location ^/setup https://FQDN/jellyseerr/setup
		
		AddOutputFilterByType INFLATE;SUBSTITUTE text/html application/javascript application/json
		SubstituteMaxLineLength 2000K
		# This is HTML and JS update
		# Please update "/jellyseerr" if needed
		Substitute "s|href=\"|href=\"/jellyseerr|inq"
		Substitute "s|src=\"|src=\"/jellyseerr|inq"
		Substitute "s|/api/|/jellyseerr/api/|inq"
		Substitute "s|\"/_next/|\"/jellyseerr/_next/|inq"
		# This is JSON update
		Substitute "s|\"/avatarproxy/|\"/jellyseerr/avatarproxy/|inq"
	</Location>
```

</TabItem>

</Tabs>
