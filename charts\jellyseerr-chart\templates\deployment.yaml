apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "jellyseerr.fullname" . }}
  labels:
    {{- include "jellyseerr.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  strategy:
    type: {{ .Values.strategy.type }}
  selector:
    matchLabels:
      {{- include "jellyseerr.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "jellyseerr.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "jellyseerr.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          {{- if .Values.image.sha }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}@sha256:{{ .Values.image.sha }}"
          {{- else }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          {{- end }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 5055
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
            {{- if .Values.probes.livenessProbe.initialDelaySeconds }}
            initialDelaySeconds: {{ .Values.probes.livenessProbe.initialDelaySeconds }}
            {{- end }}
            {{- if .Values.probes.livenessProbe.periodSeconds }}
            periodSeconds: {{ .Values.probes.livenessProbe.periodSeconds }}
            {{- end }}
            {{- if .Values.probes.livenessProbe.timeoutSeconds }}
            timeoutSeconds: {{ .Values.probes.livenessProbe.timeoutSeconds }}
            {{- end }}
            {{- if .Values.probes.livenessProbe.successThreshold }}
            successThreshold: {{ .Values.probes.livenessProbe.successThreshold }}
            {{- end }}
            {{- if .Values.probes.livenessProbe.failureThreshold }}
            failureThreshold: {{ .Values.probes.livenessProbe.failureThreshold }}
            {{- end }}
          readinessProbe:
            httpGet:
              path: /
              port: http
            {{- if .Values.probes.readinessProbe.initialDelaySeconds }}
            initialDelaySeconds: {{ .Values.probes.readinessProbe.initialDelaySeconds }}
            {{- end }}
            {{- if .Values.probes.readinessProbe.periodSeconds }}
            periodSeconds: {{ .Values.probes.readinessProbe.periodSeconds }}
            {{- end }}
            {{- if .Values.probes.readinessProbe.timeoutSeconds }}
            timeoutSeconds: {{ .Values.probes.readinessProbe.timeoutSeconds }}
            {{- end }}
            {{- if .Values.probes.readinessProbe.successThreshold }}
            successThreshold: {{ .Values.probes.readinessProbe.successThreshold }}
            {{- end }}
            {{- if .Values.probes.readinessProbe.failureThreshold }}
            failureThreshold: {{ .Values.probes.readinessProbe.failureThreshold }}
            {{- end }}
          {{- if .Values.probes.startupProbe }}
          startupProbe:
            {{- toYaml .Values.probes.startupProbe | nindent 12 }}
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          {{- with .Values.extraEnv }}
          env:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.extraEnvFrom }}
          envFrom:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: config
              mountPath: /app/config
          {{- with .Values.volumeMounts }}
            {{- toYaml . | nindent 12 }}
          {{- end }}
      volumes:
        - name: config
          persistentVolumeClaim:
            claimName: {{ include "jellyseerr.configPersistenceName" . }}
      {{- with .Values.volumes }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
