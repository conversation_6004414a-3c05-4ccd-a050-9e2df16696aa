{"components.Discover.popularmovies": "Популярные фильмы", "components.Discover.populartv": "Популярные сериалы", "components.Discover.recentlyAdded": "Недавно добавлено", "components.Discover.recentrequests": "Последние запросы", "components.Discover.trending": "В трендах", "components.Discover.upcoming": "Предстоящие фильмы", "components.Discover.upcomingmovies": "Предстоящие фильмы", "components.Layout.SearchInput.searchPlaceholder": "Поиск фильмов и сериалов", "components.Layout.Sidebar.dashboard": "Найти что-то новое", "components.Layout.Sidebar.requests": "Запросы", "components.Layout.Sidebar.settings": "Настройки", "components.Layout.Sidebar.users": "Пользователи", "components.Layout.UserDropdown.signout": "Выход", "components.MovieDetails.budget": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.cast": "В ролях", "components.MovieDetails.originallanguage": "Язык оригинала", "components.MovieDetails.overview": "Обзор", "components.MovieDetails.overviewunavailable": "Обзор недоступен.", "components.MovieDetails.recommendations": "Рекомендации", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Дата релиза} other {Даты релиза}}", "components.MovieDetails.revenue": "Доход", "components.MovieDetails.runtime": "{minutes} минут", "components.MovieDetails.similar": "Похожие фильмы", "components.PersonDetails.appearsin": "Появления в фильмах и сериалах", "components.PersonDetails.ascharacter": "в роли {character}", "components.RequestBlock.seasons": "{seasonCount, plural, one {Сезон} other {Сезоны}}", "components.RequestCard.seasons": "{seasonCount, plural, one {Сезон} other {Сезоны}}", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезоны}}", "components.RequestList.requests": "Запросы", "components.RequestModal.cancel": "Отменить запрос", "components.RequestModal.numberofepisodes": "# эпизодов", "components.RequestModal.pendingrequest": "Ожид<PERSON><PERSON><PERSON>ий запрос", "components.RequestModal.requestCancel": "Запрос на <strong>{title}</strong> отменён.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> успешно запрошен!", "components.RequestModal.requestadmin": "Этот запрос будет одобрен автоматически.", "components.RequestModal.requestfrom": "Запрос пользователя {username} ожидает одобрения.", "components.RequestModal.requestseasons": "Запросить {seasonCount} {seasonCount, plural, one {сезон} other {сезона(ов)}}", "components.RequestModal.season": "Сезон", "components.RequestModal.seasonnumber": "Сезон {number}", "components.RequestModal.selectseason": "Выберите сезон(ы)", "components.Search.searchresults": "Результаты поиска", "components.Settings.Notifications.agentenabled": "Активировать службу", "components.Settings.Notifications.authPass": "Пароль SMTP", "components.Settings.Notifications.authUser": "Имя пользователя SMTP", "components.Settings.Notifications.emailsender": "Адрес отправителя", "components.Settings.Notifications.smtpHost": "SMTP-хост", "components.Settings.Notifications.smtpPort": "SMTP порт", "components.Settings.Notifications.validationSmtpHostRequired": "Вы должны указать действительное имя хоста или IP-адрес", "components.Settings.Notifications.validationSmtpPortRequired": "Вы должны указать действительный номер порта", "components.Settings.Notifications.webhookUrl": "URL веб-перехватчика", "components.Settings.RadarrModal.add": "Добавить сервер", "components.Settings.RadarrModal.apiKey": "Ключ API", "components.Settings.RadarrModal.baseUrl": "Базовый URL", "components.Settings.RadarrModal.createradarr": "Добавить новый сервер Radarr", "components.Settings.RadarrModal.defaultserver": "Сервер по умолчанию", "components.Settings.RadarrModal.editradarr": "Редактировать сервер Radarr", "components.Settings.RadarrModal.hostname": "Имя хоста или IP-адрес", "components.Settings.RadarrModal.minimumAvailability": "Минимальная доступность", "components.Settings.RadarrModal.port": "Порт", "components.Settings.RadarrModal.qualityprofile": "Профиль качества", "components.Settings.RadarrModal.rootfolder": "Корневой каталог", "components.Settings.RadarrModal.selectMinimumAvailability": "Выберите минимальную доступность", "components.Settings.RadarrModal.selectQualityProfile": "Выберите профиль качества", "components.Settings.RadarrModal.selectRootFolder": "Выберите корневой каталог", "components.Settings.RadarrModal.server4k": "4К сервер", "components.Settings.RadarrModal.servername": "Название сервера", "components.Settings.RadarrModal.ssl": "Использовать SSL", "components.Settings.RadarrModal.toastRadarrTestFailure": "Не удалось подключиться к Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "Соединение с Radarr установлено успешно!", "components.Settings.RadarrModal.validationApiKeyRequired": "Вы должны предоставить ключ API", "components.Settings.RadarrModal.validationHostnameRequired": "Вы должны указать действительное имя хоста или IP-адрес", "components.Settings.RadarrModal.validationPortRequired": "Вы должны указать действительный номер порта", "components.Settings.RadarrModal.validationProfileRequired": "Вы должны выбрать профиль качества", "components.Settings.RadarrModal.validationRootFolderRequired": "Вы должны выбрать корневой каталог", "components.Settings.SonarrModal.add": "Добавить сервер", "components.Settings.SonarrModal.apiKey": "Ключ API", "components.Settings.SonarrModal.baseUrl": "Базовый URL", "components.Settings.SonarrModal.createsonarr": "Добавить новый сервер Sonarr", "components.Settings.SonarrModal.defaultserver": "Сервер по умолчанию", "components.Settings.SonarrModal.editsonarr": "Редактировать сервер Sonarr", "components.Settings.SonarrModal.hostname": "Имя хоста или IP-адрес", "components.Settings.SonarrModal.port": "Порт", "components.Settings.SonarrModal.qualityprofile": "Профиль качества", "components.Settings.SonarrModal.rootfolder": "Корневой каталог", "components.Settings.SonarrModal.seasonfolders": "Папки для сезонов", "components.Settings.SonarrModal.selectQualityProfile": "Выберите профиль качества", "components.Settings.SonarrModal.selectRootFolder": "Выберите корневой каталог", "components.Settings.SonarrModal.server4k": "4К сервер", "components.Settings.SonarrModal.servername": "Название сервера", "components.Settings.SonarrModal.ssl": "Использовать SSL", "components.Settings.SonarrModal.validationApiKeyRequired": "Вы должны предоставить ключ API", "components.Settings.SonarrModal.validationHostnameRequired": "Вы должны указать действительное имя хоста или IP-адрес", "components.Settings.SonarrModal.validationPortRequired": "Вы должны указать действительный номер порта", "components.Settings.SonarrModal.validationProfileRequired": "Вы должны выбрать профиль качества", "components.Settings.SonarrModal.validationRootFolderRequired": "Вы должны выбрать корневой каталог", "components.Settings.activeProfile": "Активный профиль", "components.Settings.addradarr": "Добавить сервер Radarr", "components.Settings.address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.addsonarr": "Добавить сервер Sonarr", "components.Settings.cancelscan": "Отменить сканирование", "components.Settings.copied": "Ключ API скопирован в буфер обмена.", "components.Settings.currentlibrary": "Текущая библиотека: {name}", "components.Settings.default": "По умолчанию", "components.Settings.default4k": "4К по умолчанию", "components.Settings.deleteserverconfirm": "Вы уверены, что хотите удалить этот сервер?", "components.Settings.hostname": "Имя хоста или IP-адрес", "components.Settings.librariesRemaining": "Осталось библиотек: {count}", "components.Settings.manualscan": "Сканировать библиотеки вручную", "components.Settings.manualscanDescription": "Обычно выполняется раз в 24 часа. Jellyseerr выполнит более агрессивную проверку вашего сервера Plex на предмет недавно добавленных мультимедиа. Если вы впервые настраиваете Plex, рекомендуется выполнить однократное полное сканирование библиотек вручную!", "components.Settings.menuAbout": "О проекте", "components.Settings.menuGeneralSettings": "Общее", "components.Settings.menuJobs": "Задания и кэш", "components.Settings.menuLogs": "Логи", "components.Settings.menuNotifications": "Уведомления", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "Службы", "components.Settings.notificationsettings": "Настройки уведомлений", "components.Settings.notrunning": "Не работает", "components.Settings.plexlibraries": "Библиотеки Plex", "components.Settings.plexlibrariesDescription": "Библиоте<PERSON><PERSON>, которые Jellyseerr сканирует на предмет наличия мультимедиа. Настройте и сохраните параметры подключения Plex, затем нажмите кнопку ниже, если список библиотек пуст.", "components.Settings.plexsettings": "Настройки Plex", "components.Settings.plexsettingsDescription": "Настройте параметры вашего сервера Plex. Jellyseerr сканирует ваши библиотеки Plex, чтобы определить доступность контента.", "components.Settings.port": "Порт", "components.Settings.radarrsettings": "Настройки Radarr", "components.Settings.sonarrsettings": "Настройки Sonarr", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Начать сканирование", "components.Setup.configureservices": "Настройте службы", "components.Setup.continue": "Продолжить", "components.Setup.finish": "Завершить настройку", "components.Setup.finishing": "Завершение…", "components.Setup.signinMessage": "Начните со входа в систему", "components.Setup.welcome": "Добро пожаловать в Jellyseerr", "components.TvDetails.cast": "В ролях", "components.TvDetails.originallanguage": "Язык оригинала", "components.TvDetails.overview": "Обзор", "components.TvDetails.overviewunavailable": "Обзор недоступен.", "components.TvDetails.recommendations": "Рекомендации", "components.TvDetails.similar": "Похожие сериалы", "components.UserList.admin": "Администратор", "components.UserList.created": "Присоединился", "components.UserList.plexuser": "Пользователь Plex", "components.UserList.role": "Роль", "components.UserList.totalrequests": "Зап<PERSON><PERSON>сов", "components.UserList.user": "Пользователь", "components.UserList.userlist": "Список пользователей", "i18n.approve": "Одобрить", "i18n.approved": "Одобрен", "i18n.available": "Доступен", "i18n.cancel": "Отмена", "i18n.decline": "Отклонить", "i18n.declined": "Отклонён", "i18n.delete": "Удалить", "i18n.movies": "Фильмы", "i18n.partiallyavailable": "Доступен частично", "i18n.pending": "В ожидании", "i18n.processing": "В обработке", "i18n.tvshows": "Сериалы", "i18n.unavailable": "Недоступен", "pages.oops": "Упс", "pages.returnHome": "Вернуться домой", "components.CollectionDetails.overview": "Обзор", "components.CollectionDetails.numberofmovies": "{count} фильмов", "components.CollectionDetails.requestcollection": "Запросить Коллекцию", "components.Login.email": "Адрес электронной почты", "components.UserList.users": "Пользователи", "components.UserList.userdeleted": "Пользователь успешно удален!", "components.UserList.usercreatedsuccess": "Пользователь успешно создан!", "components.Settings.SettingsAbout.totalrequests": "Всего запросов", "components.UserList.sortRequests": "Количество запросов", "components.UserList.sortCreated": "Дата присоединения", "components.Login.password": "Пароль", "components.UserList.password": "Пароль", "components.UserList.localuser": "Локальный пользователь", "i18n.edit": "Редактировать", "components.UserList.deleteuser": "Удалить пользователя", "components.UserList.creating": "Создание…", "components.UserList.createlocaluser": "Создать локального пользователя", "components.UserList.create": "Создать", "components.TvDetails.network": "{networkCount, plural, one {Телеканал} other {Телеканалы}}", "components.TvDetails.anime": "Аниме", "components.Settings.serverpresetManualMessage": "Ручная настройка", "components.Settings.serverpreset": "Сервер", "i18n.deleting": "Удаление…", "components.Settings.SettingsAbout.Releases.latestversion": "Последняя", "components.Settings.SettingsAbout.Releases.currentversion": "Текущая", "components.Settings.SonarrModal.syncEnabled": "Включить сканирование", "components.Settings.RadarrModal.syncEnabled": "Включить сканирование", "components.Settings.Notifications.sendSilentlyTip": "Отправлять уведомления без звука", "components.Settings.Notifications.telegramsettingssaved": "Настройки уведомлений Telegram успешно сохранены!", "components.Settings.Notifications.senderName": "Имя отправителя", "components.Settings.Notifications.botAPI": "Токен авторизации бота", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Активировать службу", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Активировать службу", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Активировать службу", "components.Search.search": "Поиск", "components.ResetPassword.resetpassword": "Сброс пароля", "components.ResetPassword.password": "Пароль", "components.ResetPassword.confirmpassword": "Подтвердить пароль", "components.RequestModal.requesterror": "Что-то пошло не так при отправке запроса.", "components.RequestModal.requestedited": "Запрос на <strong>{title}</strong> успешно отредактирован!", "components.RequestModal.requestcancelled": "Запрос на <strong>{title}</strong> отменён.", "components.RequestModal.errorediting": "Что-то пошло не так при редактировании запроса.", "components.RequestModal.AdvancedRequester.rootfolder": "Корневой каталог", "components.RequestModal.AdvancedRequester.requestas": "Запросить как", "components.RequestModal.AdvancedRequester.qualityprofile": "Профиль качества", "components.RequestModal.AdvancedRequester.default": "{name} (по умолчанию)", "components.RequestModal.AdvancedRequester.advancedoptions": "Расширенные настройки", "components.RequestList.sortModified": "Последнее изменение", "components.RequestList.sortAdded": "По дате", "components.RequestList.showallrequests": "Показать все запросы", "components.RequestButton.viewrequest": "Посмотреть запрос", "i18n.retry": "Повторить", "i18n.requested": "Запрошен", "components.PermissionEdit.request4k": "Запросы 4K", "components.PermissionEdit.request": "Запросы", "i18n.request": "Запросить", "i18n.failed": "Ошибка", "i18n.experimental": "Экспериментальный параметр", "i18n.close": "Закрыть", "i18n.advanced": "Для продвинутых пользователей", "components.Settings.SonarrModal.externalUrl": "Внешний URL-адрес", "components.Settings.RadarrModal.externalUrl": "Внешний URL-адрес", "components.Settings.Notifications.sendSilently": "Отправлять без звука", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Сбросить к настройкам по умолчанию", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Вы должны указать действительный URL-адрес", "components.Settings.Notifications.validationUrl": "Вы должны указать действительный URL-адрес", "components.Settings.RadarrModal.validationApplicationUrl": "Вы должны указать действительный URL-адрес", "components.Settings.SonarrModal.validationApplicationUrl": "Вы должны указать действительный URL-адрес", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Вы должны указать действительный URL-адрес", "components.Settings.Notifications.NotificationsPushover.userToken": "Ключ пользователя или группы", "components.UserList.email": "Адрес электронной почты", "components.ResetPassword.email": "Адрес электронной почты", "components.Settings.SonarrModal.languageprofile": "Языковой профиль", "components.RequestModal.AdvancedRequester.languageprofile": "Языковой профиль", "components.RequestModal.AdvancedRequester.animenote": "* Этот сериал - аниме.", "components.RequestList.RequestItem.requested": "Запрошен", "components.RequestBlock.rootfolder": "Корневой каталог", "components.RegionSelector.regionServerDefault": "По умолчанию ({region})", "components.RegionSelector.regionDefault": "Все регионы", "components.PermissionEdit.viewrequests": "Просмотр запро<PERSON>ов", "components.PermissionEdit.users": "Управление пользователями", "components.PermissionEdit.managerequests": "Управление запросами", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Администратор", "components.PermissionEdit.admin": "Администратор", "components.RequestBlock.profilechanged": "Профиль качества", "components.UserProfile.recentrequests": "Последние запросы", "components.UserProfile.UserSettings.menuPermissions": "Разрешения", "components.UserProfile.UserSettings.UserPermissions.permissions": "Разрешения", "components.UserProfile.UserSettings.menuNotifications": "Уведомления", "components.UserProfile.UserSettings.menuGeneralSettings": "Общее", "components.UserProfile.UserSettings.menuChangePass": "Пароль", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Локальный пользователь", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Общие настройки", "components.UserList.sortDisplayName": "Отображаемое имя", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Отображаемое имя", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Вы должны указать свой текущий пароль", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Вы должны подтвердить новый пароль", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Пароль успешно сохранён!", "components.UserProfile.UserSettings.UserPasswordChange.password": "Пароль", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Новый пароль", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Текущий пароль", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Подтвердите пароль", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Настройки успешно сохранены!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Разрешения успешно сохранены!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Что-то пошло не так при сохранении настроек.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Что-то пошло не так при сохранении настроек.", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Отправлять уведомления без звука", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Пользователь Plex", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.markavailable": "Пометить как доступный", "components.StatusBadge.status4k": "4K {status}", "pages.errormessagewithcode": "{statusCode} - {error}", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Токен доступа", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Вы должны указать действительный URL-адрес", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Название профиля", "components.ResetPassword.resetpasswordsuccessmessage": "Пароль сброшен успешно!", "components.ResetPassword.passwordreset": "Сбросить пароль", "components.RequestModal.edit": "Редактировать запрос", "components.RequestModal.QuotaDisplay.movie": "фильм", "components.RequestModal.AdvancedRequester.tags": "Теги", "components.RequestModal.AdvancedRequester.selecttags": "Выбрать теги", "components.RequestModal.AdvancedRequester.notagoptions": "Тегов нет.", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestList.RequestItem.requesteddate": "Запрошен", "components.RequestList.RequestItem.modifieduserdate": "{date} пользователем {user}", "components.RequestList.RequestItem.modified": "Изменено", "components.RequestList.RequestItem.editrequest": "Редактировать запрос", "components.RequestList.RequestItem.deleterequest": "Удалить запрос", "components.RequestList.RequestItem.cancelRequest": "Отменить запрос", "components.RequestCard.deleterequest": "Удалить запрос", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.alsoknownas": "Также известен(а) как: {names}", "components.NotificationTypeSelector.notificationTypes": "Типы уведомлений", "components.MovieDetails.watchtrailer": "Смотреть трейлер", "components.MovieDetails.originaltitle": "Название оригинала", "components.Login.signinwithplex": "Используйте ваш аккаунт Plex", "components.Login.signinheader": "Войдите, чтобы продолжить", "components.Login.signin": "Войти", "components.Login.forgotpassword": "Забыли пароль?", "components.Layout.UserDropdown.settings": "Настройки", "components.Layout.UserDropdown.myprofile": "Профиль", "components.LanguageSelector.originalLanguageDefault": "Все языки", "components.LanguageSelector.languageServerDefault": "По умолчанию ({language})", "components.Discover.StudioSlider.studios": "Студии", "components.Discover.NetworkSlider.networks": "Телеканалы", "components.Discover.DiscoverStudio.studioMovies": "Фильмы {studio}", "components.Discover.DiscoverMovieLanguage.languageMovies": "Фильмы на языке \"{language}\"", "components.Discover.DiscoverMovieGenre.genreMovies": "Фильмы в жанре \"{genre}\"", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON> <PERSON><PERSON>rr", "components.Settings.SettingsAbout.githubdiscussions": "Обсуждения на GitHub", "components.Settings.enablessl": "Использовать SSL", "components.Settings.is4k": "4К", "components.Settings.mediaTypeMovie": "фильм", "components.Settings.SonarrModal.tags": "Теги", "components.Settings.RadarrModal.tags": "Теги", "i18n.testing": "Тестирование…", "i18n.test": "Протестировать", "i18n.status": "Статус", "i18n.saving": "Сохранение…", "i18n.previous": "Предыдущая", "i18n.next": "Следующая", "i18n.movie": "Фильм", "i18n.canceling": "Отм<PERSON>на…", "i18n.back": "Назад", "i18n.all": "Все", "i18n.settings": "Настройки", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Уведомления", "components.Settings.plex": "Plex", "components.Settings.notifications": "Уведомления", "components.Settings.SettingsUsers.users": "Пользователи", "components.Settings.SettingsLogs.resumeLogs": "Возобновить", "components.Settings.SettingsLogs.pauseLogs": "Приостановить", "components.Settings.SettingsLogs.message": "Сообщение", "components.Settings.SettingsLogs.label": "Метка", "components.Settings.SettingsLogs.filterWarn": "Предупреждения", "components.Settings.SettingsLogs.filterInfo": "Информационные", "components.Settings.SettingsLogs.filterError": "Ошибки", "components.Settings.menuUsers": "Пользователи", "components.Settings.scanning": "Синхронизация…", "i18n.loading": "Загрузка…", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Пользователь", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Роль", "components.Settings.webhook": "Веб-перехватчик", "components.Setup.setup": "Первоначальная настройка", "components.Settings.SettingsJobsCache.process": "Процесс", "components.Settings.SettingsJobsCache.command": "Команда", "components.Settings.SettingsJobsCache.jobtype": "Тип", "components.Settings.SettingsJobsCache.cache": "Кэш", "components.Settings.SettingsAbout.documentation": "Документация", "components.PersonDetails.crewmember": "В составе съёмочной группы", "components.Settings.SettingsAbout.Releases.releases": "Релизы", "components.Settings.SettingsAbout.version": "Версия", "components.UserProfile.ProfileHeader.profile": "Посмотреть профиль", "components.Settings.SettingsJobsCache.cachename": "Название кэша", "components.Settings.SettingsJobsCache.cacheksize": "Размер ключей", "components.Settings.SettingsJobsCache.cachekeys": "Всего ключей", "components.UserList.bulkedit": "Массовое редактирование", "components.MediaSlider.ShowMoreCard.seemore": "Посмотреть больше", "components.TvDetails.watchtrailer": "Смотреть трейлер", "components.Settings.SettingsAbout.timezone": "Часовой пояс", "components.Settings.SettingsAbout.supportoverseerr": "Под<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Получать уведомления, когда другие пользователи отправляют новые медиа-запросы, которые одобряются автоматически.", "components.NotificationTypeSelector.mediarequestedDescription": "Отправлять уведомления, когда пользователи отправляют новые медиа-запросы, требующие одобрения.", "components.NotificationTypeSelector.mediarequested": "Запросы медиа<PERSON><PERSON><PERSON><PERSON>ов", "components.NotificationTypeSelector.mediafailedDescription": "Отправлять уведомления, когда медиа-запросы не удаётся добавить в Radarr или Sonarr.", "components.NotificationTypeSelector.mediafailed": "Ошибки при добавлении медиа-запросов", "components.NotificationTypeSelector.mediadeclinedDescription": "Отправлять уведомления, когда медиа-запросы отклоняются.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Отправлять уведомления, когда пользователи отправляют новые медиа-запросы, которые одобряются автоматически.", "components.NotificationTypeSelector.mediaAutoApproved": "Автоматическое одобрение медиа-запросов", "components.NotificationTypeSelector.mediaapproved": "Одобрение медиа-запросов", "components.NotificationTypeSelector.mediaapprovedDescription": "Отправлять уведомления, когда медиа-запросы одобряются вручную.", "components.NotificationTypeSelector.mediadeclined": "Отклонение медиа-запросов", "components.NotificationTypeSelector.mediaavailableDescription": "Отправлять уведомления, когда запрошенные медиафайлы становятся доступны.", "components.NotificationTypeSelector.mediaavailable": "Доступны новые медиафайлы", "components.MovieDetails.MovieCrew.fullcrew": "Полная съёмочная группа", "components.MovieDetails.viewfullcrew": "Посмотреть полную cъёмочную группу", "components.MovieDetails.showmore": "Развернуть", "components.MovieDetails.showless": "Свернуть", "components.MovieDetails.mark4kavailable": "Пометить как доступный в 4К", "components.MovieDetails.MovieCast.fullcast": "Полный актёрский состав", "components.Login.validationpasswordrequired": "Вы должны предоставить пароль", "components.Login.validationemailrequired": "Вы должны указать действительный адрес электронной почты", "components.Login.signinwithoverseerr": "Используйте ваш аккаунт {applicationTitle}", "components.Login.signingin": "Выполняется вход…", "components.Login.loginerror": "Что-то пошло не так при попытке выполнить вход.", "components.Layout.LanguagePicker.displaylanguage": "Язык интерфейса", "components.DownloadBlock.estimatedtime": "Приблизительно {time}", "components.Discover.upcomingtv": "Предстоящие сериалы", "components.Discover.discover": "Найти что-то новое", "components.Discover.TvGenreSlider.tvgenres": "Сериалы по жанрам", "components.Discover.TvGenreList.seriesgenres": "Сериалы по жанрам", "components.Discover.MovieGenreSlider.moviegenres": "Фильмы по жанрам", "components.Discover.MovieGenreList.moviegenres": "Фильмы по жанрам", "components.Discover.DiscoverTvLanguage.languageSeries": "Сериалы на языке \"{language}\"", "components.Discover.DiscoverTvGenre.genreSeries": "Сериалы в жанре \"{genre}\"", "components.Discover.DiscoverNetwork.networkSeries": "Сериалы {network}", "components.CollectionDetails.requestcollection4k": "Запросить Коллекцию в 4К", "components.QuotaSelector.movies": "{count, plural, one {фильм} other {фильмы}}", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {фильм} other {фильмы}}", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Этому пользователю разрешено запрашивать <strong>{limit}</strong> {type} каждые <strong>{days}</strong> дней.", "components.RequestModal.QuotaDisplay.allowedRequests": "Вам разрешено запрашивать <strong>{limit}</strong> {type} каждые <strong>{days}</strong> дней.", "components.Settings.SonarrModal.testFirstRootFolders": "Протестировать соединение для загрузки корневых каталогов", "components.Settings.SonarrModal.loadingrootfolders": "Загрузка корневых каталогов…", "components.Settings.SonarrModal.animerootfolder": "Корневой каталог для аниме", "components.Settings.RadarrModal.testFirstRootFolders": "Протестировать подключение для загрузки корневых каталогов", "components.Settings.RadarrModal.loadingrootfolders": "Загрузка корневых каталогов…", "components.RequestModal.AdvancedRequester.destinationserver": "Сервер-получатель", "components.RequestList.RequestItem.mediaerror": "{mediaType} не найдено", "components.RequestList.RequestItem.failedretry": "Что-то пошло не так при попытке повторить запрос.", "components.RequestCard.mediaerror": "{mediaType} не найдено", "components.RequestCard.failedretry": "Что-то пошло не так при попытке повторить запрос.", "components.RequestButton.viewrequest4k": "Посмотреть 4К запрос", "components.RequestButton.requestmore4k": "Запросить больше в 4К", "components.RequestButton.requestmore": "Запросить больше", "components.RequestButton.declinerequests": "Отклонить {requestCount, plural, one {запрос} other {{requestCount} запроса(ов)}}", "components.RequestButton.declinerequest4k": "Отклонить 4К запрос", "components.RequestButton.declinerequest": "Отклонить запрос", "components.RequestButton.approve4krequests": "Одобрить {requestCount, plural, one {4К запрос} other {{requestCount} 4К запроса(ов)}}", "components.RequestButton.decline4krequests": "Отклонить {requestCount, plural, one {4К запрос} other {{requestCount} 4К запроса(ов)}}", "components.RequestButton.approverequests": "Одобрить {requestCount, plural, one {запрос} other {{requestCount} запроса(ов)}}", "components.RequestButton.approverequest4k": "Одобрить 4К запрос", "components.RequestButton.approverequest": "Одобрить запрос", "components.RequestBlock.server": "Сервер-получатель", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{сезонов} за {quotaDays} {дней}</quotaUnits>", "components.QuotaSelector.seasons": "{count, plural, one {сезон} other {сезоны}}", "components.RequestBlock.requestoverrides": "Переопределение запроса", "components.QuotaSelector.unlimited": "Неограниченно", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{фильмов} за {quotaDays} {дней}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {день} other {дней}}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(а) {birthdate}", "components.PermissionEdit.viewrequestsDescription": "Предоставить разрешение на просмотр медиа-запрос<PERSON>, отправленных другими пользователями.", "components.PermissionEdit.usersDescription": "Предоставить разрешение на управление пользователями. Пользователи с этим разрешением не могут предоставлять права администратора и редактировать пользователей, являющихся администраторами.", "components.PermissionEdit.requestTvDescription": "Предоставить разрешение на отправку запросов всех сериалов, отличных от 4К.", "components.PermissionEdit.requestTv": "Запросы сериалов", "components.PermissionEdit.requestMoviesDescription": "Предоставить разрешение на отправку запросов всех фильмов, отличных от 4К.", "components.PermissionEdit.requestMovies": "Запросы фильмов", "components.PermissionEdit.requestDescription": "Предоставить разрешение на отправку запросов всех медиафайлов, отличных от 4К.", "components.PermissionEdit.request4kTvDescription": "Предоставить разрешение на отправку запросов сериалов в 4К.", "components.PermissionEdit.request4kTv": "Запросы сериалов в 4К", "components.PermissionEdit.request4kMoviesDescription": "Предоставить разрешение на отправку запросов фильмов в 4К.", "components.PermissionEdit.request4kMovies": "Запросы фильмов в 4К", "components.PermissionEdit.request4kDescription": "Предоставить разрешение на отправку запросов медиафайлов в 4К.", "components.PermissionEdit.managerequestsDescription": "Предоставить разрешение на управление медиа-запросами. Все запросы пользователя, имеющего данное разрешение, будут одобряться автоматически.", "components.PermissionEdit.autoapproveSeriesDescription": "Предоставить разрешение на автоматическое одобрение всех сериалов, отличных от 4К.", "components.PermissionEdit.autoapproveSeries": "Автоматическое одобрение сериалов", "components.PermissionEdit.autoapprove4kMoviesDescription": "Предоставить разрешение на автоматическое одобрение 4К фильмов.", "components.PermissionEdit.autoapprove4kMovies": "Автоматическое одобрение 4К фильмов", "components.PermissionEdit.autoapprove4kSeries": "Автоматическое одобрение 4К сериалов", "components.PermissionEdit.autoapprove4kSeriesDescription": "Предоставить разрешение на автоматическое одобрение 4К сериалов.", "components.PermissionEdit.autoapproveMoviesDescription": "Предоставить разрешение на автоматическое одобрение всех фильмов, отличных от 4К.", "components.PermissionEdit.autoapproveMovies": "Автоматическое одобрение фильмов", "components.PermissionEdit.autoapprove4kDescription": "Предоставить разрешение на автоматическое одобрение всех 4К медиа-запросов.", "components.PermissionEdit.autoapproveDescription": "Предоставить разрешение на автоматическое одобрение всех медиа-запросов, отличных от 4К.", "components.PermissionEdit.autoapprove4k": "Автоматическое одобрение 4К", "components.PermissionEdit.autoapprove": "Автоматическое одобрение", "components.PermissionEdit.advancedrequestDescription": "Предоставить разрешение на изменение дополнительных параметров запроса.", "components.PermissionEdit.advancedrequest": "Расширенные запросы", "components.PermissionEdit.adminDescription": "Администратор имеет полный доступ. Игнорирует все другие настройки разрешений.", "components.NotificationTypeSelector.usermediarequestedDescription": "Получать уведомления, когда другие пользователи отправляют новые медиа-запросы, требующие одобрения.", "components.NotificationTypeSelector.usermediafailedDescription": "Получать уведомления, когда медиа-запросы не удаётся добавить в Radarr или Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Получать уведомления, когда ваши медиа-запросы отклоняются.", "components.NotificationTypeSelector.usermediaavailableDescription": "Получать уведомления, когда запрошенные вами медиафайлы становятся доступны.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Получать уведомления, когда ваши медиа-запросы получают одобрение.", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {коммит} other {коммитов}} позади", "components.MovieDetails.studio": "{studioCount, plural, one {Студия} other {Студии}}", "components.Layout.VersionStatus.streamstable": "Стабильная версия Jellyseerr", "components.Layout.VersionStatus.streamdevelop": "Версия Jellyseerr для разработки", "components.Layout.VersionStatus.outofdate": "Устарела", "components.AppDataWarning.dockerVolumeMissingDescription": "Подключение тома <code>{appDataPath}</code> настроено неправильно. Все данные будут удалены при остановке или перезапуске контейнера.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {запросов {type} не осталось} other {осталось <strong>#</strong> запроса(ов) {type}}}", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Вы можете посмотреть сводку ограничений на количество запросов этого пользователя на <ProfileLink>странице его профиля</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "Вы можете посмотреть сводку ваших ограничений на количество запросов на <ProfileLink>странице вашего профиля</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Осталось недостаточно запросов на сезоны", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Отправка тестового уведомления веб-перехватчику…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Не удалось отправить тестовое уведомление веб-перехватчику.", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Помощь по переменным шаблона", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Полезная нагрузка JSON успешно сброшена к настройкам по умолчанию!", "components.Settings.Notifications.NotificationsWebhook.customJson": "Полезная нагрузка JSON", "components.Settings.Notifications.NotificationsWebhook.authheader": "Заголовок авторизации", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Настройки веб-push-уведомлений успешно сохранены!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Не удалось сохранить настройки веб-push-уведомлений.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Тестовое веб-push-уведомление отправлено!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Отправка тестового веб-push-уведомления…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Не удалось отправить тестовое веб-push-уведомление.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Чтобы получать push-уведомления, Jellyseerr должен работать через протокол HTTPS.", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Создайте интеграцию <WebhookLink>входящего веб-перехватчика</WebhookLink>", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL веб-перехватчика", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Тестовое уведомление отправлено в Slack!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Отправка тестового уведомления в Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Не удалось отправить тестовое уведомление в Slack.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Настройки уведомлений Slack успешно сохранены!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Не удалось сохранить настройки уведомлений Slack.", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Вы должны предоставить действительный ключ пользователя или группы", "components.Settings.Notifications.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Вы должны выбрать хотя бы один тип уведомлений", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Вы должны предоставить действительный токен приложения", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Ваш тридцатизначный <UsersGroupsLink>идентификатор пользователя или группы</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Тестовое уведомление отправлено в Pushover!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Отправка тестового уведомления в Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Не удалось отправить тестовое уведомление в Pushover.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Настройки уведомлений Pushover успешно сохранены!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Не удалось сохранить настройки уведомлений Pushover.", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Зарегистрируйте приложение</ApplicationRegistrationLink> для использования с Je<PERSON>seerr", "i18n.view": "Посмотреть", "i18n.notrequested": "Не запрошен", "i18n.noresults": "Нет результатов.", "i18n.delimitedlist": "{a}, {b}", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL веб-перехватчика", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<LunaSeaLink>URL веб-перехватчика для уведомлений</LunaSeaLink> на основе вашего пользователя или устройства", "components.Settings.Notifications.NotificationsPushover.accessToken": "Токен API приложения", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Вы должны предоставить токен доступа", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Тестовое уведомление отправлено в Pushbullet!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Отправка тестового уведомления в Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Не удалось отправить тестовое уведомление в Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Настройки уведомлений Pushbullet успешно сохранены!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Не удалось сохранить настройки уведомлений Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Создайте токен в <PushbulletSettingsLink>настройках учётной записи</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Тестовое уведомление отправлено в LunaSea!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Отправка тестового уведомления в LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Не удалось отправить тестовое уведомление в LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Настройки уведомлений LunaSea успешно сохранены!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Не удалось сохранить настройки уведомлений LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Требуется только в том случае, если не используется профиль <code>default</code>", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Активировать службу", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Активировать службу", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Активировать службу", "components.Settings.notificationAgentSettingsDescription": "Настройте и активируйте службы уведомлений.", "components.ResetPassword.emailresetlink": "Отправить ссылку для восстановления по электронной почте", "pages.somethingwentwrong": "Что-то пошло не так", "pages.serviceunavailable": "Сервис недоступен", "pages.pagenotfound": "Страница не найдена", "pages.internalservererror": "Внутренняя ошибка сервера", "components.ResetPassword.validationpasswordrequired": "Вы должны предоставить пароль", "components.ResetPassword.validationpasswordminchars": "Пароль слишком короткий: он должен содержать не менее 8 символов", "components.ResetPassword.validationpasswordmatch": "Пароли должны совпадать", "components.ResetPassword.validationemailrequired": "Вы должны указать действительный адрес электронной почты", "components.ResetPassword.requestresetlinksuccessmessage": "Ссылка для сброса пароля будет отправлена на указанный адрес электронной почты, если он связан с действительным пользователем.", "components.ResetPassword.gobacklogin": "Вернуться на страницу входа", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Языки для поиска фильмов и сериалов", "components.TvDetails.seasons": "{seasonCount, plural, one {# сезон} other {# сезонов}}", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Этому пользователю необходимо иметь по крайней мере <strong>{seasons}</strong> {seasons, plural, one {запрос на сезоны} other {запроса(ов) на сезоны}} для того, чтобы отправить запрос на этот сериал.", "components.RequestModal.QuotaDisplay.requiredquota": "Вам необходимо иметь по крайней мере <strong>{seasons}</strong> {seasons, plural, one {запрос на сезоны} other {запроса(ов) на сезоны}} для того, чтобы отправить запрос на этот сериал.", "components.RequestModal.pending4krequest": "Ожидающий 4K запрос", "components.RequestModal.autoapproval": "Автоматическое одобрение", "i18n.usersettings": "Настройки пользователя", "i18n.showingresults": "Показываются результаты с <strong>{from}</strong> по <strong>{to}</strong> из <strong>{total}</strong>", "i18n.save": "Сохранить изменения", "i18n.retrying": "Повтор…", "i18n.resultsperpage": "Отобразить {pageSize} результатов на странице", "i18n.requesting": "Запрос…", "i18n.request4k": "Запросить в 4К", "i18n.areyousure": "Вы уверены?", "components.RequestModal.alreadyrequested": "Уже запрошен", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Мы не смогли автоматически выполнить ваш запрос. Пожалуйста, выберите правильное совпадение из списка ниже.", "components.TvDetails.originaltitle": "Название оригинала", "i18n.tvshow": "Сериал", "components.Settings.mediaTypeSeries": "сериал", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Регион для поиска фильмов и сериалов", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {сезон} other {сезоны}}", "components.RequestModal.QuotaDisplay.season": "сезон", "components.RequestModal.pendingapproval": "Ваш запрос ожидает одобрения.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Настройки уведомлений по электронной почте успешно сохранены!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Не удалось сохранить настройки уведомлений по электронной почте.", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Язык интерфейса", "components.UserList.validationpasswordminchars": "Пароль слишком короткий: он должен содержать не менее 8 символов", "components.UserList.nouserstoimport": "Нет новых пользователей для импорта из Plex.", "components.UserList.autogeneratepasswordTip": "Отправить пользователю пароль, сгенерированный сервером, по электронной почте", "components.TvDetails.viewfullcrew": "Посмотреть полную cъёмочную группу", "components.TvDetails.showtype": "Тип сериала", "components.TvDetails.TvCrew.fullseriescrew": "Полная съёмочная группа сериала", "components.TvDetails.TvCast.fullseriescast": "Полный актёрский состав сериала", "components.Settings.noDefaultNon4kServer": "Если вы используете один сервер {serverType} для контента, в том числе и для 4К, или если вы загружаете только контент 4K, ваш сервер {serverType} <strong>НЕ</strong> должен быть помечен как 4К сервер.", "components.UserList.localLoginDisabled": "Параметр <strong>Включить локальный вход</strong> в настоящее время отключен.", "components.Settings.SettingsLogs.showall": "Показать все логи", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Ограничение количества запросов на сериалы", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Фильтровать контент по региональной доступности", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Ограничение количества запросов на фильмы", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Переопределить глобальные ограничения", "components.Settings.noDefaultServer": "По крайней мере один сервер {serverType} должен быть помечен как сервер по умолчанию для обработки запросов на {mediaType}ы.", "components.Settings.noDefault4kServer": "4K сервер {serverType} должен быть помечен как сервер по умолчанию, чтобы пользователи могли отправлять запросы на 4K {mediaType}ы.", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Вы должны выбрать языковой профиль", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Базовый URL-адрес должен иметь косую черту в начале", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Базовый URL-адрес должен иметь косую черту в начале", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Протестировать подключение для загрузки языковых профилей", "components.Settings.SonarrModal.loadingTags": "Загрузка тегов…", "components.Settings.SonarrModal.enableSearch": "Включить автоматический поиск", "components.Settings.SonarrModal.edit4ksonarr": "Редактировать 4К сервер Sonarr", "components.Settings.SonarrModal.animequalityprofile": "Профиль качества для аниме", "components.Settings.SonarrModal.animelanguageprofile": "Языковой профиль для аниме", "components.Settings.SonarrModal.animeTags": "Теги для аниме", "components.Settings.SettingsUsers.userSettings": "Настройки пользователей", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Общее ограничение на количество запросов сериалов", "components.Settings.SettingsUsers.toastSettingsSuccess": "Настройки пользователей успешно сохранены!", "components.Settings.SettingsUsers.toastSettingsFailure": "Что-то пошло не так при сохранении настроек.", "components.Settings.SettingsUsers.newPlexLoginTip": "Разрешить пользователям {mediaServerName} входить в систему без предварительного импорта", "components.Settings.SettingsUsers.newPlexLogin": "Включить вход через {mediaServerName} для новых пользователей", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Общее ограничение на количество запросов фильмов", "components.Settings.SettingsUsers.localLoginTip": "Разрешить пользователям входить в систему, используя свой адрес электронной почты и пароль", "components.Settings.SettingsUsers.localLogin": "Включить локальный вход", "components.Settings.SettingsUsers.defaultPermissionsTip": "Начальные разрешения, присваемые новым пользователям", "components.Settings.SettingsUsers.defaultPermissions": "Разрешения по умолчанию", "components.Settings.SettingsLogs.time": "Время", "components.Settings.SettingsLogs.level": "Важность", "components.Settings.SettingsLogs.filterDebug": "Отладочные", "components.Settings.SettingsLogs.extraData": "Дополнительная информация", "components.Settings.SettingsLogs.copyToClipboard": "Скопировать в буфер обмена", "components.Settings.serverpresetRefreshing": "Получение списка серверов…", "components.Settings.SettingsJobsCache.jobstarted": "Задание \"{jobname}\" запущено.", "components.Settings.SettingsJobsCache.unknownJob": "Неизвестное задание", "components.Settings.SettingsJobsCache.sonarr-scan": "Сканирование Sonarr", "components.Settings.SettingsJobsCache.runnow": "Выполнить сейчас", "components.Settings.SettingsJobsCache.radarr-scan": "Сканирование Radarr", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Сканирование недавно добавленных медиафайлов в Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Полное сканирование библиотек Plex", "components.Settings.SettingsJobsCache.nextexecution": "Следующее выполнение", "components.Settings.SettingsJobsCache.jobsandcache": "Задания и кэш", "components.Settings.SettingsJobsCache.jobs": "Задания", "components.Settings.SettingsJobsCache.jobname": "Название задания", "components.Settings.SettingsJobsCache.jobcancelled": "Задание \"{jobname}\" отменено.", "components.Settings.SettingsJobsCache.canceljob": "Отменить задание", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr выполняет определенные задачи по обслуживанию в виде регулярно запланированных заданий, но они также могут быть запущены вручную ниже. Выполнение задания вручную не изменит его расписание.", "components.Settings.SettingsJobsCache.flushcache": "Очистить кэш", "components.Settings.SettingsJobsCache.download-sync-reset": "Сбросить синхронизацию загрузок", "components.Settings.SettingsJobsCache.download-sync": "Синхронизировать загрузки", "components.Settings.SettingsJobsCache.cachehits": "Удачных обращений", "components.Settings.SettingsJobsCache.cachemisses": "Неудачных обращений", "components.Settings.SettingsJobsCache.cachevsize": "Размер значений", "components.Settings.SettingsAbout.uptodate": "Актуальная", "components.Settings.SettingsAbout.Releases.versionChangelog": "Изменения в версии {version}", "components.Settings.SettingsAbout.preferredmethod": "Предпочтительный способ", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} кэш сброшен.", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr кэширует запросы к внешним API, чтобы оптимизировать производительность и избежать ненужных запросов к API.", "components.Settings.SettingsAbout.totalmedia": "Всего мультимедиа", "components.Settings.SettingsAbout.outofdate": "Устарела", "components.Settings.SettingsAbout.helppaycoffee": "Помочь оплатить кофе", "components.Settings.SettingsAbout.gettingsupport": "Получить поддержку", "components.Settings.SettingsAbout.betawarning": "Это бета-версия программного обеспечения. Некоторые функции могут не работать или работать нестабильно. Пожалуйста, сообщайте о любых проблемах на GitHub!", "components.Settings.SettingsAbout.about": "О проекте", "components.Settings.SettingsAbout.Releases.viewongithub": "Посмотреть на GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Посмотреть список изменений", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Данные о релизе в настоящее время недоступны.", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Вы должны ввести новый пароль", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Вы должны предоставить действительный ID чата", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Вы должны предоставить действительный открытый ключ PGP", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Вы должны предоставить действительный ID пользователя", "components.UserList.validationEmail": "Email обязателен", "components.UserList.usercreatedfailedexisting": "Указанный адрес электронной почты уже используется другим пользователем.", "components.TvDetails.streamingproviders": "Сейчас транслируется", "components.Settings.validationPortRequired": "Вы должны указать действительный номер порта", "components.Settings.validationHostnameRequired": "Вы должны указать действительное имя хоста или IP-адрес", "components.Settings.SonarrModal.validationNameRequired": "Вы должны указать имя сервера", "components.Settings.RadarrModal.validationNameRequired": "Вы должны указать имя сервера", "components.Settings.Notifications.validationEmail": "Вы должны указать действительный адрес электронной почты", "components.MovieDetails.streamingproviders": "Сейчас транслируется", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Базовый URL-адрес не должен заканчиваться косой чертой", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Вы должны выбрать минимальную доступность", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Базовый URL-адрес не должен заканчиваться косой чертой", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL-адрес не должен заканчиваться косой чертой", "components.Settings.RadarrModal.testFirstTags": "Протестировать подключение для загрузки тегов", "components.Settings.RadarrModal.testFirstQualityProfiles": "Протестировать подключение для загрузки профилей качества", "components.Settings.RadarrModal.selecttags": "Выберите теги", "components.Settings.RadarrModal.notagoptions": "Тегов нет.", "components.Settings.RadarrModal.loadingprofiles": "Загрузка профилей качества…", "components.Settings.RadarrModal.loadingTags": "Загрузка тегов…", "components.Settings.RadarrModal.enableSearch": "Включить автоматический поиск", "components.Settings.RadarrModal.default4kserver": "4К сервер по умолчанию", "components.Settings.Notifications.webhookUrlTip": "Создайте <DiscordWebhookLink>интеграцию веб-перехватчика</DiscordWebhookLink> на своём сервере", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Настройки уведомлений веб-перехватчика успешно сохранены!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Не удалось сохранить настройки уведомлений веб-перехватчика.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Тестовое уведомление веб-перехватчику отправлено!", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL веб-перехватчика", "components.Settings.Notifications.validationPgpPrivateKey": "Вы должны предоставить действительный закрытый ключ PGP", "components.Settings.Notifications.validationPgpPassword": "Вы должны предоставить пароль PGP", "components.Settings.Notifications.validationChatIdRequired": "Вы должны предоставить действительный ID чата", "components.Settings.Notifications.validationBotAPIRequired": "Вы должны предоставить токен авторизации бота", "components.Settings.Notifications.telegramsettingsfailed": "Не удалось сохранить настройки уведомлений Telegram.", "components.Settings.Notifications.pgpPrivateKeyTip": "Подписывать зашифрованные сообщения электронной почты с помощью <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Закрытый ключ PGP", "components.Settings.Notifications.pgpPasswordTip": "Подписывать зашифрованные сообщения электронной почты с помощью <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Пароль PGP", "components.Settings.Notifications.encryptionTip": "В большинстве случаев неявный TLS использует порт 465, а STARTTLS – порт 587", "components.Settings.Notifications.encryptionOpportunisticTls": "Всегда использовать STARTTLS", "components.Settings.Notifications.encryptionNone": "Без шифрования", "components.Settings.Notifications.encryptionImplicitTls": "Использовать неявный TLS", "components.Settings.Notifications.encryptionDefault": "Использовать STARTTLS, если доступно", "components.Settings.Notifications.encryption": "Метод шифрования", "components.Settings.Notifications.emailsettingssaved": "Настройки уведомлений по электронной почте успешно сохранены!", "components.Settings.Notifications.emailsettingsfailed": "Не удалось сохранить настройки уведомлений по электронной почте.", "components.Settings.Notifications.discordsettingssaved": "Настройки уведомлений Discord успешно сохранены!", "components.Settings.Notifications.discordsettingsfailed": "Не удалось сохранить настройки уведомлений Discord.", "components.Settings.Notifications.chatIdTip": "Начните чат со своим ботом, добавьте <GetIdBotLink>@get_id_bot</GetIdBotLink> и выполните команду <code>/my_id</code>", "components.Settings.Notifications.chatId": "ID чата", "components.Settings.Notifications.botUsernameTip": "Разрешить пользователям начинать чат с вашим ботом и настраивать свои собственные уведомления", "components.Settings.Notifications.botUsername": "Имя бота", "components.Settings.Notifications.botAvatarUrl": "URL аватара бота", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Создайте бота</CreateBotLink> для использования с Je<PERSON>seerr", "components.Settings.Notifications.allowselfsigned": "Разрешить самозаверенные сертификаты", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Вы должны предоставить допустимую полезную нагрузку JSON", "components.Settings.Notifications.toastTelegramTestSuccess": "Тестовое уведомление отправлено в Telegram!", "components.Settings.Notifications.toastTelegramTestSending": "Отправка тестового уведомления в Telegram…", "components.Settings.Notifications.toastDiscordTestSuccess": "Тестовое уведомление отправлено в Discord!", "components.Settings.Notifications.toastDiscordTestSending": "Отправка тестового уведомления в Discord…", "components.Settings.Notifications.toastDiscordTestFailed": "Не удалось отправить тестовое уведомление в Discord.", "components.Settings.Notifications.toastTelegramTestFailed": "Не удалось отправить тестовое уведомление в Telegram.", "components.Settings.Notifications.toastEmailTestSuccess": "Тестовое уведомление отправлено по электронной почте!", "components.Settings.Notifications.toastEmailTestSending": "Отправка тестового уведомления по электронной почте…", "components.Settings.Notifications.toastEmailTestFailed": "Не удалось отправить тестовое уведомление по электронной почте.", "components.UserProfile.unlimited": "Неограниченно", "components.UserProfile.totalrequests": "Всего запросов", "components.UserProfile.requestsperdays": "осталось {limit}", "components.UserProfile.pastdays": "{type} (за {days} день(ей))", "components.UserProfile.seriesrequest": "Запросов сериалов", "components.UserProfile.movierequests": "Запросов фильмов", "components.UserProfile.limit": "{remaining} из {limit}", "components.UserProfile.UserSettings.unauthorizedDescription": "У вас нет разрешения на изменение настроек этого пользователя.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "У вас нет разрешения на изменение пароля этого пользователя.", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Пароли должны совпадать", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Что-то пошло не так при сохранении пароля. Правильно ли введен ваш текущий пароль?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Что-то пошло не так при сохранении пароля.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "В настоящее время для этой учётной записи не установлен пароль. Установите пароль ниже, чтобы с этой учётной записью можно было войти в систему как \"локальный пользователь\".", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "В настоящее время для вашей учётной записи не установлен пароль. Установите пароль ниже, чтобы иметь возможность войти в систему как \"локальный пользователь\", используя свой адрес электронной почты.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Настройки веб-push-уведомлений успешно сохранены!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Не удалось сохранить настройки веб-push-уведомлений.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Веб-push", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Настройки уведомлений Telegram успешно сохранены!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Не удалось сохранить настройки уведомлений Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Начните чат</TelegramBotLink>, добавьте <GetIdBotLink>@get_id_bot</GetIdBotLink> и выполните команду <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID чата", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Отправлять без звука", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Шифровать сообщения электронной почты с помощью <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Открытый ключ PGP", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Настройки уведомлений", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Электронная почта", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Настройки уведомлений Discord успешно сохранены!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Не удалось сохранить настройки уведомлений Discord.", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>ID</FindDiscordIdLink> вашей учётной записи", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID пользователя", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Тип учётной записи", "components.UserProfile.ProfileHeader.userid": "ID пользователя: {userid}", "components.UserProfile.ProfileHeader.settings": "Редактировать настройки", "components.UserProfile.ProfileHeader.joindate": "Присоединилс<PERSON> {joindate}", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Пароль слишком короткий: он должен содержать не менее 8 символов", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Вы не можете изменять собственные разрешения.", "components.UserList.userssaved": "Разрешения пользователя успешно сохранены!", "components.UserList.userfail": "Что-то пошло не так при сохранении разрешений пользователя.", "components.UserList.userdeleteerror": "Что-то пошло не так при удалении пользователя.", "components.UserList.usercreatedfailed": "Что-то пошло не так при создании пользователя.", "components.UserList.passwordinfodescription": "Настройте URL-адрес приложения и включите уведомления по электронной почте, чтобы обеспечить возможность автоматической генерации пароля.", "components.UserList.importfromplexerror": "Что-то пошло не так при импорте пользователей из Plex.", "components.UserList.importfrommediaserver": "Импортировать пользователей из {mediaServerName}", "components.UserList.importfromplex": "Импортировать пользователей из Plex", "components.UserList.importedfromplex": "<strong>{userCount}</strong> {userCount, plural, one {# новый пользователь} other {# новых пользователя(ей)}} успешно импортированы из Plex!", "components.UserList.edituser": "Изменить разрешения пользователя", "components.UserList.deleteconfirm": "Вы уверены, что хотите удалить этого пользователя? Все данные о его запросах будут удалены без возможности восстановления.", "components.UserList.autogeneratepassword": "Сгенерировать пароль автоматически", "components.UserList.accounttype": "Тип", "components.TvDetails.nextAirDate": "Следующая дата выхода в эфир", "components.TvDetails.firstAirDate": "Дата выхода в эфир", "components.TvDetails.episodeRuntimeMinutes": "{runtime} минут", "components.TvDetails.episodeRuntime": "Продолжительность эпизода", "components.Settings.webpush": "Веб-push", "components.Settings.webAppUrlTip": "При необходимости направляйте пользователей в веб-приложение на вашем сервере вместо размещённого на plex.tv", "components.Settings.webAppUrl": "URL <WebAppLink>веб-приложения</WebAppLink>", "components.Settings.toastPlexRefreshSuccess": "Список серверов Plex успешно получен!", "components.Settings.toastPlexRefresh": "Получение списка серверов Plex…", "components.Settings.toastPlexRefreshFailure": "Не удалось получить список серверов Plex.", "components.Settings.toastPlexConnecting": "Попытка подключения к Plex…", "components.Settings.settingUpPlexDescription": "Чтобы настроить Plex, вы можете либо ввести данные вручную, либо выбрать сервер, полученный со страницы <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Нажмите кнопку справа от выпадающего списка, чтобы получить список доступных серверов.", "components.Settings.services": "Службы", "components.Settings.serviceSettingsDescription": "Настройте сервер(ы) {serverType} ниже. Вы можете подключить несколько серверов {serverType}, но только два из них могут быть помечены как серверы по умолчанию (один не 4К и один 4К). Администраторы могут переопределить сервер для обработки новых запросов до их одобрения.", "components.Settings.serverpresetLoad": "Нажмите кнопку, чтобы загрузить список доступных серверов", "components.Settings.serverSecure": "защищённый", "components.Settings.serverRemote": "удалённый", "components.Settings.serverLocal": "локальный", "components.Settings.scan": "Синхронизировать библиотеки", "components.Settings.SettingsLogs.logsDescription": "Вы также можете просматривать эти логи напрямую через <code>stdout</code> или в <code>{appDataPath}/logs/jellyseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Логи", "components.Settings.SettingsLogs.logDetails": "Подробные сведения о логе", "components.Settings.SettingsLogs.copiedLogMessage": "Сообщение лога скопировано в буфер обмена.", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Контент фильтруется по языку оригинала", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "По умолчанию ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Общее", "components.Settings.SettingsUsers.userSettingsDescription": "Настройте глобальные параметры и параметры по умолчанию для пользователей.", "components.Settings.email": "Электронная почта", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL-адрес не должен заканчиваться косой чертой", "components.Settings.toastPlexConnectingSuccess": "Соединение с Plex установлено успешно!", "components.Settings.SonarrModal.toastSonarrTestSuccess": "Соединение с Sonarr установлено успешно!", "components.Settings.toastPlexConnectingFailure": "Не удалось подключиться к Plex.", "components.Settings.SonarrModal.toastSonarrTestFailure": "Не удалось подключиться к Sonarr.", "components.Settings.SonarrModal.testFirstTags": "Протестировать подключение для загрузки тегов", "components.Settings.SonarrModal.testFirstQualityProfiles": "Протестировать подключение для загрузки профилей качества", "components.Settings.SonarrModal.selecttags": "Выберите теги", "components.Settings.SonarrModal.selectLanguageProfile": "Выберите языковой профиль", "components.Settings.SonarrModal.notagoptions": "Тегов нет.", "components.Settings.SonarrModal.loadingprofiles": "Загрузка профилей качества…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Загрузка языковых профилей…", "components.Settings.SonarrModal.create4ksonarr": "Добавить новый 4К сервер Sonarr", "components.Settings.RadarrModal.edit4kradarr": "Редактировать 4К сервер Radarr", "components.Settings.RadarrModal.create4kradarr": "Добавить новый 4К сервер Radarr", "components.Settings.SonarrModal.default4kserver": "4К сервер по умолчанию", "components.Settings.SettingsJobsCache.editJobSchedule": "Изменить задание", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Частота", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Задание успешно отредактировано!", "components.Settings.SettingsAbout.runningDevelop": "Вы используете ветку <code>develop</code> проект<PERSON>, которая рекомендуется только для тех, кто вносит вклад в разработку или помогает в тестировании.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Что-то пошло не так при сохранении задания.", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Каждый {jobScheduleHours, plural, one {час} other {{jobScheduleHours} часа(ов)}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Каждую {jobScheduleMinutes, plural, one {минуту} other {{jobScheduleMinutes} минут(ы)}}", "components.StatusBadge.status": "{status}", "components.IssueDetails.IssueComment.areyousuredelete": "Вы уверены, что хотите удалить этот комментарий?", "components.IssueDetails.IssueComment.delete": "Удалить комментарий", "components.IssueDetails.IssueComment.edit": "Редактировать комментарий", "components.IssueDetails.IssueComment.postedby": "Опубликовано {relativeTime} пользователем {username}", "components.IssueDetails.IssueComment.postedbyedited": "Опубликовано {relativeTime} пользователем {username} (изменено)", "components.IssueDetails.IssueComment.validationComment": "Вы должны ввести сообщение", "components.IssueDetails.IssueDescription.deleteissue": "Удалить проблему", "components.IssueDetails.IssueDescription.description": "Описание", "components.IssueDetails.IssueDescription.edit": "Редактировать описание", "components.IssueDetails.allseasons": "Все сезоны", "components.IssueDetails.allepisodes": "Все эпизоды", "components.ManageSlideOver.manageModalClearMedia": "Очистить данные", "components.ManageSlideOver.manageModalClearMediaWarning": "* Это приведёт к необратимому удалению всех данных для этого {mediaType}а, включая любые запросы. Если этот элемент существует в вашей библиотеке {mediaServerName}, мультимедийная информация о нём будет воссоздана во время следующего сканирования.", "components.IssueDetails.problemepisode": "Затронутый эпизод", "components.ManageSlideOver.manageModalRequests": "Запросы", "components.IssueDetails.closeissue": "Закрыть проблему", "components.IssueDetails.closeissueandcomment": "Закрыть с комментарием", "components.IssueDetails.comments": "Комментарии", "components.IssueDetails.deleteissueconfirm": "Вы уверены, что хотите удалить эту проблему?", "components.IssueDetails.episode": "Эпизод {episodeNumber}", "components.IssueDetails.lastupdated": "Последнее обновление", "components.IssueDetails.openinarr": "Открыть в {arr}", "components.IssueDetails.toasteditdescriptionfailed": "Что-то пошло не так при редактировании описания проблемы.", "components.IssueDetails.toastissuedeletefailed": "Что-то пошло не так при удалении проблемы.", "components.IssueDetails.toaststatusupdatefailed": "Что-то пошло не так при обновлении статуса проблемы.", "components.IssueDetails.unknownissuetype": "Неизвестный", "components.IssueModal.CreateIssueModal.episode": "Эпизод {episodeNumber}", "components.ManageSlideOver.mark4kavailable": "Пометить как доступный в 4К", "components.IssueModal.CreateIssueModal.extras": "Дополнительно", "components.IssueModal.CreateIssueModal.problemseason": "Затронутый сезон", "components.ManageSlideOver.downloadstatus": "Загрузки", "components.ManageSlideOver.manageModalIssues": "Открытые проблемы", "components.ManageSlideOver.manageModalNoRequests": "Запросов нет.", "components.ManageSlideOver.manageModalTitle": "Управление {mediaType}ом", "components.ManageSlideOver.markavailable": "Пометить как доступный", "components.ManageSlideOver.movie": "фильм", "components.ManageSlideOver.openarr": "Открыть в {arr}", "components.ManageSlideOver.openarr4k": "Открыть в 4К {arr}", "components.ManageSlideOver.tvshow": "сериал", "components.NotificationTypeSelector.userissueresolvedDescription": "Получать уведомления, когда проблемы, о которых вы сообщили, получают решение.", "components.NotificationTypeSelector.issuecomment": "Комментарий к проблеме", "components.PermissionEdit.createissuesDescription": "Предоставить разрешение на сообщения о проблемах с медиафайлами.", "components.PermissionEdit.manageissuesDescription": "Предоставить разрешение на управление проблемами с медиафайлами.", "components.NotificationTypeSelector.userissuecreatedDescription": "Получать уведомления, когда другие пользователи сообщают о проблемах.", "components.PermissionEdit.createissues": "Сообщения о проблемах", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Настройки уведомлений Pushover успешно сохранены!", "components.PermissionEdit.viewissues": "Просмотр проблем", "components.PermissionEdit.viewissuesDescription": "Предоставить разрешение на просмотр проблем с медиафайлами, о которых сообщили другие пользователи.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Вы должны предоставить действительный ключ пользователя или группы", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Токен доступа", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Токен API приложения", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Создайте токен на странице <PushbulletSettingsLink>настроек вашей учётной записи</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Вы должны предоставить действительный токен приложения", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Ваш 30-значный <UsersGroupsLink>идентификатор пользователя или группы</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Не удалось сохранить настройки уведомлений Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Настройки уведомлений Pushbullet успешно сохранены!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Зарегистрируйте приложение</ApplicationRegistrationLink> для использования с {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Ключ пользователя или группы", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Не удалось сохранить настройки уведомлений Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Вы должны предоставить токен доступа", "i18n.resolved": "Решённые", "components.IssueDetails.openedby": "#{issueId} открыта {relativeTime} пользователем {username}", "components.IssueDetails.openin4karr": "Открыть в 4К {arr}", "components.IssueDetails.play4konplex": "Воспроизвести в {mediaServerName} в 4К", "components.IssueDetails.problemseason": "Затронутый сезон", "components.IssueDetails.reopenissue": "Снова открыть проблему", "components.IssueDetails.season": "Сезон {seasonNumber}", "components.IssueDetails.toasteditdescriptionsuccess": "Описание проблемы успешно отредактировано!", "components.IssueDetails.toastissuedeleted": "Проблема успешно удалена!", "components.IssueDetails.toaststatusupdated": "Статус проблемы успешно обновлен!", "components.IssueList.IssueItem.issuetype": "Тип", "components.IssueModal.CreateIssueModal.allseasons": "Все сезоны", "components.IssueModal.CreateIssueModal.problemepisode": "Затронутый эпизод", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Что-то пошло не так при отправке проблемы.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Отчёт о проблеме для <strong>{title}</strong> успешно отправлен!", "components.IssueModal.CreateIssueModal.toastviewissue": "Просмотреть проблему", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Вы должны предоставить описание", "components.IssueModal.issueAudio": "Аудио", "components.IssueModal.issueOther": "Другое", "components.IssueModal.issueSubtitles": "Субтитры", "components.IssueModal.issueVideo": "Видео", "components.IssueModal.CreateIssueModal.reportissue": "Сообщить о проблеме", "components.NotificationTypeSelector.adminissuecommentDescription": "Получать уведомления, когда другие пользователи остовляют комментарии к проблемам.", "components.NotificationTypeSelector.userissuecommentDescription": "Получать уведомления, когда к проблемам, о которых вы сообщили, появляются новые комментарии.", "components.NotificationTypeSelector.issuecommentDescription": "Отправлять уведомления, когда к проблемам появляются новые комментарии.", "components.NotificationTypeSelector.issuecreated": "Проблема опубликована", "components.NotificationTypeSelector.issueresolved": "Проблема решена", "components.NotificationTypeSelector.issueresolvedDescription": "Отправлять уведомления, когда проблемы получают решение.", "components.NotificationTypeSelector.issuecreatedDescription": "Отправлять уведомления, когда появляются сообщения о проблемах.", "components.PermissionEdit.manageissues": "Управление проблемами", "i18n.open": "Открыть", "components.IssueList.IssueItem.problemepisode": "Затронутый эпизод", "components.IssueList.IssueItem.unknownissuetype": "Неизвестный", "components.IssueList.issues": "Проблемы", "components.IssueList.IssueItem.opened": "Открыта", "components.IssueDetails.nocomments": "Комментариев нет.", "components.IssueDetails.issuepagetitle": "Проблема", "components.IssueDetails.deleteissue": "Удалить проблему", "components.IssueDetails.issuetype": "Тип", "components.IssueDetails.leavecomment": "Комментарий", "components.IssueDetails.playonplex": "Воспроизвести в {mediaServerName}", "components.IssueDetails.reopenissueandcomment": "Снова открыть с комментарием", "components.IssueList.IssueItem.issuestatus": "Статус", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезоны}}", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Эпизод} other {Эпизоды}}", "components.IssueList.IssueItem.viewissue": "Просмотреть проблему", "components.IssueList.showallissues": "Показать все проблемы", "components.IssueList.sortModified": "По дате изменения", "components.IssueModal.CreateIssueModal.allepisodes": "Все эпизоды", "components.IssueList.IssueItem.openeduserdate": "{date} пользователем {user}", "components.IssueList.sortAdded": "По дате добавления", "components.IssueModal.CreateIssueModal.season": "Сезон {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Отправить проблему", "components.IssueModal.CreateIssueModal.providedetail": "Пожалуйста, предоставьте подробное описание проблемы, с которой вы столкнулись.", "components.IssueModal.CreateIssueModal.whatswrong": "Что не так?", "components.Layout.Sidebar.issues": "Проблемы", "components.NotificationTypeSelector.issuereopenedDescription": "Отправлять уведомления, когда проблемы открыты заново.", "components.NotificationTypeSelector.userissuereopenedDescription": "Получать уведомления, когда проблемы о которых вы сообщили будут открыты заново.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Получать уведомления, когда проблемы открыты заново другими пользователями.", "components.NotificationTypeSelector.issuereopened": "Проблема открыта заново", "components.NotificationTypeSelector.adminissueresolvedDescription": "Получать уведомления, когда проблемы решены другими пользователями.", "components.RequestModal.requestseasons4k": "Запрос {seasonCount} {seasonCount, plural, one {сезона} other {сезонов}} в 4К", "components.TvDetails.productioncountries": "{countryCount, plural, one {Страна} other {Страны}} производства", "components.IssueDetails.commentplaceholder": "Добавить комментарий…", "components.MovieDetails.productioncountries": "{countryCount, plural, one {Страна} other {Страны}} производства", "components.RequestModal.selectmovies": "Выберите фильм(ы)", "components.RequestModal.approve": "Одобрить запрос", "components.RequestModal.requestmovies": "Запрос {count} {count, plural, one {фильма} other {фильмов}}", "components.RequestModal.requestmovies4k": "Запрос {count} {count, plural, one {фильма} other {фильмов}} в 4К", "components.Settings.RadarrModal.inCinemas": "В кино", "components.Settings.RadarrModal.released": "Выпущен", "components.RequestModal.requestApproved": "Запрос на <strong>{title}</strong> одобрен!", "components.Settings.RadarrModal.announced": "Анон<PERSON>ирован", "components.ManageSlideOver.manageModalMedia": "Медиа", "components.ManageSlideOver.manageModalMedia4k": "4К медиа", "components.ManageSlideOver.markallseasons4kavailable": "Отметить все сезоны как \"Доступные\" (4K)", "components.ManageSlideOver.markallseasonsavailable": "Отметить все сезоны как \"Доступные\"", "components.ManageSlideOver.opentautulli": "Открыть в Tautulli", "components.MovieDetails.managemovie": "Управление фильмом", "components.NotificationTypeSelector.mediaautorequested": "Запрос отправлен автоматически", "components.PermissionEdit.autorequestMovies": "Авто запрос фильмов", "components.PermissionEdit.autorequestMoviesDescription": "Предоставить разрешение на автоматическое одобрение медиа-запросов фильмов, отличных от 4К, через список просмотра Plex.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Ваш список просмотра", "components.Discover.moviegenres": "Жанры фильма", "components.Discover.studios": "Студии", "components.Discover.networks": "Телеканалы", "components.Discover.tmdbmoviekeyword": "Ключевое слово TMDB", "components.MovieDetails.reportissue": "Сообщить о проблеме", "components.PermissionEdit.autorequestSeries": "Авто запрос сериалов", "components.PermissionEdit.autorequest": "Авто запрос", "components.PermissionEdit.viewrecentDescription": "Предоставить разрешение на просмотр списка недавно добавленных медиафайлов.", "components.PermissionEdit.viewrecent": "Просмотр недавно добавленного", "components.PermissionEdit.viewwatchlists": "Просмотр списков просмотра Plex", "components.Discover.plexwatchlist": "Ваш список просмотра", "components.Discover.CreateSlider.editSlider": "Редактировать слайдер", "components.Discover.CreateSlider.addSlider": "Добавить слайдер", "components.Discover.CreateSlider.addcustomslider": "Создать слайдер", "components.Discover.CreateSlider.nooptions": "Нет результатов.", "components.Discover.CreateSlider.providetmdbgenreid": "Введите TMDB ID жанра", "components.Discover.CreateSlider.needresults": "Должен быть хотя-бы 1 результат.", "components.Discover.CreateSlider.providetmdbkeywordid": "Введите TMDB ID ключевого слова", "components.Discover.CreateSlider.providetmdbnetwork": "Введите TMDB ID сети", "components.Discover.CreateSlider.providetmdbsearch": "Введите поисковой запрос", "components.Discover.CreateSlider.providetmdbstudio": "Введите TMDB ID студии", "components.Discover.CreateSlider.searchGenres": "Поиск жанров…", "components.Discover.CreateSlider.searchKeywords": "Ключевые слова для поиска…", "components.Discover.CreateSlider.searchStudios": "Поиск студий…", "components.Discover.CreateSlider.slidernameplaceholder": "Название слайдера", "components.Discover.CreateSlider.starttyping": "Начните ввод для поиска.", "components.Discover.CreateSlider.validationDatarequired": "Вы должны ввести дату.", "components.Discover.CreateSlider.validationTitlerequired": "Вы должны ввести заголовок.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Фильмы по ключевому слову \"{keywordTitle}\"", "components.Discover.CreateSlider.editfail": "Ошибка редактирования слайдера.", "components.Discover.CreateSlider.addfail": "Ошибка создания слайдера.", "components.Discover.CreateSlider.editsuccess": "Слайдер отредактирован и настройки кастомизации сохранены.", "components.Discover.CreateSlider.addsuccess": "Новый слайдер создан и настройки кастомизации сохранены.", "components.Discover.DiscoverWatchlist.watchlist": "Список просмотра Plex", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Здесь будут отображаться медиаданные, добавленные в ваш <PlexWatchlistSupportLink>список просмотра Plex</PlexWatchlistSupportLink>.", "components.Layout.Sidebar.browsetv": "Сериалы", "components.Discover.tvgenres": "Жан<PERSON>ы сериалов", "components.Layout.UserDropdown.requests": "Запросы", "components.ManageSlideOver.manageModalAdvanced": "Дополнительно", "components.ManageSlideOver.pastdays": "Прошло {days, number} дней", "components.PermissionEdit.autorequestDescription": "Предоставить разрешение на автоматическое одобрение всех медиа-запросов, отличных от 4К, через список просмотра Plex.", "components.NotificationTypeSelector.mediaautorequestedDescription": "Получайте уведомления, когда новые запросы автоматически отправляются для элементов в вашем списке просмотра Plex.", "components.PermissionEdit.autorequestSeriesDescription": "Предоставить разрешение на автоматическое одобрение запросов сериалов, отличных от 4К, через список просмотра Plex.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Активный фильтр} other {# Активные фильтры}}", "components.Discover.DiscoverMovies.discovermovies": "Фильмы", "components.Discover.DiscoverMovies.sortPopularityAsc": "Популярность по возрастанию", "components.Discover.DiscoverMovies.sortPopularityDesc": "Популярность по убыванию", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Дата выпуска по возрастанию", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Дата выпуска по убыванию", "components.Discover.DiscoverMovies.sortTitleAsc": "Название (A-Z) по возрастанию", "components.Discover.DiscoverMovies.sortTitleDesc": "Название (Z-A) По убыванию", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Рейтинг TMDB по возрастанию", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Рейтинг TMDB по убыванию", "components.Discover.DiscoverSliderEdit.deletefail": "Ошибка удаления слайдера.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Слайдер успешно удален.", "components.Discover.DiscoverSliderEdit.enable": "Изменить видимость", "components.Discover.DiscoverSliderEdit.remove": "Удалить", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Активный фильтр} other {# Активные фильтры}}", "components.Discover.DiscoverTv.discovertv": "Сериалы", "components.Discover.DiscoverTv.sortPopularityAsc": "Популярность по возрастанию", "components.Discover.DiscoverTv.sortPopularityDesc": "Популярность по убыванию", "components.Discover.DiscoverTv.sortTitleAsc": "Название (A-Z) по возрастанию", "components.Discover.DiscoverTv.sortTitleDesc": "Название (Z-A) По убыванию", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Рейтинг TMDB по возрастанию", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Рейтинг TMDB по убыванию", "components.Discover.DiscoverTvKeyword.keywordSeries": "Сериалы по ключевому слову \"{keywordTitle}\"", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Ваш список просмотра", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Активный фильтр} other {# Активные фильтры}}", "components.Discover.FilterSlideover.clearfilters": "Очистить все активные фильтры", "components.Discover.FilterSlideover.filters": "Фильтры", "components.Discover.FilterSlideover.genres": "Ж<PERSON>н<PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "Ключевые слова", "components.Discover.FilterSlideover.from": "От", "components.Discover.FilterSlideover.originalLanguage": "Язык оригинала", "components.Discover.FilterSlideover.ratingText": "Рейтинг с {minValue} до {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Дата релиза", "components.Discover.FilterSlideover.runtime": "Длительность", "components.Discover.FilterSlideover.runtimeText": "длительность {minValue}-{maxValue} минут", "components.Discover.FilterSlideover.studio": "Студия", "components.Discover.FilterSlideover.tmdbuserscore": "Рейтинг пользователей TMDB", "components.Discover.resettodefault": "По умолчанию", "components.Discover.resetwarning": "Сброс всех настроек слайдеров. Это действие так же удалит все пользовательские слайдеры!", "components.Discover.tmdbnetwork": "Телеканал TMDB", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Запросы фильмов", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber} Эпизод {episodeNumber}", "components.Discover.emptywatchlist": "Здесь будут отображаться медиаданные, добавленные в ваш <PlexWatchlistSupportLink>список просмотра Plex</PlexWatchlistSupportLink>.", "components.Discover.FilterSlideover.streamingservices": "Стриминговые сервисы", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Недавно добавлено", "components.Discover.createnewslider": "Создать новый слайдер", "components.Discover.FilterSlideover.to": "До", "components.Discover.stopediting": "Закончить редактирование", "components.Discover.tmdbmoviegenre": "Жанр фильма TMDB", "components.Discover.tmdbsearch": "Поиск TMDB", "components.Discover.tmdbstudio": "Студия TMDB", "components.Discover.tmdbtvgenre": "Жанр сериала TMDB", "components.Discover.tmdbtvkeyword": "Ключевое слово сериала TMDB", "components.Layout.Sidebar.browsemovies": "Фильмы", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Запросы сериалов", "components.MovieDetails.rtaudiencescore": "Рейтинг Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Рейтинг Rotten Tomatoes", "components.MovieDetails.tmdbuserscore": "Рейтинг пользователей TMDB", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Текущая частота", "i18n.import": "Импортировать", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Тег канала", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Каждую {jobScheduleSeconds, plural, one {секунду} other {{jobScheduleSeconds} секунд}}", "components.StatusChecker.restartRequiredDescription": "Пожалуйста, перезапустите сервер, чтобы применить обновленные настройки.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Эпизод} other {# Эпизодов}}", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID пользователя Discord", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Вы должны предоставить действительный идентификатор пользователя Discord", "components.PermissionEdit.viewwatchlistsDescription": "Предоставьте разрешение на просмотр списков просмотра Plex других пользователей.", "components.UserList.newplexsigninenabled": "Параметр <strong>Включить новый вход в Plex</strong> в настоящее время включен. Пользователей Plex с доступом к библиотеке не нужно импортировать для входа.", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Автоматический запрос фильмов", "components.MovieDetails.digitalrelease": "Цифровой релиз", "components.MovieDetails.physicalrelease": "Физический релиз", "components.Settings.SettingsMain.toastSettingsFailure": "Что-то пошло не так при сохранении настроек.", "components.Settings.experimentalTooltip": "Включение этого параметра может привести к неожиданному поведению приложения", "components.Settings.advancedTooltip": "Неправильная настройка этого параметра может привести к нарушению функциональности", "components.Settings.externalUrl": "Внешний URL-адрес", "components.Settings.SettingsMain.applicationurl": "URL приложения", "components.Settings.urlBase": "Базовый URL-адрес", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.StatusChecker.appUpdated": "{applicationTitle} обновлено", "components.TitleCard.tvdbid": "TheTVDB ID", "components.Settings.validationApiKey": "Вы должны предоставить ключ API", "components.TitleCard.mediaerror": "{mediaType} не найдено", "components.TitleCard.tmdbid": "TMDB ID", "components.Settings.restartrequiredTooltip": "Чтобы изменения этого параметра вступили в силу, необходимо перезапустить Je<PERSON>rr", "components.ManageSlideOver.alltime": "Все время", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {просмотр} other {просмотров}}", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Включить агент", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Настройки уведомлений Gotify успешно сохранены!", "components.Settings.SettingsLogs.viewdetails": "Просмотреть детали", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Автоматически запрашивать серии в вашем <PlexWatchlistSupportLink>списке просмотра Plex</PlexWatchlistSupportLink>", "i18n.restartRequired": "Требуется перезагрузка", "components.ManageSlideOver.playedby": "Просмотрен", "components.RequestBlock.approve": "Одобрить запрос", "components.RequestBlock.lastmodifiedby": "Последнее изменение", "components.RequestCard.declinerequest": "Отклонить запрос", "components.RequestCard.cancelrequest": "Отменить запрос", "components.Settings.SettingsMain.apikey": "Ключ API", "components.Settings.SettingsMain.general": "Общие", "components.Settings.SettingsMain.originallanguageTip": "Фильтровать контент по исходному языку", "components.Settings.SettingsMain.validationApplicationUrl": "Вы должны указать действительный URL", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL не должен заканчиваться косой чертой", "components.StatusChecker.reloadApp": "Перезагрузить {applicationTitle}", "components.TvDetails.manageseries": "Управление сериалом", "components.TvDetails.reportissue": "Сообщить о проблеме", "components.TvDetails.tmdbuserscore": "Рейтинг пользователей TMDB", "components.UserProfile.emptywatchlist": "Здесь будут отображаться медиаданные, добавленные в ваш <PlexWatchlistSupportLink>список просмотра Plex</PlexWatchlistSupportLink>.", "components.UserProfile.recentlywatched": "Недавно просмотренные", "i18n.importing": "Импорт…", "components.StatusBadge.managemedia": "Управлять {mediaType}", "components.StatusBadge.playonplex": "Играть в Plex", "components.StatusChecker.restartRequired": "Требуется перезагрузка сервера", "components.StatusChecker.appUpdatedDescription": "Пожалуйста, нажмите кнопку ниже, чтобы перезагрузить приложение.", "components.Settings.Notifications.enableMentions": "Включить упоминания", "components.AirDateBadge.airedrelative": "В эфире {relativeTime}", "components.AirDateBadge.airsrelative": "Трансляция {relativeTime}", "components.TvDetails.seasonnumber": "Сезон {seasonNumber}", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Ошибка отправки тестового уведомления Gotify.", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Очистка кэша изображения", "components.Settings.SettingsJobsCache.imagecachecount": "Изображений кэшированно", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Синхронизация списка просмотра Plex", "components.Settings.SettingsMain.applicationTitle": "Название приложения", "components.Settings.SettingsMain.cacheImagesTip": "Кэшировать изображения из внешних источников (требуется значительный объем дискового пространства)", "components.Settings.SettingsMain.partialRequestsEnabled": "Разрешить запрашивать сериалы частично", "components.Settings.SettingsMain.toastApiKeySuccess": "Новый ключ API успешно сгенерирован!", "components.Settings.SettingsMain.toastSettingsSuccess": "Настройки успешно сохранены!", "components.Settings.SettingsMain.validationApplicationTitle": "Вы должны указать название приложения", "components.Settings.deleteServer": "Удалить сервер {serverType}", "components.Settings.tautulliApiKey": "Ключ API", "components.Settings.validationUrl": "Вы должны указать действительный URL", "components.TitleCard.cleardata": "Очистить данные", "components.TvDetails.Season.noepisodes": "Список эпизодов недоступен.", "components.TvDetails.seasonstitle": "Сезоны", "components.TvDetails.status4k": "4K {status}", "components.Settings.SettingsAbout.appDataPath": "Папка конфигурации", "components.RequestCard.editrequest": "Редактировать запрос", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Дата выхода в эфир по возрастанию", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Дата первого выхода в эфир по убыванию", "components.Discover.FilterSlideover.firstAirDate": "Дата выхода в эфир", "components.Discover.updatesuccess": "Настройки обнаружения успешно обновлены.", "components.RequestBlock.decline": "Отклонить запрос", "components.RequestBlock.delete": "Удалить запрос", "components.RequestBlock.edit": "Редактировать запрос", "components.RequestBlock.languageprofile": "Языковой профиль", "components.RequestBlock.requestdate": "Дата запроса", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.unknowntitle": "Неизвестный заголовок", "components.RequestModal.requestcollectiontitle": "Запросить Коллекцию", "components.RequestModal.requestmovie4ktitle": "Запросить фильм в 4К", "components.RequestModal.requestmovietitle": "Запросить фильм", "components.RequestModal.requestseriestitle": "Запросить сериал", "components.RequestModal.requestseries4ktitle": "Запросить сериал в 4К", "components.Selector.nooptions": "Нет результатов.", "components.Selector.searchGenres": "Выберите жанры…", "components.Selector.searchKeywords": "Ключевые слова для поиска…", "components.Selector.starttyping": "Начните ввод для поиска.", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Ошибка сохранения настроек Gotify.", "components.Selector.searchStudios": "Поиск студий…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Тестовое уведомление Gotify отправлено!", "components.Settings.Notifications.NotificationsGotify.url": "URL сервера", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Отправка тестового уведомления Gotify…", "components.Settings.SettingsMain.cacheImages": "Включить кэширование изображений", "components.Settings.SettingsMain.generalsettings": "Общие настройки", "components.Settings.SettingsMain.generalsettingsDescription": "Настройте глобальные параметры и параметры по умолчанию для Jellyseerr.", "components.Settings.SettingsMain.hideAvailable": "Скрыть доступные медиа", "components.Settings.SettingsMain.toastApiKeyFailure": "Что-то пошло не так при создании нового ключа API.", "components.Settings.SettingsMain.locale": "Язык приложения", "components.Settings.SettingsMain.originallanguage": "Языки для поиска фильмов и сериалов", "components.Settings.tautulliSettingsDescription": "При желании настройте параметры для вашего сервер<PERSON>. Je<PERSON>seerr получает историю просмотров Plex из Tautulli.", "components.Settings.toastTautulliSettingsFailure": "Что-то пошло не так при сохранении настроек Tautulli.", "components.Settings.toastTautulliSettingsSuccess": "Настройки Tautulli успешно сохранены!", "components.Settings.validationUrlBaseTrailingSlash": "База URL не должна заканчиваться косой чертой", "components.Settings.validationUrlTrailingSlash": "URL не должен заканчиваться косой чертой", "components.TvDetails.Season.somethingwentwrong": "Что-то пошло не так при получении данных о сериале.", "components.TvDetails.rtaudiencescore": "Рейтинг пользователей Rotten Tomatoes", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes томатометр", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>Многозначный идентификационный номер</FindDiscordIdLink>, связанный с вашей учетной записью пользователя Discord", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Автоматически запрашивать фильмы из вашего <PlexWatchlistSupportLink>списка просмотра Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Автоматический запрос сериалов", "components.UserProfile.plexwatchlist": "Список просмотра Plex", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.Settings.SettingsJobsCache.imagecache": "Кэш изображений", "components.StatusBadge.openinarr": "Открыть в {arr}", "components.RequestModal.SearchByNameModal.nomatches": "Нам не удалось найти совпадение для этого сериала.", "components.Settings.Notifications.NotificationsGotify.token": "Токен приложения", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Вы должны указать токен приложения", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Вы должны выбрать как минимум один тип уведомления", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Вы должны указать действующий URL", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL не должен заканчиваться слешем", "components.Settings.SettingsJobsCache.imagecacheDescription": "Если включено, <PERSON><PERSON><PERSON><PERSON> будет проксировать и кэшировать изображения из предварительно настроенных внешних источников. Кэшированные изображения сохраняются в папку конфигурации. Вы можете найти файлы в <code>{appDataPath}/cache/images</code>.", "components.Settings.tautulliSettings": "Настрой<PERSON><PERSON>", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Discover.customizediscover": "Настроить Обнаружение", "components.Discover.resetfailed": "Что-то пошло не так при сбросе настроек настройки обнаружения.", "components.Discover.resetsuccess": "Настройки обнаружения успешно сброшены.", "components.Discover.updatefailed": "Что-то пошло не так при обновлении настроек обнаружения.", "components.MovieDetails.theatricalrelease": "Релиз в театре", "components.RequestBlock.requestedby": "Запрос от", "components.RequestCard.approverequest": "Одобрить запрос", "components.RequestCard.unknowntitle": "Неизвестный заголовок", "components.RequestModal.requestcollection4ktitle": "Запросить Коллекцию в 4К", "components.Selector.showless": "Показать меньше", "components.Selector.showmore": "Показать больше", "components.Settings.SettingsJobsCache.imagecachesize": "Размер кэша", "components.Settings.validationUrlBaseLeadingSlash": "Базовый URL должен начинаться с косой черты", "components.Layout.UserWarnings.emailRequired": "Требуется указать email адрес.", "components.Layout.UserWarnings.passwordRequired": "Требуется указать пароль.", "components.Login.emailtooltip": "Адрес не обязательно должен быть связан с вашим {mediaServerName} сервером.", "components.Login.initialsignin": "Подключиться", "components.Login.initialsigningin": "Подключение…", "components.Login.save": "Добавить", "components.Login.saving": "Добавление…", "components.Login.signinwithjellyfin": "Используйте свой {mediaServerName} аккаунт", "components.Login.username": "Имя пользователя", "components.Login.validationEmailFormat": "Неверный email", "components.Login.validationemailformat": "Необходим корректный email", "components.Login.validationhostformat": "Необходим корректный URL", "components.Login.validationhostrequired": "Необходим {mediaServerName} URL", "components.Login.validationusernamerequired": "Необходимо имя пользователя", "components.ManageSlideOver.removearr": "Удалить из {arr}", "components.ManageSlideOver.removearr4k": "Удалить из 4К {arr}", "components.MovieDetails.imdbuserscore": "Оценка пользователей IMDB", "components.MovieDetails.openradarr": "Открыть фильм в Radarr", "components.MovieDetails.play": "Запустить на {mediaServerName}", "components.MovieDetails.play4k": "Запустить 4К на {mediaServerName}", "components.Settings.RadarrModal.tagRequests": "Тег запро<PERSON>ов", "components.Layout.UserWarnings.emailInvalid": "Неверный email адрес.", "components.Settings.SonarrModal.seriesType": "Тип сериала", "components.Discover.FilterSlideover.voteCount": "Количество голосов от {minValue} до {maxValue}", "components.Login.validationEmailRequired": "Вы должны указать адрес электронной почты", "components.Settings.Notifications.userEmailRequired": "Требуется email пользователя", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Сканировать недавно добавленное в Jellyfin", "components.Discover.tmdbmoviestreamingservices": "Сервисы потоковой передачи фильмов TMDB", "components.Discover.tmdbtvstreamingservices": "Сервисы потоковой передачи сериалов TMDB", "components.Login.description": "Поскольку вы впервые входите в систему {ApplicationName}, вам необходимо добавить адрес электронной почты.", "components.Settings.Notifications.NotificationsPushover.sound": "Звук уведомлений", "components.Settings.RadarrModal.tagRequestsInfo": "Автодобавление тега с именем и ID пользователя, отправившего запрос", "components.Settings.SonarrModal.animeSeriesType": "Тип аниме", "components.Discover.FilterSlideover.tmdbuservotecount": "Количество голосов от пользователей TMDB", "components.Login.credentialerror": "Введено неверное имя пользователя или пароль.", "components.Login.title": "Добавить email", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Это приведет к необратимому удалению {MediaType} из {arr}, включая все файлы.", "components.Settings.SettingsAbout.supportjellyseerr": "Под<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SonarrModal.tagRequests": "Тег запро<PERSON>ов", "components.MovieDetails.downloadstatus": "Статус загрузки", "components.MovieDetails.openradarr4k": "Открыть фильм в 4К Radarr", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Устройство по умолчанию", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Сканировать всю библиотек<PERSON>", "components.Settings.SettingsJobsCache.availability-sync": "Синхронизировать доступность медиа", "components.Settings.jellyfinSettingsFailure": "Что-то пошло не так во время сохранения настроек {mediaServerName}.", "components.Settings.jellyfinSettingsSuccess": "Настройки {mediaServerName} успешно сохранены!", "components.Settings.jellyfinlibraries": "Библиотеки {mediaServerName}", "components.Settings.jellyfinlibrariesDescription": "Библиотеки {mediaServerName} проверяются на наличие заголовков. Нажмите кнопку ниже, если в списке не хватает библиотек.", "components.Settings.jellyfinsettings": "Настройки {mediaServerName}", "components.Settings.SonarrModal.tagRequestsInfo": "Автодобавление тега с именем и ID пользователя, отправившего запрос", "components.Settings.jellyfinSettings": "Настройки {mediaServerName}", "components.Settings.jellyfinSettingsDescription": "Необязательно настраивать внутреннюю и внешнюю конечные точки для вашего сервера {mediaServerName}. В большинстве случаев внешний URL-адрес отличается от внутреннего. Пользовательский URL-адрес для сброса пароля также может быть задан для входа в систему {mediaServerName}, на случай, если вы хотите перенаправить на другую страницу для сброса пароля. Вы также можете изменить API-ключ от Jellyfin, который был автоматически сгенерирован ранее.", "components.Settings.jellyfinsettingsDescription": "Настройте свой {mediaServerName} сервер. {mediaServerName} отсканирует ваши библиотеки, чтобы увидеть, какие библиотеки доступны.", "components.Settings.manualscanJellyfin": "Сканировать библиотеки вручную", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.syncJellyfin": "Синхронизировать библиотеки", "components.Settings.syncing": "Синхронизация", "components.Settings.timeout": "Время ожидания", "components.Setup.signin": "Войти", "components.Setup.signinWithPlex": "Введите данные своего Plex", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> успешно удален из списка наблюдения!", "components.TitleCard.watchlistError": "Что-то пошло не так. Пожалуйста, попробуйте еще раз.", "components.TvDetails.play": "Запустить в {mediaServerName}", "components.TvDetails.play4k": "Запустить 4К в {mediaServerName}", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {userCount, plural, one {# новый пользователь} other {# новых пользователя(ей)}} успешно импортированы из {mediaServerName}!", "components.UserList.importfromJellyfin": "Добавить пользователей из {mediaServerName}", "components.UserList.noJellyfinuserstoimport": "Нет пользователей {mediaServerName} для импорта.", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "Пользователь {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Сохранение…", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Звук уведомлений", "i18n.collection": "Коллекция", "components.Setup.configuremediaserver": "Настроить медиасервер", "components.UserList.importfromJellyfinerror": "Что-то пошло не так при импорте пользователей из {mediaServerName}.", "components.UserList.newJellyfinsigninenabled": "Параметр <strong>Включить новый вход в {mediaServerName}</strong> в настоящее время включен. Пользователей {mediaServerName} с доступом к библиотеке не нужно импортировать для входа.", "components.UserProfile.UserSettings.UserGeneralSettings.email": "Электронная почта", "components.UserProfile.localWatchlist": "Список наблюдения {username}", "components.Settings.manualscanDescriptionJellyfin": "Обычно это выполняется только раз в 24 часа. Jellyseerr будет более настойчиво проверять недавно добавленный сервер {mediaServerName}. Если вы впервые настраиваете Jellyseerr, то рекомендуем однократное полное сканирование библиотеки!", "components.TitleCard.watchlistCancel": "наблюдение за <strong>{title}</strong> отменено.", "components.Settings.saving": "Сохранение…", "components.TitleCard.addToWatchList": "Добавить в список наблюдения", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> успешно добавлен в список наблюдения!", "components.Settings.save": "Сохранить изменения", "components.Setup.signinWithJellyfin": "Введите данные своего Jellyfin", "components.UserList.mediaServerUser": "Пользователь {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Сохранить изменения", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Устройство по умолчанию", "components.Login.invalidurlerror": "Не удалось подключиться к {mediaServerName} серверу.", "components.Login.validationUrlBaseLeadingSlash": "Базовый URL должен начинаться на слэш", "components.MovieDetails.addtowatchlist": "Добавить в список просмотра", "components.MovieDetails.removefromwatchlist": "Удалить из списка просмотра", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> Удален из списка просмотра успешно!", "components.TvDetails.addtowatchlist": "Добавить в список просмотра", "components.RequestList.RequestItem.profileName": "Профиль", "components.Discover.FilterSlideover.status": "Статус", "components.Login.adminerror": "Вы должны использовать аккаунт администратора чтобы войти.", "components.Login.enablessl": "Использовать SSL", "components.Login.back": "Вернуться", "components.Login.port": "Порт", "components.Login.servertype": "Тип сервера", "components.Login.urlBase": "Базовый URL", "components.Login.validationHostnameRequired": "Вы должны указать корректный домен или IP адрес", "components.Login.validationPortRequired": "Вы должны указать корректный порт", "components.Login.validationUrlBaseTrailingSlash": "Базовый URL не может оканчиваться слэшем", "components.Login.validationUrlTrailingSlash": "URL не может оканчиваться слэшем", "components.Login.validationservertyperequired": "Пожалуйста, выберите тип сервера", "components.MovieDetails.watchlistError": "Что-то пошло не так. Пожалуйста, попробуйте еще раз.", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> успешно добавлен с список просмотра!", "components.Selector.canceled": "Отменён", "components.Selector.ended": "Окончен", "components.Selector.inProduction": "В производстве", "components.Selector.pilot": "Пил<PERSON><PERSON>", "components.Selector.planned": "Запла<PERSON><PERSON><PERSON>ован", "components.Selector.returningSeries": "Возвращение сериала", "components.Selector.searchStatus": "Выберите статус...", "components.Setup.back": "Вернуться", "components.Setup.configemby": "Настроить Emby", "components.Setup.configjellyfin": "Настрои<PERSON><PERSON>", "components.Setup.configplex": "Настроить Plex", "components.Setup.servertype": "Выберите тип сервера", "components.Setup.signinWithEmby": "Введите данные своего Emby", "components.Setup.subtitle": "Начните с выбора своего медиа сервера", "components.TvDetails.removefromwatchlist": "Удалить из списка просмотра", "components.Settings.invalidurlerror": "Не удалось подключиться к {mediaServerName} серверу.", "components.Settings.jellyfinForgotPasswordUrl": "Забыли пароль URL", "components.Settings.jellyfinSyncFailedGenericError": "Что-то пошло не так при синхронизации библиотек", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Библиотеки не найдены", "component.BlacklistBlock.blacklistdate": "Дата внесения в чёрный список", "components.Blacklist.blacklistSettingsDescription": "Управлять медиа в чёрном списке.", "components.PermissionEdit.manageblacklistDescription": "Предоставить разрешение на управление чёрным списком.", "components.PermissionEdit.viewblacklistedItems": "Открыть чёрный список.", "components.Blacklist.mediaType": "Тип", "components.Layout.Sidebar.blacklist": "Чёрный список", "components.PermissionEdit.blacklistedItems": "Внести в чёрный список.", "components.Login.hostname": "Адрес {mediaServerName}", "component.BlacklistBlock.blacklistedby": "Внесено в чёрный список", "component.BlacklistModal.blacklisting": "Внесение в чёрный список", "components.Blacklist.blacklistNotFoundError": "<strong>{title}</strong> не в чёрном списке.", "components.Blacklist.blacklistdate": "дата", "components.Blacklist.blacklistedby": "{date} пользователем {user}", "components.Blacklist.blacklistsettings": "Настройки чёрного списка", "components.Blacklist.mediaName": "Название", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.PermissionEdit.blacklistedItemsDescription": "Дать права на внесение в чёрный список.", "components.PermissionEdit.manageblacklist": "Управлять чёрным списком", "components.DiscoverTvUpcoming.upcomingtv": "Предстоящие сериалы", "components.Login.loginwithapp": "Войти по {appName}", "components.Settings.Notifications.validationMessageThreadId": "Идентификатор темы/топика должен быть положительным целым числом", "components.Settings.Notifications.messageThreadIdTip": "Если в вашем чате включены темы, укажите идентификатор здесь", "components.RequestList.sortDirection": "Изменить порядок сортировки", "components.PermissionEdit.viewblacklistedItemsDescription": "Разрешить просмотр контента из черного списка.", "components.RequestList.RequestItem.removearr": "Удалить с {arr}", "components.Settings.Notifications.validationWebhookRoleId": "Необходимо указать действительный ID роли Discord", "components.Selector.searchUsers": "Выбрать пользователей…", "components.Settings.Notifications.messageThreadId": "Идентификатор темы", "components.Login.noadminerror": "На сервере отсутствует администратор.", "components.Login.orsigninwith": "Или войдите с", "components.Settings.apiKey": "API-к<PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleModal.ruleCreated": "Правило переопределения успешно создано!", "components.Settings.SettingsMain.discoverRegionTip": "Фильтровать контент по региональной доступности", "components.Settings.SettingsMain.enableSpecialEpisodes": "Разрешить запрашивать спец. эпизоды", "components.Settings.SettingsNetwork.csrfProtection": "Включить CSRF защиту", "components.Settings.SettingsNetwork.proxyBypassFilterTip": "Используйте ',' в качестве разделителя. '*.' - для указания всех поддоменов", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> успешно удалён из списка просмотра!", "components.Settings.OverrideRuleModal.conditionsDescription": "Указывает условия для применения изменений параметров. Для применения каждое поле должно быть проверено с помощью правил (операция И). Поле считается проверенным, если подходит любой из его параметров (операция ИЛИ).", "components.TvDetails.watchlistError": "Что-то пошло не так. Пожалуйста, попробуйте еще раз.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> успешно добавлен с список просмотра!", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegion": "Регион трансляции", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccountsHint": "Эти внешние аккаунты привязаны к вашему аккаунту {applicationName}.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noLinkedAccounts": "У вас нет ни одного привязанного внешнего аккаунта.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.noPermissionDescription": "У вас нет прав менять привязанные аккаунты этого пользователя.", "components.Setup.librarieserror": "Валидация не пройдена. Пожалуйста, выберите библиотеки повторно для продолжения.", "components.Settings.OverrideRuleModal.service": "Служба", "components.Settings.SettingsUsers.atLeastOneAuth": "Нужно выбрать хотя бы один метод аутентификации.", "components.Settings.SettingsUsers.mediaServerLoginTip": "Позволить пользователям входить с помощью аккаунта {mediaServerName}", "components.Settings.addrule": "Новое правило переопределения", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmailEmpty": "Другой пользователь уже использует это имя пользователя. Вам нужно указать email", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadId": "ID треда/топика", "components.UserProfile.UserSettings.menuLinkedAccounts": "Связанные аккаунты", "i18n.blacklisted": "В чёрном списке", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong> успешно удалён из черного списка.", "components.Settings.OverrideRuleModal.notagoptions": "Без тегов.", "components.Settings.OverrideRuleModal.settingsDescription": "Указывает изменяемые настройки при выполнении вышеуказанных условий.", "components.Settings.SettingsJobsCache.plex-refresh-token": "Токен обновления Plex", "components.Settings.SettingsNetwork.csrfProtectionTip": "Устанавливает доступ к внешнему API в режим только для чтения (требуется HTTPS)", "components.Settings.SettingsNetwork.networkDisclaimer": "Вместо этих настроек нужно использовать сетевые параметры из вашего контейнера/системы. Для дополнительной информации прочитайте {docs}.", "components.UserProfile.UserSettings.LinkJellyfinModal.description": "Введите ваши данные от {mediaServerName}, чтобы связать аккаунт с {applicationName}.", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnauthorized": "Не удалось подключиться к {mediaServerName} с использованием ваших данных", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Регион поиска", "components.UserProfile.UserSettings.UserNotificationSettings.telegramMessageThreadIdTip": "Если ваш групповой чат использует топики, вы можете указать здесь ID треда/топика", "i18n.addToBlacklist": "Добавить в черный список", "i18n.blacklistDuplicateError": "<strong>{title}</strong> уже добавлен в черный список.", "components.Settings.SettingsJobsCache.usersavatars": "Аватары пользователей", "components.Settings.SettingsNetwork.toastSettingsFailure": "Что-то пошло не так при сохранении настроек.", "components.Settings.SettingsNetwork.advancedNetworkSettings": "Расширенные сетевые настройки", "components.Settings.SettingsNetwork.csrfProtectionHoverTip": "НЕ включайте эту настройку, если не знаете, для чего она нужна!", "components.Settings.SettingsNetwork.docs": "документация", "components.Settings.SettingsNetwork.forceIpv4First": "Принудительно использовать сначала IPv4", "components.Settings.SettingsNetwork.forceIpv4FirstTip": "<PERSON><PERSON><PERSON>rr будет принудительно использовать сначала IPv4 адреса вместо IPv6", "components.Settings.SettingsNetwork.network": "Сеть", "components.Settings.SettingsNetwork.networksettings": "Сетевые настройки", "components.Settings.SettingsNetwork.networksettingsDescription": "Установите сетевые настройки для вашего экземпля<PERSON><PERSON>.", "components.Settings.SettingsNetwork.proxyBypassFilter": "Адреса, игнорируемые прокси", "components.Settings.SettingsNetwork.proxyBypassLocalAddresses": "Не использовать прокси для локальных адресов", "components.Settings.SettingsNetwork.proxyEnabled": "HTTP(S) прокси", "components.Settings.SettingsNetwork.proxyHostname": "Имя хоста прокси", "components.Settings.SettingsNetwork.proxyPassword": "Пароль от прокси", "components.Settings.SettingsNetwork.proxyPort": "Порт прокси", "components.Settings.SettingsNetwork.proxySsl": "Использовать SSL для прокси", "components.Settings.SettingsNetwork.proxyUser": "Имя пользователя от прокси", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Настройки успешно сохранены!", "components.Settings.SettingsNetwork.trustProxy": "Включить поддержку прокси", "components.Settings.SettingsNetwork.trustProxyTip": "Позволя<PERSON><PERSON>rr корректно определять IP адреса клиентов за прокси", "components.Settings.SettingsNetwork.validationProxyPort": "Нужно указать корректный порт", "components.Settings.SettingsUsers.loginMethods": "Методы входа", "components.Settings.SettingsUsers.loginMethodsTip": "Настройте методы входа для пользователей.", "components.Settings.SettingsUsers.mediaServerLogin": "Включить вход с помощью {mediaServerName}", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Пользовательская аутентификация с автоматической группировкой библиотек не поддерживается", "components.Settings.menuNetwork": "Сеть", "components.Settings.overrideRules": "Правила переопределения", "components.Settings.overrideRulesDescription": "Правила переопределения позволяют вам указать параметры для изменения, если запрос походит под правило.", "components.Settings.scanbackground": "Сканирование будет выполнено в фоновом режиме. Сейчас вы можете продолжить процесс настройки.", "components.Settings.tip": "Подсказка", "components.StatusBadge.seasonnumber": "S{seasonNumber}", "components.UserList.validationUsername": "Вам нужно указать имя пользователя", "components.UserProfile.UserSettings.LinkJellyfinModal.errorExists": "Этот аккаунт уже связан с пользователем {applicationName}", "components.UserProfile.UserSettings.LinkJellyfinModal.errorUnknown": "Произошла неизвестная ошибка", "components.UserProfile.UserSettings.LinkJellyfinModal.password": "Пароль", "components.UserProfile.UserSettings.LinkJellyfinModal.passwordRequired": "Вам нужно указать пароль", "components.UserProfile.UserSettings.LinkJellyfinModal.save": "Связать", "components.UserProfile.UserSettings.LinkJellyfinModal.saving": "Добавление…", "components.UserProfile.UserSettings.LinkJellyfinModal.title": "Привязать аккаунт {mediaServerName}", "components.UserProfile.UserSettings.LinkJellyfinModal.username": "Имя пользователя", "components.UserProfile.UserSettings.LinkJellyfinModal.usernameRequired": "Вам нужно указать имя пользователя", "components.UserProfile.UserSettings.UserGeneralSettings.streamingRegionTip": "Показывать сайты трансляций по региональной доступности", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Необходим корректный email", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Email обязателен", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.deleteFailed": "Не удалость удалить связанный аккаунт.", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.errorUnknown": "Произошла неизвестная ошибка", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.linkedAccounts": "Связанные аккаунты", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorExists": "Этот аккаунт уже привязан к пользователю Plex", "components.UserProfile.UserSettings.UserLinkedAccountsSettings.plexErrorUnauthorized": "Не удалось соединиться с Plex с использованием ваших данных", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramMessageThreadId": "ID треда/топика должно быть целым положительным числом", "i18n.blacklist": "Чёрный список", "i18n.blacklistError": "Что-то пошло не так. Пожалуйста, попробуйте еще раз.", "i18n.blacklistSuccess": "<strong>{title}</strong> успешно добавлен в черный список.", "i18n.removefromBlacklist": "Удалить из черного списка", "i18n.specials": "Спец. эпизоды", "components.Settings.OverrideRuleModal.languages": "Языки", "components.Settings.Notifications.webhookRoleId": "ID роли уведомления", "components.Settings.Notifications.webhookRoleIdTip": "ID роли для упоминания в теле webhook запроса. Оставьте пустым для отключения упоминания", "components.Settings.OverrideRuleModal.conditions": "Условия", "components.Settings.OverrideRuleModal.create": "Создать правило", "components.Settings.OverrideRuleModal.createrule": "Новое правило переопределения", "components.Settings.OverrideRuleModal.editrule": "Изменить правило переопределения", "components.Settings.OverrideRuleModal.genres": "Ж<PERSON>н<PERSON><PERSON>", "components.Settings.OverrideRuleModal.keywords": "Ключевые слова", "components.Settings.OverrideRuleModal.qualityprofile": "Профиль качества", "components.Settings.OverrideRuleModal.rootfolder": "Корневая папка", "components.Settings.OverrideRuleModal.ruleUpdated": "Правило переопределения успешно изменено!", "components.Settings.OverrideRuleModal.selectQualityProfile": "Выберите профиль качества", "components.Settings.OverrideRuleModal.selectRootFolder": "Выберите корневую папку", "components.Settings.OverrideRuleModal.selecttags": "Выберите теги", "components.Settings.OverrideRuleModal.serviceDescription": "Применить это правило к выбранному сервису.", "components.Settings.OverrideRuleModal.settings": "Настройки", "components.Settings.OverrideRuleModal.selectService": "Выберите службу", "components.Settings.OverrideRuleModal.tags": "Теги", "components.Settings.OverrideRuleModal.users": "Пользователи", "components.Settings.OverrideRuleTile.conditions": "Условия", "components.Settings.OverrideRuleTile.genre": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.OverrideRuleTile.keywords": "Ключевые слова", "components.Settings.OverrideRuleTile.language": "Язык", "components.Settings.OverrideRuleTile.qualityprofile": "Профиль качества", "components.Settings.OverrideRuleTile.rootfolder": "Корневая папка", "components.Settings.OverrideRuleTile.settings": "Настройки", "components.Settings.OverrideRuleTile.tags": "Теги", "components.Settings.OverrideRuleTile.users": "Пользователи", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailureEmail": "Этот email уже занят!", "components.Settings.SettingsMain.discoverRegion": "Регион поиска", "components.Settings.SettingsMain.streamingRegion": "Регион трансляции", "components.Settings.SettingsMain.streamingRegionTip": "Показывать сайты трансляций по региональной доступности", "components.UserList.username": "Имя пользователя", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Фильтровать контент по региональной доступности"}