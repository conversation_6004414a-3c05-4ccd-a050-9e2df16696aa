{"components.RequestModal.AdvancedRequester.default": "{name} (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "components.RequestModal.AdvancedRequester.animenote": "* Ez a sorozat egy anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON>", "components.RequestList.sortModified": "U<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.sortAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.showallrequests": "Összes kérés mutatása", "components.RequestList.requests": "K<PERSON><PERSON>sek", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestList.RequestItem.failedretry": "Hiba történt a kérés újrapróbálása közben.", "components.RequestCard.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestButton.viewrequest4k": "4K kérés megtekintése", "components.RequestButton.viewrequest": "<PERSON><PERSON><PERSON>s megtek<PERSON>", "components.RequestButton.requestmore4k": "Továbbiak kérése 4K-ban", "components.RequestButton.requestmore": "Továbbiak kérése", "components.RequestButton.declinerequest4k": "4K kérés elutasítása", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.approverequest4k": "4K kérés jóváhagyása", "components.RequestButton.approverequest": "K<PERSON><PERSON>s j<PERSON>", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestBlock.seasons": "{seasonCount, plural, one {Season} other {Seasons}}", "components.RequestBlock.rootfolder": "Root könyvtár", "components.RequestBlock.requestoverrides": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>í<PERSON>", "components.PersonDetails.crewmember": "<PERSON><PERSON><PERSON>", "components.PersonDetails.ascharacter": "mint {character}", "components.PersonDetails.appearsin": "Szerepel a következőkben", "components.PermissionEdit.usersDescription": "Engedélyt ad az Jellyseerr-nek felhasználók kezelésére. Az ezzel az engedéllyel rendelkező felhasználók nem módosíthatják a rendszergazdai jogosultsággal rendelkező felhasználókat, és nem adhatják meg a jogosultságot más felhasználónak.", "components.PermissionEdit.users": "Felhasználók kezelése", "components.PermissionEdit.requestDescription": "Engedélyt ad filmek és sorozatok kérésére.", "components.PermissionEdit.request4kTvDescription": "Engedélyt ad 4K-s sorozatok kérésére.", "components.PermissionEdit.request4kTv": "Kérés - 4K sorozatok", "components.PermissionEdit.request4kMoviesDescription": "Engedélyt ad 4K-s filmek kérésére.", "components.PermissionEdit.request4kMovies": "Kérés - 4K filmek", "components.PermissionEdit.request4kDescription": "Engedélyt ad 4K-s filmek és sorozatok kérésére.", "components.PermissionEdit.request4k": "Kérés 4K-ban", "components.PermissionEdit.request": "<PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.managerequestsDescription": "Engedélyt ad az Jellyseerr-nek a kérések kezelésére. Ez magában foglalja a kérelmek jóváhagyását és elutasítását.", "components.PermissionEdit.managerequests": "Kérések kezelése", "components.PermissionEdit.autoapproveMoviesDescription": "Automatikusan jóváhagyja a felhasználó minden film kérését.", "components.PermissionEdit.autoapproveSeriesDescription": "Automatikusan jóváhagyja a felhasználó minden sorozat kérését.", "components.PermissionEdit.autoapproveSeries": "Sorozatok automatikus jóváhagyása", "components.PermissionEdit.autoapproveMovies": "Filmek automatikus jóváhagyása", "components.PermissionEdit.autoapproveDescription": "Automatikusan jóváhagyja a felhasználó minden kérését.", "components.PermissionEdit.autoapprove": "Automatikus jóv<PERSON>hagyás", "components.PermissionEdit.advancedrequestDescription": "Engedélyt ad a speciális kérési opciók használatára; pl. <PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON><PERSON>, útvonalak megváltoztatása.", "components.PermissionEdit.advancedrequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.PermissionEdit.adminDescription": "Teljes rendszergazdai hozzáférés. Megkerüli az összes engedélyellenőrzést.", "components.PermissionEdit.admin": "Admin", "components.MovieDetails.watchtrailer": "Előzetes megtekintése", "components.MovieDetails.viewfullcrew": "Teljes stáblista megtekintése", "components.MovieDetails.studio": "{studioCount, plural, one {Studio} other {Studios}}", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "components.MovieDetails.runtime": "{minutes} perc", "components.MovieDetails.revenue": "Bevétel", "components.MovieDetails.releasedate": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.overviewunavailable": "Áttekintés nem elérhető.", "components.MovieDetails.overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.originallanguage": "Eredeti nyelv", "components.MovieDetails.cast": "Szereposztás", "components.MovieDetails.budget": "Költségvetés", "components.MovieDetails.MovieCrew.fullcrew": "<PERSON><PERSON><PERSON>", "components.MovieDetails.MovieCast.fullcast": "<PERSON><PERSON><PERSON>z<PERSON>", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON><PERSON><PERSON>", "components.Login.validationpasswordrequired": "<PERSON> kell adnia egy j<PERSON>", "components.Login.validationemailrequired": "Érvényes email címet kell megadnod", "components.Login.signinwithplex": "Használd a Plex fiókod", "components.Login.signinwithoverseerr": "Bejelentkezés {applicationTitle} fiókkal", "components.Login.signinheader": "Jelentkezz be a folytatáshoz", "components.Login.signingin": "Bejelentkezés…", "components.Login.signin": "Bejelentkezés", "components.Login.password": "Je<PERSON><PERSON><PERSON>", "components.Login.loginerror": "Valami nem sikerült a bejelentkezés során.", "components.Login.email": "<PERSON><PERSON>", "components.Layout.UserDropdown.signout": "Kijelentkezés", "components.Layout.Sidebar.users": "Felhasználók", "components.Layout.Sidebar.settings": "Beállítások", "components.Layout.Sidebar.requests": "K<PERSON><PERSON>sek", "components.Layout.SearchInput.searchPlaceholder": "Filmek és sorozatok keresése", "components.Discover.upcomingmovies": "<PERSON><PERSON>san <PERSON> filmek", "components.Discover.upcoming": "<PERSON><PERSON>san <PERSON> filmek", "components.Discover.trending": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.recentrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.populartv": "Népszerű sorozatok", "components.Discover.popularmovies": "Népszerű filmek", "components.CollectionDetails.requestcollection": "Gyűjtemény <PERSON>", "components.CollectionDetails.overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.CollectionDetails.numberofmovies": "Filmek száma: {count}", "components.Search.searchresults": "Keresési <PERSON>", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON>(ka)t", "components.RequestModal.seasonnumber": "{number}. <PERSON><PERSON><PERSON>", "components.RequestModal.season": "<PERSON><PERSON><PERSON>", "components.RequestModal.requesterror": "Hiba történt a kérés beküldése közben.", "components.RequestModal.requestedited": "<PERSON><PERSON><PERSON><PERSON> a <strong>{title}</strong> szerkesztve!", "components.RequestModal.requestcancelled": "<PERSON><PERSON><PERSON><PERSON> a <strong>{title}</strong> tö<PERSON><PERSON><PERSON>.", "components.RequestModal.requestadmin": "A kérésed azonnal el lesz fogadva.", "components.RequestModal.numberofepisodes": "Epizódok száma", "components.RequestModal.errorediting": "Hiba történt a kérés szerkesztése közben.", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.rootfolder": "Root könyvtár", "components.RequestModal.AdvancedRequester.qualityprofile": "Minőség profil", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.MovieDetails.markavailable": "Megjelölés elé<PERSON>", "components.MovieDetails.mark4kavailable": "Megjelölés elérhetőként - 4K", "components.Layout.Sidebar.dashboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediarequested": "Kérés elfogadásra vár", "components.NotificationTypeSelector.mediafailedDescription": "Értesítést küld amikor a kérést nem si<PERSON>ül hozz<PERSON>adni a menedzser app-okhoz (Radarr/Sonarr).", "components.NotificationTypeSelector.mediafailed": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "components.NotificationTypeSelector.mediaapprovedDescription": "Értesítések küldése kérések manuális elfogadásakor.", "components.NotificationTypeSelector.mediadeclinedDescription": "Értesítést küld a kérések elutasításakor.", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaavailableDescription": "Értesítést küld amikor a kért tartalom elérhetővé válik.", "components.NotificationTypeSelector.mediaavailable": "<PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.mediaapproved": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestButton.declinerequests": "V<PERSON>zautasítása {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.approverequests": "Jóváhagyás {requestCount, plural, one {Request} other {{requestCount} Requests}}", "components.RequestButton.approve4krequests": "Jóváhagyás {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "components.RequestButton.decline4krequests": "Visszautasítás {requestCount, plural, one {4K Request} other {{requestCount} 4K Requests}}", "pages.returnHome": "Vissza a kezdőképernyőre", "pages.oops": "<PERSON><PERSON><PERSON>", "i18n.unavailable": "<PERSON><PERSON>", "i18n.tvshows": "Sorozatok", "i18n.retry": "Újrapróbálás", "i18n.requested": "<PERSON><PERSON><PERSON><PERSON>", "i18n.request": "<PERSON><PERSON><PERSON><PERSON>", "i18n.processing": "Feldolgozás", "i18n.pending": "Függőben lévő", "i18n.partiallyavailable": "Részben elérhető", "i18n.movies": "Filmek", "i18n.failed": "Sikertelen", "i18n.experimental": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.edit": "Szerkesztés", "i18n.deleting": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.delete": "Törlés", "i18n.declined": "Visszautasítva", "i18n.decline": "Visszautasítás", "i18n.close": "Bezárás", "i18n.cancel": "<PERSON><PERSON><PERSON><PERSON>", "i18n.available": "Elérhető", "i18n.approved": "Jóváhagyva", "i18n.approve": "Jóváhagyás", "components.UserList.validationpasswordminchars": "A j<PERSON><PERSON>ó túl r<PERSON>; minimum 8 karakter hosszú kell legyen", "components.UserList.userssaved": "A felhasználói engedélyeket sikeresen mentette!", "components.UserList.userlist": "Felhasználók listája", "components.UserList.userdeleteerror": "Hiba történt a felhasználó törlése közben.", "components.UserList.userdeleted": "Felhasz<PERSON><PERSON><PERSON> sikeresen törölve!", "components.UserList.usercreatedsuccess": "Felhaszná<PERSON>ó si<PERSON>esen létrehozva!", "components.UserList.usercreatedfailed": "Hiba történt a felhasználó létrehozása közben.", "components.UserList.user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.totalrequests": "K<PERSON><PERSON>sek", "components.UserList.role": "Jogos<PERSON>ság", "components.UserList.plexuser": "Plex felhasználó", "components.UserList.passwordinfodescription": "Konfigurálja az alkalmazás URL-jét, és engedélyezze az e-mailes értesítéseket az automatikus jelszógenerálás engedélyezéséhez.", "components.UserList.password": "Je<PERSON><PERSON><PERSON>", "components.UserList.localuser": "<PERSON><PERSON><PERSON>", "components.UserList.importfromplexerror": "Hiba történt a felhasználók Plex-ről történő importálása közben.", "components.UserList.importfromplex": "Felhasználók importálása Plex-ről", "components.UserList.importedfromplex": "{userCount, plural, =0 {Nem lett új} one {# új} other {# új}} felhasználó importálva Plex-ről!", "components.UserList.email": "E-mail-cím", "components.UserList.deleteuser": "Felhasz<PERSON><PERSON><PERSON> törl<PERSON>", "components.UserList.deleteconfirm": "Biztos vagy ben<PERSON>, hogy törlöd ezt a felhasználót? A felhasználó összes adata törlődni fog.", "components.UserList.creating": "Létrehozás…", "components.UserList.createlocaluser": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.created": "Létrehozva", "components.UserList.create": "Létrehozás", "components.UserList.bulkedit": "Tömeges szerkesztés", "components.UserList.autogeneratepassword": "Jelszó generálása automatikusan", "components.TvDetails.watchtrailer": "Előzetes megtekintése", "components.TvDetails.viewfullcrew": "<PERSON><PERSON><PERSON>", "components.TvDetails.similar": "<PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.showtype": "<PERSON><PERSON><PERSON><PERSON> tí<PERSON>", "components.TvDetails.recommendations": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.overviewunavailable": "Áttekintés nem elérhető.", "components.TvDetails.overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.originallanguage": "Eredeti nyelv", "components.TvDetails.network": "{networkCount, plural, one {Network} other {Networks}}", "components.TvDetails.firstAirDate": "Első adás dá<PERSON>a", "components.TvDetails.cast": "Szereposztás", "components.TvDetails.anime": "Anime", "components.TvDetails.TvCrew.fullseriescrew": "Sorozat teljes stá<PERSON>", "components.TvDetails.TvCast.fullseriescast": "Sorozat teljes szereposztása", "components.StatusBadge.status4k": "4K {status}", "components.Setup.welcome": "Üdv az Je<PERSON>-ben", "components.Setup.signinMessage": "Kezdésnek lépj be a Plex fiókoddal", "components.Setup.finishing": "Befejezés…", "components.Setup.finish": "Beállí<PERSON><PERSON> be<PERSON>", "components.Setup.continue": "Folytatás", "components.Setup.configureservices": "Szolgáltatások beállítása", "components.RequestModal.requestfrom": "<PERSON><PERSON><PERSON> van egy folyamatban lévő kérés {username} felhasználótól.", "components.RequestModal.requestseasons": "{seasonCount} <PERSON><PERSON><PERSON>", "components.RequestModal.requestSuccess": "<strong>{title}</strong> k<PERSON><PERSON><PERSON>!", "components.RequestModal.requestCancel": "<strong>{title}</strong> <PERSON><PERSON><PERSON><PERSON>.", "components.RequestModal.pendingrequest": "Függőben lévő kérés {title} - hez", "components.RequestModal.pending4krequest": "Függőben lévő 4k-s kérés a {title}-hoz", "components.RequestModal.autoapproval": "Automatikus jóv<PERSON>hagyás", "components.RequestModal.SearchByNameModal.notvdbiddescription": "<PERSON><PERSON>tani a kérésed. <PERSON><PERSON><PERSON><PERSON>, válaszd ki a megfelelő találatot az alábbi listából.", "components.RequestBlock.profilechanged": "Minőség profil", "components.NotificationTypeSelector.mediarequestedDescription": "Értesítések küldése amikor a felhasználók olyan új média kéréseket nyújtanak be, amelyeknél elfogadásra van szükség.", "components.PermissionEdit.viewrequests": "Kérések megtekintése", "components.Discover.discover": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Login.forgotpassword": "Elfelejtett jelszó?", "pages.errormessagewithcode": "{statusCode} - {error}", "i18n.delimitedlist": "{a}, {b}", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Új jelszót kell megadnia", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "<PERSON> kell adnia az aktuá<PERSON> j<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "A jelszavaknak meg kell egyezniük", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "<PERSON> kell erősítenie az új j<PERSON>z<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Je<PERSON><PERSON>ó sikeresen mentve!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "<PERSON><PERSON> elromlott a jelszó mentése közben. <PERSON><PERSON><PERSON><PERSON> adta meg a jelenlegi j<PERSON>zavát?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Hiba történt a jelszó mentése közben.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Je<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "<PERSON><PERSON><PERSON> j<PERSON> a felhasználó jelszavának módosítására.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "<PERSON>z Ön <PERSON><PERSON> jele<PERSON>leg nincs j<PERSON><PERSON> be<PERSON>llít<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON> be egy jelsz<PERSON>t az al<PERSON>bb<PERSON>, hogy \"he<PERSON><PERSON> felhasz<PERSON><PERSON><PERSON><PERSON>t\" bejelentkezhessen az e-mail címét használva.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Ehhez a felhasználói fiókhoz jelenleg nincs j<PERSON> beállítva. Az alábbiakban konfiguráljon j<PERSON>ót, hogy ez a fiók \"helyi felhasználóként\" bejelent<PERSON>z<PERSON>sen", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "<PERSON><PERSON><PERSON>ó megerősítése", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "A webes push értesítés beállításai sikeresen mentésre kerültek!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "A webes push-értesítés beállításait nem si<PERSON>ült el<PERSON>eni.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Meg kell adnia egy érvényes chat azonosítót", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Érvényes PGP- kulcsot kell megadnia", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Érvényes felhasználói azonosítót kell megadnia", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Telegram értesítés beállítások mentése sikeres!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "A Telegram-értesítés beállításait nem si<PERSON>ült elmenteni.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Csevegés indítása</TelegramBotLink>, <GetIdBotLink>@get_id_bot</GetIdBotLink> hozzáadása, és a <code>/my_id</code> parancs kiadása", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "Chat ID", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Értesítések küldése hang nélkül", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>dben", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "E-mail üzenetek titkosítása az <OpenPgpLink>OpenPGP</OpenPgpLink> használatával", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "PGP nyilvános kulcs", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Értesítési be<PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Értesítések", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Az e-mail értesítések beállításai sikeresen mentésre kerültek!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Az e-mail értesítés beállításait nem si<PERSON>ült elmenteni.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "E-mail-cím", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Discord értesítés beállítások sikeresen mentve!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "A Discord értesítés beállításait nem si<PERSON>ült elmenteni.", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "A felhasználói fiók <FindDiscordIdLink>azonosítószáma</FindDiscordIdLink>", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "Felhasználói azonosító", "components.UserProfile.UserSettings.UserGeneralSettings.user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "A beállítások sikeresen mentve!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Valami elromlott a beállítások mentése közben.", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Sorozatkérési limit", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Jogos<PERSON>ság", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "A tartalom szűrése a regionális elérhetőség szerint", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Fedezzen fel régiót", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Plex felhasználó", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Tartalom szűrése eredeti nyelv szerint", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Nyelv felfedezése", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Filmkérési limit", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "Alap ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Általános be<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.general": "<PERSON><PERSON>lán<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "G<PERSON>b<PERSON><PERSON> k<PERSON><PERSON>ü<PERSON>bír<PERSON>l<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Megjelenített név", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Megjelenítési nyelv", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Admin", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "<PERSON><PERSON><PERSON>", "components.UserProfile.ProfileHeader.userid": "Felhasználói azonosító: {userid}", "components.UserProfile.ProfileHeader.settings": "Beállítások szerkesztése", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserProfile.ProfileHeader.joindate": "Csatlakozott: {joindate}", "components.UserList.validationEmail": "Érvényes email címet kell megadnod", "components.UserList.users": "Felhasználók", "components.UserList.userfail": "Valami hiba történt a felhasználói jogosultságok mentése közben.", "components.UserList.usercreatedfailedexisting": "A megadott e-mail címet már egy másik felhasz<PERSON><PERSON><PERSON>.", "components.UserList.sortRequests": "Kérések száma", "components.UserList.sortDisplayName": "Megjelenített név", "components.UserList.sortCreated": "Létrehozás <PERSON>", "components.UserList.owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserList.nouserstoimport": "Nincs import<PERSON><PERSON><PERSON><PERSON> új felhasználó a Plexből.", "components.UserList.localLoginDisabled": "A <strong><PERSON><PERSON><PERSON> engedélyezése</strong> be<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>.", "components.UserList.edituser": "Felhasználói engedélyek szerkesztése", "components.UserList.autogeneratepasswordTip": "A szerver által generált jelszó elküldése e-mailben a felhasználónak", "components.UserList.accounttype": "<PERSON><PERSON><PERSON>", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON> cí<PERSON>", "components.TvDetails.nextAirDate": "Következő adás dá<PERSON>a", "components.TvDetails.episodeRuntimeMinutes": "{runtime} perc", "components.Setup.setup": "Be<PERSON>llít<PERSON>", "components.Settings.webpush": "Web Push", "components.Settings.webhook": "Webhook", "components.Settings.webAppUrlTip": "Opcionálisan <PERSON> a felhasználókat a szerveren lévő webes alkalmazáshoz a \"hosztolt\" webes alkalmazás helyett", "components.Settings.webAppUrl": "<WebAppLink>Web App</WebAppLink> URL", "components.Settings.validationPortRequired": "Érvényes portszámot kell megadnia", "components.Settings.validationHostnameRequired": "Érvényes host-nevet vagy IP-címet kell megadnia", "components.Settings.toastPlexRefreshSuccess": "Plex szerverlista sikeresen lekérdezve!", "components.Settings.toastPlexRefreshFailure": "<PERSON><PERSON> le<PERSON> a Plex szerverek listáját.", "components.Settings.toastPlexRefresh": "Szerverlista lekérése a Plexből…", "components.Settings.toastPlexConnectingSuccess": "A Plex kapcsolat si<PERSON>esen létrejött!", "components.Settings.toastPlexConnectingFailure": "<PERSON><PERSON> csatlakozni a Plexhez.", "components.Settings.toastPlexConnecting": "Csatlakozási kísérlet a Plexhez …", "components.Settings.startscan": "Szkennelés indítása", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON>", "components.Settings.settingUpPlexDescription": "A Plex beállításához megadhatja kézzel az adatokat, vagy kiv<PERSON>laszthat egy <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>-ről elérhető szervert. Nyomja meg a legördülő menü jobb oldalán lévő gombot az elérhető szerverek listájának lekérdezéséhez.", "components.Settings.services": "Szolgáltatások", "components.Settings.serviceSettingsDescription": "Az alábbiakban konfigurálja a {serverType} szerver(eke)t. Több {serverType} szervert is csat<PERSON>oztathat, de ezek közül csak kettő jelölhető meg alapértelmezettként (egy nem 4K és egy 4K). A rendszergazdák felülbírálhatják az új kérések feldolgozásához használt szervert a jóváhagyás előtt.", "components.Settings.serverpresetRefreshing": "Szerverek lekérése…", "components.Settings.serverpresetManualMessage": "<PERSON><PERSON><PERSON>", "components.Settings.serverpresetLoad": "Nyomja meg a gombot az elérhető szerverek betöltéséhez", "components.Settings.serverpreset": "Szerver", "components.Settings.serverSecure": "biztonságos", "components.Settings.serverRemote": "t<PERSON><PERSON><PERSON>", "components.Settings.serverLocal": "he<PERSON>i", "components.Settings.scanning": "Szinkronizálás…", "components.Settings.scan": "Könyvtárak s<PERSON>ronizálása", "components.Settings.radarrsettings": "<PERSON><PERSON>", "components.Settings.port": "Port", "components.Settings.plexsettingsDescription": "Konfigurálja a Plex szerver beállításait. Az <PERSON>rr átvizsgálja a Plex könyvtárakat a tartalom elérhetőségének meghatározása érdekében.", "components.Settings.plexsettings": "Plex beállí<PERSON>", "components.Settings.plexlibrariesDescription": "<PERSON><PERSON> könyvtár címeket keres. <PERSON><PERSON><PERSON>ts<PERSON> be és mentse el a Plex kapcsolati beállításokat, majd katti<PERSON> az alább<PERSON> go<PERSON>, ha nincsenek könyvtárak.", "components.Settings.plexlibraries": "Plex könyvtárak", "components.Settings.plex": "Plex", "components.Settings.notrunning": "Nem fut", "components.Settings.notificationsettings": "Értesítési be<PERSON>", "components.Settings.notifications": "Értesítések", "components.Settings.notificationAgentSettingsDescription": "Értesítési ügynökök konfigurálása és engedélyezése.", "components.Settings.noDefaultServer": "Legalább egy {serverType} szervert meg kell jel<PERSON>lni alapértelmezettként a {mediaType} kérések feldolgozásához.", "components.Settings.noDefaultNon4kServer": "Ha csak egyetlen {serverType} szervert használmind a nem 4K, mind a 4K tartalomhoz (vagy ha csak 4K tartalmat tölt le), akkor a {serverType} szervert <strong> NEM </strong> kell 4K szerverként kijelölni.", "components.Settings.noDefault4kServer": "A 4K {serverType} szervert alapértelmezettként kell meg<PERSON>lölni, hogy a felhasználók 4K {mediaType} kéréseket nyújthassanak be.", "components.Settings.menuUsers": "Felhasználók", "components.Settings.menuServices": "Szolgáltatások", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Értesítések", "components.Settings.menuLogs": "Naplók", "components.Settings.menuJobs": "Feladatok és gyorsítótár", "components.Settings.menuGeneralSettings": "<PERSON><PERSON>lán<PERSON>", "components.Settings.menuAbout": "<PERSON>z <PERSON>", "components.Settings.mediaTypeSeries": "soro<PERSON>t", "components.Settings.mediaTypeMovie": "film", "components.Settings.manualscanDescription": "Normális esetben ez csak 24 óránként egyszer fut le. Az Jellyseerr agresszívebben ellenőrzi a Plex szerver \"nemrégiben hozzáadottakat\" könyvtárat. Ha ez az első alkalom a Plex konfigur<PERSON><PERSON>, egyszeri teljes kézi könyvtárellenőrzés ajánlott!", "components.Settings.manualscan": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.librariesRemaining": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>: {count}", "components.Settings.is4k": "4K", "components.Settings.enablessl": "Használjon SSL-t", "components.Settings.email": "E-mail-cím", "components.Settings.deleteserverconfirm": "<PERSON><PERSON><PERSON>, hogy tö<PERSON>ölni szeretné ezt a szervert?", "components.Settings.default4k": "Alapértelmezett 4K", "components.Settings.default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.currentlibrary": "<PERSON><PERSON><PERSON><PERSON> könyvtár: {name}", "components.Settings.copied": "API-kulcs másolva a vágólapra.", "components.Settings.cancelscan": "Beolvasás me<PERSON>zakítása", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON> s<PERSON> hozz<PERSON>", "components.Settings.address": "Cím", "components.Settings.addradarr": "Radarr s<PERSON>ver hozz<PERSON><PERSON>", "components.Settings.activeProfile": "Aktív profil", "components.Settings.SonarrModal.validationRootFolderRequired": "Ki kell választania egy root mappát", "components.Settings.SonarrModal.validationProfileRequired": "Ki kell választania egy minőség profilt", "components.Settings.SonarrModal.validationPortRequired": "Érvényes portszámot kell megadnia", "components.Settings.SonarrModal.validationNameRequired": "<PERSON> kell adnia a szerver nevét", "components.Settings.SonarrModal.validationLanguageProfileRequired": "<PERSON> kell választania egy nyelvi profilt", "components.Settings.SonarrModal.validationHostnameRequired": "Érvényes host-nevet vagy IP-címet kell megadnia", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Az alap URL-nek nem lehet vége perjel", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Az alap-URL-nek tartalmaznia kell egy perjelet", "components.Settings.SonarrModal.server4k": "4K szerver", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "Az URL-nek nem lehet vége perjel", "components.Settings.SonarrModal.validationApplicationUrl": "Érvényes URL-t kell megadnia", "components.Settings.SonarrModal.validationApiKeyRequired": "<PERSON> kell adnia egy API-kulcsot", "components.Settings.SonarrModal.toastSonarrTestSuccess": "<PERSON><PERSON>r kap<PERSON>t si<PERSON>esen létrejött!", "components.Settings.SonarrModal.toastSonarrTestFailure": "<PERSON><PERSON> csatlakozni a Sonarrhoz.", "components.Settings.SonarrModal.testFirstTags": "Tesztelje a kapcsolatot a címkék betöltéséhez", "components.Settings.SonarrModal.testFirstRootFolders": "Kapcsolat tesztelése a root mappák betöltéséhez", "components.Settings.SonarrModal.testFirstQualityProfiles": "Kapcsolat tesztelése a minőség profilok betöltéséhez", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Kapcsolat tesztelése a nyelvi profilok betöltéséhez", "components.Settings.SonarrModal.tags": "Cimkék", "components.Settings.SonarrModal.syncEnabled": "Szkennelés engedélyezése", "components.Settings.SonarrModal.ssl": "Használjon SSL-t", "components.Settings.SonarrModal.servername": "Szerver név", "components.Settings.SonarrModal.selecttags": "Cimkék kiválasztása", "components.Settings.SonarrModal.selectRootFolder": "Válassza ki a root mappát", "components.Settings.SonarrModal.selectQualityProfile": "Válassza ki a minőség profilt", "components.Settings.SonarrModal.selectLanguageProfile": "Válassza ki a nyelvi profilt", "components.Settings.SonarrModal.seasonfolders": "<PERSON><PERSON><PERSON> mappák", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Globális filmkérési limit", "components.Settings.SettingsUsers.localLoginTip": "Lehetővé teszi a felhasználók számára, hogy a \"Plex OAuth\" helyett az e-mail címükkel és jelszavukkal jelentkezzenek be", "components.Settings.SettingsUsers.localLogin": "<PERSON><PERSON><PERSON> engedélyezése", "components.Settings.SettingsUsers.defaultPermissionsTip": "Az új felhasználókhoz rendelt kezdeti jogosultságok", "components.Settings.SettingsUsers.defaultPermissions": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.time": "Időbélyegző", "components.Settings.SettingsLogs.showall": "Minden napló me<PERSON>", "components.Settings.SettingsLogs.resumeLogs": "Folytatás", "components.Settings.SettingsLogs.pauseLogs": "Szünet", "components.Settings.SettingsLogs.message": "Üzenet", "components.Settings.SettingsLogs.logsDescription": "Ezeket a naplókat közvetlenül a <code>stdout</code>-on keresztül vagy a <code>{appDataPath}/logs/overseerr.log</code>-ben is megt<PERSON>intheti.", "components.Settings.SettingsLogs.logs": "Naplók", "components.Settings.SettingsLogs.logDetails": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.level": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.label": "\"Címke\"", "components.Settings.SettingsLogs.filterWarn": "Figyelmeztetés", "components.Settings.SettingsLogs.filterInfo": "Infó", "components.Settings.SettingsLogs.filterError": "Hiba", "components.Settings.SettingsLogs.filterDebug": "Hibakeresés (Debug)", "components.Settings.SettingsLogs.extraData": "Kiegészítő adatok", "components.Settings.SettingsLogs.copyToClipboard": "Másolás a vágólapra", "components.Settings.SettingsLogs.copiedLogMessage": "Naplóüzenet másolása a vágólapra.", "components.Settings.SettingsJobsCache.unknownJob": "Ismeretlen feladat", "components.Settings.SettingsJobsCache.sonarr-scan": "Sonarr s<PERSON>", "components.Settings.SettingsJobsCache.runnow": "Futtatás most", "components.Settings.SettingsJobsCache.radarr-scan": "Radarr szkennelés", "components.Settings.SettingsJobsCache.process": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Plex \"nemrégiben hozzáadott\" beolvasása", "components.Settings.SettingsJobsCache.plex-full-scan": "Plex összes könyvtárának beolvasása", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Je<PERSON>fin összes könyvtárának beolvasása", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "<PERSON><PERSON><PERSON> \"nemrégiben hozz<PERSON>\" beol<PERSON>ása", "components.Settings.SettingsJobsCache.nextexecution": "Következő végrehajtás", "components.Settings.SettingsJobsCache.jobtype": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} elindult.", "components.Settings.SettingsJobsCache.jobsandcache": "Feladatok és gyorsítótár", "components.Settings.SettingsJobsCache.jobsDescription": "Az Je<PERSON>seerr bizonyos karbantartási feladatokat rendszeresen ütemezett munkákként végez, de ezeket az alábbiakban manuális<PERSON> is elindíthatja. Egy feladat kézi futtatása nem változtatja meg annak ütemezését.", "components.Settings.SettingsJobsCache.jobs": "Feladatok", "components.Settings.SettingsJobsCache.jobname": "Feladat neve", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} törölve.", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync-reset": "Töltse le a Sync Reset", "components.Settings.SettingsJobsCache.download-sync": "Szinkronizálás letöltése", "components.Settings.SettingsJobsCache.command": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.canceljob": "<PERSON><PERSON>at me<PERSON>zakítása", "components.Settings.SettingsJobsCache.cachevsize": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> neve", "components.Settings.SettingsJobsCache.cachemisses": "Hiányzók", "components.Settings.SettingsJobsCache.cacheksize": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cachekeys": "Összes \"api lékérés\"", "components.Settings.SettingsJobsCache.cachehits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheflushed": "A {cachename} g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "components.Settings.SettingsJobsCache.cacheDescription": "Az Jellyseerr a külső API végpontokhoz intézett kéréseket gyorsítótárba helyezi a teljesítmény optimalizálása és a felesleges API-hívások elkerülése érdekében.", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON>", "components.Settings.SettingsAbout.version": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.uptodate": "Naprak<PERSON><PERSON>", "components.Settings.SettingsAbout.totalrequests": "Összes kérés", "components.Settings.SettingsAbout.totalmedia": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Id<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.supportoverseerr": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Előnyben részesített", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.outofdate": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.helppaycoffee": "Seg<PERSON><PERSON> egy ká<PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "GitHub Megbeszélések", "components.Settings.SettingsAbout.gettingsupport": "Támogatás kérése", "components.Settings.SettingsAbout.documentation": "Do<PERSON>ment<PERSON><PERSON>ó", "components.Settings.SettingsAbout.betawarning": "Ez egy BETA szoftver. A funkciók hibásak és/vagy instabilak lehetnek. K<PERSON><PERSON><PERSON><PERSON>k, jelezzen minden problémát a GitHubon!", "components.Settings.SettingsAbout.about": "<PERSON>z <PERSON>", "components.Settings.SettingsAbout.Releases.viewongithub": "Megtekintés a GitHubon", "components.Settings.SettingsAbout.Releases.viewchangelog": "Változásnapló megtekintése", "components.Settings.SettingsAbout.Releases.versionChangelog": "Verzió változásnapló", "components.Settings.SettingsAbout.Releases.releases": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Kiadási adatok nem állnak rendelkezésre.", "components.Settings.SettingsAbout.Releases.latestversion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.Releases.currentversion": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.validationRootFolderRequired": "Ki kell választania egy root mappát", "components.Settings.RadarrModal.validationProfileRequired": "Ki kell választania egy minőség profilt", "components.Settings.RadarrModal.validationPortRequired": "Érvényes portszámot kell megadnia", "components.Settings.RadarrModal.validationNameRequired": "<PERSON> kell adnia a szerver nevét", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Ki kell választania egy minimális elérhetőséget", "components.Settings.RadarrModal.validationHostnameRequired": "Érvényes gazdagépnevet vagy IP-címet kell megadnia", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Az URL alapja nem végződhet perjelre", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Az alap URL-nek perjellel kell végződnie", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "Az URL-nek nem lehet vége perjel", "components.Settings.RadarrModal.validationApplicationUrl": "Érvényes URL-t kell megadnia", "components.Settings.RadarrModal.validationApiKeyRequired": "<PERSON> kell adnia egy API-kulcsot", "components.Settings.RadarrModal.toastRadarrTestSuccess": "A Radarr kapcsolat si<PERSON>esen létrejött!", "components.Settings.RadarrModal.toastRadarrTestFailure": "<PERSON><PERSON> csatlakozni a Radarrhoz.", "components.Settings.RadarrModal.testFirstTags": "Tesztelje a kapcsolatot a címkék betöltéséhez", "components.Settings.RadarrModal.testFirstRootFolders": "Kapcsolat tesztelése a root mappák betöltéséhez", "components.Settings.RadarrModal.testFirstQualityProfiles": "Kapcsolat tesztelése a minőség profilokkal", "components.Settings.RadarrModal.tags": "Cimkék", "components.Settings.RadarrModal.syncEnabled": "Szkennelés engedélyezése", "components.Settings.RadarrModal.ssl": "Használjon SSL-t", "components.Settings.RadarrModal.servername": "Szerver név", "components.Settings.RadarrModal.server4k": "4K szerver", "components.Settings.RadarrModal.selecttags": "Cimkék kiválasztása", "components.Settings.RadarrModal.selectRootFolder": "Válassza ki a root mappát", "components.Settings.RadarrModal.selectQualityProfile": "Válassza ki a minőség profilt", "components.Settings.RadarrModal.selectMinimumAvailability": "Válassza ki a minimális elérhetőséget", "components.Settings.RadarrModal.rootfolder": "Root Könyvtár", "components.Settings.RadarrModal.qualityprofile": "Minőség profil", "components.Settings.RadarrModal.port": "Port", "components.Settings.RadarrModal.notagoptions": "<PERSON><PERSON><PERSON><PERSON> címkék.", "components.Settings.RadarrModal.minimumAvailability": "<PERSON><PERSON><PERSON><PERSON>g", "components.Settings.RadarrModal.loadingrootfolders": "Root mappák betöltése …", "components.Settings.RadarrModal.loadingprofiles": "Minőségi profilok betöltése…", "components.Settings.RadarrModal.loadingTags": "Címkék betöltése…", "components.Settings.RadarrModal.hostname": "Hostnév vagy IP-cím", "components.Settings.RadarrModal.externalUrl": "Külső URL", "components.Settings.RadarrModal.enableSearch": "Engedélyezze az automatikus keresést", "components.Settings.RadarrModal.editradarr": "Radarr szerver szerkesztése", "components.Settings.RadarrModal.edit4kradarr": "A 4K-s Radarr szerver szerkesztése", "components.Settings.RadarrModal.defaultserver": "Alapértelmezett szerver", "components.Settings.RadarrModal.default4kserver": "Alapértelmezett 4K szerver", "components.Settings.RadarrModal.createradarr": "Új Radarr szerver hozzáadása", "components.Settings.RadarrModal.create4kradarr": "Új 4K Radarr szerver hozzáadása", "components.Settings.RadarrModal.baseUrl": "URL-alap", "components.Settings.RadarrModal.apiKey": "API kulcs", "components.Settings.RadarrModal.add": "Szerver hozzáadása", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON><PERSON> létre egy <DiscordWebhookLink>webhook integrációt</DiscordWebhookLink> a Discord szerverén", "components.Settings.Notifications.webhookUrl": "Webhook URL-cím", "components.Settings.Notifications.validationUrl": "Érvényes URL-t kell megadnia", "components.Settings.Notifications.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.validationSmtpPortRequired": "Érvényes portszámot kell megadnia", "components.Settings.Notifications.validationSmtpHostRequired": "Érvényes host-nevet vagy IP-címet kell megadnia", "components.Settings.Notifications.validationPgpPrivateKey": "Meg kell adnia egy érvényes PGP privát kulcsot", "components.Settings.Notifications.validationPgpPassword": "PGP-jelszót kell megadnia", "components.Settings.Notifications.validationEmail": "Érvényes email címet kell megadnod", "components.Settings.Notifications.validationChatIdRequired": "Meg kell adnia egy érvényes chat azonosítót", "components.Settings.Notifications.validationBotAPIRequired": "Meg kell adnia egy botengedélyezési tokent", "components.Settings.Notifications.toastTelegramTestSuccess": "Telegram teszt értesítés elküldve!", "components.Settings.Notifications.toastTelegramTestSending": "Telegram teszt értesítés küldése…", "components.Settings.Notifications.toastTelegramTestFailed": "A Telegram tesztértesítés elküldése sikertelen volt.", "components.Settings.Notifications.toastEmailTestSuccess": "Email teszt értesítés elküldve!", "components.Settings.Notifications.toastEmailTestSending": "Email teszt értesítés küldése…", "components.Settings.Notifications.toastEmailTestFailed": "Az e-mailes tesztértesítés nem sikerült elküldeni.", "components.Settings.Notifications.toastDiscordTestSuccess": "Discord teszt értesítés elküldve!", "components.Settings.Notifications.toastDiscordTestSending": "Discord teszt értesítés küldése …", "components.Settings.Notifications.toastDiscordTestFailed": "A Discord tesztértesítés nem sikerült elküldeni.", "components.Settings.Notifications.telegramsettingssaved": "Telegram értesítés beállítások mentése sikeresen!", "components.Settings.Notifications.telegramsettingsfailed": "A Telegram-értesítés beállításait nem si<PERSON>ült elmenteni.", "components.Settings.Notifications.smtpPort": "SMTP port", "components.Settings.Notifications.smtpHost": "SMTP Host", "components.Settings.Notifications.senderName": "K<PERSON>ld<PERSON> neve", "components.Settings.Notifications.sendSilentlyTip": "Értesítések küldése hang nélkül", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>dben", "components.Settings.Notifications.pgpPrivateKeyTip": "Titkosított e-mail üzenetek aláírása az <OpenPgpLink>OpenPGP</OpenPgpLink> segítségével", "components.Settings.Notifications.pgpPrivateKey": "PGP privát kulcs", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {request} other {requests}} hátralévő", "components.Settings.Notifications.pgpPasswordTip": "Titkosított e-mail üzenetek aláírása az <OpenPgpLink>OpenPGP</OpenPgpLink> segítségével", "components.Settings.Notifications.pgpPassword": "PGP jelszó", "components.Settings.Notifications.encryptionTip": "A legtöbb esetben az Implicit TLS a 465-ös portot, a STARTTLS pedig az 587-es portot használja", "components.Settings.Notifications.encryptionOpportunisticTls": "<PERSON><PERSON> has<PERSON>l<PERSON> a STARTTLS-t", "components.Settings.Notifications.encryptionNone": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.encryptionImplicitTls": "Használja az Implicit TLS-t", "components.Settings.Notifications.encryptionDefault": "Használja a STARTTLS-t, ha rendelkezésre áll", "components.Settings.Notifications.encryption": "Titkosítási mó<PERSON>", "components.Settings.Notifications.emailsettingssaved": "Az e-mail értesítések beállításai sikeresen mentésre kerültek!", "components.Settings.Notifications.emailsettingsfailed": "Az e-mail értesítés beállításait nem si<PERSON>ült elmenteni.", "components.Settings.Notifications.emailsender": "<PERSON><PERSON><PERSON>", "components.Settings.Notifications.discordsettingssaved": "Discord értesítés beállítások sikeresen mentve!", "components.Settings.Notifications.discordsettingsfailed": "A Discord értesítés beállításait nem si<PERSON>ült elmenteni.", "components.Settings.Notifications.chatIdTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> egy csevegést a bot<PERSON><PERSON><PERSON>, adja hozz<PERSON> a <GetIdBotLink>@get_id_bot</GetIdBotLink>, és adja ki a <code>/my_id</code> parancsot", "components.Settings.Notifications.chatId": "Chat ID", "components.Settings.Notifications.botUsernameTip": "Lehetővé teszi a felhasználók számára, hogy csevegést indítsanak a botj<PERSON>val, és beállíthatják saját értesítéseiket", "components.Settings.Notifications.botUsername": "<PERSON><PERSON>", "components.Settings.Notifications.botAvatarUrl": "Bot Avatar URL-je", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Hozzon létre egy botot</CreateBotLink> az Jellyseerrrel való has<PERSON>z", "components.Settings.Notifications.botAPI": "<PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON> token", "components.Settings.Notifications.authUser": "SMTP felhasználónév", "components.Settings.Notifications.authPass": "SMTP jelszó", "components.Settings.Notifications.allowselfsigned": "Önaláírt tanúsítványok engedélyezése", "components.Settings.Notifications.agentenabled": "Ügynök engedélyezése", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Webhook értesítés beállításai sikeresen mentve!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "A Webhook-értesítés beállításait nem si<PERSON>ült el<PERSON>.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "Webhook URL-je", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Érvényes URL-t kell megadnia", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Meg kell adnia egy érvényes JSON payload-t", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Webhook teszt értesítés elküldve!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Webhook teszt értesítés küldése …", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "A webhook tesztértesítés elküldése sikertelen volt.", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Sablonváltozó súgó", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "JSON payload visszaállítása si<PERSON>esen!", "components.RequestModal.AdvancedRequester.notagoptions": "<PERSON><PERSON><PERSON><PERSON> címkék.", "components.TvDetails.seasons": "{seasonCount, plural, one {# Season} other {# Seasons}}", "i18n.showingresults": "<PERSON><PERSON><PERSON><PERSON><PERSON> <strong>{from}</strong>-tól <strong>{to}</strong> of <strong>{total}</strong>ig", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {season} other {seasons}}", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Az Ön 30 karakteres <UsersGroupsLink>felhasználó vagy csoport azonosítója</UsersGroupsLink>", "components.UserProfile.pastdays": "{type} (elmúlt {days} napok)", "components.UserProfile.UserSettings.menuGeneralSettings": "<PERSON><PERSON>lán<PERSON>", "components.TvDetails.episodeRuntime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.hostname": "Hostnév vagy IP-cím", "pages.somethingwentwrong": "Valami hiba tö<PERSON><PERSON>", "pages.serviceunavailable": "A szolgáltatás nem elérhető", "pages.pagenotfound": "Oldal nem talá<PERSON>", "pages.internalservererror": "Belső kiszolgálóhiba", "i18n.view": "Nézet", "i18n.usersettings": "Felhasználói beállítások", "i18n.tvshow": "<PERSON><PERSON><PERSON><PERSON>", "i18n.testing": "Tesztelés…", "i18n.test": "Teszt", "i18n.status": "<PERSON><PERSON><PERSON>", "i18n.settings": "Beállítások", "i18n.saving": "<PERSON><PERSON><PERSON>…", "i18n.save": "Változások mentése", "i18n.retrying": "Újrap<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.resultsperpage": "{pageSize} eredmények megjeleníté<PERSON> oldalanként", "i18n.requesting": "<PERSON><PERSON><PERSON><PERSON>…", "i18n.request4k": "Kérés 4K-ban", "i18n.previous": "Előző", "i18n.notrequested": "<PERSON><PERSON><PERSON>", "i18n.noresults": "<PERSON><PERSON><PERSON>.", "i18n.next": "Következő", "i18n.movie": "Film", "i18n.loading": "Betöltés…", "i18n.canceling": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "i18n.back": "<PERSON><PERSON><PERSON>", "i18n.areyousure": "Biztos vagy benne?", "i18n.all": "Összes", "i18n.advanced": "<PERSON><PERSON><PERSON>", "components.UserProfile.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.totalrequests": "Összes kérés", "components.UserProfile.seriesrequest": "Sorozatkérések", "components.UserProfile.requestsperdays": "{limit} maradt", "components.UserProfile.recentrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.movierequests": "Filmkérések", "components.UserProfile.limit": "{remaining} a {limit}-ből", "components.UserProfile.UserSettings.unauthorizedDescription": "<PERSON><PERSON><PERSON> j<PERSON> a felhasználó beállításainak módosítására.", "components.UserProfile.UserSettings.menuPermissions": "Jogosultságok", "components.UserProfile.UserSettings.menuNotifications": "Értesítések", "components.UserProfile.UserSettings.menuChangePass": "Je<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "A saját jogosultságait nem módosíthatja.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "A jogosultságok sikeresen mentve!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Valami elromlott a beállítások mentése közben.", "components.UserProfile.UserSettings.UserPermissions.permissions": "Jogosultságok", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "A j<PERSON>zó túl r<PERSON>; minimum 8 karakter hosszú legyen", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Alapértelmezettre visszaállítása", "components.Settings.Notifications.NotificationsWebhook.customJson": "JSON Payload", "components.Settings.Notifications.NotificationsWebhook.authheader": "Engedélyezési f<PERSON>", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Ügynök engedélyezése", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "A webes push értesítés beállításai sikeresen mentésre kerültek!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "A webes push-értesítés beállításait nem si<PERSON>ült el<PERSON>eni.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Web push teszt értesítés elküldve!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Web push teszt értesítés küldése…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "A webes push tesztértesítés elküldése sikertelen volt.", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "A webes push-értesítések fogadásához az Jellyseerr-t HTTPS-en keresztül kell kiszolgálni.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Ügynök engedélyezése", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Hozzon létre egy <WebhookLink>Bejövő Webhook</WebhookLink> integrációt", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "Webhook URL-je", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Érvényes URL-t kell megadnia", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Slack teszt értesítés elküldve!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Slack teszt értesítés küldése…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "A Slack tesztértesítés elküldése sikertelen volt.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Slack értesítés beállításai sikeresen mentve!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "A Slack értesítés beállításait nem si<PERSON>ült el<PERSON>.", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Érvényes alkalmazási tokent kell megadnia", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Legalább <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} kell lennie ah<PERSON>, hogy k<PERSON><PERSON><PERSON> nyú<PERSON>tson be ehhez a sorozathoz.", "components.RequestModal.QuotaDisplay.requiredquota": "Legalább <strong>{seasons}</strong> {seasons, plural, one {season request} other {season requests}} kell lennie ah<PERSON>, hogy k<PERSON><PERSON><PERSON> nyú<PERSON>tson be ehhez a sorozathoz.", "components.RequestModal.pendingapproval": "Kérése jóváhagyásra vár.", "components.RequestModal.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.alreadyrequested": "<PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.season": "<PERSON><PERSON><PERSON>", "components.RequestModal.QuotaDisplay.quotaLinkUser": "A kérelmek korlátozásainak összefoglalóját a <ProfileLink>profil odlalon</ProfileLink> megtekintheti.", "components.RequestModal.QuotaDisplay.quotaLink": "A kérelmek korlátozásainak összefoglalóját a <ProfileLink>profil oldalon</ProfileLink> megtekintheti.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "<PERSON><PERSON><PERSON> el<PERSON><PERSON> \"k<PERSON><PERSON><PERSON>\" hozz<PERSON>", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {movie} other {movies}}", "components.RequestModal.QuotaDisplay.movie": "film", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "<PERSON><PERSON><PERSON> a felhasználónak <strong>{limit}</strong> {type} k<PERSON><PERSON><PERSON> van mind<PERSON> <strong>{days}</strong> naponta.", "components.RequestModal.AdvancedRequester.selecttags": "Cimkék kiválasztása", "components.RequestModal.AdvancedRequester.tags": "Cimkék", "components.RequestModal.AdvancedRequester.requestas": "Kérve mint", "components.RequestModal.AdvancedRequester.languageprofile": "Nyelvprofil", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestList.RequestItem.requesteddate": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.requested": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.modifieduserdate": "{date} {user} által", "components.RequestList.RequestItem.modified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.mediaerror": "{mediaType} <PERSON><PERSON>", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.mediaerror": "A kéréshez társított címe már nem érhető el.", "components.RequestCard.failedretry": "Hiba történt a kérés újrapróbálása közben.", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "components.RegionSelector.regionServerDefault": "Alap ({region})", "components.RegionSelector.regionDefault": "<PERSON><PERSON> r<PERSON>", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.QuotaSelector.seasons": "{count, plural, one {season} other {seasons}}", "components.QuotaSelector.movies": "{count, plural, one {movie} other {movies}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} per {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.days": "{count, plural, one {day} other {days}}", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "Született", "components.PersonDetails.alsoknownas": "Úgy is ismert mint : {names}", "components.PermissionEdit.viewrequestsDescription": "Engedélyt ad a többi felhasználó kérésének megtekintésére.", "components.PermissionEdit.requestTvDescription": "Engedélyt ad nem 4k-s sorozatok kéréséhez.", "components.PermissionEdit.requestTv": "Sorozatok Kérése", "components.PermissionEdit.requestMoviesDescription": "Engedélyt ad nem 4K-s filmek igényléséhez.", "components.PermissionEdit.requestMovies": "Filmek <PERSON>", "components.PermissionEdit.autoapprove4kSeriesDescription": "Automatikus elfogadást biztosít 4k sorozat kérésekhez.", "components.PermissionEdit.autoapprove4kSeries": "Automatikus jóváhagyás 4k sorozatok", "components.PermissionEdit.autoapprove4kMoviesDescription": "Adjon automatikus jóváhagyást a 4K-s filmkérelmekhez.", "components.PermissionEdit.autoapprove4kMovies": "4K filmek automatikus jóváhagyása", "components.PermissionEdit.autoapprove4kDescription": "Automatikus jóváhagyás biztosítása minden 4K kérelemhez.", "components.PermissionEdit.autoapprove4k": "Automatikus jóváhagyás 4k", "components.NotificationTypeSelector.usermediarequestedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kap, ha más felhasználók új média kéréseket nyújtanak be, am<PERSON><PERSON> jóváhagyást igényelnek.", "components.NotificationTypeSelector.usermediafailedDescription": "<PERSON><PERSON>s<PERSON><PERSON><PERSON> kap, ha a média kéréseket nem si<PERSON>ült hozzáadni a Radarrhoz vagy a Sonarrhoz.", "components.NotificationTypeSelector.usermediadeclinedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, amikor a média kérését v<PERSON>utasították.", "components.NotificationTypeSelector.usermediaavailableDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, amikor elérhetővé válnak a kérései.", "components.NotificationTypeSelector.usermediaapprovedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha a média kérelmeket jóváhagyják.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha más felhasználók új média kéréseket nyújtanak be, amelyeket automatikusan jóváhagynak.", "components.NotificationTypeSelector.notificationTypes": "Értesítési t<PERSON>ok", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Értesítések küldése amikor a felhasználók olyan új média kéréseket nyújtanak be, amelyek automatikusan jóvá vannak hagyva.", "components.NotificationTypeSelector.mediaAutoApproved": "Kérés Automatikusan Jóváhagyva", "components.MovieDetails.showmore": "<PERSON><PERSON><PERSON>", "components.MovieDetails.showless": "<PERSON><PERSON><PERSON>", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON> cí<PERSON>", "components.Layout.VersionStatus.streamstable": "Jellyseerr stabil", "components.Layout.VersionStatus.streamdevelop": "<PERSON><PERSON><PERSON><PERSON> Fejlesztő", "components.Layout.VersionStatus.outofdate": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {commit} other {commits}} mögött", "components.Layout.UserDropdown.settings": "Beállítások", "components.Layout.UserDropdown.myprofile": "Profil", "components.Layout.LanguagePicker.displaylanguage": "Megjelenítési nyelv", "components.LanguageSelector.originalLanguageDefault": "Minden nyelv", "components.LanguageSelector.languageServerDefault": "Alap ({language})", "components.DownloadBlock.estimatedtime": "<PERSON><PERSON><PERSON><PERSON> {time}", "components.Discover.upcomingtv": "<PERSON><PERSON><PERSON> me<PERSON> sorozatok", "components.Discover.TvGenreSlider.tvgenres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.TvGenreList.seriesgenres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.StudioSlider.studios": "Studiók", "components.Discover.NetworkSlider.networks": "Hálózatok", "components.Discover.MovieGenreSlider.moviegenres": "Filmműfaj", "components.Discover.MovieGenreList.moviegenres": "Filmműfaj", "components.Discover.DiscoverTvLanguage.languageSeries": "{language} Sorozatok", "components.Discover.DiscoverTvGenre.genreSeries": "{genre} So<PERSON>zatok", "components.Discover.DiscoverStudio.studioMovies": "{studio} Filmek", "components.Discover.DiscoverNetwork.networkSeries": "{network} Sorozat", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Filmek", "components.Discover.DiscoverMovieGenre.genreMovies": "{genre} Filmek", "components.CollectionDetails.requestcollection4k": "Gyűjtemény kérése 4k-ban", "components.AppDataWarning.dockerVolumeMissingDescription": "A <code>{appDataPath}</code> kötet nincs megfelelően csatlakoztatva. A tároló leállításakor vagy újraindításakor minden adat törlődik.", "components.Settings.SonarrModal.rootfolder": "Root Könyvtár", "components.Settings.SonarrModal.qualityprofile": "Minőség profil", "components.Settings.SonarrModal.port": "Port", "components.Settings.SonarrModal.notagoptions": "<PERSON><PERSON><PERSON><PERSON> címkék.", "components.Settings.SonarrModal.loadingrootfolders": "Root mappák betöltése …", "components.Settings.SonarrModal.loadingprofiles": "Minőségi profilok betöltése…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Nyelvi profilok betöltése …", "components.Settings.SonarrModal.loadingTags": "Címkék betöltése…", "components.Settings.SonarrModal.languageprofile": "Nyelvprofil", "components.Settings.SonarrModal.hostname": "Hostnév vagy IP-cím", "components.Settings.SonarrModal.externalUrl": "Külső URL", "components.Settings.SonarrModal.enableSearch": "Engedélyezze az automatikus keresést", "components.Settings.SonarrModal.editsonarr": "A Sonarr szerver szerkesztése", "components.Settings.SonarrModal.edit4ksonarr": "A 4K-s Sonarr szerver szerkesztése", "components.Settings.SonarrModal.defaultserver": "Alapértelmezett szerver", "components.Settings.SonarrModal.default4kserver": "Alapértelmezett 4K szerver", "components.Settings.SonarrModal.createsonarr": "<PERSON>j <PERSON>arr s<PERSON> hozzáadása", "components.Settings.SonarrModal.create4ksonarr": "Új 4K Sonarr szerver hozzáadása", "components.Settings.SonarrModal.baseUrl": "URL-alap", "components.Settings.SonarrModal.apiKey": "API-kulcs", "components.Settings.SonarrModal.animerootfolder": "Anime root mappa", "components.Settings.SonarrModal.animequalityprofile": "<PERSON><PERSON> min<PERSON> profil", "components.Settings.SonarrModal.animelanguageprofile": "<PERSON><PERSON> nyelvi profil", "components.Settings.SonarrModal.animeTags": "<PERSON><PERSON>", "components.Settings.SonarrModal.add": "Szerver hozzáadása", "components.Settings.SettingsUsers.users": "Felhasználók", "components.Settings.SettingsUsers.userSettingsDescription": "Konfigurálja a globális és az alapértelmezett felhasználói beállításokat.", "components.Settings.SettingsUsers.userSettings": "Felhasználói beállítások", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Globális sorozat kérési limit", "components.Settings.SettingsUsers.toastSettingsSuccess": "A felhasználói beállítások sikeresen mentve!", "components.Settings.SettingsUsers.toastSettingsFailure": "Valami elromlott a beállítások mentése közben.", "components.Settings.SettingsUsers.newPlexLoginTip": "Lehetővé teszi a {mediaServerName} felhasználók s<PERSON>á<PERSON>ára, hogy első importá<PERSON><PERSON> n<PERSON>ül jelentkezzenek be", "components.Settings.SettingsUsers.newPlexLogin": "Engedélyezze az új {mediaServerName} bejelentkezést", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} per {quotaDays} {days}</quotaUnits>", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "Webhook URL-je", "components.RequestModal.QuotaDisplay.allowedRequests": "Napi <strong>{limit}</strong> {type} k<PERSON><PERSON><PERSON> engedé<PERSON><PERSON><PERSON> minden <strong>{days}</strong> naponta.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "\"ügynök\" engedélyezése", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Meg kell adnia egy érvényes felhasználói vagy csoportos kulcsot", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsPushover.userToken": "<PERSON><PERSON><PERSON>z<PERSON><PERSON><PERSON><PERSON> vagy csoport key", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Pushover teszt értesítés elküldve!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Pushover teszt üzenet küldése …", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Pushover tesztüzenet elküldése nem sikerült.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Pushover értesítési beállítások sikeresen mentve!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Pushover az értesítési beállításokat nem si<PERSON>ült menteni.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "\"Ügynök\" engedályezése", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Alkalmazás regisztrálása</ApplicationRegistrationLink> az <PERSON><PERSON><PERSON><PERSON> has<PERSON>", "components.Settings.Notifications.NotificationsPushover.accessToken": "Alkalmazás API Token", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Meg kell adnia egy hozzáférési tokent (acces token)", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Elküldtük a Pushbullet teszt értesítését!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Pushbullet teszt értesítés küldése …", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "A pushbullet teszt értesítését nem sikerült elküldeni.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "A Pushbullet értesítési beállításainak mentése sikeresen megtö<PERSON>ént!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "A Pushbullet értesítési beállításainak mentése nem si<PERSON>ült.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Agent en<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Hozzon létre egy Tokent a <PushbulletSettingsLink><PERSON><PERSON><PERSON>ll<PERSON>ások<PERSON></PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Hozzáfé<PERSON>si <PERSON> (Access Token)", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Felhasználói vagy eszközalapú <LunaSeaLink>értesitési webhook URL</LunaSeaLink>", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Érvényes URL-t kell megadnia", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "A LunaSea teszt értesítése elküldve!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "LunaSea teszt értesítés küldése …", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "A LunaSea teszt értesítését nem sikerült elküldeni.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "A LunaSea értesítési beállításainak mentése si<PERSON>ült!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "A LunaSea értesítési beállításainak mentése nem si<PERSON>ült.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Csak akkor szükséges, ha nem használja a <code>default</code> profil", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Profil n<PERSON>", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "\"Ügynök\" engedélyezése", "components.Search.search": "Keresés", "components.ResetPassword.validationpasswordrequired": "<PERSON> kell adnia egy j<PERSON>", "components.ResetPassword.validationpasswordminchars": "A j<PERSON>zó túl r<PERSON>; minimum 8 karakter hosszú legyen", "components.ResetPassword.validationpasswordmatch": "a jelszavaknak egyezniük kell", "components.ResetPassword.validationemailrequired": "Meg kell adnia egy érvényes e-mail címet", "components.ResetPassword.resetpasswordsuccessmessage": "A jelszó visszaállítása sikeres volt!", "components.ResetPassword.resetpassword": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "components.ResetPassword.requestresetlinksuccessmessage": "Jelszó-visszaállító linket küldünk a megadott e-mail címre, ha az érvényes felhasználóhoz van társítva.", "components.ResetPassword.passwordreset": "Je<PERSON><PERSON><PERSON> visszaállítás", "components.ResetPassword.password": "Je<PERSON><PERSON><PERSON>", "components.ResetPassword.gobacklogin": "Vissza a bejelentkező lapra", "components.ResetPassword.emailresetlink": "Email v<PERSON>llítási link", "components.ResetPassword.email": "<PERSON><PERSON>", "components.ResetPassword.confirmpassword": "<PERSON><PERSON><PERSON>ó megerősítése", "components.StatusBadge.status": "{status}", "components.IssueDetails.closeissue": "<PERSON>b<PERSON><PERSON>", "components.IssueDetails.closeissueandcomment": "Lezá<PERSON>ás <PERSON>ás<PERSON>", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Valami hiba történt a probléma elküldése során.", "components.IssueDetails.play4konplex": "Lejátszás Plexen 4K-ban", "components.IssueModal.CreateIssueModal.toastviewissue": "<PERSON>b<PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMediaWarning": "* Ez visszafordíthatatlanul eltávolítja az összes adatot ehhez a {mediaType}-hez, beleértve a kéréseket is. Ha ez az elem létezik a {mediaServerName} könyv<PERSON><PERSON><PERSON><PERSON><PERSON>, a médiainformáció a következő beolvasás során újra létrejön.", "components.IssueDetails.commentplaceholder": "Hozzászólás írása…", "components.IssueDetails.comments": "Hozzászólások", "components.IssueDetails.deleteissue": "Probléma Törlése", "components.IssueDetails.deleteissueconfirm": "<PERSON><PERSON><PERSON>, hogy ki akarja tö<PERSON>ölni ezt a problémát?", "components.IssueDetails.episode": "{episodeNumber}. <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuepagetitle": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueDetails.lastupdated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.leavecomment": "Hozzászólás", "components.IssueDetails.nocomments": "Nincsenek hozzászólások.", "components.IssueDetails.openedby": "#{issueId} bejelentve {relativeTime} {username} által", "components.IssueDetails.openin4karr": "Megnyitás 4K-ban itt: {arr}", "components.IssueDetails.playonplex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.problemepisode": "Érin<PERSON><PERSON>", "components.IssueDetails.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.reopenissueandcomment": "Újranyitás Hozzászólással", "components.IssueDetails.season": "{seasonNumber}. Évad", "components.IssueDetails.toasteditdescriptionsuccess": "A probléma leírása sikeresen szerkesztve!", "components.IssueDetails.toaststatusupdated": "A probléma állapota sikeresen frissítve!", "components.IssueDetails.toaststatusupdatefailed": "Valami hiba történt a probléma állapotának frissítése közben.", "components.IssueList.IssueItem.issuetype": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.opened": "Bejelentve", "components.IssueList.IssueItem.openeduserdate": "{date} {user} által", "components.IssueList.IssueItem.problemepisode": "Érin<PERSON><PERSON>", "components.IssueList.showallissues": "Minden Probléma <PERSON>", "components.IssueList.sortModified": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.allepisodes": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.allseasons": "<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.episode": "{episodeNumber}. <PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.extras": "<PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemepisode": "Érin<PERSON><PERSON>", "components.IssueModal.CreateIssueModal.problemseason": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.CreateIssueModal.providedetail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> r<PERSON>zletes magyarázatot a felmerült problémára.", "components.IssueModal.CreateIssueModal.reportissue": "Probléma <PERSON>", "components.IssueModal.CreateIssueModal.season": "{seasonNumber}. Évad", "components.IssueModal.CreateIssueModal.submitissue": "Probléma <PERSON>", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Adnia kell egy <PERSON>", "components.IssueModal.CreateIssueModal.whatswrong": "Mi a probléma?", "components.IssueModal.issueAudio": "Hang", "components.IssueModal.issueOther": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueModal.issueSubtitles": "<PERSON><PERSON><PERSON>", "components.IssueModal.issueVideo": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.issues": "Problémák", "components.ManageSlideOver.alltime": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.downloadstatus": "Letöltések", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalClearMedia": "Adatok Törlése", "components.ManageSlideOver.manageModalIssues": "Megoldatlan Problémák", "components.ManageSlideOver.manageModalMedia4k": "4K Média", "components.ManageSlideOver.manageModalNoRequests": "<PERSON>ncsenek kérések.", "components.ManageSlideOver.manageModalMedia": "Média", "components.ManageSlideOver.manageModalRequests": "K<PERSON><PERSON>sek", "components.ManageSlideOver.manageModalTitle": "{mediaType} Kezelése", "components.ManageSlideOver.mark4kavailable": "Megjelölés 4K-ban elérhetők<PERSON>t", "components.ManageSlideOver.markallseasons4kavailable": "Az Összes Évad Megjelölése 4K-ban Elérhetőként", "components.ManageSlideOver.markavailable": "Megjelölés Elérhe<PERSON>t", "components.ManageSlideOver.movie": "film", "components.ManageSlideOver.markallseasonsavailable": "Az Összes Évad <PERSON>lése Elérhetőként", "components.ManageSlideOver.openarr": "Megnyitás itt: {arr}", "components.ManageSlideOver.openarr4k": "Megnyitás 4K-ban itt: {arr}", "components.ManageSlideOver.opentautulli": "Megnyitás itt: <PERSON><PERSON><PERSON>", "components.ManageSlideOver.pastdays": "Elmúlt {days, number} Napban", "components.ManageSlideOver.tvshow": "soro<PERSON>t", "components.MovieDetails.streamingproviders": "<PERSON><PERSON><PERSON> Streamelhető", "components.NotificationTypeSelector.userissuecommentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kap, ha az Ön által jelentett problémákhoz új hozzászólások érkeznek.", "components.NotificationTypeSelector.userissuecreatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha más felhasználók problémákat jelentenek.", "components.NotificationTypeSelector.userissuereopenedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha az Ön által bejelentett problémákat új<PERSON> me<PERSON>ják.", "components.NotificationTypeSelector.userissueresolvedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha az Ön által bejelentett problémák megoldódnak.", "components.IssueDetails.IssueComment.edit": "Hozzászólás Szerkesztése", "components.IssueDetails.IssueComment.areyousuredelete": "Biztos, hogy ki akarja törölni ezt a hozzászólást?", "components.IssueDetails.IssueComment.delete": "Hozzászólás Törlése", "components.IssueDetails.IssueComment.postedby": "Közzétéve {relativeTime} {username} által", "components.IssueDetails.IssueDescription.deleteissue": "Probléma Törlése", "components.IssueDetails.IssueComment.postedbyedited": "Közzétéve {relativeTime} {username} által (Szerkesztve)", "components.IssueDetails.IssueDescription.edit": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.allepisodes": "<PERSON><PERSON>", "components.IssueDetails.reopenissue": "Probléma <PERSON>", "components.IssueDetails.toastissuedeletefailed": "Valami hiba történt a probléma törlése során.", "components.IssueList.IssueItem.issuestatus": "<PERSON><PERSON><PERSON>", "components.IssueList.IssueItem.unknownissuetype": "Ismeretlen", "components.IssueList.issues": "Problémák", "components.IssueList.IssueItem.viewissue": "<PERSON>b<PERSON><PERSON>", "components.IssueDetails.IssueDescription.description": "Le<PERSON><PERSON><PERSON>", "components.IssueDetails.allseasons": "<PERSON><PERSON>", "components.IssueDetails.openinarr": "Megnyitás itt: {arr}", "components.IssueDetails.toasteditdescriptionfailed": "Valami hiba történt a probléma leírásának szerkesztése közben.", "components.IssueDetails.toastissuedeleted": "A probléma sikeresen törölve!", "components.IssueDetails.unknownissuetype": "Ismeretlen", "components.IssueList.sortAdded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.validationComment": "Be kell írnia egy üzenetet", "components.NotificationTypeSelector.issuecreated": "<PERSON>b<PERSON><PERSON>", "components.NotificationTypeSelector.issuereopened": "<PERSON>b<PERSON><PERSON>", "components.PermissionEdit.manageissuesDescription": "Engedélyt ad média problémák kezelésére.", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Valami hiba történt a feladat mentése közben.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Alkalmazás API Token", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Ügynök Engedélyezése", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "A Gotify teszt értesítést nem sikerült elküldeni.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Gotify teszt értesítés küldése…", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Legalább egy értesítési típust ki kell választania", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Érvényes URL-t kell megadnia", "i18n.open": "Megnyitás", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Hozzáférési <PERSON>", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Gotify értesítési beállítások sikeresen mentve!", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "A <FindDiscordIdLink>többjegyű azonosítószám</FindDiscordIdLink>, amely az Ön Discord felhasználói fiókjához kapcsolódik", "components.Settings.RadarrModal.released": "Megjelent", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Hozzon létre egy tokent a <PushbulletSettingsLink>Fiókbeállításokban</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Egy érvényes Discord felhasználói azonosítót kell megadnia", "components.RequestModal.selectmovies": "Film(ek) Kiválasztása", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "Discord Felhasználó ID", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "A Pushbullet értesítési beállításai sikeresen mentve lettek!", "components.ManageSlideOver.playedby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.adminissuecommentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kap, ha más felhasználók hozzászólnak a problémákhoz.", "components.NotificationTypeSelector.adminissuereopenedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha más felhasználók újranyitják a problémákat.", "components.NotificationTypeSelector.adminissueresolvedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha más felhasználók megoldják a problémákat.", "components.NotificationTypeSelector.issuecommentDescription": "Értesítések küldése, ha a problémákhoz új hozzászólások érkeznek.", "components.NotificationTypeSelector.issuereopenedDescription": "Értesítések küldése a problémák újranyitásakor.", "components.PermissionEdit.createissuesDescription": "Engedélyt ad média problémák bejelentésére.", "components.PermissionEdit.manageissues": "Problémák Kezelése", "components.PermissionEdit.createissues": "Problémák Bejelentése", "components.PermissionEdit.viewissuesDescription": "Engedélyt ad más felhasználók által bejelentett média problémák megtekintésére.", "components.Settings.tautulliApiKey": "API Kulcs", "components.Settings.tautulliSettings": "<PERSON><PERSON><PERSON>", "components.Settings.toastTautulliSettingsFailure": "Valami hiba történt a Tautulli beállítások mentése közben.", "components.Settings.urlBase": "Alap URL", "components.Settings.validationUrl": "Érvényes URL-t kell megadnia", "components.Settings.validationUrlBaseLeadingSlash": "Az Alap URL perjellel kell, hogy kezdődjön", "components.Settings.validationUrlTrailingSlash": "Az URL-nek nem szabad perjellel végződnie", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Csatorna <PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "A Pushover értesítési beállításait nem si<PERSON>ült el<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "A Pushover értesítési beállításai sikeresen mentve lettek!", "components.NotificationTypeSelector.issueresolvedDescription": "Értesítések küldése problémák megoldása esetén.", "components.NotificationTypeSelector.issuecreatedDescription": "Értesítések küldése problémák bejelentésekor.", "components.NotificationTypeSelector.issueresolved": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.approve": "<PERSON><PERSON><PERSON><PERSON>", "i18n.resolved": "<PERSON><PERSON><PERSON>", "components.Settings.RadarrModal.inCinemas": "Mo<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.editJobSchedule": "Feladat Módosítása", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "<PERSON>j frek<PERSON>", "components.RequestBlock.languageprofile": "Nyelv Profil", "components.RequestModal.requestApproved": "<PERSON><PERSON><PERSON><PERSON> <strong>{title}</strong> elfogadva!", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "A Gotify értesítési beállításait nem si<PERSON>ült elmenteni.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Gotify teszt értesítés elküldve!", "components.Settings.Notifications.NotificationsGotify.token": "Alkalmazás Token", "components.Settings.Notifications.NotificationsGotify.url": "Szerver URL", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "<PERSON> kell adnia egy alkalmazás <PERSON>t", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "Az URL nem végződhet perjellel", "components.Settings.Notifications.enableMentions": "Említések Engedélyezése", "components.Settings.RadarrModal.announced": "Bejelentve", "components.Settings.SettingsAbout.appDataPath": "Adat Kö<PERSON>vtár", "components.Settings.SettingsAbout.runningDevelop": "<PERSON><PERSON> a<PERSON> <code>develop</code> <PERSON><PERSON><PERSON><PERSON>, ami csak azoknak a<PERSON>, akik hozzájárulnak a fejlesztéshez vagy segítenek a tesztelésben.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Feladat sikeresen szerkesztve!", "components.Settings.tautulliSettingsDescription": "Opcionálisan konfigurálja a Tautulli szerver beállításait. Az Jellyseerr lekérdezi a Plex média nézési előzményeinek adatait a Tautulliból.", "components.Settings.toastTautulliSettingsSuccess": "<PERSON><PERSON>lli beállítások sikeresen mentve!", "components.Settings.validationApiKey": "<PERSON> kell adnia egy API-kulcsot", "components.Settings.validationUrlBaseTrailingSlash": "Az Alap URL-nek nem szabad perjellel végződnie", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "A Pushbullet értesítési beállításait nem si<PERSON>ült el<PERSON>.", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Meg kell adnia egy hozzáférési tokent", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Érvényes alkalmazás tokent kell megadnia", "components.UserProfile.recentlywatched": "Mostanában Nézett", "i18n.importing": "Import<PERSON><PERSON><PERSON>…", "i18n.import": "Importálás", "components.PermissionEdit.viewissues": "Problémák <PERSON>", "components.Settings.externalUrl": "Külső URL", "components.MovieDetails.physicalrelease": "Fizikai kiadás", "components.MovieDetails.digitalrelease": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestCard.editrequest": "Kérelem szerkesztése", "components.Discover.DiscoverWatchlist.discoverwatchlist": "", "components.PermissionEdit.autorequest": "Automatikus <PERSON>", "components.NotificationTypeSelector.mediaautorequested": "A kérelem automatikusan elküldve", "components.MovieDetails.reportissue": "Prob<PERSON><PERSON> be<PERSON>", "components.PermissionEdit.autorequestMovies": "Filmek automatikus kérése", "components.NotificationTypeSelector.issuecomment": "Probléma Megjegyzés", "components.PermissionEdit.autorequestSeries": "Automatikus kérés sorozatok", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Sorozatkérések", "components.MovieDetails.managemovie": "Film kezelése", "components.MovieDetails.rtaudiencescore": "Rotten Tomatoes közönségpontszám", "components.MovieDetails.tmdbuserscore": "TMDB felhasználói pontszám", "components.RequestBlock.delete": "<PERSON><PERSON><PERSON>em törlése", "components.RequestBlock.edit": "Kérelem szerkesztése", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.lastmodifiedby": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.RequestBlock.requestdate": "Igénylés <PERSON>", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Filmkérések", "components.Layout.UserDropdown.requests": "K<PERSON><PERSON>sek", "components.RequestModal.requestcollectiontitle": "Gyűjtemény <PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Aktuális frek<PERSON>", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON>-t <PERSON><PERSON><PERSON> kell in<PERSON>, hogy a beállítás módosításai életbe lépjenek", "components.StatusBadge.managemedia": "{mediaType} kezelése", "components.StatusBadge.openinarr": "Nyitás itt: {arr}", "components.StatusChecker.appUpdatedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, katti<PERSON><PERSON> az alábbi gombra az alkalmazás újratöltéséhez.", "components.TitleCard.mediaerror": "{mediaType} <PERSON><PERSON>", "components.TvDetails.Season.somethingwentwrong": "Hiba történt az évadadatok lekérésekor.", "components.StatusBadge.playonplex": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.TvDetails.rtaudiencescore": "Rotten Tomatoes közönségpontszám", "components.TvDetails.seasonstitle": "Évadok", "components.TvDetails.seasonnumber": "{seasonNumber} Évad", "components.TvDetails.tmdbuserscore": "TMDB felhasználói pontszám", "components.UserProfile.emptywatchlist": "Itt jelennek meg a <PlexWatchlistSupportLink>Plex figyelőlistájához</PlexWatchlistSupportLink> hozz<PERSON><PERSON><PERSON> médiák.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "Felhasználói vagy <PERSON>", "components.StatusChecker.appUpdated": "{applicationTitle} Frissítve", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "A(z) <strong>{title}</strong> problémajelentése si<PERSON>esen el<PERSON>üldve!", "components.PermissionEdit.viewrecentDescription": "Adjon engedélyt a nemrég hozzáadott médialista megtekintéséhez.", "components.MovieDetails.theatricalrelease": "Színházi kiadás", "components.NotificationTypeSelector.mediaautorequestedDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>, ha a rendszer automatikusan új médiakérelmeket küld a Plex figyelőlistáján szereplő elemekhez.", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Plex figyelőlista szinkronizálása", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Filmek automatikus kérése a <PlexWatchlistSupportLink>Plex figyelőlistán</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Automatikus sorozat kérés", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Filmek automatikus kérése", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Sorozatok automatikus kérése a <PlexWatchlistSupportLink>Plex figyelőlistán</PlexWatchlistSupportLink>", "components.PermissionEdit.viewwatchlists": "Plex figyelőlisták megtekintése", "components.Settings.experimentalTooltip": "A beállítás engedélyezése váratlan alkalmazási viselkedést eredményezhet", "components.Settings.deleteServer": "T<PERSON>r<PERSON><PERSON><PERSON> a {serverType} szervert", "components.StatusChecker.reloadApp": "{applicationTitle} újratöltése", "components.StatusChecker.restartRequired": "Szerver újraindítása szükséges", "components.StatusChecker.restartRequiredDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, indítsa újra a szervert a frissített beállítások alkalmazásához.", "components.TitleCard.cleardata": "Adatok törlése", "components.UserProfile.plexwatchlist": "Plex figyelőlista", "components.TvDetails.manageseries": "Sorozatok k<PERSON>ése", "components.Settings.advancedTooltip": "A beállítás helytelen konfigurálása a funkció meghibásodását eredményezheti", "components.Discover.DiscoverWatchlist.watchlist": "Plex figyelőlista", "components.Settings.SettingsLogs.viewdetails": "Részletek megtekintése", "components.TvDetails.reportissue": "Prob<PERSON><PERSON> be<PERSON>", "components.PermissionEdit.viewwatchlistsDescription": "Adjon engedélyt más felhasználók Plex figyelőlistájának megtekintéséhez.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Az Ön 30 karakteres <UsersGroupsLink>felhasználó- vagy csoportazonosítója</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "<PERSON> kell adnia egy érvényes felhasználói vagy cso<PERSON>", "components.Discover.plexwatchlist": "Az Ön Plex figyelőlistája", "components.RequestModal.SearchByNameModal.nomatches": "<PERSON>em tal<PERSON>unk megfelelőt ehhez a sorozathoz.", "components.RequestModal.requestmovie4ktitle": "Film kérése 4K-ban", "components.RequestModal.requestmovietitle": "Film kérése", "components.RequestModal.requestseries4ktitle": "Sorozat kérése 4K-ban", "components.Discover.emptywatchlist": "Itt jelennek meg a <PlexWatchlistSupportLink>Plex figyelőlistájához</PlexWatchlistSupportLink> hozz<PERSON><PERSON><PERSON> médiák.", "components.RequestModal.requestseriestitle": "So<PERSON>zat kérelem", "components.PermissionEdit.autorequestDescription": "Adjon engedélyt a nem 4K-s mé<PERSON><PERSON><PERSON> vonat<PERSON><PERSON><PERSON> kérések automatikus benyújtására a Plex figyelőlistán keresztül.", "components.PermissionEdit.autorequestMoviesDescription": "Adjon engedélyt nem 4K-s filmekre vonatkozó kérelmek automatikus benyújtására a Plex figyelőlistán keresztül.", "components.AirDateBadge.airedrelative": "<PERSON><PERSON> {relativeTime}", "components.AirDateBadge.airsrelative": "Sug<PERSON><PERSON><PERSON> {relativeTime}", "components.PermissionEdit.autorequestSeriesDescription": "Adjon engedélyt a nem 4K sorozatokra vonatkozó kérelmek automatikus benyújtására a Plex figyelőlistán keresztül.", "components.RequestModal.requestseasons4k": "<PERSON><PERSON><PERSON><PERSON> {seasonCount} {seasonCount, plural, one {Season} other {Seasons}} 4K-ban", "components.TitleCard.tmdbid": "TMDB azonosító", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episode} other {# Episodes}}", "components.TvDetails.rtcriticsscore": "Rotten Tomatoes Tomatomérő", "components.TvDetails.status4k": "4K {status}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Minden {jobScheduleMinutes, plural, one {minute} other {{jobScheduleMinutes} minutes}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Minden {jobScheduleHours, plural, one {hour} other {{jobScheduleHours} hours}}", "components.PermissionEdit.viewrecent": "Nemrég hozzáadott megtekintése", "components.TitleCard.tvdbid": "TheTVDB azonosító", "components.MovieDetails.productioncountries": "Gyártás {countryCount, plural, one {Country} other {Countries}}", "components.TvDetails.productioncountries": "Gyártás {countryCount, plural, one {Country} other {Countries}}", "components.UserList.newplexsigninenabled": "Az <strong>Új Plex bejelentkezés engedélyezése</strong> be<PERSON><PERSON>í<PERSON><PERSON> jelenleg engedélyezve van. A könyvtár-hozzáféréssel rendelkező Plex-felhasználókat nem kell importálni a bejelentkezéshez.", "components.MovieDetails.rtcriticsscore": "Rotten Tomatoes Tomatomérő", "components.RequestBlock.requestedby": "<PERSON><PERSON><PERSON>", "components.RequestModal.requestmovies": "<PERSON><PERSON><PERSON>s {count} {count, plural, one {Movie} other {Movies}}", "components.RequestModal.requestmovies4k": "<PERSON><PERSON><PERSON><PERSON> {count} {count, plural, one {Movie} other {Movies}} 4K-ban", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Alkalmazás regisztrálása</ApplicationRegistrationLink> a(z) {applicationTitle} alkalmazáshoz", "components.RequestCard.tmdbid": "TMDB azonosító", "components.RequestCard.tvdbid": "TheTVDB azonosító", "components.RequestList.RequestItem.tmdbid": "TMDB azonosító", "components.RequestList.RequestItem.tvdbid": "TheTVDB azonosító", "components.RequestModal.requestcollection4ktitle": "Gyűjtemény kérés 4K-ban", "components.Discover.CreateSlider.editsuccess": "Szerkesztett csúszka és mentett felfedezés testreszabási beállítások.", "components.Settings.SettingsJobsCache.imagecache": "Képgyorsít<PERSON>tá<PERSON>", "components.Settings.SettingsJobsCache.imagecachesize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.slidernameplaceholder": "Csúszka neve", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Első adás dátuma növekvő", "components.Discover.DiscoverMovies.sortPopularityDesc": "Népszerűség csökkenő", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Képgyors<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "i18n.restartRequired": "Újraindítás szükséges", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "TMDB minősítés növekvő", "components.Discover.FilterSlideover.clearfilters": "Aktív szűrők törlése", "components.Discover.CreateSlider.searchStudios": "Stúdiók keresése…", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Megjelenés dátuma csökkenő sorrendben", "components.Discover.CreateSlider.providetmdbnetwork": "Adja meg a TMDB hálózati azonosítót", "components.Settings.SettingsMain.applicationTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "components.Discover.CreateSlider.addfail": "<PERSON><PERSON>j csúszkát létrehozni.", "components.Discover.DiscoverMovies.sortPopularityAsc": "Növekvő népszerűség", "components.Discover.CreateSlider.needresults": "Legalább 1 eredménynek kell lennie.", "components.Discover.CreateSlider.addcustomslider": "Egyéni csúszka létrehozása", "components.Discover.DiscoverTv.sortPopularityAsc": "Növekvő népszerűség", "components.Discover.CreateSlider.editSlider": "Csúszka szerkesztése", "components.Discover.CreateSlider.validationDatarequired": "<PERSON> kell adnia egy adatért<PERSON>.", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Első adási dátum csökkenő", "components.Discover.DiscoverTv.discovertv": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON><PERSON> törölni a csúszkát.", "components.Discover.CreateSlider.providetmdbstudio": "Adja meg a TMDB Studio azonosítóját", "components.Settings.SettingsMain.cacheImages": "Képgyors<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> engedélyez<PERSON>e", "components.Discover.DiscoverTv.sortPopularityDesc": "Népszerűség csökkenő", "components.Discover.CreateSlider.searchGenres": "Műfajok keresése…", "components.Discover.CreateSlider.editfail": "<PERSON><PERSON> szerkeszteni a csúszkát.", "components.Discover.CreateSlider.starttyping": "Gépelés megkezdése a kereséshez.", "components.Discover.FilterSlideover.filters": "Szűrők", "components.Discover.DiscoverSliderEdit.enable": "Ka<PERSON><PERSON><PERSON><PERSON> be a láthatóságot", "components.Discover.CreateSlider.addSlider": "Csúszka hozzáadása", "components.Settings.SettingsJobsCache.imagecachecount": "Képek gyorsítótárban", "components.Discover.CreateSlider.providetmdbsearch": "Adjon meg egy keresési le<PERSON>", "components.Discover.CreateSlider.providetmdbkeywordid": "Adjon meg egy TMDB kulcsszóazonosítót", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Filmek", "components.Discover.CreateSlider.validationTitlerequired": "<PERSON> kell adnia a címet.", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Kiadási dátum Növekvő sorrendben", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Értesítési hang", "components.Discover.CreateSlider.nooptions": "<PERSON><PERSON><PERSON> er<PERSON>.", "components.Discover.CreateSlider.searchKeywords": "Kulcsszavak keresése…", "components.Discover.CreateSlider.addsuccess": "Új c<PERSON>úszk<PERSON> ho<PERSON> létre, és elmentette a felfedezés testreszabási beállításait.", "components.Discover.DiscoverSliderEdit.deletesuccess": "A csúszka sikeresen törölve.", "components.Discover.DiscoverMovies.discovermovies": "Filmek", "components.Discover.CreateSlider.providetmdbgenreid": "Adjon meg egy TMDB műfajazonosítót", "components.Discover.DiscoverSliderEdit.remove": "Távolítsa el", "components.Discover.PlexWatchlistSlider.emptywatchlist": "A hozzáadott média a <PlexWatchlistSupportLink>Plex figyelőlistáján</PlexWatchlistSupportLink> elérhető.", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} <PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "TMDB Értékelés Csökkenő", "components.Discover.DiscoverTv.sortTitleAsc": "Cím (A-Z) Növekvő", "components.Discover.DiscoverTv.sortTitleDesc": "Cím (Z-A) Csökkenő", "components.Discover.FilterSlideover.ratingText": "<PERSON>rt<PERSON>kelések {minValue} és {maxValue} között", "components.Discover.FilterSlideover.tmdbuservotecount": "TMDB Felhasználók szavazat szám", "components.Discover.FilterSlideover.releaseDate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.voteCount": "<PERSON>rt<PERSON>kelések s<PERSON>ma {minValue} és {maxValue} között", "components.Discover.FilterSlideover.originalLanguage": "Eredeti Nyelv", "components.Discover.PlexWatchlistSlider.plexwatchlist": "A te Plex figyelőlistád", "components.Discover.RecentlyAddedSlider.recentlyAdded": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.moviegenres": "Film műfajok", "components.Discover.DiscoverMovies.sortTitleAsc": "Cím (A-Z) Növekvő", "components.Discover.DiscoverMovies.sortTitleDesc": "Cím (Z-A) Csökkenő", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "TMDB Értékelés Növekvő", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "TMDB Értékelés Csökkenő", "components.Discover.FilterSlideover.firstAirDate": "Első adási dátum", "components.Discover.FilterSlideover.genres": "Műfajok", "components.Discover.FilterSlideover.keywords": "Kulcsszavak", "components.Discover.FilterSlideover.runtime": "Músoridő", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} perc<PERSON><PERSON>", "components.Discover.FilterSlideover.studio": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.tmdbuserscore": "TMDB Felhasználói Értékelés", "components.Discover.FilterSlideover.streamingservices": "Streaming Szolgáltatók"}