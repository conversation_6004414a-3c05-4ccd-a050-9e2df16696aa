# Jellyseerr Documentation

Jellyseerr docs is built using [Docusaurus](https://docusaurus.io/), a modern static website generator.

Jellyseerr docs will be available at [docs.jellyseerr.dev](https://docs.jellyseerr.dev).

### Installation

```
$ pnpm
```

### Local Development

```
$ pnpm start
```

This command starts a local development server and opens up a browser window. Most changes are reflected live without having to restart the server.

### Build

```
$ pnpm build
```

This command generates static content into the `build` directory and can be served using any static contents hosting service.
