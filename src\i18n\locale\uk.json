{"components.AirDateBadge.airedrelative": "Еф<PERSON>р {relativeTime}", "components.AirDateBadge.airsrelative": "Еф<PERSON>р {relativeTime}", "components.AppDataWarning.dockerVolumeMissingDescription": "Підключення тома <code>{appDataPath}</code> налаштовано неправильно. Всі дані будуть видалені при зупинці або перезапуску контейнера.", "components.CollectionDetails.numberofmovies": "{count} фільмів", "components.CollectionDetails.overview": "Огляд", "components.CollectionDetails.requestcollection": "Запит на Колекцію", "components.CollectionDetails.requestcollection4k": "Запит на Колекцію в 4К", "components.Discover.DiscoverMovieGenre.genreMovies": "Фільми в жанрі \"{genre}\"", "components.Discover.DiscoverMovieLanguage.languageMovies": "Фільми мовою \"{language}\"", "components.Discover.DiscoverNetwork.networkSeries": "Серіали {network}", "components.Discover.DiscoverStudio.studioMovies": "Фільми {studio}", "components.Discover.DiscoverTvGenre.genreSeries": "Серіали в жанрі {genre}", "components.Discover.DiscoverTvLanguage.languageSeries": "Серіали мовою {language}", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Ваш список перегляду Plex", "components.Discover.DiscoverWatchlist.watchlist": "Список перегляду Plex", "components.Discover.MovieGenreList.moviegenres": "Фільми за жанрами", "components.Discover.MovieGenreSlider.moviegenres": "Фільми за жанрами", "components.Discover.NetworkSlider.networks": "Телеканали", "components.Discover.StudioSlider.studios": "Студії", "components.Discover.TvGenreList.seriesgenres": "Серіали за жанрами", "components.Discover.TvGenreSlider.tvgenres": "Серіали за жанрами", "components.Discover.discover": "Знайти щось нове", "components.Discover.emptywatchlist": "Тут з’являться медіафайли, додані до вашого <PlexWatchlistSupportLink>списку спостереження Plex</PlexWatchlistSupportLink>.", "components.Discover.plexwatchlist": "Ваш список перегляду Plex", "components.Discover.popularmovies": "Популярні фільми", "components.Discover.populartv": "Популярні серіали", "components.Discover.recentlyAdded": "Нещодавно додані", "components.Discover.recentrequests": "Останні запити", "components.Discover.trending": "У трендах", "components.Discover.upcoming": "Майбутні фільми", "components.Discover.upcomingmovies": "Майбутні фільми", "components.Discover.upcomingtv": "Майбутні серіали", "components.DownloadBlock.estimatedtime": "Приблизно {time}", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON><PERSON><PERSON> {seasonNumber} Епізод {episodeNumber}", "components.IssueDetails.IssueComment.areyousuredelete": "Ви впевнені, що хочете видалити цей коментар?", "components.IssueDetails.IssueComment.delete": "Видалити коментар", "components.IssueDetails.IssueComment.edit": "Редагувати коментар", "components.IssueDetails.IssueComment.postedby": "Опубліковано {relativeTime} користувачем {username}", "components.IssueDetails.IssueComment.postedbyedited": "Опубліковано {relativeTime} користувачем {username} (змінено)", "components.IssueDetails.IssueComment.validationComment": "Ви повинні ввести повідомлення", "components.IssueDetails.IssueDescription.deleteissue": "Видалити проблему", "components.IssueDetails.IssueDescription.description": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.edit": "Редагувати опис", "components.IssueDetails.allepisodes": "Всі епізоди", "components.IssueDetails.allseasons": "Всі сезони", "components.IssueDetails.closeissue": "Закрити проблему", "components.IssueDetails.closeissueandcomment": "Закрити з коментарем", "components.IssueDetails.commentplaceholder": "Додати коментар…", "components.IssueDetails.comments": "Коментарі", "components.IssueDetails.deleteissue": "Видалити проблему", "components.IssueDetails.deleteissueconfirm": "Ви впевнені, що хочете видалити цю проблему?", "components.IssueDetails.episode": "Епізод {episodeNumber}", "components.IssueDetails.issuepagetitle": "Проблема", "components.IssueDetails.issuetype": "Тип", "components.IssueDetails.lastupdated": "Останнє оновлення", "components.IssueDetails.leavecomment": "Коментар", "components.IssueDetails.nocomments": "Коментар<PERSON>в немає.", "components.IssueDetails.openedby": "#{issueId} відкрита {relativeTime} користувачем {username}", "components.IssueDetails.openin4karr": "Відкрити в 4К {arr}", "components.IssueDetails.openinarr": "Відкрити в {arr}", "components.IssueDetails.play4konplex": "Відтворити в {mediaServerName} у 4К", "components.IssueDetails.playonplex": "Відтворити в {mediaServerName}", "components.IssueDetails.problemepisode": "Зачеплений епізод", "components.IssueDetails.problemseason": "Зачеплений сезон", "components.IssueDetails.reopenissue": "Знову відкрити проблему", "components.IssueDetails.reopenissueandcomment": "Знову відкрити з коментарем", "components.IssueDetails.season": "Сезон {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Щось пішло не так при редагуванні опису проблеми.", "components.IssueDetails.toasteditdescriptionsuccess": "Опис проблеми успішно відредаговано!", "components.IssueDetails.toastissuedeleted": "Проблема успішно видалена!", "components.IssueDetails.toastissuedeletefailed": "Щось пішло не так при видаленні проблеми.", "components.IssueDetails.toaststatusupdated": "Статус проблеми успішно оновлено!", "components.IssueDetails.toaststatusupdatefailed": "Щось пішло не так при оновленні статусу проблеми.", "components.IssueDetails.unknownissuetype": "Невідомий", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {Епізод} other {Епізоди}}", "components.IssueList.IssueItem.issuestatus": "Статус", "components.IssueList.IssueItem.issuetype": "Тип", "components.IssueList.IssueItem.opened": "Відкрито", "components.IssueList.IssueItem.openeduserdate": "{date} користувачем {user}", "components.IssueList.IssueItem.problemepisode": "Зачеплений епізод", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.IssueList.IssueItem.unknownissuetype": "Невідомий", "components.IssueList.IssueItem.viewissue": "Переглянути проблему", "components.IssueList.issues": "Проблеми", "components.IssueList.showallissues": "Показати всі проблеми", "components.IssueList.sortAdded": "За датою додавання", "components.IssueList.sortModified": "За датою зміни", "components.IssueModal.CreateIssueModal.allepisodes": "Всі епізоди", "components.IssueModal.CreateIssueModal.allseasons": "Всі сезони", "components.IssueModal.CreateIssueModal.episode": "Епізод {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Додатково", "components.IssueModal.CreateIssueModal.problemepisode": "Зачеплений епізод", "components.IssueModal.CreateIssueModal.problemseason": "Зачеплений сезон", "components.IssueModal.CreateIssueModal.providedetail": "Будь ласка, надайте детальний опис проблеми, з якою ви зіткнулися.", "components.IssueModal.CreateIssueModal.reportissue": "Повідомити про проблему", "components.IssueModal.CreateIssueModal.season": "Сезон {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Надіслати проблему", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Щось пішло не так під час надсилання проблеми.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "Звіт про проблему для <strong>{title}</strong> успішно надіслано!", "components.IssueModal.CreateIssueModal.toastviewissue": "Переглянути проблему", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Ви повинні надати опис", "components.IssueModal.CreateIssueModal.whatswrong": "Що не так?", "components.IssueModal.issueAudio": "Ау<PERSON><PERSON><PERSON>", "components.IssueModal.issueOther": "Інше", "components.IssueModal.issueSubtitles": "Субтитри", "components.IssueModal.issueVideo": "Відео", "components.LanguageSelector.languageServerDefault": "За замовчуванням ({language})", "components.LanguageSelector.originalLanguageDefault": "Всі мови", "components.Layout.LanguagePicker.displaylanguage": "Мова інтерфейсу", "components.Layout.SearchInput.searchPlaceholder": "Пошук фільмів та серіалів", "components.Layout.Sidebar.dashboard": "Знайти щось нове", "components.Layout.Sidebar.issues": "Проблеми", "components.Layout.Sidebar.requests": "Запити", "components.Layout.Sidebar.settings": "Налаштування", "components.Layout.Sidebar.users": "Користувачі", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Запити на фільми", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Запити на сезони", "components.Layout.UserDropdown.myprofile": "Профіль", "components.Layout.UserDropdown.requests": "Запити", "components.Layout.UserDropdown.settings": "Налаштування", "components.Layout.UserDropdown.signout": "<PERSON>и<PERSON><PERSON>д", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {комміт} other {коммітів}} позаду", "components.Layout.VersionStatus.outofdate": "Застар<PERSON>ла", "components.Layout.VersionStatus.streamdevelop": "Версія Jellyseerr для розробки", "components.Layout.VersionStatus.streamstable": "Стаб<PERSON>льна версія Jellyseerr", "components.Login.email": "Адреса електронної пошти", "components.Login.forgotpassword": "Забули пароль?", "components.Login.loginerror": "Щось пішло не так при спробі виконати вхід.", "components.Login.password": "Пароль", "components.Login.signin": "Увійти", "components.Login.signingin": "Виконується вхід...", "components.Login.signinheader": "Увійдіть, щоб продовжити", "components.Login.signinwithoverseerr": "Використовуйте ваш обліковий запис {applicationTitle}", "components.Login.signinwithplex": "Використовуйте ваш обліковий запис Plex", "components.Login.validationemailrequired": "Ви повинні вказати дійсну адресу електронної пошти", "components.Login.validationpasswordrequired": "Ви повинні надати пароль", "components.ManageSlideOver.alltime": "Весь час", "components.ManageSlideOver.downloadstatus": "Завантаження", "components.ManageSlideOver.manageModalAdvanced": "Просунутий", "components.ManageSlideOver.manageModalClearMedia": "Очистити дані", "components.ManageSlideOver.manageModalClearMediaWarning": "* Це призведе до незворотного видалення всіх даних для цього {mediaType}а, включаючи будь-які запити. Якщо цей елемент існує у вашій бібліотеці {mediaServerName}, мультимедійна інформація про нього буде відтворена під час наступного сканування.", "components.ManageSlideOver.manageModalIssues": "Відкриті проблеми", "components.ManageSlideOver.manageModalMedia": "Media", "components.ManageSlideOver.manageModalMedia4k": "4K Media", "components.ManageSlideOver.manageModalNoRequests": "Запитів немає.", "components.ManageSlideOver.manageModalRequests": "Запити", "components.ManageSlideOver.manageModalTitle": "Управління {mediaType}ом", "components.ManageSlideOver.mark4kavailable": "Позначити як доступний у 4К", "components.ManageSlideOver.markallseasons4kavailable": "Позначити всі сезони як доступні в 4K", "components.ManageSlideOver.markallseasonsavailable": "Позначити всі сезони як доступні", "components.ManageSlideOver.markavailable": "Позначити як доступний", "components.ManageSlideOver.movie": "фільм", "components.ManageSlideOver.openarr": "Відкрити в {arr}", "components.ManageSlideOver.openarr4k": "Відкрити в 4К {arr}", "components.ManageSlideOver.opentautulli": "Відкрити в Tautulli", "components.ManageSlideOver.pastdays": "Останні {days, number} днів", "components.ManageSlideOver.playedby": "Переглядає", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {перегляд} other {переглядів}}", "components.ManageSlideOver.tvshow": "серіал", "components.MediaSlider.ShowMoreCard.seemore": "Подивитися більше", "components.MovieDetails.MovieCast.fullcast": "Повний акторський склад", "components.MovieDetails.MovieCrew.fullcrew": "Повна знімальна група", "components.MovieDetails.budget": "<PERSON>ю<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.cast": "У ролях", "components.MovieDetails.digitalrelease": "Цифровий випуск", "components.MovieDetails.managemovie": "Керувати фільмом", "components.MovieDetails.mark4kavailable": "Позначити як доступний у 4К", "components.MovieDetails.markavailable": "Позначити як доступний", "components.MovieDetails.originallanguage": "Мова оригіналу", "components.MovieDetails.originaltitle": "Назва оригіналу", "components.MovieDetails.overview": "Огляд", "components.MovieDetails.overviewunavailable": "Огляд недоступний.", "components.MovieDetails.physicalrelease": "Фізичний реліз", "components.MovieDetails.productioncountries": "{countryCount, plural, one {Країна} other {Країни}} виробництва", "components.MovieDetails.recommendations": "Рекомендації", "components.MovieDetails.releasedate": "{releaseCount, plural, one {Дата релізу} other {Дати релізу}}", "components.MovieDetails.reportissue": "Повідомити про проблему", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.rtaudiencescore": "Оцінка аудитор<PERSON><PERSON> Rotten Tomatoes", "components.MovieDetails.rtcriticsscore": "Томатометр Rotten Tomatoes", "components.MovieDetails.runtime": "{minutes} хвилин", "components.MovieDetails.showless": "Згорнути", "components.MovieDetails.showmore": "Розгорнути", "components.MovieDetails.similar": "Схожі фільми", "components.MovieDetails.streamingproviders": "Зараз транслюється", "components.MovieDetails.studio": "{studioCount, plural, one {Студія} other {Студії}}", "components.MovieDetails.theatricalrelease": "Вихід у кінотеатр", "components.MovieDetails.tmdbuserscore": "TMDB Оцінка користувача", "components.MovieDetails.viewfullcrew": "Подивитися повну знімальну групу", "components.MovieDetails.watchtrailer": "Дивити<PERSON>ь трейлер", "components.NotificationTypeSelector.adminissuecommentDescription": "Отримувати повідомлення, коли інші користувачі надсилають коментарі до проблем.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Отримувати повідомлення, коли проблеми відкриті заново іншими користувачами.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Отримувати повідомлення, коли проблеми вирішені іншими користувачами.", "components.NotificationTypeSelector.issuecomment": "Коментар до проблеми", "components.NotificationTypeSelector.issuecommentDescription": "Надсилати повідомлення, коли до проблем з'являються нові коментарі.", "components.NotificationTypeSelector.issuecreated": "Проблема опублікована", "components.NotificationTypeSelector.issuecreatedDescription": "Надсилати повідомлення, коли з'являються повідомлення про проблеми.", "components.NotificationTypeSelector.issuereopened": "Проблема відкрита знову", "components.NotificationTypeSelector.issuereopenedDescription": "Надсилати повідомлення, коли проблеми відкриті заново.", "components.NotificationTypeSelector.issueresolved": "Проблема вирішена", "components.NotificationTypeSelector.issueresolvedDescription": "Надсилати повідомлення, коли проблеми отримують рішення.", "components.NotificationTypeSelector.mediaAutoApproved": "Автоматичне схвалення медіа-запитів", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Надсилати повідомлення, коли користувачі надсилають нові медіа-запити, які схвалюються автоматично.", "components.NotificationTypeSelector.mediaapproved": "Схвалення медіа-запитів", "components.NotificationTypeSelector.mediaapprovedDescription": "Надсилати повідомлення, коли медіа-запити схвалюються вручну.", "components.NotificationTypeSelector.mediaautorequested": "Запит надіслано автоматично", "components.NotificationTypeSelector.mediaautorequestedDescription": "Отримуйте сповіщення, коли нові медіа-запити автоматично надсилаються для елементів у вашому списку перегляду Plex.", "components.NotificationTypeSelector.mediaavailable": "Доступні нові медіафайли", "components.NotificationTypeSelector.mediaavailableDescription": "Надсилати повідомлення, коли запитані медіафайли стають доступними.", "components.NotificationTypeSelector.mediadeclined": "Відхилення медіа-запитів", "components.NotificationTypeSelector.mediadeclinedDescription": "Надсилати повідомлення, коли медіа-запити відхиляються.", "components.NotificationTypeSelector.mediafailed": "Помилки при додаванні медіа-запитів", "components.NotificationTypeSelector.mediafailedDescription": "Відправляти повідомлення, коли медіа-запити не вдається додати до Radarr або Sonarr.", "components.NotificationTypeSelector.mediarequested": "Запити медіафайлів", "components.NotificationTypeSelector.mediarequestedDescription": "Надсилати повідомлення, коли користувачі надсилають нові медіа-запити, які вимагають схвалення.", "components.NotificationTypeSelector.notificationTypes": "Типи повідомлень", "components.NotificationTypeSelector.userissuecommentDescription": "Отримувати повідомлення, коли до проблем, про які ви повідомили, з'являються нові коментарі.", "components.NotificationTypeSelector.userissuecreatedDescription": "Отримувати повідомлення, коли інші користувачі повідомляють про проблеми.", "components.NotificationTypeSelector.userissuereopenedDescription": "Отримувати повідомлення, коли проблеми, про які ви повідомили, будуть відкриті заново.", "components.NotificationTypeSelector.userissueresolvedDescription": "Отримувати повідомлення, коли проблеми, про які ви повідомили, отримують рішення.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Отримувати повідомлення, коли інші користувачі надсилають нові медіа-запити, які схвалюються автоматично.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Отримувати повідомлення, коли ваші медіа-запити отримують схвалення.", "components.NotificationTypeSelector.usermediaavailableDescription": "Отримувати сповіщення, коли медіа, на які ви створили запит, стають доступними.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Отримувати повідомлення, коли медіа-запити відхиляються.", "components.NotificationTypeSelector.usermediafailedDescription": "Отримувати повідомлення, коли медіа-запити не вдається додати до Radarr або Sonarr.", "components.NotificationTypeSelector.usermediarequestedDescription": "Отримувати повідомлення, коли інші користувачі надсилають нові медіа-запити, які вимагають схвалення.", "components.PermissionEdit.admin": "Адміністратор", "components.PermissionEdit.adminDescription": "Адміністратор має повний доступ. Ігнорує всі інші налаштування дозволів.", "components.PermissionEdit.advancedrequest": "Розширені запити", "components.PermissionEdit.advancedrequestDescription": "Надати дозвіл на зміну додаткових параметрів запиту.", "components.PermissionEdit.autoapprove": "Автоматичне схвалення", "components.PermissionEdit.autoapprove4k": "Автоматичне схвалення 4К", "components.PermissionEdit.autoapprove4kDescription": "Надати дозвіл на автоматичне схвалення всіх 4К медіа-запитів.", "components.PermissionEdit.autoapprove4kMovies": "Автоматичне схвалення фільмів 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Надати дозвіл на автоматичне схвалення 4К фільмів.", "components.PermissionEdit.autoapprove4kSeries": "Автоматичне схвалення 4К серіалів", "components.PermissionEdit.autoapprove4kSeriesDescription": "Надати дозвіл на автоматичне схвалення 4К серіалів.", "components.PermissionEdit.autoapproveDescription": "Надати дозвіл на автоматичне схвалення всіх медіа-запитів, відмінних від 4К.", "components.PermissionEdit.autoapproveMovies": "Автоматичне схвалення фільмів", "components.PermissionEdit.autoapproveMoviesDescription": "Надати дозвіл на автоматичне схвалення всіх фільмів, відмінних від 4К.", "components.PermissionEdit.autoapproveSeries": "Автоматичне схвалення серіалів", "components.PermissionEdit.autoapproveSeriesDescription": "Надати дозвіл на автоматичне схвалення всіх серіалів, відмінних від 4K.", "components.PermissionEdit.autorequest": "Автоматичний запит", "components.PermissionEdit.autorequestDescription": "Надайте дозвіл на автоматичне надсилання запитів на медіафайли, відмінні від 4K, через список перегляду Plex.", "components.PermissionEdit.autorequestMovies": "Автоматичний запит фільмів", "components.PermissionEdit.autorequestMoviesDescription": "Надайте дозвіл на автоматичне надсилання запитів на фільми, відмінні від 4K, через список перегляду Plex.", "components.PermissionEdit.autorequestSeries": "Автоматичний запит Серіалів", "components.PermissionEdit.autorequestSeriesDescription": "Надайте дозвіл на автоматичне надсилання запитів на серіали, відмінні від 4K, через список перегляду Plex.", "components.PermissionEdit.createissues": "Повідомлення про проблеми", "components.PermissionEdit.createissuesDescription": "Надати дозвіл на повідомлення про проблеми з медіафайлами.", "components.PermissionEdit.manageissues": "Управління проблемами", "components.PermissionEdit.manageissuesDescription": "Надати дозвіл на керування проблемами з медіафайлами.", "components.PermissionEdit.managerequests": "Керування запитами", "components.PermissionEdit.managerequestsDescription": "Надати дозвіл на керування медіа-запитами. Всі запити користувача, що має цю роздільну здатність, будуть схвалюватися автоматично.", "components.PermissionEdit.request": "Запити", "components.PermissionEdit.request4k": "Запити 4K", "components.PermissionEdit.request4kDescription": "Надати дозвіл на надсилання запитів медіафайлів у 4К.", "components.PermissionEdit.request4kMovies": "Запити фільмів у 4К", "components.PermissionEdit.request4kMoviesDescription": "Надати дозвіл на надсилання запитів фільмів у 4К.", "components.PermissionEdit.request4kTv": "Запити серіалів у 4К", "components.PermissionEdit.request4kTvDescription": "Надати дозвіл на надсилання запитів серіалів у 4К.", "components.PermissionEdit.requestDescription": "Надати дозвіл на надсилання запитів усіх медіафайлів, відмінних від 4К.", "components.PermissionEdit.requestMovies": "Запити фільмів", "components.PermissionEdit.requestMoviesDescription": "Надати дозвіл на надсилання запитів усіх фільмів, відмінних від 4К.", "components.PermissionEdit.requestTv": "Запити серіалів", "components.PermissionEdit.requestTvDescription": "Надати дозвіл на надсилання запитів усіх серіалів, відмінних від 4К.", "components.PermissionEdit.users": "Керування користувачами", "components.PermissionEdit.usersDescription": "Надати дозвіл на керування користувачами. Користувачі з цим дозволом не можуть надавати права адміністратора та редагувати користувачів, які є адміністраторами.", "components.PermissionEdit.viewissues": "Перегляд проблем", "components.PermissionEdit.viewissuesDescription": "Надати дозвіл на перегляд проблем з медіафайлами, про які повідомили інші користувачі.", "components.PermissionEdit.viewrecent": "Переглянути нещодавно додані", "components.PermissionEdit.viewrecentDescription": "Надайте дозвіл на перегляд списку нещодавно доданих медіа.", "components.PermissionEdit.viewrequests": "Перегляд запитів", "components.PermissionEdit.viewrequestsDescription": "Надати дозвіл на перегляд медіа-запитів, надісланих іншими користувачами.", "components.PermissionEdit.viewwatchlists": "Перегляд списків переглядів Plex", "components.PermissionEdit.viewwatchlistsDescription": "Надайте дозвіл на перегляд списків перегляду Plex інших користувачів.", "components.PersonDetails.alsoknownas": "Також відомий(а) як: {names}", "components.PersonDetails.appearsin": "Появи у фільмах та серіалах", "components.PersonDetails.ascharacter": "в ролі {character}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.crewmember": "У складі знімальної групи", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.QuotaSelector.days": "{count, plural, one {день} other {днів}}", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{movies} на {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.movies": "{count, plural, one {фільм} other {фільми}}", "components.QuotaSelector.seasons": "{count, plural, one {сезон} other {сезони}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{seasons} на {quotaDays} {days}</quotaUnits>", "components.QuotaSelector.unlimited": "Необмежено", "components.RegionSelector.regionDefault": "Всі регіони", "components.RegionSelector.regionServerDefault": "За замовчуванням ({region})", "components.RequestBlock.approve": "Підтвердити запит", "components.RequestBlock.decline": "Від<PERSON>илити запит", "components.RequestBlock.delete": "Видалити запит", "components.RequestBlock.edit": "Редагувати запит", "components.RequestBlock.languageprofile": "Мовний профіль", "components.RequestBlock.lastmodifiedby": "Востаннє змінено", "components.RequestBlock.profilechanged": "Профіль якості", "components.RequestBlock.requestdate": "Дата запиту", "components.RequestBlock.requestedby": "З проханням", "components.RequestBlock.requestoverrides": "Перевизначення запиту", "components.RequestBlock.rootfolder": "Кореневий каталог", "components.RequestBlock.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.RequestBlock.server": "Сервер-одер<PERSON><PERSON>в<PERSON>ч", "components.RequestButton.approve4krequests": "Схвалити {requestCount, plural, one {4К запит} other {{requestCount} 4К запиту(ів)}}", "components.RequestButton.approverequest": "Схвалити запит", "components.RequestButton.approverequest4k": "Схвалити 4К запит", "components.RequestButton.approverequests": "Схвалити {requestCount, plural, one {запит} other {{requestCount} запиту(ів)}}", "components.RequestButton.decline4krequests": "Відхилити {requestCount, plural, one {4К запит}} other {{requestCount} 4К запиту(ів)}}", "components.RequestButton.declinerequest": "Від<PERSON>илити запит", "components.RequestButton.declinerequest4k": "Відхилити 4К запит", "components.RequestButton.declinerequests": "Відхилити {requestCount, plural, one {запит} other {{requestCount} запиту(ів)}}", "components.RequestButton.requestmore": "Створити більше запитів", "components.RequestButton.requestmore4k": "Створити більше запитів у 4К", "components.RequestButton.viewrequest": "Подивитися запит", "components.RequestButton.viewrequest4k": "Подивитися 4К запит", "components.RequestCard.approverequest": "Підтвердити запит", "components.RequestCard.cancelrequest": "Скасувати запит", "components.RequestCard.declinerequest": "Від<PERSON>илити запит", "components.RequestCard.deleterequest": "Видалити запит", "components.RequestCard.editrequest": "Редагувати запит", "components.RequestCard.failedretry": "Щось пішло не так при спробі повторити запит.", "components.RequestCard.mediaerror": "{mediaType} Не знайдено", "components.RequestCard.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.RequestCard.tmdbid": "TMDB ID", "components.RequestCard.tvdbid": "TheTVDB ID", "components.RequestCard.unknowntitle": "Невідома назва", "components.RequestList.RequestItem.cancelRequest": "Скасувати запит", "components.RequestList.RequestItem.deleterequest": "Видалити запит", "components.RequestList.RequestItem.editrequest": "Редагувати запит", "components.RequestList.RequestItem.failedretry": "Щось пішло не так при спробі повторити запит.", "components.RequestList.RequestItem.mediaerror": "{mediaType} Не знайдено", "components.RequestList.RequestItem.modified": "Змінено", "components.RequestList.RequestItem.modifieduserdate": "{date} користувачем {user}", "components.RequestList.RequestItem.requested": "Запрошений", "components.RequestList.RequestItem.requesteddate": "Запрошений", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Сезон} other {Сезони}}", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "TheTVDB ID", "components.RequestList.RequestItem.unknowntitle": "Невідома назва", "components.RequestList.requests": "Запити", "components.RequestList.showallrequests": "Показати всі запити", "components.RequestList.sortAdded": "За датою", "components.RequestList.sortModified": "Остання зміна", "components.RequestModal.AdvancedRequester.advancedoptions": "Розширені налаштування", "components.RequestModal.AdvancedRequester.animenote": "* Цей серіал - аніме.", "components.RequestModal.AdvancedRequester.default": "{name} (за замовчуванням)", "components.RequestModal.AdvancedRequester.destinationserver": "Сервер-одер<PERSON><PERSON>в<PERSON>ч", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestModal.AdvancedRequester.languageprofile": "Мовний профіль", "components.RequestModal.AdvancedRequester.notagoptions": "Тегов немає.", "components.RequestModal.AdvancedRequester.qualityprofile": "Профіль якості", "components.RequestModal.AdvancedRequester.requestas": "Запитати як", "components.RequestModal.AdvancedRequester.rootfolder": "Кореневий каталог", "components.RequestModal.AdvancedRequester.selecttags": "Вибрати теги", "components.RequestModal.AdvancedRequester.tags": "Теги", "components.RequestModal.QuotaDisplay.allowedRequests": "Вам дозволено запитувати <strong>{limit}</strong> {type} кожні <strong>{days}</strong> днів.", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Цьому користувачеві дозволено запитувати <strong>{limit}</strong> {type} кожні <strong>{days}</strong> днів.", "components.RequestModal.QuotaDisplay.movie": "фільм", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {фільм} other {фільмів}}", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "Залишилося недостатньо запитів на сезони", "components.RequestModal.QuotaDisplay.quotaLink": "Ви можете переглянути зведення ваших обмежень на кількість запитів на <ProfileLink>сторінці вашого профілю</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Ви можете переглянути зведення обмежень на кількість запитів цього користувача на <ProfileLink>сторінці його профілю</ProfileLink>.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {Запитів на {type} не залишилося} other {Залишилось <strong>#</strong> запити(ів) на {type}}}", "components.RequestModal.QuotaDisplay.requiredquota": "Вам необхідно мати принаймні <strong>{seasons}</strong> {seasons, plural, one {запит на сезони} other {запити(ів) на сезони}} для того, щоб надіслати запит на цей серіал.", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Цьому користувачеві необхідно мати принаймні <strong>{seasons}</strong> {seasons, plural, one {запит на сезони} other {запити(ів) на сезони}} для того, щоб надіслати запит на цей серіал.", "components.RequestModal.QuotaDisplay.season": "сезон", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {сезон} other {сезонів}}", "components.RequestModal.SearchByNameModal.nomatches": "Нам не вдалося знайти відповідність для цього серіалу.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "Нам не вдалося автоматично знайти цей серіал. Будь ласка, виберіть правильний збіг зі списку нижче.", "components.RequestModal.alreadyrequested": "Вже запрошений", "components.RequestModal.approve": "Схвалити запит", "components.RequestModal.autoapproval": "Автоматичне схвалення", "components.RequestModal.cancel": "Скасувати запит", "components.RequestModal.edit": "Редагувати запит", "components.RequestModal.errorediting": "Щось пішло не так під час редагування запиту.", "components.RequestModal.numberofepisodes": "# епізодів", "components.RequestModal.pending4krequest": "Очікуючий запит в 4К", "components.RequestModal.pendingapproval": "Ваш запит чекає схвалення.", "components.RequestModal.pendingrequest": "Очік<PERSON>ю<PERSON>ий запит", "components.RequestModal.requestApproved": "Запит на <strong>{title}</strong> схвалений!", "components.RequestModal.requestCancel": "Запит на <strong>{title}</strong> скасовано.", "components.RequestModal.requestSuccess": "<strong>{title}</strong> успішно запрошений!", "components.RequestModal.requestadmin": "Цей запит буде схвалено автоматично.", "components.RequestModal.requestcancelled": "Запит на <strong>{title}</strong> скасовано.", "components.RequestModal.requestcollection4ktitle": "Запит на колекцію в 4K", "components.RequestModal.requestcollectiontitle": "<PERSON><PERSON><PERSON><PERSON> за<PERSON>и<PERSON><PERSON>в", "components.RequestModal.requestedited": "Запит на <strong>{title}</strong> успішно відредаговано!", "components.RequestModal.requesterror": "Щось пішло не так під час надсилання запиту.", "components.RequestModal.requestfrom": "Запит користувача {username} очікує схвалення.", "components.RequestModal.requestmovie4ktitle": "Надіслати запит на фільм у 4K", "components.RequestModal.requestmovies": "Запит {count} {count, plural, one {фільма} other {фільмів}}", "components.RequestModal.requestmovies4k": "Запит {count} {count, plural, one {фільма} other {фільмів}} у 4К", "components.RequestModal.requestmovietitle": "Запит на фільм", "components.RequestModal.requestseasons": "Запит {seasonCount} {seasonCount, plural, one {сезон} other {сезони(ів)}}", "components.RequestModal.requestseasons4k": "Запит {seasonCount} {seasonCount, plural, one {сезону} other {сезонів}} у 4К", "components.RequestModal.requestseries4ktitle": "Запит на серіал у 4K", "components.RequestModal.requestseriestitle": "Запит на серіал", "components.RequestModal.season": "Сезон", "components.RequestModal.seasonnumber": "Сезон {number}", "components.RequestModal.selectmovies": "Виберіть фільм(и)", "components.RequestModal.selectseason": "Виберіть сезон(и)", "components.ResetPassword.confirmpassword": "Підтвердити пароль", "components.ResetPassword.email": "Адреса електронної пошти", "components.ResetPassword.emailresetlink": "Надіслати посилання для відновлення електронною поштою", "components.ResetPassword.gobacklogin": "Повернутися до сторінки входу", "components.ResetPassword.password": "Пароль", "components.ResetPassword.passwordreset": "Скинути пароль", "components.ResetPassword.requestresetlinksuccessmessage": "Посилання для скидання пароля буде надіслано на вказану адресу електронної пошти, якщо вона пов'язана з дійсним користувачем.", "components.ResetPassword.resetpassword": "Скидання пароля", "components.ResetPassword.resetpasswordsuccessmessage": "Пароль скинутий успішно!", "components.ResetPassword.validationemailrequired": "Ви повинні вказати дійсну адресу електронної пошти", "components.ResetPassword.validationpasswordmatch": "Паролі повинні збігатися", "components.ResetPassword.validationpasswordminchars": "Пароль занадто короткий: він повинен містити не менше 8 символів", "components.ResetPassword.validationpasswordrequired": "Ви повинні надати пароль", "components.Search.search": "По<PERSON><PERSON>к", "components.Search.searchresults": "Результати пошуку", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Увімкнути агента", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "Не вдалося зберегти налаштування сповіщень Gotify.", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "Налаштування сповіщень Gotify успішно збережено!", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "Не вдалося надіслати тестове сповіщення Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Надсилання тестового сповіщення Gotify…", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "Тестове сповіщення Gotify надіслано!", "components.Settings.Notifications.NotificationsGotify.token": "Маркер програми", "components.Settings.Notifications.NotificationsGotify.url": "Сервер URL", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Ви повинні надати маркер програми", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Необхідно вибрати принаймні один тип сповіщення", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Ви повинні надати дійсну URL-адресу", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "URL-адреса не має закінчуватися косою рискою", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Активувати службу", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Назва профілю", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Потрібно лише в тому випадку, якщо не використовується профіль <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Не вдалося зберегти налаштування повідомлень LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "Налаштування повідомлень LunaSea успішно збережено!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Не вдалося надіслати тестове повідомлення до LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Надсилання тестового повідомлення в LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "Тестове повідомлення надіслано до LunaSea!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL веб-перехоплювача", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "<LunaSeaLink>URL веб-перехоплювача для повідомлень</LunaSeaLink> на основі користувача або пристрою", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Токен доступу", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Створіть токен у <PushbulletSettingsLink>налаштуваннях облікового запису</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Активувати службу", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Тег каналу", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Не вдалося зберегти налаштування сповіщень Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "Налаштування сповіщень Pushbullet успішно збережено!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Не вдалося надіслати тестове повідомлення до Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Надсилання тестового повідомлення в Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "Тестове повідомлення надіслано в Pushbullet!", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Ви повинні надати токен доступу", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.NotificationsPushover.accessToken": "Токен API програми", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Зареєструйте програму</ApplicationRegistrationLink> для використання з Jellyseerr", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Активувати службу", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "Не вдалося зберегти налаштування сповіщень Pushover.", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "Параметри сповіщень Pushover успішно збережені!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Не вдалося надіслати тестове повідомлення Pushover.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Надсилання тестового повідомлення Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "Тестове повідомлення надіслано в Pushover!", "components.Settings.Notifications.NotificationsPushover.userToken": "К<PERSON><PERSON>ч користувача або групи", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Ваш тридцятизначний <UsersGroupsLink>ідентифікатор користувача або групи</UsersGroupsLink>", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Ви повинні надати дійсний токен програми", "components.Settings.Notifications.NotificationsPushover.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Ви повинні надати дійсний ключ користувача або групи", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Активувати службу", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Не вдалося зберегти налаштування повідомлень Slack.", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "Налаштування повідомлень Slack успішно збережено!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Не вдалося надіслати тестове повідомлення до Slack.", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Надсилання тестового повідомлення у Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "Тестове повідомлення надіслано до Slack!", "components.Settings.Notifications.NotificationsSlack.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL веб-перехоплювача", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Створіть інтеграцію <WebhookLink>вхідного веб-перехоплювача</WebhookLink>", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Активувати службу", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Щоб отримувати веб-push-сповіщення, Jellyseerr повинен обслуговуватися за протоколом HTTPS.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Не вдалося надіслати тестове веб-push-сповіщення.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Надсилання тестового веб-push-сповіщення…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "Тестове веб-повідомлення надіслано!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Не вдалося зберегти налаштування веб-повідомлень.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "Налаштування веб-повідомлень успішно збережено!", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Активувати службу", "components.Settings.Notifications.NotificationsWebhook.authheader": "Заголовок авторизації", "components.Settings.Notifications.NotificationsWebhook.customJson": "Корисне навантаження JSON", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Скинути до стандартних налаштувань", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "Корисне навантаження JSON успішно скинуто до стандартних налаштувань!", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Допомога за змінними шаблону", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Не вдалося надіслати тестове повідомлення веб-перехоплювачу.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Надсилання тестового повідомлення веб-перехоплювачу…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "Тестове повідомлення веб-перехоплювачу надіслано!", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Ви повинні надати допустиме корисне навантаження JSON", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL веб-перехоплювача", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "Не вдалося зберегти налаштування повідомлень веб-перехоплювача.", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "Налаштування повідомлень веб-перехоплювача успішно збережено!", "components.Settings.Notifications.agentenabled": "Активувати службу", "components.Settings.Notifications.allowselfsigned": "Дозволити самозавірені сертифікати", "components.Settings.Notifications.authPass": "Пароль SMTP", "components.Settings.Notifications.authUser": "Ім'я користувача SMTP", "components.Settings.Notifications.botAPI": "Токен авторизації бота", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Створіть бота</CreateBotLink> для використання з Jellyseerr", "components.Settings.Notifications.botAvatarUrl": "URL аватара бота", "components.Settings.Notifications.botUsername": "Ім'я бота", "components.Settings.Notifications.botUsernameTip": "Дозволити користувачам починати чат з вашим ботом і налаштовувати власні повідомлення", "components.Settings.Notifications.chatId": "ID чату", "components.Settings.Notifications.chatIdTip": "Почніть чат зі своїм ботом, додайте <GetIdBotLink>@get_id_bot</GetIdBotLink> і виконайте команду <code>/my_id</code>", "components.Settings.Notifications.discordsettingsfailed": "Не вдалося зберегти налаштування повідомлень Discord.", "components.Settings.Notifications.discordsettingssaved": "Налаштування повідомлень Discord успішно збережено!", "components.Settings.Notifications.emailsender": "Адреса відправника", "components.Settings.Notifications.emailsettingsfailed": "Не вдалося зберегти налаштування повідомлень електронною поштою.", "components.Settings.Notifications.emailsettingssaved": "Налаштування повідомлень електронною поштою успішно збережено!", "components.Settings.Notifications.enableMentions": "Enable Mentions", "components.Settings.Notifications.encryption": "Метод шифрування", "components.Settings.Notifications.encryptionDefault": "Використовувати STARTTLS, якщо доступно", "components.Settings.Notifications.encryptionImplicitTls": "Використовувати неявний TLS", "components.Settings.Notifications.encryptionNone": "Без шифрування", "components.Settings.Notifications.encryptionOpportunisticTls": "Завжди використовувати STARTTLS", "components.Settings.Notifications.encryptionTip": "У більшості випадків неявний TLS використовує порт 465, а STARTTLS - порт 587", "components.Settings.Notifications.pgpPassword": "Пароль PGP", "components.Settings.Notifications.pgpPasswordTip": "Підписувати зашифровані повідомлення електронної пошти за допомогою <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Закритий ключ PGP", "components.Settings.Notifications.pgpPrivateKeyTip": "Підписувати зашифровані повідомлення електронної пошти за допомогою <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.sendSilently": "Надсилати без звуку", "components.Settings.Notifications.sendSilentlyTip": "Надсилати повідомлення без звуку", "components.Settings.Notifications.senderName": "Ім'я відправника", "components.Settings.Notifications.smtpHost": "SMTP-хост", "components.Settings.Notifications.smtpPort": "SMTP порт", "components.Settings.Notifications.telegramsettingsfailed": "Не вдалося зберегти налаштування сповіщень Telegram.", "components.Settings.Notifications.telegramsettingssaved": "Налаштування сповіщень Telegram успішно збережено!", "components.Settings.Notifications.toastDiscordTestFailed": "Не вдалося надіслати тестове повідомлення до Discord.", "components.Settings.Notifications.toastDiscordTestSending": "Надсилання тестового повідомлення в Discord…", "components.Settings.Notifications.toastDiscordTestSuccess": "Тестове повідомлення надіслано до Discord!", "components.Settings.Notifications.toastEmailTestFailed": "Не вдалося надіслати тестове повідомлення електронною поштою.", "components.Settings.Notifications.toastEmailTestSending": "Надсилання тестового повідомлення електронною поштою…", "components.Settings.Notifications.toastEmailTestSuccess": "Тестове повідомлення надіслано електронною поштою!", "components.Settings.Notifications.toastTelegramTestFailed": "Не вдалося надіслати тестове повідомлення до Telegram.", "components.Settings.Notifications.toastTelegramTestSending": "Надсилання тестового повідомлення в Telegram…", "components.Settings.Notifications.toastTelegramTestSuccess": "Тестове повідомлення надіслано до Telegram!", "components.Settings.Notifications.validationBotAPIRequired": "Ви повинні надати токен авторизації бота", "components.Settings.Notifications.validationChatIdRequired": "Ви повинні надати дійсний ID чату", "components.Settings.Notifications.validationEmail": "Ви повинні вказати дійсну адресу електронної пошти", "components.Settings.Notifications.validationPgpPassword": "Ви повинні надати пароль PGP", "components.Settings.Notifications.validationPgpPrivateKey": "Ви повинні надати дійсний закритий ключ PGP", "components.Settings.Notifications.validationSmtpHostRequired": "Ви повинні вказати дійсне ім'я хоста або IP-адресу", "components.Settings.Notifications.validationSmtpPortRequired": "Ви повинні вказати дійсний номер порту", "components.Settings.Notifications.validationTypes": "Ви повинні вибрати хоча б один тип повідомлень", "components.Settings.Notifications.validationUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.Notifications.webhookUrl": "URL веб-перехоплювача", "components.Settings.Notifications.webhookUrlTip": "Створіть <DiscordWebhookLink>інтеграцію веб-перехоплювача</DiscordWebhookLink> на своєму сервері", "components.Settings.RadarrModal.add": "Додати сервер", "components.Settings.RadarrModal.announced": "Анонсовано", "components.Settings.RadarrModal.apiKey": "Ключ API", "components.Settings.RadarrModal.baseUrl": "Базовий URL", "components.Settings.RadarrModal.create4kradarr": "Додати новий 4К сервер Radarr", "components.Settings.RadarrModal.createradarr": "Додати новий сервер Radarr", "components.Settings.RadarrModal.default4kserver": "4К сервер за промовчанням", "components.Settings.RadarrModal.defaultserver": "Сервер за замовчуванням", "components.Settings.RadarrModal.edit4kradarr": "Редагувати 4К сервер Radarr", "components.Settings.RadarrModal.editradarr": "Редагувати сервер Radarr", "components.Settings.RadarrModal.enableSearch": "Увімкнути автоматичний пошук", "components.Settings.RadarrModal.externalUrl": "Зовнішня URL-адреса", "components.Settings.RadarrModal.hostname": "Ім'я хоста або IP-адреса", "components.Settings.RadarrModal.inCinemas": "У кіно", "components.Settings.RadarrModal.loadingTags": "Завантаження тегів…", "components.Settings.RadarrModal.loadingprofiles": "Завантаження профілів якості…", "components.Settings.RadarrModal.loadingrootfolders": "Завантаження кореневих каталогів…", "components.Settings.RadarrModal.minimumAvailability": "Мінімальна доступність", "components.Settings.RadarrModal.notagoptions": "Тегов немає.", "components.Settings.RadarrModal.port": "Порт", "components.Settings.RadarrModal.qualityprofile": "Профіль якості", "components.Settings.RadarrModal.released": "Випущено", "components.Settings.RadarrModal.rootfolder": "Кореневий каталог", "components.Settings.RadarrModal.selectMinimumAvailability": "Виберіть мінімальну доступність", "components.Settings.RadarrModal.selectQualityProfile": "Виберіть профіль якості", "components.Settings.RadarrModal.selectRootFolder": "Виберіть кореневий каталог", "components.Settings.RadarrModal.selecttags": "Виберіть теги", "components.Settings.RadarrModal.server4k": "4К сервер", "components.Settings.RadarrModal.servername": "Назва сервера", "components.Settings.RadarrModal.ssl": "Використовувати SSL", "components.Settings.RadarrModal.syncEnabled": "Увімкнути сканування", "components.Settings.RadarrModal.tags": "Теги", "components.Settings.RadarrModal.testFirstQualityProfiles": "Протестувати підключення для завантаження профілів якості", "components.Settings.RadarrModal.testFirstRootFolders": "Протестувати підключення для завантаження кореневих каталогів", "components.Settings.RadarrModal.testFirstTags": "Протестувати підключення для завантаження тегів", "components.Settings.RadarrModal.toastRadarrTestFailure": "Не вдалося підключитися до Radarr.", "components.Settings.RadarrModal.toastRadarrTestSuccess": "З'єднання з Radarr встановлено успішно!", "components.Settings.RadarrModal.validationApiKeyRequired": "Ви повинні надати ключ API", "components.Settings.RadarrModal.validationApplicationUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "URL-адреса не повинна закінчуватися косою межею", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "Базова URL-адреса повинна мати косу межу на початку", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "Базова URL-адреса не повинна закінчуватися косою межею", "components.Settings.RadarrModal.validationHostnameRequired": "Ви повинні вказати дійсне ім'я хоста або IP-адресу", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Ви повинні вибрати мінімальну доступність", "components.Settings.RadarrModal.validationNameRequired": "Ви повинні вказати ім'я сервера", "components.Settings.RadarrModal.validationPortRequired": "Ви повинні вказати дійсний номер порту", "components.Settings.RadarrModal.validationProfileRequired": "Ви повинні вибрати профіль якості", "components.Settings.RadarrModal.validationRootFolderRequired": "Ви повинні вибрати кореневий каталог", "components.Settings.SettingsAbout.Releases.currentversion": "Поточна", "components.Settings.SettingsAbout.Releases.latestversion": "Остання", "components.Settings.SettingsAbout.Releases.releasedataMissing": "Дані про реліз наразі недоступні.", "components.Settings.SettingsAbout.Releases.releases": "Релізи", "components.Settings.SettingsAbout.Releases.versionChangelog": "З<PERSON><PERSON><PERSON>и у версії {version}", "components.Settings.SettingsAbout.Releases.viewchangelog": "Переглянути список змін", "components.Settings.SettingsAbout.Releases.viewongithub": "Подивитися на GitHub", "components.Settings.SettingsAbout.about": "Про проект", "components.Settings.SettingsAbout.appDataPath": "Каталог даних", "components.Settings.SettingsAbout.betawarning": "Це бета-версія програмного забезпечення. Деякі функції можуть не працювати або працювати нестабільно. Будь ласка, повідомляйте про будь-які проблеми на GitHub!", "components.Settings.SettingsAbout.documentation": "Документація", "components.Settings.SettingsAbout.gettingsupport": "Отримати підтримку", "components.Settings.SettingsAbout.githubdiscussions": "Обговорення на GitHub", "components.Settings.SettingsAbout.helppaycoffee": "Допомога оплатити каву", "components.Settings.SettingsAbout.outofdate": "Застар<PERSON>ла", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.preferredmethod": "Переважний спосіб", "components.Settings.SettingsAbout.runningDevelop": "Ви використовуєте гілку <code>develop</code> проєкту Jellyseerr, яка рекомендується тільки для тих, хто робить внесок у розробку або допомагає в тестуванні.", "components.Settings.SettingsAbout.supportoverseerr": "Підтри<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.timezone": "Часовий пояс", "components.Settings.SettingsAbout.totalmedia": "Усього мультимедіа", "components.Settings.SettingsAbout.totalrequests": "Усього запитів", "components.Settings.SettingsAbout.uptodate": "Актуальна", "components.Settings.SettingsAbout.version": "Версія", "components.Settings.SettingsJobsCache.cache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.cacheDescription": "Jellyseerr кешує запити до зовнішніх кінцевих точок API, щоб оптимізувати продуктивність і уникнути непотрібних викликів API.", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} кеш скинутий.", "components.Settings.SettingsJobsCache.cachehits": "Вдалих звернень", "components.Settings.SettingsJobsCache.cachekeys": "Усього ключів", "components.Settings.SettingsJobsCache.cacheksize": "Роз<PERSON><PERSON><PERSON> клю<PERSON><PERSON>в", "components.Settings.SettingsJobsCache.cachemisses": "Невдалих звернень", "components.Settings.SettingsJobsCache.cachename": "Назва кеша", "components.Settings.SettingsJobsCache.cachevsize": "Розмір значень", "components.Settings.SettingsJobsCache.canceljob": "Скасувати завдання", "components.Settings.SettingsJobsCache.command": "Команда", "components.Settings.SettingsJobsCache.download-sync": "Синхронізувати завантаження", "components.Settings.SettingsJobsCache.download-sync-reset": "Скинути синхронізацію завантажень", "components.Settings.SettingsJobsCache.editJobSchedule": "Змінити завдання", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Поточна частота", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Частота", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Кожен {jobScheduleHours, plural, one {година} other {{jobScheduleHours} години(ів)}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Кожну {jobScheduleMinutes, plural, one {хвилину} other {{jobScheduleMinutes} хвилин(и)}}", "components.Settings.SettingsJobsCache.flushcache": "Очистити кеш", "components.Settings.SettingsJobsCache.jellyfin-recently-added-scan": "Нещодавно додане сканування Jellyfin", "components.Settings.SettingsJobsCache.jellyfin-full-scan": "Сканування повної бібліотек<PERSON>", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Очищення кешу зображень", "components.Settings.SettingsJobsCache.imagecache": "Кеш зображень", "components.Settings.SettingsJobsCache.imagecacheDescription": "Якщо в налаштуваннях увімкнено, Je<PERSON><PERSON>rr буде проксі-сервером і кешувати зображення з попередньо налаштованих зовнішніх джерел. Кешовані зображення зберігаються у папці конфігурації. Ви можете знайти файли в <code>{appDataPath}/cache/images</code>.", "components.Settings.SettingsJobsCache.imagecachecount": "Зображення кешовані", "components.Settings.SettingsJobsCache.imagecachesize": "Загальний розмір кешу", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Щось пішло не так при збереженні завдання.", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "Завдання успішно відредаговано!", "components.Settings.SettingsJobsCache.jobcancelled": "Завдання \"{jobname}\" скасовано.", "components.Settings.SettingsJobsCache.jobname": "Назва завдання", "components.Settings.SettingsJobsCache.jobs": "Завдання", "components.Settings.SettingsJobsCache.jobsDescription": "Jellyseerr виконує певні завдання обслуговування як регулярно запланованих завдань, але вони також можуть бути запущені вручну нижче. Виконання завдання вручну не змінить його розклад.", "components.Settings.SettingsJobsCache.jobsandcache": "Завдання та кеш", "components.Settings.SettingsJobsCache.jobstarted": "Завдання \"{jobname}\" запущено.", "components.Settings.SettingsJobsCache.jobtype": "Тип", "components.Settings.SettingsJobsCache.nextexecution": "Наступне виконання", "components.Settings.SettingsJobsCache.plex-full-scan": "Повне сканування бібліотек Plex", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Сканування нещодавно доданих медіафайлів у Plex", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Синхронізація списку перегляду Plex", "components.Settings.SettingsJobsCache.process": "Процес", "components.Settings.SettingsJobsCache.radarr-scan": "Сканування Radarr", "components.Settings.SettingsJobsCache.runnow": "Виконати зараз", "components.Settings.SettingsJobsCache.sonarr-scan": "Сканування Sonarr", "components.Settings.SettingsJobsCache.unknownJob": "Невідоме завдання", "components.Settings.SettingsLogs.copiedLogMessage": "Повідомлення лога скопійовано в буфер обміну.", "components.Settings.SettingsLogs.copyToClipboard": "Скопіювати в буфер обміну", "components.Settings.SettingsLogs.extraData": "Додаткова інформація", "components.Settings.SettingsLogs.filterDebug": "Налагоджувальні", "components.Settings.SettingsLogs.filterError": "Помилки", "components.Settings.SettingsLogs.filterInfo": "Інформаційні", "components.Settings.SettingsLogs.filterWarn": "Попередження", "components.Settings.SettingsLogs.label": "Мітка", "components.Settings.SettingsLogs.level": "Важливість", "components.Settings.SettingsLogs.logDetails": "Докладні відомості про лог", "components.Settings.SettingsLogs.logs": "Логи", "components.Settings.SettingsLogs.logsDescription": "Ви також можете переглядати ці логи безпосередньо через <code>stdout</code> або в <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.message": "Повідомлення", "components.Settings.SettingsLogs.pauseLogs": "Зупинити", "components.Settings.SettingsLogs.resumeLogs": "Відновити", "components.Settings.SettingsLogs.showall": "Показати всі логі", "components.Settings.SettingsLogs.time": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.viewdetails": "Переглянути деталі", "components.Settings.SettingsUsers.defaultPermissions": "Дозволи за замовчуванням", "components.Settings.SettingsUsers.defaultPermissionsTip": "Початкові дозволи, надані новим користувачам", "components.Settings.SettingsUsers.localLogin": "Увімкнути локальний вхід", "components.Settings.SettingsUsers.localLoginTip": "Дозволити користувачам входити в систему, використовуючи свою адресу електронної пошти та пароль замість Plex OAuth", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Загальне обмеження кількості запитів фільмів", "components.Settings.SettingsUsers.newPlexLogin": "Увімкнути вхід через {mediaServerName} для нових користувачів", "components.Settings.SettingsUsers.newPlexLoginTip": "Дозволити користувачам {mediaServerName} входити до системи без попереднього імпорту", "components.Settings.SettingsUsers.toastSettingsFailure": "Щось пішло не так при збереженні налаштувань.", "components.Settings.SettingsUsers.toastSettingsSuccess": "Налаштування користувачів успішно збережено!", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Загальне обмеження кількості запитів серіалів", "components.Settings.SettingsUsers.userSettings": "Налаштування користувачів", "components.Settings.SettingsUsers.userSettingsDescription": "Налаштуйте глобальні параметри та параметри за промовчанням для користувачів.", "components.Settings.SettingsUsers.users": "Користувачі", "components.Settings.SonarrModal.add": "Додати сервер", "components.Settings.SonarrModal.animeTags": "Теги для аніме", "components.Settings.SonarrModal.animelanguageprofile": "Мовний профіль для аніме", "components.Settings.SonarrModal.animequalityprofile": "Профіль якості для аніме", "components.Settings.SonarrModal.animerootfolder": "Кореневий каталог для аніме", "components.Settings.SonarrModal.apiKey": "Ключ API", "components.Settings.SonarrModal.baseUrl": "Базовий URL", "components.Settings.SonarrModal.create4ksonarr": "Додати новий 4К сервер Sonarr", "components.Settings.SonarrModal.createsonarr": "Додати новий сервер Sonarr", "components.Settings.SonarrModal.default4kserver": "4К сервер за промовчанням", "components.Settings.SonarrModal.defaultserver": "Сервер за замовчуванням", "components.Settings.SonarrModal.edit4ksonarr": "Редагувати 4К сервер Sonarr", "components.Settings.SonarrModal.editsonarr": "Редагувати сервер Sonarr", "components.Settings.SonarrModal.enableSearch": "Увімкнути автоматичний пошук", "components.Settings.SonarrModal.externalUrl": "Зовнішня URL-адреса", "components.Settings.SonarrModal.hostname": "Ім'я хоста або IP-адреса", "components.Settings.SonarrModal.languageprofile": "Мовний профіль", "components.Settings.SonarrModal.loadingTags": "Завантаження тегів…", "components.Settings.SonarrModal.loadinglanguageprofiles": "Завантаження мовних профілів…", "components.Settings.SonarrModal.loadingprofiles": "Завантаження профілів якості…", "components.Settings.SonarrModal.loadingrootfolders": "Завантаження кореневих каталогів…", "components.Settings.SonarrModal.notagoptions": "Тегов немає.", "components.Settings.SonarrModal.port": "Порт", "components.Settings.SonarrModal.qualityprofile": "Профіль якості", "components.Settings.SonarrModal.rootfolder": "Кореневий каталог", "components.Settings.SonarrModal.seasonfolders": "Папки для сезонів", "components.Settings.SonarrModal.selectLanguageProfile": "Виберіть мовний профіль", "components.Settings.SonarrModal.selectQualityProfile": "Виберіть профіль якості", "components.Settings.SonarrModal.selectRootFolder": "Виберіть кореневий каталог", "components.Settings.SonarrModal.selecttags": "Виберіть теги", "components.Settings.SonarrModal.server4k": "4К сервер", "components.Settings.SonarrModal.servername": "Назва сервера", "components.Settings.SonarrModal.ssl": "Використовувати SSL", "components.Settings.SonarrModal.syncEnabled": "Увімкнути сканування", "components.Settings.SonarrModal.tags": "Теги", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Протестувати підключення для завантаження мовних профілів", "components.Settings.SonarrModal.testFirstQualityProfiles": "Протестувати підключення для завантаження профілів якості", "components.Settings.SonarrModal.testFirstRootFolders": "Протестувати з'єднання для завантаження кореневих каталогів", "components.Settings.SonarrModal.testFirstTags": "Протестувати підключення для завантаження тегів", "components.Settings.SonarrModal.toastSonarrTestFailure": "Не вдалося підключитися до Sonarr.", "components.Settings.SonarrModal.toastSonarrTestSuccess": "З'єднання з Sonarr встановлено успішно!", "components.Settings.SonarrModal.validationApiKeyRequired": "Ви повинні надати ключ API", "components.Settings.SonarrModal.validationApplicationUrl": "Ви повинні вказати дійсну URL-адресу", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "URL-адреса не повинна закінчуватися косою межею", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "Базова URL-адреса повинна мати косу межу на початку", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "Базова URL-адреса не повинна закінчуватися косою межею", "components.Settings.SonarrModal.validationHostnameRequired": "Ви повинні вказати дійсне ім'я хоста або IP-адресу", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Ви повинні вибрати мовний профіль", "components.Settings.SonarrModal.validationNameRequired": "Ви повинні вказати ім'я сервера", "components.Settings.SonarrModal.validationPortRequired": "Ви повинні вказати дійсний номер порту", "components.Settings.SonarrModal.validationProfileRequired": "Ви повинні вибрати профіль якості", "components.Settings.SonarrModal.validationRootFolderRequired": "Ви повинні вибрати кореневий каталог", "components.Settings.activeProfile": "Активний профіль", "components.Settings.addradarr": "Додати сервер Radarr", "components.Settings.address": "Адреса", "components.Settings.addsonarr": "Додати сервер Sonarr", "components.Settings.advancedTooltip": "Неправильне налаштування цього параметра може призвести до несправності функціональності", "components.Settings.cancelscan": "Скасувати сканування", "components.Settings.copied": "Ключ API скопійовано в буфер обміну.", "components.Settings.currentlibrary": "Поточна бібліотека: {name}", "components.Settings.default": "За замовчуванням", "components.Settings.default4k": "4К за замовчуванням", "components.Settings.deleteServer": "Видалити сервер {serverType}", "components.Settings.deleteserverconfirm": "Ви впевнені, що хочете видалити цей сервер?", "components.Settings.email": "Електронна пошта", "components.Settings.enablessl": "Використовувати SSL", "components.Settings.experimentalTooltip": "Увімкнення цього параметра може призвести до неочікуваної поведінки програми", "components.Settings.externalUrl": "Зовнішня URL-адреса", "components.Settings.hostname": "Ім'я хоста або IP-адреса", "components.Settings.is4k": "4К", "components.Settings.librariesRemaining": "За<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> бібліотек: {count}", "components.Settings.manualscan": "Сканувати бібліотеки вручну", "components.Settings.manualscanDescription": "Зазвичай виконується раз на 24 години. Je<PERSON>seerr виконає більш агресивну перевірку вашого сервера Plex на предмет нещодавно доданих мультимедіа. Якщо ви вперше налаштовуєте Plex, рекомендується виконати одноразове повне сканування бібліотек вручну!", "components.Settings.mediaTypeMovie": "фільм", "components.Settings.mediaTypeSeries": "серіал", "components.Settings.menuAbout": "Про проект", "components.Settings.menuGeneralSettings": "Спільне", "components.Settings.menuJobs": "Завдання та кеш", "components.Settings.menuLogs": "Логи", "components.Settings.menuNotifications": "Сповіщення", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuServices": "Служби", "components.Settings.menuUsers": "Користувачі", "components.Settings.noDefault4kServer": "4K сервер {serverType} повинен бути позначений як сервер за промовчанням, щоб користувачі могли надсилати запити на 4K {mediaType}и.", "components.Settings.noDefaultNon4kServer": "Якщо ви використовуєте один сервер {serverType} для контенту, в тому числі і для 4К, або якщо ви завантажуєте лише контент 4K, ваш сервер {serverType} <strong>НЕ</strong> має бути позначений як 4К сервер.", "components.Settings.noDefaultServer": "Принаймні один сервер {serverType} повинен бути позначений як сервер за промовчанням для обробки запитів на {mediaType}и.", "components.Settings.notificationAgentSettingsDescription": "Налаштуйте та активуйте служби сповіщень.", "components.Settings.notifications": "Сповіщення", "components.Settings.notificationsettings": "Налаштування повідомлень", "components.Settings.notrunning": "Не працює", "components.Settings.plex": "Plex", "components.Settings.plexlibraries": "Бібліотеки Plex", "components.Settings.plexlibrariesDescription": "Бібліотек<PERSON>, як<PERSON> Jellyseerr сканує на наявність мультимедіа. Налаштуйте та збережіть параметри підключення Plex, потім натисніть кнопку нижче, якщо список бібліотек порожній.", "components.Settings.plexsettings": "Налаштування Plex", "components.Settings.plexsettingsDescription": "Налаштуйте параметри вашого сервера Plex. Jellyseerr сканує ваші бібліотеки Plex, щоб визначити доступність контенту.", "components.Settings.port": "Порт", "components.Settings.radarrsettings": "Налаштування Radarr", "components.Settings.restartrequiredTooltip": "Je<PERSON><PERSON>rr потрібно перезапустити, щоб зміни цього параметра набули чинності", "components.Settings.scan": "Синхронізувати бібліотеки", "components.Settings.scanning": "Синхронізація…", "components.Settings.serverLocal": "локальний", "components.Settings.serverRemote": "віддалений", "components.Settings.serverSecure": "захищений", "components.Settings.serverpreset": "Сервер", "components.Settings.serverpresetLoad": "Натисніть кнопку, щоб завантажити список доступних серверів", "components.Settings.serverpresetManualMessage": "Ручне налаштування", "components.Settings.serverpresetRefreshing": "Отримання списку серверів…", "components.Settings.serviceSettingsDescription": "Налаштуйте сервер(и) {serverType} нижче. Ви можете підключити кілька серверів {serverType}, але тільки два з них можуть бути позначені як сервери за промовчанням (один не 4К і один 4К). Адміністратори можуть перевизначити сервер для обробки нових запитів до їх схвалення.", "components.Settings.services": "Служби", "components.Settings.settingUpPlexDescription": "Щоб налаштувати Plex, ви можете або ввести дані вручну, або вибрати сервер, отриманий зі сторінки <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Натисніть кнопку праворуч від випадаючого списку, щоб отримати список доступних серверів .", "components.Settings.sonarrsettings": "Налаштування Sonarr", "components.Settings.ssl": "SSL", "components.Settings.startscan": "Почати сканування", "components.Settings.tautulliApiKey": "Ключ API", "components.Settings.tautulliSettings": "Tautulli Налаштування", "components.Settings.tautulliSettingsDescription": "За бажанням налаштуйте параметри для вашого сервер<PERSON>. Jellyseerr отримує дані історії переглядів для медіафайлів Plex від <PERSON>.", "components.Settings.toastPlexConnecting": "Спроба підключення до Plex…", "components.Settings.toastPlexConnectingFailure": "Не вдалося підключитися до Plex.", "components.Settings.toastPlexConnectingSuccess": "З'єднання з Plex встановлено успішно!", "components.Settings.toastPlexRefresh": "Отримання списку серверів Plex…", "components.Settings.toastPlexRefreshFailure": "Не вдалося отримати список серверів Plex.", "components.Settings.toastPlexRefreshSuccess": "Список серверів Plex успішно отримано!", "components.Settings.toastTautulliSettingsFailure": "Під час збереження налаштувань Tautulli сталася помилка.", "components.Settings.toastTautulliSettingsSuccess": "Налаштування Tautulli успішно збережено!", "components.Settings.urlBase": "URL Base", "components.Settings.validationApiKey": "Ви повинні надати ключ API", "components.Settings.validationHostnameRequired": "Ви повинні вказати дійсне ім'я хоста або IP-адресу", "components.Settings.validationPortRequired": "Ви повинні вказати дійсний номер порту", "components.Settings.validationUrl": "Ви повинні надати дійсну URL-адресу", "components.Settings.validationUrlBaseLeadingSlash": "Основа URL-адреси повинна мати скісну риску на початку", "components.Settings.validationUrlBaseTrailingSlash": "База URL-адреси не має закінчуватися косою рискою", "components.Settings.validationUrlTrailingSlash": "URL-адреса не має закінчуватися косою рискою", "components.Settings.webAppUrl": "URL <WebAppLink>веб-програми</WebAppLink>", "components.Settings.webAppUrlTip": "При необхідності надсилайте користувачів у веб-додаток на вашому сервері замість розміщеного на plex.tv", "components.Settings.webhook": "Веб-перехоплювач", "components.Settings.webpush": "Веб-push", "components.Setup.configureservices": "Налаштуйте служби", "components.Setup.continue": "Продовжити", "components.Setup.finish": "Завершити налаштування", "components.Setup.finishing": "Завершення…", "components.Setup.setup": "Налаштування", "components.Setup.signinMessage": "Почніть з входу в систему за допомогою облікового запису Plex", "components.Setup.welcome": "Ласкаво просимо до <PERSON><PERSON>rr", "components.StatusBadge.managemedia": "Керувати {mediaType}", "components.StatusBadge.openinarr": "Відкрити в{arr}", "components.StatusBadge.playonplex": "Грати в {mediaServerName}", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.StatusBadge.status": "{status}", "components.StatusBadge.status4k": "4K {status}", "components.StatusChecker.appUpdated": "{applicationTitle} Оновлено", "components.StatusChecker.appUpdatedDescription": "Натисніть кнопку нижче, щоб перезавантажити програму.", "components.StatusChecker.reloadApp": "Перезавантажити {applicationTitle}", "components.StatusChecker.restartRequired": "Потрібен перезапуск сервера", "components.StatusChecker.restartRequiredDescription": "Перезапустіть сервер, щоб застосувати оновлені налаштування.", "components.TitleCard.cleardata": "Очистити дані", "components.TitleCard.mediaerror": "{mediaType} Не знайдено", "components.TitleCard.tmdbid": "TMDB ID", "components.TitleCard.tvdbid": "TheTVDB ID", "components.TvDetails.Season.noepisodes": "Список епізодів недоступний.", "components.TvDetails.Season.somethingwentwrong": "Під час отримання даних про сезон сталася помилка.", "components.TvDetails.TvCast.fullseriescast": "Повний акторський склад серіалу", "components.TvDetails.TvCrew.fullseriescrew": "Повна знімальна група серіалу", "components.TvDetails.anime": "Ані<PERSON>е", "components.TvDetails.cast": "У ролях", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Епізод} other {# Епізоди}}", "components.TvDetails.episodeRuntime": "Тривалість епізоду", "components.TvDetails.episodeRuntimeMinutes": "{runtime} хвилин", "components.TvDetails.firstAirDate": "Дата першого ефіру", "components.TvDetails.manageseries": "Керувати Серіалами", "components.TvDetails.network": "{networkCount, plural, one {Телеканал} other {Телеканали}}", "components.TvDetails.nextAirDate": "Наступна дата виходу в ефір", "components.TvDetails.originallanguage": "Мова оригіналу", "components.TvDetails.originaltitle": "Назва оригіналу", "components.TvDetails.overview": "Огляд", "components.TvDetails.overviewunavailable": "Огляд недоступний.", "components.TvDetails.productioncountries": "{countryCount, plural, one {Країна} other {Країни}} виробництва", "components.TvDetails.recommendations": "Рекомендації", "components.TvDetails.reportissue": "Повідомити про проблему", "components.TvDetails.rtaudiencescore": "Оцінка аудитор<PERSON><PERSON> Rotten Tomatoes", "components.TvDetails.rtcriticsscore": "Томатометр Rotten Tomatoes", "components.TvDetails.seasonnumber": "Сезон {seasonNumber}", "components.TvDetails.seasons": "{seasonCount, plural, one {# сезон} other {# сезонів}}", "components.TvDetails.seasonstitle": "Сезони", "components.TvDetails.showtype": "Тип серіалу", "components.TvDetails.similar": "Схожі серіали", "components.TvDetails.status4k": "4K {status}", "components.TvDetails.streamingproviders": "Зараз транслюється", "components.TvDetails.tmdbuserscore": "Оцінка користувача TMDB", "components.TvDetails.viewfullcrew": "Подивитися повну знімальну групу", "components.TvDetails.watchtrailer": "Дивити<PERSON>ь трейлер", "components.UserList.accounttype": "Тип", "components.UserList.admin": "Адміністратор", "components.UserList.autogeneratepassword": "Згенерувати пароль автоматично", "components.UserList.autogeneratepasswordTip": "Надіслати користувачеві пароль, згенерований сервером, електронною поштою", "components.UserList.bulkedit": "Масове редагування", "components.UserList.create": "Створити", "components.UserList.created": "Приєднався", "components.UserList.createlocaluser": "Створити локального користувача", "components.UserList.creating": "Створення…", "components.UserList.deleteconfirm": "Ви впевнені, що хочете видалити цього користувача? Усі дані про його запити будуть видалені без можливості відновлення.", "components.UserList.deleteuser": "Видалити користувача", "components.UserList.edituser": "Змінити дозволи користувача", "components.UserList.email": "Адреса електронної пошти", "components.UserList.importedfromplex": "{userCount, plural, one {# новий користувач} other {# нових користувачів} успішно імпортовані з Plex!", "components.UserList.importfrommediaserver": "Імпортувати користувачів з {mediaServerName}", "components.UserList.importfromplex": "Імпортувати користувачів з Plex", "components.UserList.importfromplexerror": "Щось пішло не так при імпорті користувачів з Plex.", "components.UserList.localLoginDisabled": "Параметр <strong>Увімкнути локальний вхід</strong> в даний час вимкнено.", "components.UserList.localuser": "Локальний користувач", "components.UserList.newplexsigninenabled": "Параметр <strong>Увімкнути новий вхід Plex</strong> наразі ввімкнено. Користувачам Plex із доступом до бібліотеки не потрібно імпортувати, щоб увійти.", "components.UserList.nouserstoimport": "Немає нових користувачів для імпорту з Plex.", "components.UserList.owner": "Власник", "components.UserList.password": "Пароль", "components.UserList.passwordinfodescription": "Налаштуйте URL-адресу програми та увімкніть повідомлення електронною поштою, щоб забезпечити можливість автоматичної генерації пароля.", "components.UserList.plexuser": "Користувач Plex", "components.UserList.role": "Роль", "components.UserList.sortCreated": "Дата приєднання", "components.UserList.sortDisplayName": "Відображуване ім'я", "components.UserList.sortRequests": "Кількість запитів", "components.UserList.totalrequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>в", "components.UserList.user": "Користув<PERSON><PERSON>", "components.UserList.usercreatedfailed": "Щось пішло не так при створенні користувача.", "components.UserList.usercreatedfailedexisting": "Вказана адреса електронної пошти вже використовується іншим користувачем.", "components.UserList.usercreatedsuccess": "Користувача успішно створено!", "components.UserList.userdeleted": "Користувача успішно видалено!", "components.UserList.userdeleteerror": "Щось пішло не так при видаленні користувача.", "components.UserList.userfail": "Щось пішло не так при збереженні дозволів користувача.", "components.UserList.userlist": "Список користувачів", "components.UserList.users": "Користувачі", "components.UserList.userssaved": "Дозволи користувача успішно збережені!", "components.UserList.validationEmail": "Потрібно вказати Email", "components.UserList.validationpasswordminchars": "Пароль занадто короткий: він повинен містити не менше 8 символів", "components.UserProfile.ProfileHeader.joindate": "Приєднався {joindate}", "components.UserProfile.ProfileHeader.profile": "Подивитися профіль", "components.UserProfile.ProfileHeader.settings": "Редагувати налаштування", "components.UserProfile.ProfileHeader.userid": "ID користувача: {userid}", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Тип облікового запису", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Адміністратор", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Мова інтерфейсу", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID користувача Discord", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "<FindDiscordIdLink>багатозначний ідентифікаційний номер</FindDiscordIdLink>, пов’язаний із вашим обліковим записом користувача Discord", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Відображуване ім'я", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Перевизначити глобальні обмеження", "components.UserProfile.UserSettings.UserGeneralSettings.general": "Загальне", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Загальні налаштування", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "За замовчуванням ({language})", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Локальний користувач", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Обмеження кількості запитів на фільми", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Мови для пошуку фільмів та серіалів", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Контент фільтрується за мовою оригіналу", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Власник", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Користувач Plex", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Автоматичний запит фільмів", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Автоматично запитувати фільми з вашого <PlexWatchlistSupportLink>списку перегляду Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Автоматичний запит Серіалів", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Автоматично запитувати серіали з вашого <PlexWatchlistSupportLink>списку перегляду Plex</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Регіон для пошуку фільмів та серіалів", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Контент фільтрується за доступністю у вибраному регіоні", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Роль", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Обмеження кількості запитів на серіали", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Щось пішло не так при збереженні налаштувань.", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "Налаштування успішно збережено!", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Користув<PERSON><PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Ви повинні надати дійсний ідентифікатор користувача Discord", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID користувача", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "<FindDiscordIdLink>ID</FindDiscordIdLink> вашого облікового запису", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "Не вдалося зберегти налаштування повідомлень Discord.", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "Налаштування повідомлень Discord успішно збережено!", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Електронна пошта", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Не вдалося зберегти налаштування повідомлень електронною поштою.", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "Налаштування повідомлень електронною поштою успішно збережено!", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Сповіщення", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Налаштування повідомлень", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Відкритий ключ PGP", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Шифрувати повідомлення електронної пошти за допомогою <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessToken": "Токен доступу", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletAccessTokenTip": "Створіть токен на сторінці <PushbulletSettingsLink>налаштувань вашого облікового запису</PushbulletSettingsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingsfailed": "Не вдалося зберегти налаштування сповіщень Pushbullet.", "components.UserProfile.UserSettings.UserNotificationSettings.pushbulletsettingssaved": "Налаштування сповіщень Pushbullet успішно збережено!", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationToken": "Токен API програми", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverApplicationTokenTip": "<ApplicationRegistrationLink>Зареєструйте програму</ApplicationRegistrationLink> для використання з {applicationTitle}", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKey": "К<PERSON><PERSON>ч користувача або групи", "components.UserProfile.UserSettings.UserNotificationSettings.pushoverUserKeyTip": "Ваш 30-<PERSON><PERSON><PERSON><PERSON><PERSON> <UsersGroupsLink>ідентифікатор користувача або групи</UsersGroupsLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingsfailed": "Не вдалося зберегти налаштування сповіщень Pushover.", "components.UserProfile.UserSettings.UserNotificationSettings.pushoversettingssaved": "Налаштування сповіщень Pushover успішно збережено!", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Надсилати без звуку", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Надсилати повідомлення без звуку", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID чату", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Почніть чат</TelegramBotLink>, додайте <GetIdBotLink>@get_id_bot</GetIdBotLink> і виконайте команду <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Не вдалося зберегти налаштування сповіщень Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "Налаштування сповіщень Telegram успішно збережено!", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Ви повинні надати дійсний ID користувача", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Ви повинні надати дійсний відкритий ключ PGP", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushbulletAccessToken": "Ви повинні надати токен доступу", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverApplicationToken": "Ви повинні надати дійсний токен програми", "components.UserProfile.UserSettings.UserNotificationSettings.validationPushoverUserKey": "Ви повинні надати дійсний ключ користувача або групи", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Ви повинні надати дійсний ID чату", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Веб-push", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Не вдалося зберегти налаштування веб-повідомлень.", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "Налаштування веб-повідомлень успішно збережено!", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Підтвердіть пароль", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Поточний пароль", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Новий пароль", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "В даний час для цього облікового запису не встановлено пароль. Встановіть пароль нижче, щоб з цим обліковим записом можна було увійти в систему як \"локальний користувач\".", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "В даний час для вашого облікового запису не встановлено пароль. Встановіть пароль нижче, щоб мати можливість увійти в систему як \"локальний користувач\", використовуючи свою адресу електронної пошти.", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "У вас немає дозволу на зміну пароля цього користувача.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Пароль", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Щось пішло не так при збереженні пароля.", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Щось пішло не так при збереженні пароля. Чи правильно введено ваш поточний пароль?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "Пароль успішно збережений!", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Ви повинні підтвердити новий пароль", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Паролі повинні збігатися", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Ви повинні вказати свій поточний пароль", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Ви повинні ввести новий пароль", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "Пароль занадто короткий: він повинен містити не менше 8 символів", "components.UserProfile.UserSettings.UserPermissions.permissions": "Дозволи", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Щось пішло не так при збереженні налаштувань.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "Дозволи успішно збережені!", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "Ви не можете змінювати власні дозволи.", "components.UserProfile.UserSettings.menuChangePass": "Пароль", "components.UserProfile.UserSettings.menuGeneralSettings": "Спільне", "components.UserProfile.UserSettings.menuNotifications": "Сповіщення", "components.UserProfile.UserSettings.menuPermissions": "Дозволи", "components.UserProfile.UserSettings.unauthorizedDescription": "У вас немає дозволу на зміну налаштувань цього користувача.", "components.UserProfile.emptywatchlist": "Тут з’являться медіафайли, додані до вашого <PlexWatchlistSupportLink>списку перегляду Plex</PlexWatchlistSupportLink>.", "components.UserProfile.limit": "{remaining} з {limit}", "components.UserProfile.movierequests": "Запитів фільмів", "components.UserProfile.pastdays": "{type} (на {days} дні(в)", "components.UserProfile.plexwatchlist": "Список перегляду Plex", "components.UserProfile.recentlywatched": "Нещодавно переглянуто", "components.UserProfile.recentrequests": "Останні запити", "components.UserProfile.requestsperdays": "залишилось {limit}", "components.UserProfile.seriesrequest": "Запитів сезонів", "components.UserProfile.totalrequests": "Усього запитів", "components.UserProfile.unlimited": "Необмежено", "i18n.advanced": "Для просунутих користувачів", "i18n.all": "Всі", "i18n.approve": "Схвалити", "i18n.approved": "Схвалений", "i18n.areyousure": "Ви впевнені?", "i18n.available": "Доступний", "i18n.back": "Назад", "i18n.cancel": "Скасувати", "i18n.canceling": "Скасувати…", "i18n.close": "Закрити", "i18n.decline": "Від<PERSON><PERSON><PERSON>ити", "i18n.declined": "В<PERSON>д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "Видалити", "i18n.deleting": "Видалення…", "i18n.delimitedlist": "{a}, {b}", "i18n.edit": "Редагувати", "i18n.experimental": "Експериментальний параметр", "i18n.failed": "Помилка", "i18n.import": "Імпорт", "i18n.importing": "Імпортування...", "i18n.loading": "Завантаження…", "i18n.movie": "Фільм", "i18n.movies": "Фільми", "i18n.next": "Наступна", "i18n.noresults": "Результатів немає.", "i18n.notrequested": "Не запрошений", "i18n.open": "Відкрити", "i18n.partiallyavailable": "Доступний частково", "i18n.pending": "Чекаючи", "i18n.previous": "Попередня", "i18n.processing": "В обробці", "i18n.request": "Запит", "i18n.request4k": "Запит у 4K", "i18n.requested": "Запрошений", "i18n.requesting": "Запит…", "i18n.resolved": "Вирішені", "i18n.restartRequired": "Потрібне перезавантаження", "i18n.resultsperpage": "Відобразити {pageSize} результатів на сторінці", "i18n.retry": "Повторити", "i18n.retrying": "Повтор…", "i18n.save": "Зберегти зміни", "i18n.saving": "Збереження…", "i18n.settings": "Налаштування", "i18n.showingresults": "Показуються результати з <strong>{from}</strong> по <strong>{to}</strong> з <strong>{total}</strong>", "i18n.status": "Статус", "i18n.test": "Протестувати", "i18n.testing": "Тестування…", "i18n.tvshow": "Серіал", "i18n.tvshows": "Серіали", "i18n.unavailable": "Недоступний", "i18n.usersettings": "Налаштування користувача", "i18n.view": "Подивитися", "pages.errormessagewithcode": "{statusCode} - {error}", "pages.internalservererror": "Внутрішня помилка сервера", "pages.oops": "Упс", "pages.pagenotfound": "Сторінку не знайдено", "pages.returnHome": "Повернутись додому", "pages.serviceunavailable": "Сервіс недоступний", "pages.somethingwentwrong": "Щось пішло не так", "components.Discover.CreateSlider.nooptions": "Немає результатів.", "components.Discover.CreateSlider.searchKeywords": "Ключові слова пошуку…", "components.Discover.CreateSlider.searchStudios": "По<PERSON><PERSON><PERSON> студій…", "components.Discover.CreateSlider.starttyping": "Починайте писати для пошуку.", "components.Discover.CreateSlider.validationTitlerequired": "Ви повинні вказати назву.", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Активний фільтр} other {# Активні фільтри}}", "components.Discover.DiscoverMovies.discovermovies": "Фільми", "components.Discover.DiscoverSliderEdit.enable": "Змінити видимість", "components.Discover.DiscoverSliderEdit.remove": "Видалити", "components.Discover.DiscoverTv.discovertv": "Серіали", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Активний фільтр} other {# Активні фільтри}}", "components.Discover.FilterSlideover.filters": "Фільтри", "components.Discover.FilterSlideover.from": "<PERSON>ід", "components.Discover.FilterSlideover.genres": "Ж<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.keywords": "Ключові слова", "components.Discover.FilterSlideover.originalLanguage": "Ори<PERSON><PERSON><PERSON><PERSON><PERSON>на мова", "components.Discover.FilterSlideover.ratingText": "Оцінки від {minValue} до {maxValue}", "components.Discover.FilterSlideover.releaseDate": "Дата релізу", "components.Discover.FilterSlideover.runtime": "Тривалість", "components.Discover.FilterSlideover.studio": "Студія", "components.Discover.FilterSlideover.tmdbuserscore": "Оцінка користувачів TMDB", "components.Discover.FilterSlideover.tmdbuservotecount": "Кількість голосів від користувачів TMDB", "components.Discover.FilterSlideover.to": "До", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Медіа додано до вашого <PlexWatchlistSupportLink>списку перегляду Plex</PlexWatchlistSupportLink>.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Ваш список перегляду Plex", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Нещодавно додані", "components.Discover.studios": "Студії", "components.Discover.tmdbmoviegenre": "Жанр фільму TMDB", "components.Discover.tmdbmoviekeyword": "Ключове слово фільму TMDB", "components.Discover.tmdbmoviestreamingservices": "Сервіси потокової передачі фільмів TMDB", "components.Discover.tmdbnetwork": "Телеканал TMDB", "components.Discover.tmdbstudio": "Студія TMDB", "components.Discover.tmdbtvgenre": "Жанр серіалу TMDB", "components.Discover.tmdbtvkeyword": "Ключове слово серіалу TMDB", "components.Discover.tvgenres": "Жанри серіалів", "components.Layout.UserWarnings.passwordRequired": "Потрібно вказати пароль.", "components.Login.initialsignin": "Підключитися", "components.Login.initialsigningin": "Підключення…", "components.Login.save": "Додати", "components.Login.signinwithjellyfin": "Використовуйте свій {mediaServerName} обліковий запис", "components.Login.title": "Додати email", "components.Login.username": "Ім'я користувача", "components.ManageSlideOver.removearr4k": "Видалити з 4K {arr}", "components.MovieDetails.downloadstatus": "Статус завантаження", "components.MovieDetails.imdbuserscore": "Оцінка користувачів IMDB", "components.MovieDetails.openradarr4k": "Відкрити фільм у 4К Radarr", "components.Selector.searchKeywords": "Ключові слова пошуку…", "components.Selector.searchStudios": "По<PERSON><PERSON><PERSON> студій…", "components.Selector.starttyping": "Початок введення для пошуку.", "components.Settings.Notifications.NotificationsPushover.sound": "Звук сповіщення", "components.Settings.SettingsMain.general": "Загальні", "components.Settings.SettingsMain.generalsettings": "Загальні налаштування", "components.Settings.SettingsMain.applicationTitle": "Назва програми", "components.Settings.SettingsMain.applicationurl": "URL програми", "components.Settings.SettingsMain.cacheImages": "Увімкнути кешування зображень", "components.Settings.SettingsMain.hideAvailable": "Приховати доступні медіа", "components.Settings.SettingsMain.locale": "Мова програми", "components.Settings.SettingsMain.originallanguage": "Мови для пошуку фільмів та серіалів", "components.Settings.SettingsMain.partialRequestsEnabled": "Дозволити запитувати серіали частково", "components.Settings.SettingsMain.toastApiKeySuccess": "Новий ключ API успішно згенеровано!", "components.Settings.SettingsMain.toastSettingsFailure": "Під час збереження налаштувань сталася помилка.", "components.Settings.SettingsMain.toastSettingsSuccess": "Налаштування успішно збережено!", "components.Settings.SettingsMain.validationApplicationUrl": "Ви повинні надати дійсну URL-адресу", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "URL-адреса не має закінчуватися косою рискою", "components.Settings.SonarrModal.animeSeriesType": "Тип аніме", "components.Settings.jellyfinSettings": "Налаштування {mediaServerName}", "components.Settings.jellyfinSettingsSuccess": "Налаштування {mediaServerName} успішно збережено!", "components.Settings.jellyfinlibraries": "Бібліотеки {mediaServerName}", "components.Settings.jellyfinsettings": "Налаштування {mediaServerName}", "components.Setup.signinWithPlex": "Використовуйте свій обліковий запис Plex", "components.TvDetails.play": "Відтворити в {mediaServerName}", "components.UserList.mediaServerUser": "Користув<PERSON>ч {mediaServerName}", "components.UserProfile.UserSettings.UserGeneralSettings.email": "Електронна пошта", "components.UserProfile.UserSettings.UserGeneralSettings.saving": "Збереження…", "components.UserProfile.UserSettings.UserNotificationSettings.sound": "Звук сповіщення", "i18n.collection": "Колекція", "components.Discover.CreateSlider.needresults": "Ви повинні мати принаймні 1 результат.", "components.Discover.CreateSlider.searchGenres": "Пошук жанрів…", "components.Discover.DiscoverMovieKeyword.keywordMovies": "Фільми {keywordTitle}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Активний фільтр} other {# Активні фільтри}}", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Серіали", "components.Discover.FilterSlideover.clearfilters": "Очистити всі активні фільтри", "components.Discover.FilterSlideover.runtimeText": "тривалість {minValue}-{maxValue} хвилин", "components.Discover.FilterSlideover.voteCount": "Кількість голосів від {minValue} до {maxValue}", "components.Discover.networks": "Телеканали", "components.Discover.resettodefault": "Скинути за замовчуванням", "components.Discover.tmdbsearch": "Пошук TMDB", "components.Discover.tmdbtvstreamingservices": "Сервіси потокового передавання серіалів TMDB", "components.Layout.Sidebar.browsemovies": "Фільми", "components.Layout.Sidebar.browsetv": "Серіали", "components.Layout.UserWarnings.emailInvalid": "Адреса електронної пошти недійсна.", "components.Login.saving": "Додавання…", "components.Login.validationhostformat": "Потрібна дійсна URL-адреса", "components.Login.validationusernamerequired": "Потрібно ім'я користувача", "components.ManageSlideOver.removearr": "Видалити з {arr}", "components.MovieDetails.openradarr": "Відкрити фільм у Radarr", "components.Selector.nooptions": "Немає результатів.", "components.Selector.searchGenres": "Виберіть жанри…", "components.Settings.Notifications.NotificationsPushover.deviceDefault": "Пристрій за замовчуванням", "components.Settings.Notifications.userEmailRequired": "Потрібен email користувача", "components.Settings.RadarrModal.tagRequestsInfo": "Автоматично додавати додатковий тег з ID та іменем користувача, який запитує", "components.Settings.SettingsAbout.supportjellyseerr": "Підтри<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.availability-sync": "Синхронізація доступності медіа", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Кожну {jobScheduleSeconds, plural, one {секунду} other {{jobScheduleSeconds} секунд}}", "components.Settings.SettingsMain.apikey": "Ключ API", "components.Settings.SettingsMain.toastApiKeyFailure": "Під час створення нового ключа API сталася помилка.", "components.UserProfile.UserSettings.UserNotificationSettings.deviceDefault": "Пристрій за замовчуванням", "components.UserList.noJellyfinuserstoimport": "Немає користувачів {mediaServerName} для імпорту.", "components.UserList.importfromJellyfinerror": "Під час імпорту користувачів з {mediaServerName} сталася помилка.", "components.Settings.SettingsMain.cacheImagesTip": "Кешувати зображення із зовнішніх джерел (потрібний значний об'єм дискового простору)", "components.Settings.SettingsMain.validationApplicationTitle": "Ви повинні вказати назву програми", "components.Settings.SonarrModal.seriesType": "Тип серіалу", "components.UserProfile.UserSettings.UserGeneralSettings.mediaServerUser": "Користув<PERSON>ч {mediaServerName}", "components.TitleCard.watchlistError": "Щось пішло не так, повторіть спробу.", "components.Settings.SettingsMain.generalsettingsDescription": "Налаштуйте глобальні параметри та параметри за замовчуванням для Jelly<PERSON>rr.", "components.Settings.SettingsMain.originallanguageTip": "Фільтрувати вміст за мовою оригіналу", "components.Settings.jellyfinSettingsFailure": "Під час збереження налаштувань {mediaServerName} сталася помилка.", "components.UserProfile.UserSettings.UserGeneralSettings.save": "Зберегти зміни", "components.TvDetails.play4k": "Відтворити 4K в {mediaServerName}", "components.Discover.CreateSlider.providetmdbsearch": "Введіть пошуковий запит", "components.Discover.FilterSlideover.streamingservices": "Сервіси потокового передавання", "components.Discover.moviegenres": "Жанри фільмів", "components.MovieDetails.play": "Відтворити в {mediaServerName}", "components.MovieDetails.play4k": "Відтворити в {mediaServerName} у 4К", "components.Selector.showless": "Згорнути", "components.Discover.CreateSlider.addSlider": "Додати повзунок", "components.Discover.CreateSlider.addcustomslider": "Створити власний повзунок", "components.Discover.CreateSlider.addfail": "Не вдалося створити новий повзунок.", "components.Discover.DiscoverSliderEdit.deletefail": "Не вдалося видалити повзунок.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Повзунок успішно видалено.", "components.Discover.CreateSlider.addsuccess": "Створено новий повзунок і збережено параметри налаштування Discover.", "components.Discover.CreateSlider.editSlider": "Редагувати повзунок", "components.Discover.CreateSlider.editfail": "Не вдалося відредагувати повзунок.", "components.Discover.CreateSlider.editsuccess": "Відредаговано повзунок і збережено параметри налаштування Discover.", "components.Discover.CreateSlider.providetmdbgenreid": "Введіть TMDB ID жанру", "components.Discover.CreateSlider.providetmdbkeywordid": "Введіть TMDB ID ключового слова", "components.Discover.CreateSlider.providetmdbnetwork": "Введіть TMDB ID мережі", "components.Discover.CreateSlider.providetmdbstudio": "Введіть TMDB ID студії", "components.Discover.CreateSlider.slidernameplaceholder": "Назва повзунка", "components.Discover.CreateSlider.validationDatarequired": "Ви повинні надати доступний для пошуку вміст.", "components.Discover.DiscoverMovies.sortPopularityAsc": "Популярність за зростанням", "components.Discover.DiscoverMovies.sortPopularityDesc": "Популярність за спаданням", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "Дата випуску за зростанням", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "Дата випуску за спаданням", "components.Discover.DiscoverMovies.sortTitleAsc": "Назва (А-Я) за зростанням", "components.Discover.DiscoverMovies.sortTitleDesc": "Назва (Я-А) за спаданням", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Рейтинг TMDB за зростанням", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Рейтинг TMDB за спаданням", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Дата виходу в ефір за зростанням", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Дата виходу в ефір за спаданням", "components.Discover.DiscoverTv.sortPopularityAsc": "Популярність за зростанням", "components.Discover.DiscoverTv.sortPopularityDesc": "Популярність за спаданням", "components.Discover.DiscoverTv.sortTitleAsc": "Назва (А-Я) за зростанням", "components.Discover.DiscoverTv.sortTitleDesc": "Назва (Я-А) за спаданням", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Рейтинг TMDB за зростанням", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Рейтинг TMDB за спаданням", "components.Discover.FilterSlideover.firstAirDate": "Дата виходу в ефір", "components.Discover.createnewslider": "Створити новий повзунок", "components.Discover.customizediscover": "Налаштувати Discover", "components.Discover.resetfailed": "Щось пішло не так під час скидання налаштувань Discover.", "components.Discover.resetsuccess": "Успішно скинуто параметри налаштування.", "components.Discover.resetwarning": "Скинути всі повзунки до стандартних. Це також видалить будь-які спеціальні повзунки!", "components.Discover.stopediting": "Зупинити редагування", "components.Discover.updatefailed": "Під час оновлення налаштувань Discover сталася помилка.", "components.Discover.updatesuccess": "Оновлено параметри налаштування Discover.", "components.Login.credentialerror": "Ім'я користувача або пароль неправильні.", "components.Login.description": "Оскільки ви вперше входите в {applicationName}, вам потрібно додати дійсну адресу електронної пошти.", "components.Login.validationEmailFormat": "Невірний email", "components.Login.validationEmailRequired": "Ви повинні вказати адресу електронної пошти", "components.Login.validationemailformat": "Потрібен дійсний email", "components.Layout.UserWarnings.emailRequired": "Потрібно вказати адресу електронної пошти.", "components.Login.emailtooltip": "Адресу не потрібно пов’язувати з вашим {mediaServerName} сервером.", "components.Login.validationhostrequired": "Необ<PERSON><PERSON>дна URL-адреса {mediaServerName}", "components.ManageSlideOver.manageModalRemoveMediaWarning": "* Це безповоротно видалить цей {mediaType} з {arr}, включаючи всі файли.", "components.Selector.showmore": "Показати більше", "components.Settings.RadarrModal.tagRequests": "Теги запитів", "components.Settings.SonarrModal.tagRequests": "Теги запитів", "components.Setup.configuremediaserver": "Налаштуйте медіасервер", "components.Settings.jellyfinsettingsDescription": "Налаштуйте свій {mediaServerName} сервер. {mediaServerName} відсканує бібліотеки, щоб побачити, які бібліотеки доступні.", "components.Settings.manualscanJellyfin": "Сканувати бібліотеки вручну", "components.Settings.menuJellyfinSettings": "{mediaServerName}", "components.Settings.jellyfinlibrariesDescription": "Бібліотеки {mediaServerName} перевіряються на наявність заголовків. Натисніть нижче, якщо в списку не вистачає бібліотек.", "components.Settings.saving": "Збереження…", "components.Settings.syncJellyfin": "Синхронізувати бібліотеки", "components.Settings.syncing": "Синхронізація", "components.Setup.signin": "Увійти", "components.Setup.signinWithJellyfin": "Введіть да<PERSON><PERSON>", "components.TitleCard.addToWatchList": "Додати в список перегляду", "components.TitleCard.watchlistSuccess": "<strong>{title}</strong> успішно додано до списку перегляду!", "components.UserList.importedfromJellyfin": "<strong>{userCount}</strong> {mediaServerName} {userCount, plural, one {user} other {users}} успішно імпортовано!", "components.UserList.importfromJellyfin": "Додати користувачів з {mediaServerName}", "components.Settings.manualscanDescriptionJellyfin": "Зазвичай це запускається лише раз на 24 години. Je<PERSON><PERSON>rr перевірятиме нещодавно доданий сервер {mediaServerName} більш агресивно. Якщо ви вперше налаштовуєте Jellyseerr, рекомендується одноразове повне сканування бібліотеки вручну!", "components.Settings.save": "Зберегти зміни", "components.TitleCard.watchlistDeleted": "<strong>{title}</strong> Успішно видалено зі списку перегляду!", "components.UserList.newJellyfinsigninenabled": "Параметр <strong>Увімкнути новий вхід на {mediaServerName}</strong> наразі ввімкнено. Користувачам {mediaServerName} із доступом до бібліотеки не потрібно імпортувати, щоб увійти.", "components.UserProfile.localWatchlist": "Список перегляду {username}", "components.Settings.jellyfinSettingsDescription": "Додатково налаштуйте внутрішні та зовнішні кінцеві точки для вашого сервера {mediaServerName}. У більшості випадків зовнішня URL-адреса відрізняється від внутрішньої URL-адреси. Для входу в систему {mediaServerName} також можна встановити спеціальну URL-адресу скидання пароля, якщо ви хочете переспрямувати на іншу сторінку скидання пароля. Ви також можете змінити ключ Jellyfin API, який був автоматично згенерований раніше.", "components.Settings.SonarrModal.tagRequestsInfo": "Автоматично додавати додатковий тег з ID та іменем користувача, який запитує", "components.Login.servertype": "Тип сервера", "components.Login.validationPortRequired": "Ви повинні вказати дійсний номер порту", "components.Login.validationUrlTrailingSlash": "URL-адреса не має закінчуватися косою рискою", "components.MovieDetails.watchlistSuccess": "<strong>{title}</strong> додано до списку перегляду!", "components.MovieDetails.addtowatchlist": "Додати до списку перегляду", "components.Selector.pilot": "Піл<PERSON><PERSON>", "components.RequestList.RequestItem.profileName": "Профіль", "components.Settings.jellyfinSyncFailedAutomaticGroupedFolders": "Спеціальна автентифікація з автоматичним групуванням бібліотек не підтримується", "components.Settings.timeout": "<PERSON>ас очікування", "components.Selector.canceled": "Скасовано", "components.Settings.jellyfinForgotPasswordUrl": "Забули пароль URL", "components.TitleCard.watchlistCancel": "список перегляду для <strong>{title}</strong> скасовано.", "components.TvDetails.watchlistDeleted": "<strong>{title}</strong> Видалено зі списку перегляду успішно!", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailformat": "Потрібен дійсний email", "components.Login.adminerror": "Для входу необхідно використовувати обліковий запис адміністратора.", "components.Login.enablessl": "Використовувати SSL", "components.Login.hostname": "{mediaServerName} URL", "components.Login.invalidurlerror": "Неможливо підключитися до {mediaServerName} сервера.", "components.Login.port": "Порт", "components.Login.back": "Назад", "components.Login.validationUrlBaseLeadingSlash": "Базова URL-адреса повинна мати скісну риску на початку", "components.Login.validationUrlBaseTrailingSlash": "Базова URL-адреса не має закінчуватися косою рискою", "components.Login.validationservertyperequired": "Виберіть тип сервера", "components.Login.urlBase": "Базова URL-адреса", "components.Login.validationHostnameRequired": "Ви повинні вказати дійсний домен або IP-адресу", "components.MovieDetails.watchlistDeleted": "<strong>{title}</strong> Видалено зі списку перегляду успішно!", "components.MovieDetails.watchlistError": "Щось пішло не так, повторіть спробу.", "components.Selector.ended": "Завершено", "components.Selector.inProduction": "У виробництві", "components.Selector.planned": "Планується", "components.Selector.returningSeries": "Повернення серіалу", "components.Selector.searchStatus": "Виберіть статус...", "components.Settings.jellyfinSyncFailedGenericError": "Під час синхронізації бібліотек сталася помилка", "components.Settings.jellyfinSyncFailedNoLibrariesFound": "Бібліотеки не знайдено", "components.Setup.configemby": "Налаштувати Emby", "components.Setup.configjellyfin": "Налаштува<PERSON><PERSON>", "components.Setup.configplex": "Налаштувати Plex", "components.Setup.servertype": "Виберіть тип сервера", "components.Setup.signinWithEmby": "Введіть свої дані Emby", "components.Setup.subtitle": "Почніть із вибору медіа-сервера", "components.StatusBadge.seasonnumber": "С{seasonNumber}", "components.Setup.back": "Назад", "components.TvDetails.addtowatchlist": "Додати до списку перегляду", "components.TvDetails.removefromwatchlist": "Видалити зі списку перегляду", "components.TvDetails.watchlistError": "Щось пішло не так, повторіть спробу.", "components.TvDetails.watchlistSuccess": "<strong>{title}</strong> додано до списку перегляду!", "components.UserList.username": "Ім'я користувача", "components.UserProfile.UserSettings.UserGeneralSettings.validationemailrequired": "Потрібно вказати Email", "components.Settings.invalidurlerror": "Неможливо підключитися до {mediaServerName} сервера.", "components.UserList.validationUsername": "Ви повинні вказати ім'я користувача", "components.Discover.FilterSlideover.status": "Статус", "components.MovieDetails.removefromwatchlist": "Видалити зі списку перегляду", "components.PermissionEdit.viewblacklistedItemsDescription": "Надати дозвіл на перегляд медіафайлів із чорного списку.", "i18n.blacklistSuccess": "<strong>{title}</strong> було успішно додано в чорний список.", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegion": "Регіон для пошуку фільмів та серіалів", "components.PermissionEdit.blacklistedItemsDescription": "Надати дозвіл на додавання медіа в чорний список.", "components.PermissionEdit.viewblacklistedItems": "Перегляд медіафайлів із чорного списку.", "components.UserProfile.UserSettings.UserGeneralSettings.discoverRegionTip": "Вміст фільтрується за доступністю у вибраному регіоні", "i18n.blacklistError": "Щось пішло не так, повторіть спробу.", "i18n.blacklistDuplicateError": "<strong>{title}</strong> вже додано в чорний список.", "components.PermissionEdit.manageblacklistDescription": "Надати дозвіл на керування медіа-файлами з чорного списку.", "components.Settings.apiKey": "Ключ API", "i18n.removefromBlacklist": "Видалити з чорного списку", "components.RequestList.RequestItem.removearr": "Видалити з {arr}", "components.Settings.SettingsMain.discoverRegion": "Регіон для пошуку фільмів та серіалів", "component.BlacklistBlock.blacklistdate": "Дата додавання в чорний список", "components.Blacklist.blacklistdate": "дата", "components.Blacklist.blacklistsettings": "Налаштування чорного списку", "components.Blacklist.mediaTmdbId": "tmdb Id", "components.Blacklist.mediaType": "Тип", "components.Layout.Sidebar.blacklist": "Чорний список", "components.Settings.SettingsMain.discoverRegionTip": "Фільтрувати вміст за регіональною доступністю", "components.Settings.scanbackground": "Сканування працюватиме у фоновому режимі. Тим часом ви можете продовжити процес налаштування.", "i18n.addToBlacklist": "Додати в чорний список", "i18n.blacklist": "Чорний список", "i18n.removeFromBlacklistSuccess": "<strong>{title}</strong>було успішно видалено з чорного списку.", "i18n.blacklisted": "У чорному списку", "components.Settings.SettingsJobsCache.usersavatars": "Ава<PERSON><PERSON><PERSON> користувача", "components.PermissionEdit.manageblacklist": "Керувати чорним списком", "components.Settings.Notifications.validationWebhookRoleId": "Ви повинні надати дійсний ідентифікатор ролі Discord", "components.Settings.OverrideRuleModal.service": "Серв<PERSON>с", "components.Settings.SettingsNetwork.docs": "документація", "components.Settings.SettingsNetwork.network": "Мережа", "components.Settings.SettingsNetwork.networksettings": "Налаштування мережі", "components.Settings.SettingsNetwork.toastSettingsFailure": "Під час збереження налаштувань сталася помилка.", "components.Settings.SettingsNetwork.toastSettingsSuccess": "Налаштування успішно збережено!"}