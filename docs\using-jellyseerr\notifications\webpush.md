---
title: Web Push
description: Configure web push notifications for your users.
sidebar_position: 2
---

# Web Push

The web push notification agent enables you and your users to receive Jellyseerr notifications in a supported browser.

This notification agent does not require any configuration, but is not enabled in Jellyseerr by default.

:::warning
Web push notifications require a secure connection to your Jellyseerr instance. Refer to the [Reverse Proxy](/extending-jellyseerr/reverse-proxy) documentation for more information.
:::

To set up web push notifications, simply enable the agent in **Settings → Notifications → Web Push**. You and your users will then be prompted to allow notifications in your web browser.

Users can opt out of these notifications, or customize the notification types they would like to subscribe to, in their user settings.

:::info
Web push notifications offer a native notification experience without the need to install an app.
:::
