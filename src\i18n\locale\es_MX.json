{"components.Settings.SonarrModal.ssl": "Usar SSL", "components.Settings.SonarrModal.servername": "Nombre del Servidor", "components.Settings.SonarrModal.server4k": "Servidor 4K", "components.Settings.SonarrModal.selectRootFolder": "Selecciona la carpeta raíz", "components.Settings.SonarrModal.selectQualityProfile": "Selecciona un perfil de calidad", "components.Settings.SonarrModal.seasonfolders": "Carpetas por Temporada", "components.Settings.SonarrModal.rootfolder": "Carpeta Raíz", "components.Settings.SonarrModal.qualityprofile": "Perfil de Calidad", "components.Settings.SonarrModal.port": "Puerto", "components.Settings.SonarrModal.hostname": "Nombre de Host o Dirección IP", "components.Settings.SonarrModal.editsonarr": "<PERSON><PERSON>", "components.Settings.SonarrModal.defaultserver": "Servidor por Defecto", "components.Settings.SonarrModal.createsonarr": "<PERSON><PERSON>dir <PERSON> Servidor <PERSON>", "components.Settings.SonarrModal.baseUrl": "URL de base", "components.Settings.SonarrModal.apiKey": "Clave API", "components.Settings.SonarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsAbout.version": "Versión", "components.Settings.SettingsAbout.totalrequests": "Peticiones Totales", "components.Settings.SettingsAbout.totalmedia": "Contenido Total", "components.Settings.SettingsAbout.overseerrinformation": "<PERSON><PERSON>", "components.Settings.SettingsAbout.githubdiscussions": "Discursiones en GitHub", "components.Settings.SettingsAbout.gettingsupport": "Soporte", "components.Settings.RadarrModal.validationRootFolderRequired": "Debes seleccionar una carpeta raíz", "components.Settings.RadarrModal.validationProfileRequired": "Debes seleccionar un perfil de calidad", "components.Settings.RadarrModal.validationPortRequired": "Debes proporcionar un número de puerto válido", "components.Settings.RadarrModal.validationNameRequired": "Debes proporcionar un nombre de servidor", "components.Settings.RadarrModal.validationHostnameRequired": "Debes proporcionar un nombre de host o dirección IP válido", "components.Settings.RadarrModal.validationApiKeyRequired": "Debes proporcionar la clave API", "components.Settings.RadarrModal.toastRadarrTestSuccess": "¡Conexión con Radarr establecida con éxito!", "components.Settings.RadarrModal.toastRadarrTestFailure": "Error al connectar al Radarr.", "components.Settings.RadarrModal.ssl": "Usar SSL", "components.Settings.RadarrModal.servername": "Nombre del Servidor", "components.Settings.RadarrModal.server4k": "Servidor 4K", "components.Settings.RadarrModal.selectRootFolder": "Selecciona la carpeta raíz", "components.Settings.RadarrModal.selectQualityProfile": "Selecciona un perfil de calidad", "components.Settings.RadarrModal.selectMinimumAvailability": "Selecciona Disponibilidad Mínima", "components.Settings.RadarrModal.rootfolder": "Carpeta Raíz", "components.Settings.RadarrModal.qualityprofile": "Perfil de Calidad", "components.Settings.RadarrModal.port": "Puerto", "components.Settings.RadarrModal.minimumAvailability": "Disponibilidad Mínima", "components.Settings.RadarrModal.hostname": "Nombre de Host o Dirección IP", "components.Settings.RadarrModal.editradarr": "<PERSON><PERSON>", "components.Settings.RadarrModal.defaultserver": "Servidor por Defecto", "components.Settings.RadarrModal.createradarr": "<PERSON><PERSON>dir Nuevo Servidor Radarr", "components.Settings.RadarrModal.baseUrl": "URL de base", "components.Settings.RadarrModal.apiKey": "Clave API", "components.Settings.RadarrModal.add": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.Notifications.webhookUrl": "URL de Webhook", "components.Settings.Notifications.validationSmtpPortRequired": "Debes proporcionar un número de puerto válido", "components.Settings.Notifications.validationSmtpHostRequired": "Debes proporcionar un nombre válido de host o una dirección IP", "components.Settings.Notifications.smtpPort": "Puerto SMTP", "components.Settings.Notifications.smtpHost": "Host SMTP", "components.Settings.Notifications.emailsettingssaved": "¡Ajustes de notificación de Email guardados con éxito!", "components.Settings.Notifications.emailsettingsfailed": "Fallo al guardar ajustes de notificación de Email.", "components.Settings.Notifications.emailsender": "Dirección del Remitente", "components.Settings.Notifications.discordsettingssaved": "¡Ajustes de notificación de Discord guardados con éxito!", "components.Settings.Notifications.discordsettingsfailed": "Fallo al guardar ajustes de notificación de Discord.", "components.Settings.Notifications.authUser": "Usuario SMTP", "components.Settings.Notifications.authPass": "Contraseña SMTP", "components.Settings.Notifications.agentenabled": "Habilitar Agente", "components.Search.searchresults": "Resultado de la búsqueda", "components.RequestModal.selectseason": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "components.RequestModal.seasonnumber": "Temporada {number}", "components.RequestModal.season": "Temporada", "components.RequestModal.requestseasons": "Solicitar {seasonCount} {seasonCount, plural, one {Temporada} other {Temporadas}}", "components.RequestModal.requestfrom": "La solicitud de {username} está pendiente de aprobación.", "components.RequestModal.requestadmin": "Esta solicitud será aprobada automáticamente.", "components.RequestModal.requestSuccess": "¡<strong>{title}</strong> solicitada con éxito!", "components.RequestModal.requestCancel": "Solicitud para <strong>{title}</strong> cancelada.", "components.RequestModal.pendingrequest": "Solicitud pendiente", "components.RequestModal.numberofepisodes": "# de Episodios", "components.RequestModal.cancel": "<PERSON><PERSON><PERSON>", "components.RequestList.requests": "Solicitudes", "components.RequestList.RequestItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestCard.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.RequestBlock.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.PersonDetails.ascharacter": "como {character}", "components.PersonDetails.appearsin": "Apariciones", "components.MovieDetails.similar": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.runtime": "{minutes} minutos", "components.MovieDetails.revenue": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.releasedate": "{releaseCount, plural, one {<PERSON><PERSON>} other {<PERSON><PERSON><PERSON>}} de <PERSON>nzamie<PERSON>", "components.MovieDetails.cast": "Reparto", "components.MovieDetails.MovieCast.fullcast": "<PERSON>arto <PERSON>", "components.MovieDetails.recommendations": "Recomendaciones", "components.MovieDetails.overviewunavailable": "Resumen indisponible.", "components.MovieDetails.overview": "Resumen", "components.MovieDetails.originallanguage": "Idioma Original", "components.MovieDetails.budget": "Presupuesto", "components.Layout.UserDropdown.signout": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.users": "Usuarios", "components.Layout.Sidebar.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.Sidebar.requests": "Solicitudes", "components.Layout.Sidebar.dashboard": "Descubrir", "components.Layout.SearchInput.searchPlaceholder": "Buscar Películas y Series", "components.Discover.upcomingmovies": "<PERSON>ró<PERSON><PERSON>", "components.Discover.upcoming": "<PERSON>ró<PERSON><PERSON>", "components.Discover.trending": "Tendencias", "components.Discover.recentrequests": "Peticiones Recientes", "components.Discover.recentlyAdded": "Agregado Recientemente", "components.Discover.populartv": "Series Populares", "components.Discover.popularmovies": "Películas Populares", "components.Settings.addsonarr": "<PERSON><PERSON><PERSON><PERSON> servid<PERSON>", "components.Settings.address": "Dirección", "components.Settings.addradarr": "<PERSON>g<PERSON><PERSON> servid<PERSON>", "components.Settings.activeProfile": "Perfil activo", "components.Settings.SonarrModal.validationRootFolderRequired": "Debes seleccionar una carpeta raíz", "components.Settings.SonarrModal.validationProfileRequired": "Debes seleccionar un perfil", "components.Settings.SonarrModal.validationPortRequired": "Debes proporcionar un número de puerto valido", "components.Settings.SonarrModal.validationNameRequired": "Debes proporcionar un nombre de servidor", "components.Settings.SonarrModal.validationHostnameRequired": "Debes proporcionar un nombre de host o dirección IP válido", "components.Settings.SonarrModal.validationApiKeyRequired": "Debes proporcionar la clave API", "components.Settings.menuLogs": "Registro", "pages.returnHome": "Volver al Inicio", "pages.oops": "Ups", "i18n.unavailable": "No Disponible", "i18n.tvshows": "Series", "i18n.processing": "Procesando", "i18n.pending": "Pendiente", "i18n.partiallyavailable": "Parcialmente Disponible", "i18n.movies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.delete": "Eliminar", "i18n.declined": "<PERSON><PERSON><PERSON><PERSON>", "i18n.decline": "<PERSON><PERSON><PERSON>", "i18n.cancel": "<PERSON><PERSON><PERSON>", "i18n.available": "Disponible", "i18n.approved": "Aprobado", "i18n.approve": "<PERSON><PERSON><PERSON>", "components.UserList.userlist": "Lista de usuarios", "components.UserList.user": "Usuario", "components.UserList.totalrequests": "Solicitudes", "components.UserList.role": "Rol", "components.UserList.plexuser": "Usuario de Plex", "components.UserList.created": "Unido", "components.UserList.admin": "Administrador", "components.TvDetails.similar": "Series Similares", "components.TvDetails.recommendations": "Recomendaciones", "components.TvDetails.overviewunavailable": "Resumen no disponible.", "components.TvDetails.overview": "Resumen", "components.TvDetails.originallanguage": "Idioma original", "components.TvDetails.cast": "Reparto", "components.TvDetails.TvCast.fullseriescast": "Reparto completo de la serie", "components.Setup.welcome": "Bienvenido a Jellyseerr", "components.Setup.signinMessage": "Comience iniciando sesión con su cuenta de Plex", "components.Setup.finishing": "Finalizando…", "components.Setup.finish": "Finalizar configuración", "components.Setup.continue": "<PERSON><PERSON><PERSON><PERSON>", "components.Setup.configureservices": "Configu<PERSON> servic<PERSON>", "components.Settings.validationPortRequired": "Debes proporcionar un número de puerto válido", "components.Settings.validationHostnameRequired": "Debes proporcionar un nombre de host o dirección IP válido", "components.Settings.startscan": "Iniciar <PERSON>", "components.Settings.ssl": "SSL", "components.Settings.sonarrsettings": "<PERSON><PERSON><PERSON><PERSON> Sonarr", "components.Settings.radarrsettings": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.port": "Puerto", "components.Settings.plexsettingsDescription": "Configure los ajustes de su servidor Plex. Jellyseerr escanea tu biblioteca para determinar la disponibilidad de contenidos.", "components.Settings.plexsettings": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>", "components.Settings.plexlibrariesDescription": "Las bibliotecas en las que Jellyseerr escanea para buscar títulos. Configure y guarde la configuración de conexión Plex, y después haga clic en el botón de abajo si no aparece ninguna.", "components.Settings.plexlibraries": "Bibliotecas Plex", "components.Settings.notrunning": "Sin ejecutarse", "components.Settings.notificationsettings": "Configuración de notificaciones", "components.Settings.menuServices": "<PERSON><PERSON><PERSON>", "components.Settings.menuPlexSettings": "Plex", "components.Settings.menuNotifications": "Notificaciones", "components.Settings.menuJobs": "<PERSON><PERSON><PERSON> y Caché", "components.Settings.menuGeneralSettings": "General", "components.Settings.menuAbout": "Acerca de", "components.Settings.manualscanDescription": "Normalmente, esto sólo se ejecutará una vez cada 24 horas. Je<PERSON><PERSON>rr comprobará de forma más agresiva los añadidos recientemente de su servidor Plex. ¡Si es la primera vez que configura Plex, se recomienda un escaneo manual completo de la biblioteca!", "components.Settings.manualscan": "Escaneo Manual de Biblioteca", "components.Settings.librariesRemaining": "Bibliotecas restantes: {count}", "components.Settings.hostname": "Nombre de host o Dirección IP", "components.Settings.deleteserverconfirm": "¿Está seguro de que desea eliminar este servidor?", "components.Settings.default4k": "4K predeterminado", "components.Settings.default": "Predeterminado", "components.Settings.currentlibrary": "Biblioteca actual: {name}", "components.Settings.copied": "Clave API copiada en el portapapeles.", "components.Settings.cancelscan": "<PERSON><PERSON><PERSON>", "i18n.deleting": "Eliminando…", "components.UserList.userdeleteerror": "Algo salió mal al eliminar al usuario.", "components.UserList.userdeleted": "¡Usuario eliminado con éxito!", "components.UserList.deleteuser": "Eliminar usuario", "components.UserList.deleteconfirm": "¿Está seguro de que desea eliminar este usuario? Se eliminarán todas sus solicitudes de forma permanente.", "components.Settings.SonarrModal.testFirstRootFolders": "Probar conexión para cargar carpetas raíz", "components.Settings.SonarrModal.testFirstQualityProfiles": "Probar conexión para cargar perfiles de calidad", "components.Settings.SonarrModal.loadingrootfolders": "Cargando carpetas raíz…", "components.Settings.SonarrModal.loadingprofiles": "Cargando perfiles de calidad…", "components.Settings.RadarrModal.validationMinimumAvailabilityRequired": "Debes seleccionar disponibilidad mínima", "components.Settings.RadarrModal.testFirstRootFolders": "Prueba la conexión para cargar carpetas raíz", "components.Settings.RadarrModal.testFirstQualityProfiles": "Prueba la conexión para cargar perfiles de calidad", "components.Settings.RadarrModal.loadingrootfolders": "Cargando carpetas raíz…", "components.Settings.RadarrModal.loadingprofiles": "Cargando perfiles de calidad…", "i18n.close": "<PERSON><PERSON><PERSON>", "components.TvDetails.showtype": "Tipos de Series", "components.TvDetails.network": "{networkCount, plural, one {Red} other {Redes}}", "components.TvDetails.anime": "Anime", "components.Settings.SonarrModal.animerootfolder": "Carpeta raíz de anime", "components.Settings.SonarrModal.animequalityprofile": "Perfil de calidad de anime", "components.Settings.SettingsAbout.timezone": "Zona horaria", "components.Settings.SettingsAbout.supportoverseerr": "Apoya a <PERSON><PERSON>rr", "components.Settings.SettingsAbout.helppaycoffee": "Ayúdame invitándome a un café", "components.Settings.SettingsAbout.Releases.viewongithub": "Ver en GitHub", "components.Settings.SettingsAbout.Releases.viewchangelog": "Ver registro de cambios", "components.Settings.SettingsAbout.Releases.versionChangelog": "Cambios de la versión {version}", "components.Settings.SettingsAbout.Releases.releases": "Versiones", "components.Settings.SettingsAbout.Releases.releasedataMissing": "La fecha de lanzamiento no está actualmente disponible.", "components.Settings.SettingsAbout.Releases.latestversion": "Última Versión", "components.Settings.SettingsAbout.Releases.currentversion": "Actual", "components.MovieDetails.studio": "{studioCount, plural, one {<PERSON>st<PERSON><PERSON>} other {<PERSON>st<PERSON><PERSON>}}", "components.UserList.importfromplexerror": "Algo salió mal importando usuarios de Plex.", "components.UserList.importfromplex": "Importar Usuarios de Plex", "components.UserList.importedfromplex": "<strong> {userCount} </strong> Plex {userCount, plural, one {usuario} other {usuarios}} importado correctamente!", "components.TvDetails.viewfullcrew": "<PERSON><PERSON><PERSON>", "components.TvDetails.firstAirDate": "Primera fecha de emisión", "components.TvDetails.TvCrew.fullseriescrew": "Equipo completo de la serie", "components.Settings.Notifications.allowselfsigned": "Permitir certificados autofirmados", "components.PersonDetails.crewmember": "Equipo", "components.MovieDetails.viewfullcrew": "<PERSON><PERSON><PERSON>", "components.MovieDetails.MovieCrew.fullcrew": "E<PERSON><PERSON>", "components.CollectionDetails.requestcollection": "Solicitar <PERSON>", "components.CollectionDetails.overview": "Resumen", "components.CollectionDetails.numberofmovies": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n.retry": "Reintentar", "i18n.requested": "Solicitado", "i18n.failed": "Fallido", "components.TvDetails.watchtrailer": "<PERSON>er Trailer", "components.Settings.Notifications.NotificationsSlack.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsSlack.slacksettingssaved": "¡Ajustes de notificación de Slack guardados con éxito!", "components.Settings.Notifications.NotificationsSlack.slacksettingsfailed": "Fallo al guardar ajustes de notificación de Slack.", "components.Settings.Notifications.NotificationsSlack.agentenabled": "Habilitar Agente", "components.RequestList.RequestItem.failedretry": "Algo salió mal al reintentar la solicitud.", "components.MovieDetails.watchtrailer": "<PERSON>er Trailer", "components.NotificationTypeSelector.mediarequestedDescription": "Envía una notificación cuando se solicita nuevo contenido que requiere ser aprobado.", "components.Settings.SettingsAbout.documentation": "Documentación", "components.Settings.Notifications.validationChatIdRequired": "Debes proporcionar un ID de chat válido", "components.Settings.Notifications.validationBotAPIRequired": "Debes proporcionar un token de autorización del bot", "components.Settings.Notifications.telegramsettingssaved": "¡Se han guardado los ajustes de notificación de Telegram con éxito!", "components.Settings.Notifications.telegramsettingsfailed": "La configuración de notificaciones de Telegram no se pudo guardar.", "components.Settings.Notifications.senderName": "Nombre del remitente", "components.Settings.Notifications.chatId": "ID de chat", "components.Settings.Notifications.botAPI": "Token de Autorización del Bot", "components.NotificationTypeSelector.mediarequested": "Petición pendiente de aprobar", "components.NotificationTypeSelector.mediafailedDescription": "Envía una notificación cuando el contenido no se agrega a los servicios (Radarr / Sonarr).", "components.NotificationTypeSelector.mediafailed": "Procesamiento de Petición Fallida", "components.NotificationTypeSelector.mediaavailableDescription": "Envía una notificación cuando el contenido solicitado está disponible.", "components.NotificationTypeSelector.mediaavailable": "Petición Disponible", "components.NotificationTypeSelector.mediaapprovedDescription": "Envía una notificación cuando el contenido solicitado es aprobado manualmente.", "components.NotificationTypeSelector.mediaapproved": "Petición Aprobada", "i18n.request": "Solicitar", "components.Settings.Notifications.NotificationsPushover.validationUserTokenRequired": "Debes proporcionar una clave de usuario o grupo válida", "components.Settings.Notifications.NotificationsPushover.validationAccessTokenRequired": "Debes proporcionar un token de aplicación válido", "components.Settings.Notifications.NotificationsPushover.userToken": "Clave de usuario o grupo", "components.Settings.Notifications.NotificationsPushover.pushoversettingssaved": "¡Se han guardado los ajustes de notificación de Pushover!", "components.Settings.Notifications.NotificationsPushover.pushoversettingsfailed": "No se pudo guardar la configuración de notificaciones de Pushover.", "components.Settings.Notifications.NotificationsPushover.agentenabled": "Agente habilitado", "components.Settings.Notifications.NotificationsPushover.accessToken": "Token de aplicación API", "components.RequestList.sortModified": "Última modificación", "components.RequestList.sortAdded": "Más Reciente", "components.RequestList.showallrequests": "Mostrar todas las solicitudes", "components.RequestBlock.requestoverrides": "Anulaciones de solicitudes", "i18n.edit": "<PERSON><PERSON>", "components.UserList.validationpasswordminchars": "La contraseña es demasiado corta; debe tener 8 caracteres como mínimo", "components.UserList.usercreatedsuccess": "¡Usuario creado con éxito!", "components.UserList.usercreatedfailed": "Algo salió mal al intentar crear al usuario.", "components.UserList.passwordinfodescription": "Configura una URL de aplicación y habilita las notificaciones por email para poder utilizar las contraseñas generadas automáticamente.", "components.UserList.password": "Contraseña", "components.UserList.localuser": "Usuario local", "components.UserList.email": "Dirección de correo electrónico", "components.UserList.creating": "<PERSON><PERSON><PERSON><PERSON>", "components.UserList.createlocaluser": "Crear usuario local", "components.UserList.create": "<PERSON><PERSON><PERSON>", "components.UserList.autogeneratepassword": "Generar Contraseña Automáticamente", "components.StatusBadge.status4k": "4K {status}", "components.Settings.Notifications.NotificationsWebhook.webhooksettingssaved": "¡Configuración de notificación de webhook guardada con éxito!", "components.Settings.Notifications.NotificationsWebhook.webhooksettingsfailed": "No se pudo guardar la configuración de notificación de webhook.", "components.Settings.Notifications.NotificationsWebhook.webhookUrl": "URL de Webhook", "components.Settings.Notifications.NotificationsWebhook.validationJsonPayloadRequired": "Debes proporcionar un payload de JSON válido", "components.Settings.Notifications.NotificationsWebhook.templatevariablehelp": "Ayuda de variable de plantilla", "components.Settings.Notifications.NotificationsWebhook.resetPayloadSuccess": "¡Payload de JSON restablecido con éxito!", "components.Settings.Notifications.NotificationsWebhook.resetPayload": "Restablecer predeterminado", "components.Settings.Notifications.NotificationsWebhook.customJson": "Payload de JSON", "components.Settings.Notifications.NotificationsWebhook.authheader": "Encabezado de autorización", "components.Settings.Notifications.NotificationsWebhook.agentenabled": "Habilitar Agente", "components.RequestModal.requestedited": "¡Solicitud para <strong>{title}</strong> modificada con éxito!", "components.RequestModal.requestcancelled": "Solicitud para <strong>{title}</strong> cancelada.", "components.RequestModal.pending4krequest": "Solicitud 4K Pendiente", "components.RequestModal.errorediting": "Algo salió mal al editar la solicitud.", "components.RequestModal.AdvancedRequester.rootfolder": "Carpeta Raíz", "components.RequestModal.AdvancedRequester.qualityprofile": "Perfil de calidad", "components.RequestModal.AdvancedRequester.destinationserver": "<PERSON><PERSON><PERSON>", "components.RequestModal.AdvancedRequester.default": "{name} (Predeterminado)", "components.RequestModal.AdvancedRequester.animenote": "* Esta serie es un anime.", "components.RequestModal.AdvancedRequester.advancedoptions": "<PERSON><PERSON><PERSON>", "components.RequestButton.viewrequest4k": "Ver Solicitud 4K", "components.RequestButton.viewrequest": "<PERSON><PERSON>", "components.RequestButton.requestmore4k": "Solicitar más en 4K", "components.RequestButton.requestmore": "Solicitar más", "components.RequestButton.declinerequests": "Rechazar {requestCount, plural, one {solicitud} other {{requestCount} solicitudes}}", "components.RequestButton.declinerequest4k": "<PERSON><PERSON><PERSON> 4K", "components.RequestButton.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.decline4krequests": "Rechazar {requestCount, plural, one {solicitud en 4K} other {{requestCount} solicitudes en 4K}}", "components.RequestButton.approverequests": "Aprobar {requestCount, plural, one {solicitud} other {{requestCount} solicitudes}}", "components.RequestButton.approverequest4k": "Aprobar <PERSON> 4K", "components.RequestButton.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestButton.approve4krequests": "Aprobar {requestCount, plural, one {petición en 4K} other {requestCount} peticiones en 4K}}", "components.RequestBlock.server": "<PERSON><PERSON><PERSON>", "components.RequestBlock.rootfolder": "Carpeta Raíz", "components.RequestBlock.profilechanged": "Perfil de Calidad", "components.MediaSlider.ShowMoreCard.seemore": "<PERSON>er más", "components.Login.validationpasswordrequired": "Se requiere contraseña", "components.Login.validationemailrequired": "Debes indicar una dirección de email válida", "components.Login.signinwithoverseerr": "Usa tu cuenta de {applicationTitle}", "components.Login.password": "Contraseña", "components.Login.loginerror": "Algo salió mal al intentar iniciar sesión.", "components.Login.email": "Dirección de correo electrónico", "components.NotificationTypeSelector.mediadeclined": "<PERSON><PERSON><PERSON>", "components.RequestModal.autoapproval": "Aprobación Automática", "components.NotificationTypeSelector.mediadeclinedDescription": "Envía notificaciones cuando las solicitudes sean rechazadas.", "i18n.experimental": "Experimental", "components.Login.signingin": "Iniciando <PERSON>…", "components.Login.signin": "<PERSON><PERSON><PERSON>", "components.Login.signinwithplex": "Usa tu cuenta de Plex", "components.Login.signinheader": "Inicia sesión para continuar", "components.PermissionEdit.request4k": "Solicitar 4K", "components.PermissionEdit.request": "Solicitar", "components.PermissionEdit.admin": "Administrador", "components.Discover.discover": "Descubre", "components.Layout.UserDropdown.settings": "<PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.myprofile": "Perfil", "components.Discover.DiscoverTvLanguage.languageSeries": "Series en {language}", "components.Discover.DiscoverTvGenre.genreSeries": "Series de {genre}", "components.Discover.DiscoverStudio.studioMovies": "Pelí<PERSON>s de {studio}", "components.Discover.DiscoverNetwork.networkSeries": "Series de {network}", "components.Discover.DiscoverMovieLanguage.languageMovies": "{language} Películas", "components.Discover.DiscoverMovieGenre.genreMovies": "Pel<PERSON><PERSON><PERSON> de {genre}", "i18n.loading": "Cargando…", "components.ResetPassword.emailresetlink": "Enviar enlace de recuperación por email", "components.ResetPassword.email": "Dirección de Email", "components.ResetPassword.confirmpassword": "Con<PERSON><PERSON><PERSON>", "components.RequestModal.requesterror": "Algo fue mal al realizar la solicitud.", "components.RequestModal.SearchByNameModal.notvdbiddescription": "No se ha podido emparejar automáticamente tu solicitud. Por favor, seleccione el correcto de la lista.", "components.RequestModal.AdvancedRequester.requestas": "Pedir como", "components.RequestModal.AdvancedRequester.languageprofile": "Perfil de Idioma", "components.RequestModal.AdvancedRequester.folder": "{path} ({space})", "components.RequestList.RequestItem.requested": "Pedida", "components.RequestList.RequestItem.modifieduserdate": "{date} por {user}", "components.RequestList.RequestItem.modified": "Modificado", "components.RegionSelector.regionServerDefault": "({Region}) por defecto", "components.RegionSelector.regionDefault": "Todas las Regiones", "components.PermissionEdit.viewrequestsDescription": "Conceder permiso para ver las solicitudes de otros usuarios.", "components.PermissionEdit.viewrequests": "Ver Solicitudes", "components.PermissionEdit.usersDescription": "Concede permisos para gestionar usuarios. Los usuarios con este permiso no pueden modificar usuarios o conceder privilegios de Administrador.", "components.PermissionEdit.users": "Gestión de Usuarios", "components.PermissionEdit.requestDescription": "Concede permisos para solicitar contenidos que no sean 4K.", "components.PermissionEdit.request4kTvDescription": "Concede permisos para solicitar series en 4K.", "components.PermissionEdit.request4kTv": "Pedir Series 4K", "components.PermissionEdit.request4kMoviesDescription": "Concede permisos para solicitar películas en 4K.", "components.PermissionEdit.request4kDescription": "Concede permisos para solicitar contenidos en 4K.", "components.PermissionEdit.managerequestsDescription": "Concede permisos para gestionar las solicitudes de contenidos. Todas las solicitudes de un usuario con este permiso serán aprobadas automáticamente.", "components.PermissionEdit.request4kMovies": "Solicita Películas 4K", "components.PermissionEdit.managerequests": "Gestionar Solicitudes", "components.PermissionEdit.autoapproveSeriesDescription": "Concede aprobación automática para todas las solicitudes de series no 4K.", "components.PermissionEdit.autoapproveSeries": "Auto-Aprueba Series", "components.PermissionEdit.autoapproveMoviesDescription": "Concede aprobación automática para todas las solicitudes de películas no 4K.", "components.PermissionEdit.autoapproveMovies": "Auto-Aprueba Películas", "components.PermissionEdit.autoapproveDescription": "Concede aprobación automática para todas las solicitudes que no sean 4K.", "components.PermissionEdit.autoapprove4kSeriesDescription": "Concede aprobación automática para todas las solicitudes de series 4K.", "components.PermissionEdit.autoapprove4kSeries": "Auto-Aprueba Series 4K", "components.PermissionEdit.autoapprove4kMoviesDescription": "Concede aprobación automática para todas las solicitudes de películas 4K.", "components.PermissionEdit.autoapprove4kMovies": "Auto-Aprueba Películas 4K", "components.PermissionEdit.autoapprove4kDescription": "Concede aprobación automática para todas las solicitudes de contenidos 4K.", "components.PermissionEdit.autoapprove4k": "Auto-Aprobación 4K", "components.PermissionEdit.autoapprove": "Auto-Aprobación", "components.PermissionEdit.advancedrequestDescription": "Concede permisos para configurar opciones avanzadas en las solicitudes.", "components.PermissionEdit.advancedrequest": "Solicitudes Avanzadas", "components.PermissionEdit.adminDescription": "Acceso completo de administrador. Ignora otras comprobaciones de permisos.", "components.NotificationTypeSelector.mediaAutoApprovedDescription": "Envía notificaciones cuando los usuarios solicitan nuevos contenidos que se aprueban automáticamente.", "components.NotificationTypeSelector.mediaAutoApproved": "Petición Aprobada Automáticamente", "components.MovieDetails.markavailable": "Marcar como Disponible", "components.MovieDetails.mark4kavailable": "Marcar como Disponible en 4K", "components.Login.forgotpassword": "¿Contraseña olvidada?", "components.Discover.upcomingtv": "Próximas Series", "components.Discover.TvGenreSlider.tvgenres": "Géneros de Series", "components.Discover.StudioSlider.studios": "Est<PERSON><PERSON>", "components.Discover.NetworkSlider.networks": "Cadenas de TV", "components.Discover.MovieGenreSlider.moviegenres": "Géneros de Películas", "components.CollectionDetails.requestcollection4k": "<PERSON><PERSON><PERSON> en 4K", "components.AppDataWarning.dockerVolumeMissingDescription": "El montaje del volumen <code>{appDataPath}</code> no se ha configurado correctamente. Todos los datos se eliminarán cuando el contenedor se pare o reinicie.", "components.Settings.SettingsUsers.defaultPermissions": "Permisos por Defecto", "components.Settings.SettingsJobsCache.unknownJob": "Tarea Desconocida", "components.Settings.SettingsJobsCache.sonarr-scan": "Escaneo de Sonarr", "components.Settings.SettingsJobsCache.runnow": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.radarr-scan": "Escaneo de Radarr", "components.Settings.SettingsJobsCache.process": "Procesamiento", "components.Settings.SettingsJobsCache.plex-recently-added-scan": "Escaneo de Recien Añadidos de Plex", "components.Settings.SettingsJobsCache.plex-full-scan": "Escaneo Completo de la Biblioteca de Plex", "components.Settings.SettingsJobsCache.nextexecution": "Próxima Ejecución", "components.Settings.SettingsJobsCache.jobtype": "Tipo", "components.Settings.SettingsJobsCache.jobstarted": "{jobname} comenza<PERSON>.", "components.Settings.SettingsJobsCache.jobsDescription": "Overserr realiza ciertas tareas de mantenimiento como Tareas Programadas, pero también pueden lanzarse manualmente a continuación. Lanzar una tarea manual no altera su programación.", "components.Settings.SettingsJobsCache.jobs": "Tareas <PERSON>adas", "components.Settings.SettingsJobsCache.jobname": "Nombre de Tarea Programada", "components.Settings.SettingsJobsCache.jobcancelled": "{jobname} cancelada.", "components.Settings.SettingsJobsCache.flushcache": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.download-sync-reset": "Reiniciar Descarga Sincronizada", "components.Settings.SettingsJobsCache.download-sync": "Descarga Sincronizada", "components.Settings.SettingsJobsCache.command": "Comand<PERSON>", "components.Settings.SettingsJobsCache.canceljob": "Cancelar <PERSON>rea <PERSON>ada", "components.Settings.SettingsJobsCache.cachevsize": "Valor del Tamaño", "components.Settings.SettingsJobsCache.cachename": "Nombre de Caché", "components.Settings.SettingsJobsCache.cachemisses": "Fall<PERSON>", "components.Settings.SettingsJobsCache.cacheksize": "Tamaño de Clave", "components.Settings.SettingsJobsCache.cachekeys": "Claves Totales", "components.Settings.SettingsJobsCache.cachehits": "Consultas", "components.Settings.SettingsJobsCache.cacheflushed": "{cachename} caché limpiada.", "components.Settings.SettingsJobsCache.cacheDescription": "Overseer cachea peticiones a APIs externas para optimizar el rendimiento y evitar llamadas innecesarias a esas APIs.", "components.Settings.SettingsJobsCache.cache": "Caché", "components.Settings.SettingsAbout.preferredmethod": "Preferida", "components.Settings.RadarrModal.validationBaseUrlTrailingSlash": "La base de la URL no debe terminar en una barra al final", "components.Settings.RadarrModal.validationBaseUrlLeadingSlash": "La base de la URL debe tener una barra al principio", "components.Settings.RadarrModal.validationApplicationUrlTrailingSlash": "La URL no puede acabar con una barra", "components.Settings.RadarrModal.validationApplicationUrl": "Debes indicar una URL válida", "components.Settings.RadarrModal.syncEnabled": "Habilitar Escaneo", "components.Settings.RadarrModal.externalUrl": "URL externa", "components.Settings.Notifications.validationUrl": "Debes indicar una URL válida", "components.Settings.Notifications.validationEmail": "Debes indicar una dirección de email válida", "components.Settings.Notifications.sendSilentlyTip": "Enviar notificaciones sin sonido", "components.Settings.Notifications.sendSilently": "<PERSON><PERSON><PERSON> silen<PERSON>", "components.Settings.Notifications.botUsername": "Nombre de usuario del Bot", "components.Settings.Notifications.botAvatarUrl": "URL del Bot Avatar", "components.Settings.Notifications.NotificationsWebhook.validationWebhookUrl": "Debes indicar una URL válida", "components.Settings.Notifications.NotificationsSlack.validationWebhookUrl": "Debes indicar una URL válida", "components.Settings.Notifications.NotificationsPushbullet.validationAccessTokenRequired": "Debes indicar un token de acceso", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsSaved": "¡Los ajustes de notificación Pushbullet se han guardado con éxito!", "components.Settings.Notifications.NotificationsPushbullet.pushbulletSettingsFailed": "Fallo al guardar los ajustes de la notificación Pushbullet.", "components.Settings.Notifications.NotificationsPushbullet.agentEnabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsPushbullet.accessToken": "Token de Acceso", "components.Search.search": "Buscar", "components.ResetPassword.validationpasswordrequired": "Debes indicar una contraseña", "components.ResetPassword.validationpasswordminchars": "La contraseña es demasiado corta; debería tener al menos 8 caracteres", "components.ResetPassword.validationpasswordmatch": "La contraseña debe coincidir", "components.ResetPassword.validationemailrequired": "Debes indicar una dirección de email valida", "components.ResetPassword.resetpasswordsuccessmessage": "¡Contraseña reiniciada con éxito!", "components.ResetPassword.resetpassword": "Reiniciar contraseña", "components.ResetPassword.requestresetlinksuccessmessage": "Un enlace para reiniciar la contraseña se enviará a la dirección de email indicada si está asociada a un usuario valido.", "components.ResetPassword.password": "Contraseña", "components.ResetPassword.gobacklogin": "Volver a la Página de Login", "components.RequestModal.alreadyrequested": "<PERSON>", "components.Discover.TvGenreList.seriesgenres": "Géneros de Series", "components.Discover.MovieGenreList.moviegenres": "Géneros de Películas", "components.UserProfile.UserSettings.UserNotificationSettings.notificationsettings": "Ajustes de Notificaciones", "components.UserProfile.UserSettings.UserNotificationSettings.discordId": "ID de Usuario", "components.UserProfile.UserSettings.UserGeneralSettings.user": "Usuario", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsSuccess": "¡Ajustes guardados con éxito!", "components.UserProfile.UserSettings.UserGeneralSettings.toastSettingsFailure": "Algo fue mal al guardar los ajustes.", "components.UserProfile.UserSettings.UserGeneralSettings.role": "Rol", "components.UserProfile.UserSettings.UserGeneralSettings.region": "Región en sección \"Descubre\"", "components.UserProfile.UserSettings.UserGeneralSettings.plexuser": "Usuario en Plex", "components.UserProfile.UserSettings.UserGeneralSettings.owner": "Propietario", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguage": "Idioma de la sección \"Descubre\"", "components.UserProfile.UserSettings.UserGeneralSettings.localuser": "Usuario Local", "components.UserProfile.UserSettings.UserGeneralSettings.generalsettings": "Ajustes Generales", "components.UserProfile.UserSettings.UserGeneralSettings.displayName": "Nombre a mostrar", "components.UserProfile.UserSettings.UserGeneralSettings.admin": "Administrador", "components.UserProfile.UserSettings.UserGeneralSettings.accounttype": "Tipo de Cuenta", "components.UserProfile.ProfileHeader.userid": "<PERSON><PERSON> <PERSON> Usuario: {userid}", "components.UserProfile.ProfileHeader.settings": "<PERSON><PERSON>", "components.UserProfile.ProfileHeader.profile": "<PERSON><PERSON>", "components.UserProfile.ProfileHeader.joindate": "Unido el {joindate}", "components.UserList.validationEmail": "Debes indicar un dirección de email válida", "components.UserList.userssaved": "¡Permisos de usuario guardados con éxito!", "components.UserList.users": "Usuarios", "components.UserList.userfail": "Algo fue mal al guardar los permisos del usuario.", "components.UserList.sortRequests": "Número de Solicitudes", "components.UserList.sortDisplayName": "Nombre a mostrar", "components.UserList.sortCreated": "Fecha de Unión", "components.UserList.owner": "Propietario", "components.UserList.edituser": "Editar <PERSON> Usuario", "components.UserList.bulkedit": "Edición <PERSON>", "components.UserList.accounttype": "Tipo", "components.TvDetails.nextAirDate": "Próxima Emisión", "components.TvDetails.episodeRuntimeMinutes": "{runtime} minutos", "components.TvDetails.episodeRuntime": "Duración del Episodio", "components.Setup.setup": "Configuración", "components.Settings.webhook": "Webhook", "components.Settings.toastPlexRefreshSuccess": "¡Recibida la lista de servidores de Plex con éxito!", "components.Settings.toastPlexRefreshFailure": "Fallo al obtener la lista de servidores de Plex.", "components.Settings.toastPlexRefresh": "Obteniendo la lista de servidores desde Plex…", "components.Settings.toastPlexConnectingSuccess": "¡Conexión al servidor Plex establecida con éxito!", "components.Settings.toastPlexConnectingFailure": "Fallo al conectar con Plex.", "components.Settings.toastPlexConnecting": "Intentando conectar con Plex…", "components.Settings.settingUpPlexDescription": "Para configurar Plex, puedes introducir manualmente los detalles o seleccionar un servidor obtenido de <RegisterPlexTVLink>plex.tv</RegisterPlexTVLink>. Pulsa el botón de la derecha del desplegable para obtener los servidores disponibles.", "components.Settings.serverpresetRefreshing": "Obteniendo servidores…", "components.Settings.serverpresetManualMessage": "Configuración manual", "components.Settings.serverpresetLoad": "Presiona el botón para obtener los servidores disponibles", "components.Settings.serverpreset": "<PERSON><PERSON><PERSON>", "components.Settings.serverRemote": "remoto", "components.Settings.serverLocal": "local", "components.Settings.scanning": "Sincronizando…", "components.Settings.scan": "Sincronizar Bibliotecas", "components.Settings.notificationAgentSettingsDescription": "Configura y habilita los agentes de notificaciones.", "components.Settings.menuUsers": "Usuarios", "components.Settings.email": "Email", "components.Settings.SonarrModal.validationLanguageProfileRequired": "Debes seleccionar un perfil de idioma", "components.Settings.SonarrModal.validationBaseUrlTrailingSlash": "La URL BASE no puede terminar con una barra", "components.Settings.SonarrModal.validationBaseUrlLeadingSlash": "La URL Base debe comenzar con una barra", "components.Settings.SonarrModal.validationApplicationUrlTrailingSlash": "La URL no puede terminar con una barra", "components.Settings.SonarrModal.validationApplicationUrl": "Debe indicar una URL válida", "components.Settings.SonarrModal.toastSonarrTestSuccess": "¡Conexión establecida con Sonarr con éxito!", "components.Settings.SonarrModal.toastSonarrTestFailure": "Fallo al conectar con Sonarr.", "components.Settings.SonarrModal.testFirstLanguageProfiles": "Probar conexión para obtener los perfiles de idioma", "components.Settings.SonarrModal.syncEnabled": "Habilitar Escaneo", "components.Settings.SonarrModal.selectLanguageProfile": "Seleccionar <PERSON> Idioma", "components.Settings.SonarrModal.loadinglanguageprofiles": "Cargando perfiles de idioma…", "components.Settings.SonarrModal.languageprofile": "Perfil de Idioma", "components.Settings.SonarrModal.externalUrl": "URL Externa", "components.Settings.SonarrModal.animelanguageprofile": "Perfil de Idioma para Anime", "components.Settings.SettingsUsers.userSettingsDescription": "Configura los ajustes de usuarios globales y por defecto.", "components.Settings.SettingsUsers.userSettings": "Ajustes de Usuario", "components.Settings.SettingsUsers.toastSettingsSuccess": "¡Ajustes de usuario guardados con éxito!", "components.Settings.SettingsUsers.toastSettingsFailure": "Algo fue mal mientras se guardaban los cambios.", "components.Settings.SettingsUsers.localLogin": "Habilitar Autenticación Local", "components.Settings.SettingsLogs.time": "<PERSON><PERSON> (timestamp)", "components.Settings.SettingsLogs.showall": "Mostrar todos los logs", "components.Settings.SettingsLogs.pauseLogs": "Pausar", "components.Settings.SettingsLogs.resumeLogs": "<PERSON><PERSON><PERSON>", "components.Settings.SettingsLogs.message": "Men<PERSON><PERSON>", "components.Settings.SettingsLogs.logsDescription": "<PERSON><PERSON>e ver estos logs directamente via <code>stdout</code> o en <code>{appDataPath}/logs/overseerr.log</code>.", "components.Settings.SettingsLogs.logs": "Registros", "components.Settings.SettingsLogs.level": "Severidad", "components.Settings.SettingsLogs.label": "Etiqueta", "components.Settings.SettingsLogs.filterWarn": "Advertencia", "components.Settings.SettingsLogs.filterInfo": "Info", "components.Settings.SettingsLogs.filterError": "Error", "components.Settings.SettingsLogs.filterDebug": "Depuración", "components.Settings.Notifications.pgpPrivateKeyTip": "Firmar mensajes de email encriptados usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPrivateKey": "Clave Privada PGP", "components.Settings.Notifications.pgpPasswordTip": "<PERSON><PERSON><PERSON> men<PERSON><PERSON> de email usando <OpenPgpLink>OpenPGP</OpenPgpLink>", "components.Settings.Notifications.pgpPassword": "Contraseña de PGP", "components.UserProfile.UserSettings.UserPasswordChange.newpassword": "Nueva Contraseña", "components.UserProfile.UserSettings.UserPasswordChange.currentpassword": "Contraseña Actual", "components.UserProfile.UserSettings.UserPasswordChange.confirmpassword": "Con<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.validationTelegramChatId": "Debes indicar un Id de chat válido", "components.UserProfile.UserSettings.UserNotificationSettings.validationDiscordId": "Debes indicar un Id de usuario válido", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatIdTipLong": "<TelegramBotLink>Comienza un chat</TelegramBotLink>, aña<PERSON> el <GetIdBotLink>@get_id_bot</GetIdBotLink>, y envía el comando <code>/my_id</code>", "components.UserProfile.UserSettings.UserNotificationSettings.telegramChatId": "ID del Chat", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilentlyDescription": "Enviar notificaciones sin sonido", "components.UserProfile.UserSettings.UserNotificationSettings.sendSilently": "Enviar de forma silenciosa", "components.UserProfile.UserSettings.UserNotificationSettings.notifications": "Notificaciones", "components.UserProfile.UserSettings.UserNotificationSettings.discordIdTip": "El <FindDiscordIdLink>multi-digit ID number</FindDiscordIdLink> asociado a su cuenta de usuario", "components.UserProfile.UserSettings.UserGeneralSettings.regionTip": "Filtrar contenido por disponibilidad regional", "components.UserProfile.UserSettings.UserGeneralSettings.originallanguageTip": "Filtrar contenido por idioma original", "components.UserProfile.UserSettings.UserGeneralSettings.general": "General", "components.Settings.services": "<PERSON><PERSON><PERSON>", "components.Settings.plex": "Plex", "components.Settings.notifications": "Notificaciones", "components.Settings.SettingsUsers.users": "Usuarios", "components.Settings.SettingsJobsCache.jobsandcache": "<PERSON><PERSON><PERSON> y Caché", "components.Settings.SettingsAbout.about": "Acerca de", "components.ResetPassword.passwordreset": "<PERSON><PERSON><PERSON> de Contraseña", "pages.errormessagewithcode": "{statusCode} - {error}", "components.UserProfile.UserSettings.unauthorizedDescription": "No tienes permiso para modificar estos ajustes de usuario.", "components.TvDetails.seasons": "{seasonCount, plural, one {# Temporada} other {# Temporadas}}", "pages.somethingwentwrong": "Algo fue mal", "pages.serviceunavailable": "Servicio No Disponible", "pages.pagenotfound": "Página No Encontrada", "pages.internalservererror": "Error Interno del Servidor", "i18n.usersettings": "Ajustes de Usuario", "i18n.settings": "<PERSON><PERSON><PERSON><PERSON>", "i18n.advanced": "<PERSON><PERSON><PERSON>", "components.UserProfile.recentrequests": "Solicitudes Recientes", "components.UserProfile.UserSettings.menuPermissions": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.menuNotifications": "Notificaciones", "components.UserProfile.UserSettings.menuGeneralSettings": "General", "components.UserProfile.UserSettings.menuChangePass": "Contraseña", "components.UserProfile.UserSettings.UserPermissions.unauthorizedDescription": "No puedes modificar tus propios permisos.", "components.UserProfile.UserSettings.UserPermissions.toastSettingsSuccess": "¡Permisos guardados con éxito!", "components.UserProfile.UserSettings.UserPermissions.toastSettingsFailure": "Algo fue mal al guardar los ajustes.", "components.UserProfile.UserSettings.UserPermissions.permissions": "<PERSON><PERSON><PERSON>", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPasswordLength": "La contraseña es muy corta; <PERSON><PERSON><PERSON> tener, al menos, 8 caracteres", "components.UserProfile.UserSettings.UserPasswordChange.validationNewPassword": "Debes proporcionar una nueva contraseña", "components.UserProfile.UserSettings.UserPasswordChange.validationCurrentPassword": "Debes indicar tu contraseña actual", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPasswordSame": "Las contraseñas deben coincidir", "components.UserProfile.UserSettings.UserPasswordChange.validationConfirmPassword": "Debes confirmar la nueva contraseña", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsSuccess": "¡Contraseña guardada correctamente!", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailureVerifyCurrent": "Algo fue mal al guardar la contraseña. ¿Has introducido correctamente tu contraseña actual?", "components.UserProfile.UserSettings.UserPasswordChange.toastSettingsFailure": "Algo fue mal al guardar la contraseña.", "components.UserProfile.UserSettings.UserPasswordChange.password": "Contraseña", "components.UserProfile.UserSettings.UserPasswordChange.nopermissionDescription": "No tienes permiso para modificar la contraseña del usuario.", "components.Settings.enablessl": "Usar SSL", "components.Settings.SettingsLogs.logDetails": "Detalles del Log", "components.Settings.SettingsLogs.extraData": "Datos Adicionales", "components.Settings.SettingsLogs.copyToClipboard": "Copiar al Portapapeles", "components.Settings.SettingsLogs.copiedLogMessage": "Copiar log al portapapeles.", "components.UserList.nouserstoimport": "No hay usuarios de Plex para importar.", "components.PersonDetails.lifespan": "{birthdate} – {deathdate}", "components.PersonDetails.birthdate": "<PERSON><PERSON><PERSON> {birthdate}", "components.PersonDetails.alsoknownas": "También Conocido como: {names}", "i18n.delimitedlist": "{a}, {b}", "i18n.view": "<PERSON>er", "i18n.tvshow": "Series", "i18n.testing": "Probando…", "i18n.test": "Probar", "i18n.status": "Estado", "i18n.showingresults": "Mostrando <strong>{from}</strong> a <strong>{to}</strong> de <strong>{total}</strong> resultados", "i18n.saving": "Guardando…", "i18n.save": "Guardar Cambios", "i18n.resultsperpage": "Mostrar {pageSize} resultados por página", "i18n.requesting": "Pidiendo…", "i18n.request4k": "Pedir en 4K", "i18n.previous": "Anterior", "i18n.notrequested": "No Solicitado", "i18n.noresults": "Sin resultados.", "i18n.next": "Adelante", "i18n.movie": "<PERSON><PERSON><PERSON><PERSON>", "i18n.canceling": "Cancelando…", "i18n.back": "Atrás", "i18n.areyousure": "¿Estás Seguro?", "i18n.all": "<PERSON><PERSON>", "components.UserProfile.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.UserProfile.totalrequests": "Solicitudes Totales", "components.UserProfile.seriesrequest": "Solicitudes de Series", "components.UserProfile.requestsperdays": "{limit} restantes", "components.UserProfile.pastdays": "{type} (últimos {days} días)", "components.UserProfile.movierequests": "Solicitudes de Películas", "components.UserProfile.limit": "{remaining} de {limit}", "components.UserProfile.UserSettings.UserGeneralSettings.seriesrequestlimit": "Límite de Solicitudes de Series", "components.UserProfile.UserSettings.UserGeneralSettings.movierequestlimit": "Límite de Solicitudes de Películas", "components.UserProfile.UserSettings.UserGeneralSettings.enableOverride": "Límite global de Sobreescritura", "components.TvDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsUsers.tvRequestLimitLabel": "Límite Global de Solicitudes de Series", "components.Settings.SettingsUsers.movieRequestLimitLabel": "Límite Global de Solicitudes de Películas", "components.RequestModal.QuotaDisplay.seasonlimit": "{limit, plural, one {temporada} other {temporadas}}", "components.RequestModal.QuotaDisplay.season": "temporada", "components.RequestModal.QuotaDisplay.requiredquotaUser": "Este usuario necesita tener al menos <strong>{seasons}</strong> {seasons, plural, one {solicitud de temporada} other {solicitudes de temporadas}} restante(s) para poder enviar una solicitud para esta serie.", "components.RequestModal.QuotaDisplay.requiredquota": "Necesitas tener al menos <strong>{seasons}</strong> {seasons, plural, one {solicitud de temporada} other {solicitudes de temporadas}} restante(s) para poder enviar una solicitud para esta serie.", "components.RequestModal.QuotaDisplay.requestsremaining": "{remaining, plural, =0 {No} other {<strong>#</strong>}} {type} {remaining, plural, one {solicitud} other {solicitudes}} restante(s)", "components.RequestModal.QuotaDisplay.quotaLinkUser": "Puedes ver un resumen de los límites de solicitudes de estos usuarios en sus <ProfileLink>páginas de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.quotaLink": "<PERSON><PERSON><PERSON> ver un resumen de tus límites de peticiones en tu <ProfileLink>página de perfil</ProfileLink>.", "components.RequestModal.QuotaDisplay.notenoughseasonrequests": "No te quedan suficientes solicitudes de temporadas restantes", "components.RequestModal.QuotaDisplay.movielimit": "{limit, plural, one {película} other {películas}}", "components.RequestModal.QuotaDisplay.movie": "pel<PERSON><PERSON>", "components.RequestModal.QuotaDisplay.allowedRequestsUser": "Este usuario tiene permitido <strong>{limit}</strong> {type} cada <strong>{days}</strong> días.", "components.RequestModal.QuotaDisplay.allowedRequests": "Se te permite pedir <strong>{limit}</strong> {type} cada <strong>{days}</strong> días.", "components.QuotaSelector.unlimited": "<PERSON><PERSON><PERSON><PERSON>", "components.MovieDetails.originaltitle": "<PERSON><PERSON><PERSON><PERSON>", "components.LanguageSelector.originalLanguageDefault": "Todos los Idiomas", "components.LanguageSelector.languageServerDefault": "({Language}) por defecto", "components.Layout.VersionStatus.commitsbehind": "{commitsBehind} {commitsBehind, plural, one {cambio} other {cambios}} por detrás", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSetOwnAccount": "Tu cuenta no tiene configurada una contraseña actualmente. Configure una contraseña a continuación para habilitar el acceso como \"usuario local\" utilizando tu dirección de email.", "components.UserProfile.UserSettings.UserPasswordChange.noPasswordSet": "Esta cuenta de usuario no tiene configurada una contraseña actualmente. Configure una contraseña a continuación para habilitar el acceso como \"usuario local\"", "components.UserProfile.UserSettings.UserNotificationSettings.validationPgpPublicKey": "Debes introducir una clave pública PGP valida", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingssaved": "¡Ajustes de notificaciones de Telegram guardados correctamente!", "components.UserProfile.UserSettings.UserNotificationSettings.telegramsettingsfailed": "Error al guardar los ajustes de notificaciones de Telegram.", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKeyTip": "Encriptar mensajes por email usando <OpenPgpLink>OpenPGP2</OpenPgpLink>", "components.UserProfile.UserSettings.UserNotificationSettings.pgpPublicKey": "Clave Pública PGP", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingssaved": "¡Ajustes de notificación por email guardados correctamente!", "components.UserProfile.UserSettings.UserNotificationSettings.emailsettingsfailed": "Error al guardar los ajustes de notificaciones por email.", "components.UserProfile.UserSettings.UserNotificationSettings.email": "Email", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingssaved": "¡Los ajustes de notificaciones de Discord se han guardado correctamente!", "components.UserProfile.UserSettings.UserNotificationSettings.discordsettingsfailed": "No se han podido guardar los ajustes de notificaciones de Discord.", "components.Settings.serviceSettingsDescription": "Configura tu(s) servidor(es) {serverType} a continuación. Puedes conectar a múltiples servidores de {serverType}, pero solo dos de ellos pueden ser marcados como por defecto (uno no-4k y otro 4k). Los administradores podrán modificar el servidor usado al procesar nuevas solicitudes antes de su aprobación.", "components.Settings.mediaTypeMovie": "pel<PERSON><PERSON>", "components.Settings.SonarrModal.testFirstTags": "Probar conexión para cargar etiquetas", "components.Settings.SonarrModal.tags": "Etiquetas", "components.Settings.SonarrModal.selecttags": "Seleccionar etiquetas", "components.Settings.SonarrModal.notagoptions": "Sin etiquetas.", "components.Settings.SonarrModal.loadingTags": "Cargando etiquetas…", "components.Settings.SonarrModal.edit4ksonarr": "Modificar servidor 4K Sonarr", "components.Settings.SonarrModal.default4kserver": "Servidor 4K por defecto", "components.Settings.SonarrModal.create4ksonarr": "Añadir nuevo servidor Sonarr 4K", "components.Settings.SonarrModal.animeTags": "Etiquetas Anime", "components.Settings.noDefaultServer": "Al menos un servidor {serverType} debe marcarse como por defecto para que las solicitudes de {mediaType} sean procesadas.", "components.Settings.noDefaultNon4kServer": "Si solo tienes un único servidor {serverType} para contenidos 4K y no 4K (o si solo descargas contenidos 4k), tu servidor {serverType} <strong>NO</strong> debería marcarse como un servidor 4k.", "components.Settings.mediaTypeSeries": "serie", "components.Settings.SettingsAbout.uptodate": "Actualizado", "components.Settings.SettingsAbout.outofdate": "Desactualizado", "components.Settings.RadarrModal.testFirstTags": "Probar conexión para cargar etiquetas", "components.Settings.RadarrModal.tags": "Etiquetas", "components.Settings.RadarrModal.selecttags": "Seleccionar etiquetas", "components.Settings.RadarrModal.notagoptions": "Sin etiquetas.", "components.Settings.RadarrModal.loadingTags": "Cargando etiquetas…", "components.Settings.RadarrModal.edit4kradarr": "Modificar servidor Radarr 4K", "components.Settings.RadarrModal.default4kserver": "Servidor 4K por defecto", "components.Settings.RadarrModal.create4kradarr": "Añadir un nuevo servidor Radarr 4K", "components.Settings.Notifications.validationPgpPrivateKey": "Debes indicar una clave privada PGP", "components.Settings.Notifications.validationPgpPassword": "Debes indicar una contraseña PGP", "components.Settings.Notifications.botUsernameTip": "Permite a los usuarios iniciar también un chat con tu bot y configurar sus propias notificaciones", "components.RequestModal.pendingapproval": "Tu solicitud está pendiente de aprobación.", "components.RequestModal.AdvancedRequester.tags": "Etiquetas", "components.RequestModal.AdvancedRequester.selecttags": "Seleccionar etiquetas", "components.RequestModal.AdvancedRequester.notagoptions": "Sin etiquetas.", "components.RequestList.RequestItem.mediaerror": "{mediaType} No Encontrado", "components.RequestList.RequestItem.deleterequest": "<PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.cancelRequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.mediaerror": "{mediaType} No Encontrado", "components.RequestCard.deleterequest": "<PERSON><PERSON><PERSON>", "components.NotificationTypeSelector.notificationTypes": "Tipos de Notificación", "components.Layout.VersionStatus.streamstable": "Overseer (Estable)", "components.Layout.VersionStatus.streamdevelop": "Overseer (Desarrollo)", "components.Layout.VersionStatus.outofdate": "Desactualizado", "components.UserList.autogeneratepasswordTip": "Envía por email una contraseña al usuario generada por el servidor", "i18n.retrying": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Settings.serverSecure": "seguro", "components.UserList.usercreatedfailedexisting": "La dirección de email proporcionada ya está en uso por otro usuario.", "components.Settings.SonarrModal.enableSearch": "Habilitar Búsqueda Automática", "components.Settings.RadarrModal.enableSearch": "Habilitar Búsqueda Automática", "components.RequestModal.edit": "<PERSON><PERSON>", "components.RequestList.RequestItem.editrequest": "<PERSON><PERSON>", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingssaved": "¡Ajustes de notificacion de Web Push guardados con éxito!", "components.UserProfile.UserSettings.UserNotificationSettings.webpushsettingsfailed": "Fallo al guardar los ajustes de notificaciones de Web Push.", "components.UserProfile.UserSettings.UserNotificationSettings.webpush": "Web Push", "components.UserProfile.UserSettings.UserGeneralSettings.applanguage": "Mostrar <PERSON>", "components.Settings.webpush": "Web Push", "components.Settings.noDefault4kServer": "Un servidor 4K de {serverType} debe ser marcado por defecto para poder habilitar las solicitudes 4K de {mediaType} de los usuarios.", "components.Settings.is4k": "4K", "components.Settings.SettingsUsers.newPlexLoginTip": "Habilitar inicio de sesión de usuarios de Plex sin importarse previamente", "components.Settings.SettingsUsers.newPlexLogin": "Habilitar nuevo inicio de sesión de Plex", "components.Settings.Notifications.toastTelegramTestSuccess": "¡Notificación de Telegram enviada con éxito!", "components.Settings.Notifications.toastTelegramTestSending": "Enviando notificación de prueba de Telegram…", "components.Settings.Notifications.toastTelegramTestFailed": "Fallo al enviar notificación de prueba de Telegram.", "components.Settings.Notifications.toastEmailTestSuccess": "¡Notificación por Email de prueba enviada!", "components.Settings.Notifications.toastEmailTestSending": "Enviando notificación de prueba por Email…", "components.Settings.Notifications.toastEmailTestFailed": "Fallo al enviar la notificación de prueba por Email.", "components.Settings.Notifications.toastDiscordTestSuccess": "¡Notificación de prueba enviada de Discord!", "components.Settings.Notifications.toastDiscordTestSending": "Enviando notificación de prueba de Discord…", "components.Settings.Notifications.toastDiscordTestFailed": "Fallo al enviar notificación de prueba de Discord.", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSuccess": "¡Notificación de prueba de Webhook enviada!", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestSending": "Enviando notificación de prueba de Webhook…", "components.Settings.Notifications.NotificationsWebhook.toastWebhookTestFailed": "Fallo al enviar la notificación de prueba de Webhook.", "components.Settings.Notifications.NotificationsWebPush.webpushsettingssaved": "¡Ajustes de notificación de Web Push guardados con éxito!", "components.Settings.Notifications.NotificationsWebPush.webpushsettingsfailed": "Fallo al guardar los ajustes de notificación de Web Push.", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSuccess": "¡Notificación de prueba de Web Push enviada!", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestSending": "Enviando notificación de prueba de Web Push…", "components.Settings.Notifications.NotificationsWebPush.toastWebPushTestFailed": "Fallo al enviar la notificación de prueba de Web Push.", "components.Settings.Notifications.NotificationsWebPush.agentenabled": "Habilitar Agente", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSuccess": "¡Notificación de prueba de Slack enviada!", "components.Settings.Notifications.NotificationsSlack.toastSlackTestSending": "Enviando notificación de prueba de Slack…", "components.Settings.Notifications.NotificationsSlack.toastSlackTestFailed": "Fallo al enviar la notificación de prueba de Slack.", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSuccess": "¡Notificación de prueba de Pushover enviada!", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestSending": "Enviando notificación de prueba de Pushover…", "components.Settings.Notifications.NotificationsPushover.toastPushoverTestFailed": "Fallo al enviar la notificación de prueba de Pushover.", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSuccess": "¡Notificación de prueba de Pushbullet enviada!", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestSending": "Enviando notificación de prueba de Pushbullet…", "components.Settings.Notifications.NotificationsPushbullet.toastPushbulletTestFailed": "Fallo al enviar notificación de prueba de Pushbullet.", "components.Settings.Notifications.NotificationsLunaSea.webhookUrl": "URL del Webhook", "components.Settings.Notifications.NotificationsLunaSea.validationWebhookUrl": "Debes indicar una URL válida", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSuccess": "¡Notificación de LunaSea enviada!", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestSending": "Enviando notificación de prueba de LunaSea…", "components.Settings.Notifications.NotificationsLunaSea.toastLunaSeaTestFailed": "Fallo al enviar la notificación de prueba de LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.settingsSaved": "¡Los ajustes de notificación se han guardado con éxito!", "components.Settings.Notifications.NotificationsLunaSea.settingsFailed": "Fallo al guardar los ajustes de notificación de LunaSea.", "components.Settings.Notifications.NotificationsLunaSea.profileNameTip": "Requerido solo si no se usa el perfil por <code>default</code>", "components.Settings.Notifications.NotificationsLunaSea.profileName": "Nombre de Perfil", "components.Settings.Notifications.NotificationsLunaSea.agentenabled": "Habilitar Agente", "components.PermissionEdit.requestTvDescription": "Conceder permisos para solicitar series que no sean 4k.", "components.PermissionEdit.requestTv": "Solicitar Series", "components.PermissionEdit.requestMoviesDescription": "Conceder permisos para solicitar películas que no sean 4K.", "components.PermissionEdit.requestMovies": "Solicitar películas", "components.Settings.SettingsAbout.betawarning": "¡Este es un software BETA. Algunas funcionalidades podrían fallar. Por favor, reporta cualquier problema en Github!", "components.Settings.Notifications.NotificationsLunaSea.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificacion", "components.RequestList.RequestItem.requesteddate": "Solicitado", "components.RequestCard.failedretry": "Algo fue mal al reintentar la solicitud.", "components.NotificationTypeSelector.usermediarequestedDescription": "Notificar cuando otros usuarios envíen nuevas solicitudes que requieran aprobación.", "components.NotificationTypeSelector.usermediafailedDescription": "Notificar cuando las solicitudes de contenido fallen al añadirse a Radarr o Sonarr.", "components.NotificationTypeSelector.usermediadeclinedDescription": "Notificar cuando tus contenidos solicitados sean rechazados.", "components.NotificationTypeSelector.usermediaavailableDescription": "Notificar cuando tus contenidos solicitados estén disponibles.", "components.NotificationTypeSelector.usermediaapprovedDescription": "Notificar cuando las solicitudes sean aprobadas.", "components.NotificationTypeSelector.usermediaAutoApprovedDescription": "Notificar cuando otros usuarios envíen nuevas solicitudes que se aprueben automáticamente.", "components.MovieDetails.showmore": "Mostrar más", "components.MovieDetails.showless": "<PERSON><PERSON> menos", "components.Layout.LanguagePicker.displaylanguage": "Mostrar idioma", "components.DownloadBlock.estimatedtime": "Estimación de {time}", "components.Settings.Notifications.encryptionOpportunisticTls": "Usa siempre STARTTLS", "components.TvDetails.streamingproviders": "Emisión Actual en", "components.UserProfile.UserSettings.UserGeneralSettings.languageDefault": "{{Language}} por defecto", "components.Settings.Notifications.NotificationsWebPush.httpsRequirement": "Para recibir notificaciones web push, <PERSON><PERSON>seerr debe servirse mediante HTTPS.", "components.Settings.Notifications.NotificationsWebhook.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificación", "components.Settings.Notifications.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificación", "components.Settings.SettingsUsers.localLoginTip": "Permite a los usuarios registrarse consumo email y password, en lugar de la OAuth de Plex", "components.Settings.webAppUrl": "Url de la <WebAppLink>Web App</WebAppLink>", "components.Settings.Notifications.encryption": "Método de Encriptación", "components.Settings.Notifications.encryptionDefault": "Usa STARTTLS si está disponible", "components.Settings.Notifications.encryptionNone": "Ninguna", "components.Settings.Notifications.NotificationsLunaSea.webhookUrlTip": "Tu <LunaSeaLink>URL del webhook de notificación</LunaSeaLink> basado en tu usuario o dispositivo", "components.Settings.Notifications.NotificationsPushbullet.accessTokenTip": "Crea un token desde tu <PushbulletSettingsLink>Opciones de Cuenta</PushbulletSettingsLink>", "components.Settings.Notifications.NotificationsPushover.accessTokenTip": "<ApplicationRegistrationLink>Registrar una aplicación</ApplicationRegistrationLink> para su uso con Je<PERSON>seerr", "components.Settings.Notifications.NotificationsPushover.userTokenTip": "Tu <UsersGroupsLink>identificador de usuario o grupo</UsersGroupsLink> de 30 caracteres", "components.Settings.Notifications.NotificationsPushbullet.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificación", "components.Settings.Notifications.NotificationsPushover.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificación", "components.QuotaSelector.seasons": "{count, plural, one {temporada} other {temporadas}}", "components.QuotaSelector.movies": "{count, plural, one {película} other {películas}}", "components.Settings.Notifications.NotificationsSlack.validationTypes": "<PERSON><PERSON> se<PERSON>, al menos, un tipo de notificación", "components.Settings.Notifications.chatIdTip": "Empieza un chat con tu bot, a<PERSON><PERSON> el <GetIdBotLink>@get_id_bot</GetIdBotLink> e indica el comando <code>/my_id</code>", "components.Settings.Notifications.encryptionImplicitTls": "Usa TLS Implícito", "components.Settings.Notifications.webhookUrlTip": "<PERSON><PERSON> una <DiscordWebhookLink>integración webhook</DiscordWebhookLink> en tu servidor", "components.MovieDetails.streamingproviders": "Emisión Actual en", "components.QuotaSelector.movieRequests": "{quotaLimit} <quotaUnits>{películas} per {quotaDays} {días}</quotaUnits>", "components.Settings.Notifications.NotificationsSlack.webhookUrlTip": "Crea una integración con un <WebhookLink>Webhook de Entrada</WebhookLink>", "components.Settings.webAppUrlTip": "Dirige a los usuarios, opcionalmente, a la web app en tu servidor, en lugar de la app alojada en Plex", "components.QuotaSelector.days": "{count, plural, one {día} other {días}}", "components.QuotaSelector.tvRequests": "{quotaLimit} <quotaUnits>{temporadas} per {quotaDays} {días}</quotaUnits>", "components.Settings.Notifications.botApiTip": "<CreateBotLink>Crea un bot</CreateBotLink> para usar con <PERSON><PERSON><PERSON>rr", "components.Settings.Notifications.encryptionTip": "Normalmente, TLS Implícito usa el puerto 465 y STARTTLS usa el puerto 587", "components.UserList.localLoginDisabled": "El ajuste para <strong>Habilitar el Inicio de Sesión Local</strong> está actualmente deshabilitado.", "components.Settings.SettingsUsers.defaultPermissionsTip": "Permisos iniciales asignados a nuevos usuarios", "components.Settings.SettingsAbout.runningDevelop": "Estás utilizando la rama de <code>develop</code> de <PERSON><PERSON><PERSON><PERSON>, la cual solo se recomienda para aquellos que contribuyen al desarrollo o al soporte de las pruebas de nuevos desarrollos.", "components.StatusBadge.status": "{status}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorMinutes": "Cada {jobScheduleMinutes, plural, one {minuto} other {{jobScheduleMinutes} minutos}}", "components.Settings.SettingsJobsCache.editJobScheduleSelectorHours": "Cada {jobScheduleHours, plural, one {hora} other {{jobScheduleHours} horas}}", "components.Settings.SettingsJobsCache.jobScheduleEditFailed": "Algo fue mal al guardar la tarea programada.", "components.Settings.SettingsJobsCache.editJobSchedule": "Modificar tarea programada", "components.Settings.SettingsJobsCache.editJobSchedulePrompt": "Nueva frecuencia", "components.Settings.SettingsJobsCache.jobScheduleEditSaved": "¡Tarea programada modificada con éxito!", "components.IssueDetails.IssueComment.areyousuredelete": "¿Estás seguro de querer borrar este comentario?", "components.IssueDetails.IssueComment.delete": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueComment.edit": "Modificar Comentario", "components.IssueDetails.IssueComment.postedby": "Escrito por {username} el {relativeTime}", "components.IssueDetails.IssueComment.postedbyedited": "Escrito por {username} el {relativeTime} (Modificado)", "components.IssueDetails.IssueComment.validationComment": "Debes introducir un mensaje", "components.IssueDetails.IssueDescription.deleteissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.IssueDescription.description": "Descripción", "components.IssueDetails.IssueDescription.edit": "Modificar Descripción", "components.IssueDetails.allepisodes": "Todos los Episodios", "components.IssueDetails.allseasons": "Todas las Temporadas", "components.IssueDetails.closeissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.closeissueandcomment": "Cerrar con Comentarios", "components.IssueDetails.comments": "Comentarios", "components.IssueDetails.deleteissue": "<PERSON><PERSON><PERSON>", "components.IssueDetails.deleteissueconfirm": "¿Estás seguro de querer borrar esta incidencia?", "components.IssueDetails.episode": "Episodio {episodeNumber}", "components.IssueDetails.issuepagetitle": "Incidencia", "components.IssueDetails.issuetype": "Tipo", "components.IssueDetails.lastupdated": "Última Actualización", "components.IssueDetails.leavecomment": "Commentario", "components.IssueDetails.nocomments": "Sin comentarios.", "components.IssueDetails.openedby": "#{issueId} abierta {relativeTime} por {username}", "components.IssueDetails.openin4karr": "<PERSON><PERSON>r en {arr} 4K", "components.IssueDetails.openinarr": "Abierta en {arr}", "components.IssueDetails.play4konplex": "Ver en 4K en {mediaServerName}", "components.IssueDetails.playonplex": "Ver en {mediaServerName}", "components.IssueDetails.problemepisode": "Episodio Afectado", "components.IssueDetails.problemseason": "Temporada Afectada", "components.IssueDetails.reopenissue": "<PERSON><PERSON><PERSON><PERSON>", "components.IssueDetails.reopenissueandcomment": "Reabrir con Comentarios", "components.IssueDetails.season": "Temporada {seasonNumber}", "components.IssueDetails.toasteditdescriptionfailed": "Algo fue mal mientras se modificaba la descripción de la incidencia.", "components.IssueDetails.toasteditdescriptionsuccess": "¡La descripción de la incidencia se ha modificado correctamente!", "components.IssueDetails.toastissuedeleted": "¡Incidencia eliminada correctamente!", "components.IssueDetails.toastissuedeletefailed": "Algo fue mal mientras se eliminaba la incidencia.", "components.IssueDetails.toaststatusupdated": "¡Estado de la incidencia actualizado con éxito!", "components.IssueDetails.toaststatusupdatefailed": "Algo fue mal mientras se actualizaba el estado de la incidencia.", "components.IssueDetails.unknownissuetype": "Desconocido", "components.IssueList.IssueItem.episodes": "{episodeCount, plural, one {<PERSON><PERSON>od<PERSON>} other {<PERSON><PERSON>od<PERSON>}}", "components.IssueList.IssueItem.issuestatus": "Estado", "components.IssueList.IssueItem.issuetype": "Tipo", "components.IssueList.IssueItem.opened": "Abierta", "components.IssueList.IssueItem.openeduserdate": "{date} por {user}", "components.IssueList.IssueItem.problemepisode": "Episodio Afectado", "components.IssueList.IssueItem.seasons": "{seasonCount, plural, one {Te<PERSON><PERSON>} other {Temporadas}}", "components.IssueList.IssueItem.unknownissuetype": "Desconocida", "components.IssueList.IssueItem.viewissue": "Ver Incidencia", "components.IssueList.issues": "Incidencias", "components.IssueList.showallissues": "Mostrar Todas las Incidencias", "components.IssueList.sortAdded": "Más Reciente", "components.IssueList.sortModified": "Última Modificación", "components.IssueModal.CreateIssueModal.allepisodes": "Todos los Episodios", "components.IssueModal.CreateIssueModal.allseasons": "Todas las Temporadas", "components.IssueModal.CreateIssueModal.episode": "Episodio {episodeNumber}", "components.IssueModal.CreateIssueModal.extras": "Extras", "components.IssueModal.CreateIssueModal.problemepisode": "Episodio Afectado", "components.IssueModal.CreateIssueModal.problemseason": "Temporada Afectada", "components.IssueModal.CreateIssueModal.providedetail": "Por favor envíe una explicación detallada de la incidencia encontrada.", "components.IssueModal.CreateIssueModal.reportissue": "Reportar Incidencia", "components.IssueModal.CreateIssueModal.season": "Temporada {seasonNumber}", "components.IssueModal.CreateIssueModal.submitissue": "Enviar Incidencia", "components.IssueModal.CreateIssueModal.toastFailedCreate": "Algo fue mal mientras se enviaba la incidencia.", "components.IssueModal.CreateIssueModal.toastSuccessCreate": "¡Incidencia reportada por <strong>{title}</strong> enviada con éxito!", "components.IssueModal.CreateIssueModal.toastviewissue": "Ver Incidencia", "components.IssueModal.CreateIssueModal.validationMessageRequired": "Debes escribir una descripción", "components.IssueModal.CreateIssueModal.whatswrong": "¿Qué sucede?", "components.IssueModal.issueAudio": "Audio", "components.IssueModal.issueOther": "<PERSON><PERSON>", "components.IssueModal.issueSubtitles": "Subtítulo", "components.IssueModal.issueVideo": "Vídeo", "components.Layout.Sidebar.issues": "Incidencias", "components.ManageSlideOver.downloadstatus": "Estado de la Descarga", "components.ManageSlideOver.manageModalClearMedia": "Limpiar Datos de los Contenidos", "components.ManageSlideOver.manageModalClearMediaWarning": "* Esto eliminará irreversiblemente todos los datos de {mediaType}, incluyendo todas las solicitudes. Si este elemento existe en la biblioteca de {mediaServerName}, la información de los contenidos se recreará en el siguiente escaneado.", "components.ManageSlideOver.manageModalIssues": "Incidencias Abiertas", "components.ManageSlideOver.manageModalNoRequests": "Sin solicitudes.", "components.ManageSlideOver.manageModalRequests": "Solicitudes", "components.ManageSlideOver.manageModalTitle": "Gestionar {mediaType}", "components.ManageSlideOver.mark4kavailable": "Marcar como Disponible en 4K", "components.ManageSlideOver.markavailable": "Marcar como Disponible", "components.ManageSlideOver.movie": "pel<PERSON><PERSON>", "components.ManageSlideOver.openarr": "<PERSON><PERSON><PERSON> en {arr}", "components.ManageSlideOver.openarr4k": "Abrir en 4K {arr}", "components.ManageSlideOver.tvshow": "series", "components.NotificationTypeSelector.adminissuecommentDescription": "Notificar cuando otros usuarios comenten incidencias.", "components.NotificationTypeSelector.adminissuereopenedDescription": "Notificar cuando se reabran incidencias por otros usuarios.", "components.NotificationTypeSelector.adminissueresolvedDescription": "Notificar cuando otros usuarios resuelvan incidencias.", "components.NotificationTypeSelector.issuecomment": "Comentario de Incidencia", "components.NotificationTypeSelector.issuecommentDescription": "Envía notificaciones cuando las incidencias reciben nuevos comentarios.", "components.NotificationTypeSelector.issuecreated": "Incidencia Reportada", "components.NotificationTypeSelector.issuecreatedDescription": "Enviar notificaciones cuando las incidencias sean reportadas.", "components.NotificationTypeSelector.issuereopened": "Incidencia <PERSON>a", "i18n.import": "Importar", "i18n.importing": "Importando…", "components.MovieDetails.digitalrelease": "Lanzamiento Digital", "components.MovieDetails.physicalrelease": "Lanzamiento Físico", "components.MovieDetails.theatricalrelease": "Lanzamiento en cines", "components.IssueDetails.commentplaceholder": "Añadir un comentario…", "components.ManageSlideOver.markallseasons4kavailable": "Marcar Todas las Temporadas como Disponible en 4K", "components.ManageSlideOver.markallseasonsavailable": "Marcar Todas las Temporadas como Disponible", "components.MovieDetails.rtcriticsscore": "Tomatometer de Rotten Tomatoes", "components.MovieDetails.rtaudiencescore": "Puntuación audiencia de Rotten Tomatoes", "components.Discover.plexwatchlist": "Tu Lista de Visualización de Plex", "components.Discover.DiscoverWatchlist.discoverwatchlist": "Tu lista de seguimiento de Plex", "components.ManageSlideOver.manageModalMedia": "Contenido", "components.MovieDetails.managemovie": "Admini<PERSON><PERSON>", "components.MovieDetails.reportissue": "Reportar una Incidencia", "components.AirDateBadge.airedrelative": "Emitido {relativeTime}", "components.AirDateBadge.airsrelative": "Emitiendo {relativeTime}", "components.ManageSlideOver.pastdays": "Últimos {days, number} Días", "components.Discover.CreateSlider.addSlider": "<PERSON><PERSON><PERSON>", "components.Discover.CreateSlider.addcustomslider": "<PERSON><PERSON><PERSON> personalizado", "components.Discover.CreateSlider.addfail": "Error al crear deslizador nuevo.", "components.Discover.CreateSlider.addsuccess": "Nuevo deslizador creado y guardado de los ajustes personalizados.", "components.Discover.CreateSlider.editSlider": "Modificar <PERSON>", "components.Discover.CreateSlider.editfail": "Fallo al editar deslizador.", "components.Discover.CreateSlider.nooptions": "Sin resultados.", "components.Discover.CreateSlider.providetmdbgenreid": "Proporcione un ID de categoría de TMDB", "components.Discover.CreateSlider.needresults": "Necesita tener al menos un resultado en la búsqueda.", "components.Discover.CreateSlider.providetmdbkeywordid": "Proporcione una ID de TMDB de la palabra clave", "components.Discover.CreateSlider.editsuccess": "Deslizador editado y ajustes personalizados guardados.", "components.Discover.CreateSlider.providetmdbsearch": "Proporcione una consulta a buscar", "components.Discover.CreateSlider.searchGenres": "Buscar categorías…", "components.Discover.CreateSlider.searchKeywords": "Buscar palabras clave…", "components.Discover.CreateSlider.searchStudios": "Buscar estudios…", "components.Discover.CreateSlider.providetmdbstudio": "Proporcione la ID de TMDB del estudio", "components.Discover.CreateSlider.providetmdbnetwork": "Proporcione la ID de TMDB de la cadena", "components.Discover.CreateSlider.slidernameplaceholder": "Nombre del deslizador", "components.Discover.CreateSlider.starttyping": "Escriba para buscar.", "components.Discover.CreateSlider.validationDatarequired": "Debe proporcionar un valor.", "components.Discover.DiscoverMovieKeyword.keywordMovies": "{keywordTitle} Películas", "components.Discover.CreateSlider.validationTitlerequired": "Debe proporcionar un título.", "components.Discover.DiscoverSliderEdit.deletefail": "<PERSON><PERSON><PERSON> al borrar desli<PERSON>.", "components.Discover.DiscoverSliderEdit.deletesuccess": "Deslizador eliminado con éxito.", "components.Discover.DiscoverSliderEdit.remove": "Bo<PERSON>r", "components.Discover.DiscoverSliderEdit.enable": "Alternar Visibilidad", "components.Discover.PlexWatchlistSlider.emptywatchlist": "Contenido añadido a tu <PlexWatchlistSupportLink> lista de visualización de Plex </PlexWatchlistSupportLink> aparecerá aquí.", "components.Discover.PlexWatchlistSlider.plexwatchlist": "Vista de Visualización de Plex", "components.Discover.DiscoverTvKeyword.keywordSeries": "{keywordTitle} Series", "components.Discover.createnewslider": "Crear Nuevo Deslizador", "components.Discover.RecentlyAddedSlider.recentlyAdded": "Añadido <PERSON>", "components.Discover.moviegenres": "Géneros de Películas", "components.Discover.networks": "Cadenas", "components.Discover.emptywatchlist": "Contenido añadido a tu <PlexWatchlistSupportLink> lista de visualización de Plex </PlexWatchlistSupportLink> aparecerá aquí.", "components.Discover.resettodefault": "Restablecer valores Predeterminados", "components.Discover.resetwarning": "Restablecer todos los deslizadores por defecto. ¡Esto también borrará los deslizadores personalizados!", "components.Discover.resetfailed": "Algo falló restableciendo los ajustes personalizados de recomendaciones.", "components.Discover.resetsuccess": "Se ha restablecido correctamente la configuración de personalización de Descubrir.", "components.Discover.stopediting": "<PERSON><PERSON>", "components.Discover.tmdbmoviegenre": "Género de películas TMDB", "components.Discover.tmdbmoviekeyword": "Palabras clave de la película de TMDB", "components.Discover.tmdbnetwork": "Cadena TMDB", "components.Discover.studios": "Est<PERSON><PERSON>", "components.Discover.tvgenres": "Géneros de Series", "components.Discover.tmdbsearch": "Búsqueda TMDB", "components.Discover.tmdbstudio": "Estudio TMDB", "components.Discover.tmdbtvgenre": "Género de Series TMDB", "components.Discover.tmdbtvkeyword": "Palabra clave de Series TMDB", "components.Discover.updatefailed": "Algo falló al actualizar las opciones personalizadas de recomendaciones.", "components.Discover.updatesuccess": "Opciones personalizadas de recomendaciones actualizadas.", "components.ManageSlideOver.opentautulli": "<PERSON><PERSON><PERSON> en <PERSON>tulli", "components.ManageSlideOver.playedby": "Reproducido <PERSON>", "components.MovieDetails.tmdbuserscore": "Puntuación de usuario de TMDB", "components.Discover.DiscoverMovies.discovermovies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Discover.DiscoverMovies.sortPopularityAsc": "Popularidad Ascendente", "components.Discover.DiscoverMovies.sortPopularityDesc": "Popularidad descendente", "components.Discover.DiscoverMovies.sortReleaseDateAsc": "<PERSON><PERSON> de salida Ascendente", "components.Discover.DiscoverMovies.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverMovies.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.DiscoverMovies.sortTmdbRatingAsc": "Ratio TMDB Ascendente", "components.Discover.DiscoverMovies.sortTmdbRatingDesc": "Ratio TMDB Descendente", "components.Discover.DiscoverMovies.sortReleaseDateDesc": "<PERSON><PERSON> de salida Descendente", "components.Discover.DiscoverTv.sortPopularityAsc": "Popularidad Ascendente", "components.Discover.DiscoverTv.sortPopularityDesc": "Popularidad Descendente", "components.Discover.DiscoverTv.sortTitleAsc": "<PERSON><PERSON><PERSON><PERSON> (A-Z) Ascendente", "components.Discover.DiscoverTv.sortTitleDesc": "<PERSON><PERSON><PERSON><PERSON> (Z-A) Descendente", "components.Discover.DiscoverTv.discovertv": "Series", "components.Discover.DiscoverTv.sortTmdbRatingAsc": "Puntuación TMDB Ascendente", "components.Discover.DiscoverTv.sortFirstAirDateAsc": "Fecha de Primera Emisión Ascendente", "components.Discover.DiscoverTv.sortFirstAirDateDesc": "Fecha de Primera Emisión Descendente", "components.Discover.DiscoverTv.sortTmdbRatingDesc": "Puntuación TMDB Descendente", "components.Discover.FilterSlideover.keywords": "Palabras claves", "components.Discover.FilterSlideover.filters": "<PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.clearfilters": "Limpiar Filtros Activos", "components.Discover.FilterSlideover.firstAirDate": "Primera Fecha de Emisión", "components.Discover.FilterSlideover.originalLanguage": "Idioma Original", "components.Discover.FilterSlideover.releaseDate": "Fecha de Publicación", "components.Discover.FilterSlideover.studio": "Estudio", "components.Discover.FilterSlideover.tmdbuserscore": "Puntuación de Usuarios de TMDB", "components.Discover.FilterSlideover.to": "A", "components.Discover.FilterSlideover.from": "<PERSON><PERSON>", "components.Discover.FilterSlideover.genres": "<PERSON><PERSON><PERSON><PERSON>", "components.Discover.FilterSlideover.ratingText": "Puntuaciones entre {minValue} y {maxValue}", "components.Discover.customizediscover": "<PERSON><PERSON><PERSON>", "components.Layout.Sidebar.browsetv": "Series", "components.Layout.Sidebar.browsemovies": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "components.Layout.UserDropdown.MiniQuotaDisplay.movierequests": "Peticiones de Películas", "components.ManageSlideOver.manageModalAdvanced": "<PERSON><PERSON><PERSON>", "components.ManageSlideOver.manageModalMedia4k": "Contenido 4K", "components.DownloadBlock.formattedTitle": "{title}: <PERSON><PERSON><PERSON> {seasonNumber} Episodio {episodeNumber}", "components.Layout.UserDropdown.MiniQuotaDisplay.seriesrequests": "Peticiones de Series", "components.NotificationTypeSelector.mediaautorequested": "Petición Enviada Automáticamente", "components.Discover.DiscoverWatchlist.watchlist": "Lista de seguimiento Plex", "components.Discover.FilterSlideover.streamingservices": "Servic<PERSON> de Streaming", "components.Layout.UserDropdown.requests": "Peticiones", "components.TvDetails.reportissue": "Informar de un problema", "components.TvDetails.rtcriticsscore": "Tomatómetro de Rotten Tomatoes", "components.TvDetails.seasonstitle": "Temporadas", "components.TvDetails.tmdbuserscore": "Puntuación de Usuarios de TMDB", "components.Settings.Notifications.NotificationsGotify.validationTypes": "Debe seleccionar al menos un tipo de notificación", "components.NotificationTypeSelector.mediaautorequestedDescription": "Reciba notificaciones cuando se envíen nuevas solicitudes de contenido para elementos de su lista de seguimiento de Plex.", "components.PermissionEdit.autorequestDescription": "Conceder permiso para enviar solicitudes de medios que no sean 4K a través de Plex Watchlist.", "components.PermissionEdit.autorequestMovies": "Solicitar Películas automáticamente", "components.Discover.FilterSlideover.runtimeText": "{minValue}-{maxValue} Duración en minutos", "components.PermissionEdit.viewwatchlistsDescription": "Conceder permiso para ver las listas de seguimiento de Plex de otros usuarios.", "components.RequestBlock.decline": "<PERSON><PERSON><PERSON> so<PERSON>", "components.RequestBlock.lastmodifiedby": "Última Modificación Por", "components.RequestBlock.requestdate": "<PERSON><PERSON>", "components.RequestCard.declinerequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.tvdbid": "Identificador de TheTVDB", "components.RequestModal.requestseries4ktitle": "Solicitar Serie en 4K", "components.RequestModal.requestseasons4k": "Solicitar {seasonCount} {seasonCount, plural, one {Season} other {Seasons}} en 4K", "components.RequestModal.selectmovies": "<PERSON><PERSON><PERSON><PERSON><PERSON>(s)", "components.Selector.showmore": "Mostrar más", "components.Selector.searchStudios": "Buscar estudios…", "components.Selector.nooptions": "Sin resultados.", "components.Selector.searchKeywords": "Buscar palabras clave…", "components.Selector.showless": "<PERSON>er menos", "components.Selector.searchGenres": "<PERSON><PERSON><PERSON><PERSON><PERSON>…", "components.Selector.starttyping": "Escriba para buscar.", "components.Settings.Notifications.NotificationsGotify.validationUrlTrailingSlash": "La URL no debe terminar con una barra al final", "components.Settings.Notifications.enableMentions": "Activar menciones", "components.Settings.SettingsJobsCache.editJobScheduleCurrent": "Frecuencia actual", "components.Settings.SettingsJobsCache.imagecachesize": "Tamaño total de la caché", "components.Settings.SettingsJobsCache.imagecachecount": "Imágenes en caché", "components.Settings.SettingsMain.general": "General", "components.Settings.SettingsMain.generalsettings": "Configuración general", "components.Settings.SettingsMain.hideAvailable": "Ocultar Contenido disponible", "components.StatusBadge.managemedia": "Gestionar {mediaType}", "components.StatusBadge.seasonepisodenumber": "S{seasonNumber}E{episodeNumber}", "components.Settings.SettingsMain.generalsettingsDescription": "Configure los ajustes globales y por defecto de Jellyseerr.", "components.Settings.SettingsMain.toastSettingsFailure": "Algo ha ido mal al guardar los ajustes.", "components.Settings.SettingsMain.validationApplicationUrl": "Debe proporcionar una URL válida", "components.Settings.SettingsMain.validationApplicationTitle": "Debe proporcionar un título de solicitud", "components.Settings.tautulliApiKey": "Clave API", "components.StatusBadge.openinarr": "<PERSON><PERSON><PERSON> en {arr}", "components.StatusBadge.playonplex": "Ver en Plex", "components.StatusChecker.reloadApp": "Recargar {applicationTitle}", "components.TitleCard.mediaerror": "{mediaType} No Encontrado", "components.TvDetails.Season.somethingwentwrong": "Algo ha ido mal al obtener los datos de la temporada.", "components.TvDetails.episodeCount": "{episodeCount, plural, one {# Episodio} other {# Episodios}}", "components.TvDetails.rtaudiencescore": "Puntuación audiencia de Rotten Tomatoes", "components.UserProfile.UserSettings.UserGeneralSettings.discordIdTip": "El <FindDiscordIdLink>multi-digit ID number</FindDiscordIdLink> asociado a su cuenta de usuario de Discord", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmovies": "Solicitar Películas automáticamente", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncmoviestip": "Solicite películas automáticamente en su <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseriestip": "Solicite series automáticamente series en su <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink>", "components.UserProfile.emptywatchlist": "Los medios añadidos a su <PlexWatchlistSupportLink>Plex Watchlist</PlexWatchlistSupportLink> aparecerán aquí.", "components.UserProfile.plexwatchlist": "Lista de seguimiento Plex", "components.Discover.FilterSlideover.activefilters": "{count, plural, one {# Filtro Activo} other {# Filtros Activos}}", "components.MovieDetails.productioncountries": "Producción {countryCount, plural, one {País} other {Países}}", "components.Settings.SettingsJobsCache.imagecacheDescription": "Cuando está habilitado en la configuración, <PERSON><PERSON><PERSON><PERSON> obtendrá y almacenará en caché imágenes de fuentes externas preconfiguradas. Las imágenes almacenadas en caché se guardan en la carpeta de configuración. Puede encontrar los archivos en <code>{appDataPath}/cache/images</code>.", "components.PermissionEdit.autorequest": "Auto solicitar", "components.PermissionEdit.autorequestMoviesDescription": "Conceder permiso para enviar solicitudes de películas que no sean 4K a través de Plex Watchlist.", "components.RequestBlock.approve": "<PERSON><PERSON><PERSON> solicitud", "components.RequestCard.approverequest": "<PERSON><PERSON><PERSON>", "components.RequestCard.cancelrequest": "<PERSON><PERSON><PERSON>", "components.PermissionEdit.autorequestSeriesDescription": "Conceder permiso para enviar solicitudes de series que no sean 4K a través de Plex Watchlist.", "components.RequestCard.editrequest": "<PERSON><PERSON>", "components.RequestBlock.delete": "Eliminar solicitud", "components.RequestBlock.edit": "<PERSON><PERSON> solicitud", "components.RequestBlock.requestedby": "Solicitado Por", "components.RequestCard.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestList.RequestItem.unknowntitle": "<PERSON><PERSON><PERSON><PERSON>", "components.RequestModal.SearchByNameModal.nomatches": "No se ha podido encontrar ninguna coincidencia para esta serie.", "components.RequestModal.requestseriestitle": "Solicitar Serie", "components.RequestModal.requestmovie4ktitle": "Solicitar Película en 4K", "components.RequestModal.approve": "<PERSON><PERSON><PERSON>", "components.RequestModal.requestApproved": "¡Solicitud de <strong>{title}</strong> aprobada!", "components.RequestModal.requestcollection4ktitle": "Solicitar Colección en 4K", "components.RequestModal.requestcollectiontitle": "Solicitar <PERSON>", "components.RequestModal.requestmovietitle": "Solicitar <PERSON>", "components.Settings.urlBase": "Base URL", "components.Settings.validationUrl": "Debe proporcionar una URL válida", "components.StatusChecker.appUpdatedDescription": "Haga clic en el botón de abajo para recargar la aplicación.", "components.StatusChecker.restartRequiredDescription": "Reinicie el servidor para aplicar la configuración actualizada.", "components.TitleCard.cleardata": "Limpia<PERSON>", "components.UserProfile.UserSettings.UserGeneralSettings.discordId": "ID de usuario de Discord", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestFailed": "No se ha podido enviar la notificación de prueba de Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSending": "Enviando notificación de prueba de Gotify…", "components.StatusChecker.restartRequired": "Es necesario reiniciar el servidor", "components.Settings.Notifications.NotificationsGotify.gotifysettingssaved": "¡La configuración de notificaciones de Gotify se ha guardado correctamente!", "components.Settings.Notifications.NotificationsGotify.url": "URL del servidor", "components.Settings.Notifications.NotificationsGotify.validationTokenRequired": "Debe proporcionar un token de aplicación", "components.Settings.Notifications.NotificationsGotify.validationUrlRequired": "Debe proporcionar una URL válida", "components.Settings.RadarrModal.released": "Publicado", "components.PermissionEdit.viewrecentDescription": "Concede permiso para ver la lista de contenidos añadidos recientemente.", "components.PermissionEdit.viewrecent": "Ver recientemente añadido", "components.Settings.toastTautulliSettingsFailure": "Algo salió mal al guardar la configuración de Tautulli.", "components.Settings.validationUrlBaseLeadingSlash": "La base de la URL debe tener una barra al principio", "components.StatusChecker.appUpdated": "{applicationTitle} Actualizado", "components.Settings.SettingsLogs.viewdetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "components.Settings.SettingsMain.apikey": "Clave API", "components.Settings.SettingsMain.applicationurl": "URL de la aplicación", "components.Settings.SettingsMain.cacheImages": "Activar el caché de imágenes", "components.Settings.SettingsMain.originallanguage": "Idioma en Descubre", "components.Settings.SettingsMain.locale": "Idioma de visualización", "components.Settings.SettingsMain.originallanguageTip": "Filtrar contenidos por idioma original", "components.Settings.SettingsMain.partialRequestsEnabled": "Permitir Solicitudes Parciales de Series", "components.Settings.SettingsMain.toastApiKeyFailure": "Algo ha ido mal al generar una nueva clave API.", "components.Settings.SettingsMain.toastApiKeySuccess": "¡Nueva clave API generada con éxito!", "components.Settings.advancedTooltip": "Una configuración incorrecta de este parámetro puede afectar a la funcionalidad", "components.Settings.restartrequiredTooltip": "<PERSON><PERSON><PERSON><PERSON> debe reiniciarse para que los cambios en esta configuración surtan efecto", "components.TvDetails.seasonnumber": "Temporada {seasonNumber}", "components.TitleCard.tmdbid": "TMDB ID", "components.UserList.newplexsigninenabled": "El ajuste <strong>Enable New Plex Sign-In</strong> está actualmente activado. Los usuarios de Plex con acceso a la biblioteca no necesitan ser importados para abrir sesión.", "components.ManageSlideOver.alltime": "Tiempo total", "components.RequestModal.requestmovies": "Solicitar {count} {count, plugarl, one {Película} other {Películas}}", "components.Settings.tautulliSettings": "Configuración de Tautulli", "components.Settings.validationUrlTrailingSlash": "La URL no debe terminar con una barra al final", "components.PermissionEdit.autorequestSeries": "Solicitar series automáticamente", "components.UserProfile.UserSettings.UserGeneralSettings.plexwatchlistsyncseries": "Solicitar series automáticamente", "components.PermissionEdit.viewwatchlists": "Ver listas de seguimiento de Plex", "components.RequestCard.tmdbid": "TMDB ID", "components.Settings.Notifications.NotificationsPushbullet.channelTag": "Etiqueta de canal", "components.RequestBlock.languageprofile": "Perfil lingüístico", "components.RequestModal.requestmovies4k": "Solicitar {count} {count, plugarl, one {Pel<PERSON><PERSON>} other {Pelí<PERSON>s}} en 4K", "components.Settings.RadarrModal.inCinemas": "En Cines", "components.UserProfile.recentlywatched": "Vistos recientemente", "components.Settings.Notifications.NotificationsGotify.agentenabled": "Habilitar agente", "components.Settings.Notifications.NotificationsGotify.gotifysettingsfailed": "No se han guardado los ajustes de notificación de Gotify.", "components.Settings.Notifications.NotificationsGotify.toastGotifyTestSuccess": "¡Notificación de prueba de Gotify enviada!", "components.Settings.Notifications.NotificationsGotify.token": "Token de aplicación", "components.TvDetails.manageseries": "Gestionar Series", "components.TvDetails.status4k": "4K {status}", "components.Settings.SettingsAbout.appDataPath": "Directorio de datos", "components.Discover.DiscoverMovies.activefilters": "{count, plural, one {# Filtro Activo} other {# Filtros Activos}}", "components.Discover.DiscoverTv.activefilters": "{count, plural, one {# Filtro Activo} other {# Filtros Activos}}", "components.Discover.FilterSlideover.runtime": "Duración", "components.ManageSlideOver.plays": "<strong>{playCount, number}</strong> {playCount, plural, one {reproducir} other {reproducir}}", "components.Settings.RadarrModal.announced": "<PERSON><PERSON><PERSON><PERSON>", "components.Settings.SettingsJobsCache.plex-watchlist-sync": "Sincronización de la lista de seguimiento de Plex", "components.Settings.SettingsMain.applicationTitle": "Titulo de la aplicación", "components.Settings.SettingsMain.cacheImagesTip": "Almacenamiento en caché de imágenes de origen externo (requiere una cantidad significativa de espacio en disco)", "components.Settings.SettingsMain.toastSettingsSuccess": "¡Ajustes guardados correctamente!", "components.Settings.SettingsMain.validationApplicationUrlTrailingSlash": "La URL no debe terminar con una barra al final", "components.Settings.tautulliSettingsDescription": "Opcionalmente configure los ajustes para su servidor <PERSON>. Jellyseerr obtiene de Tautulli los datos del historial de visionado de los medios de Plex.", "components.TitleCard.tvdbid": "Identificador de TheTVDB", "components.TvDetails.Season.noepisodes": "Lista de episodios no disponible.", "components.TvDetails.productioncountries": "Producción {countryCount, plural, one {País} other {Países}}", "components.UserProfile.UserSettings.UserGeneralSettings.validationDiscordId": "Debe proporcionar un ID de Discord válido", "i18n.restartRequired": "Reinicio necesario", "components.Settings.deleteServer": "Eliminar servidor {serverType}", "components.Settings.experimentalTooltip": "Activar esta opción puede provocar un comportamiento inesperado de la aplicación", "components.Settings.externalUrl": "URL externa", "components.Settings.toastTautulliSettingsSuccess": "¡Configuración de Tautulli guardada con éxito!", "components.Settings.validationApiKey": "Debe proporcionar una clave API", "components.Settings.validationUrlBaseTrailingSlash": "La base de la URL no debe terminar en una barra al final", "components.RequestList.RequestItem.tmdbid": "TMDB ID", "components.RequestList.RequestItem.tvdbid": "Identificador de TheTVDB", "components.Settings.SettingsJobsCache.image-cache-cleanup": "Limpieza de la caché de imágenes", "components.Settings.SettingsJobsCache.imagecache": "<PERSON><PERSON><PERSON> imágene<PERSON>", "components.Settings.SettingsJobsCache.editJobScheduleSelectorSeconds": "Cada {jobScheduleSeconds, plural, one {segundo} other {{jobScheduleSeconds} segundos}}", "components.Settings.SettingsJobsCache.availability-sync": "Sincronización de la disponibilidad de medios", "components.Discover.tmdbmoviestreamingservices": "Servicios de streaming de películas TMDB", "components.Discover.tmdbtvstreamingservices": "Servicios de TV en streaming TMDB", "components.Discover.FilterSlideover.tmdbuservotecount": "Recuento de votos de los usuarios de TMDB", "components.Discover.FilterSlideover.voteCount": "Número de votos entre {minValue} y {maxValue}", "components.Settings.RadarrModal.tagRequests": "Solicitudes de etiquetas", "components.Settings.SonarrModal.tagRequests": "Solicitudes de etiquetas", "i18n.collection": "Colección"}