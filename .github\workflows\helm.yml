name: Release Charts

on:
  push:
    branches:
      - develop

jobs:
  package-helm-chart:
    name: Package helm chart
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: read
    outputs:
      has_artifacts: ${{ steps.check-artifacts.outputs.has_artifacts }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install helm
        uses: azure/setup-helm@v4

      - name: Install Oras
        uses: oras-project/setup-oras@v1

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Package helm charts
        run: |
          mkdir -p ./.cr-release-packages
          for chart_path in ./charts/*; do
            if [ -d "$chart_path" ] && [ -f "$chart_path/Chart.yaml" ]; then
              chart_name=$(grep '^name:' "$chart_path/Chart.yaml" | awk '{print $2}')
              # get current version
              current_version=$(grep '^version:' "$chart_path/Chart.yaml" | awk '{print $2}')
              # try to get current release version
              set +e
              oras discover ghcr.io/${GITHUB_REPOSITORY@L}/${chart_name}:${current_version}
              oras_exit_code=$?
              set -e

              if [ $oras_exit_code -ne 0 ]; then
                helm dependency build "$chart_path"
                helm package "$chart_path" --destination ./.cr-release-packages
              else
                echo "No version change for $chart_name. Skipping."
              fi
            else
              echo "Skipping $chart_name: Not a valid Helm chart"
            fi
          done

      - name: Check if artifacts exist
        id: check-artifacts
        run: |
          if ls .cr-release-packages/* >/dev/null 2>&1; then
            echo "has_artifacts=true" >> $GITHUB_OUTPUT
          else
            echo "has_artifacts=false" >> $GITHUB_OUTPUT
          fi

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        if: steps.check-artifacts.outputs.has_artifacts == 'true'
        with:
          name: artifacts
          include-hidden-files: true
          path: .cr-release-packages/

  publish:
    name: Publish to ghcr.io
    runs-on: ubuntu-latest
    permissions:
      packages: write # needed for pushing to github registry
      id-token: write # needed for signing the images with GitHub OIDC Token
    needs: [package-helm-chart]
    if: needs.package-helm-chart.outputs.has_artifacts == 'true'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install helm
        uses: azure/setup-helm@v4

      - name: Install Oras
        uses: oras-project/setup-oras@v1

      - name: Install Cosign
        uses: sigstore/cosign-installer@v3

      - name: Downloads artifacts
        uses: actions/download-artifact@v4
        with:
          name: artifacts
          path: .cr-release-packages/

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push charts to GHCR
        env:
          COSIGN_YES: true
        run: |
          for chart_path in `find .cr-release-packages -name '*.tgz' -print`; do
            # push chart to OCI
            chart_release_file=$(basename "$chart_path")
            chart_name=${chart_release_file%-*}
            helm push ${chart_path} oci://ghcr.io/${GITHUB_REPOSITORY@L} |& tee helm-push-output.log
            chart_digest=$(awk -F "[, ]+" '/Digest/{print $NF}' < helm-push-output.log)
            # sign chart
            cosign sign "ghcr.io/${GITHUB_REPOSITORY@L}/${chart_name}@${chart_digest}"
            # push artifacthub-repo.yml to OCI
            oras push \
              ghcr.io/${GITHUB_REPOSITORY@L}/${chart_name}:artifacthub.io \
              --config /dev/null:application/vnd.cncf.artifacthub.config.v1+yaml \
              charts/$chart_name/artifacthub-repo.yml:application/vnd.cncf.artifacthub.repository-metadata.layer.v1.yaml \
              |& tee oras-push-output.log
            artifacthub_digest=$(grep "Digest:" oras-push-output.log | awk '{print $2}')
            # sign artifacthub-repo.yml
            cosign sign "ghcr.io/${GITHUB_REPOSITORY@L}/${chart_name}:artifacthub.io@${artifacthub_digest}"
          done
